#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帶 GUI 的增強檢測測試
Enhanced Detection Test with GUI
"""

import os
import sys
import time
import tkinter as tk
from tkinter import messagebox, scrolledtext
from datetime import datetime
import threading

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AGES-KH-Bot 增強檢測測試")
        self.root.geometry("800x600")
        
        # 測試狀態
        self.browser_started = False
        self.detection_completed = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """設置 UI"""
        # 標題
        title_label = tk.Label(self.root, text="🧪 AGES-KH-Bot 增強檢測測試", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 狀態顯示
        self.status_label = tk.Label(self.root, text="準備開始測試", 
                                   font=("Arial", 12), fg="blue")
        self.status_label.pack(pady=5)
        
        # 步驟說明
        steps_frame = tk.Frame(self.root)
        steps_frame.pack(pady=10, padx=20, fill="x")
        
        steps_text = """
測試步驟：
1. 點擊 "啟動瀏覽器" 按鈕
2. 在瀏覽器中手動登入系統
3. 導航到進廠確認單列表
4. 點擊任一編輯按鈕打開編輯彈窗
5. 確保編輯彈窗完全打開後，點擊 "執行檢測" 按鈕
6. 查看檢測結果
        """
        
        steps_label = tk.Label(steps_frame, text=steps_text, 
                             font=("Arial", 10), justify="left")
        steps_label.pack(anchor="w")
        
        # 按鈕區域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 啟動瀏覽器按鈕
        self.start_browser_btn = tk.Button(button_frame, text="🌐 啟動瀏覽器", 
                                         command=self.start_browser,
                                         font=("Arial", 12), bg="lightgreen")
        self.start_browser_btn.pack(side="left", padx=10)
        
        # 執行檢測按鈕
        self.run_detection_btn = tk.Button(button_frame, text="🔍 執行檢測", 
                                         command=self.run_detection,
                                         font=("Arial", 12), bg="lightblue",
                                         state="disabled")
        self.run_detection_btn.pack(side="left", padx=10)
        
        # 查看結果按鈕
        self.view_results_btn = tk.Button(button_frame, text="📊 查看結果", 
                                        command=self.view_results,
                                        font=("Arial", 12), bg="lightyellow",
                                        state="disabled")
        self.view_results_btn.pack(side="left", padx=10)
        
        # 關閉按鈕
        self.close_btn = tk.Button(button_frame, text="❌ 關閉", 
                                 command=self.close_app,
                                 font=("Arial", 12), bg="lightcoral")
        self.close_btn.pack(side="right", padx=10)
        
        # 日誌顯示區域
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(log_frame, text="檢測日誌:", font=("Arial", 12, "bold")).pack(anchor="w")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill="both", expand=True)
        
        # 結果存儲
        self.detection_result = None
        
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def update_status(self, status, color="blue"):
        """更新狀態"""
        self.status_label.config(text=status, fg=color)
        self.root.update()
        
    def start_browser(self):
        """啟動瀏覽器"""
        try:
            self.update_status("正在啟動瀏覽器...", "orange")
            self.log("🌐 開始啟動瀏覽器...")
            
            # 在新線程中啟動瀏覽器
            def browser_thread():
                try:
                    from mvp_grabber import start_browser
                    
                    test_task = {
                        'browser': 'chrome',
                        'order_id': 'TEST',
                        'trigger_time': '09:30:00.001',
                        'model': 'A'
                    }
                    
                    if start_browser(test_task):
                        self.browser_started = True
                        self.root.after(0, lambda: self.on_browser_started())
                    else:
                        self.root.after(0, lambda: self.on_browser_failed())
                        
                except Exception as e:
                    self.root.after(0, lambda: self.on_browser_error(str(e)))
            
            threading.Thread(target=browser_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動瀏覽器失敗: {e}")
            self.update_status("瀏覽器啟動失敗", "red")
            
    def on_browser_started(self):
        """瀏覽器啟動成功回調"""
        self.log("✅ 瀏覽器啟動成功")
        self.update_status("瀏覽器已啟動，請手動操作到編輯彈窗", "green")
        self.start_browser_btn.config(state="disabled")
        self.run_detection_btn.config(state="normal")
        
    def on_browser_failed(self):
        """瀏覽器啟動失敗回調"""
        self.log("❌ 瀏覽器啟動失敗")
        self.update_status("瀏覽器啟動失敗", "red")
        
    def on_browser_error(self, error):
        """瀏覽器啟動錯誤回調"""
        self.log(f"❌ 瀏覽器啟動錯誤: {error}")
        self.update_status("瀏覽器啟動錯誤", "red")
        
    def run_detection(self):
        """執行檢測"""
        if not self.browser_started:
            messagebox.showerror("錯誤", "請先啟動瀏覽器")
            return
            
        # 確認用戶已完成手動操作
        result = messagebox.askyesno("確認", 
                                   "請確認您已經：\n"
                                   "1. 登入系統\n"
                                   "2. 導航到進廠確認單列表\n"
                                   "3. 點擊編輯按鈕打開編輯彈窗\n"
                                   "4. 編輯彈窗已完全打開\n\n"
                                   "是否繼續執行檢測？")
        
        if not result:
            return
            
        try:
            self.update_status("正在執行檢測...", "orange")
            self.log("🔍 開始執行增強檢測...")
            self.run_detection_btn.config(state="disabled")
            
            # 在新線程中執行檢測
            def detection_thread():
                try:
                    from mvp_grabber import enhanced_dialog_button_detection
                    
                    result = enhanced_dialog_button_detection()
                    self.root.after(0, lambda: self.on_detection_completed(result))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.on_detection_error(str(e)))
            
            threading.Thread(target=detection_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 執行檢測失敗: {e}")
            self.update_status("檢測執行失敗", "red")
            self.run_detection_btn.config(state="normal")
            
    def on_detection_completed(self, result):
        """檢測完成回調"""
        self.detection_result = result
        self.detection_completed = True
        
        if result:
            self.log("✅ 檢測完成")
            self.update_status("檢測完成，點擊查看結果", "green")
            
            # 簡要顯示結果
            submit_count = len(result.get('submit_buttons', []))
            cancel_count = len(result.get('cancel_buttons', []))
            other_count = len(result.get('other_buttons', []))
            
            self.log(f"📊 檢測結果摘要:")
            self.log(f"  - 送出按鈕: {submit_count} 個")
            self.log(f"  - 取消按鈕: {cancel_count} 個")
            self.log(f"  - 其他按鈕: {other_count} 個")
            
            if result.get('active_iframe') is not None:
                self.log(f"  - 檢測位置: iframe {result['active_iframe'] + 1}")
            else:
                self.log(f"  - 檢測位置: 主頁面")
                
        else:
            self.log("❌ 檢測失敗")
            self.update_status("檢測失敗", "red")
            
        self.view_results_btn.config(state="normal")
        self.run_detection_btn.config(state="normal")
        
    def on_detection_error(self, error):
        """檢測錯誤回調"""
        self.log(f"❌ 檢測錯誤: {error}")
        self.update_status("檢測錯誤", "red")
        self.run_detection_btn.config(state="normal")
        
    def view_results(self):
        """查看詳細結果"""
        if not self.detection_completed or not self.detection_result:
            messagebox.showwarning("警告", "沒有可查看的檢測結果")
            return
            
        # 創建結果窗口
        result_window = tk.Toplevel(self.root)
        result_window.title("檢測結果詳情")
        result_window.geometry("600x500")
        
        # 結果文本
        result_text = scrolledtext.ScrolledText(result_window, height=25, width=70)
        result_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 格式化結果
        result = self.detection_result
        result_content = f"""
🔍 增強檢測結果詳情
{'='*50}

📊 按鈕統計:
  - 送出按鈕: {len(result.get('submit_buttons', []))} 個
  - 取消按鈕: {len(result.get('cancel_buttons', []))} 個  
  - 其他按鈕: {len(result.get('other_buttons', []))} 個
  - 總計: {len(result.get('submit_buttons', [])) + len(result.get('cancel_buttons', [])) + len(result.get('other_buttons', []))} 個

🎯 檢測位置:
  - 活動 iframe: {result.get('active_iframe', 'None')}
  - 找到彈窗標題: {result.get('title_found', False)}

📸 截圖文件:
  - {result.get('screenshot_path', 'None')}

🔍 送出按鈕詳情:
"""
        
        for i, btn in enumerate(result.get('submit_buttons', [])):
            result_content += f"  {i+1}. {btn.get('tag', 'N/A')} - '{btn.get('text', '')}' (visible={btn.get('visible', False)})\n"
            
        result_content += "\n⚠️ 取消按鈕詳情:\n"
        for i, btn in enumerate(result.get('cancel_buttons', [])):
            result_content += f"  {i+1}. {btn.get('tag', 'N/A')} - '{btn.get('text', '')}' (visible={btn.get('visible', False)})\n"
            
        result_content += "\n❓ 其他按鈕詳情:\n"
        for i, btn in enumerate(result.get('other_buttons', [])):
            result_content += f"  {i+1}. {btn.get('tag', 'N/A')} - '{btn.get('text', '')}' (visible={btn.get('visible', False)})\n"
        
        result_text.insert(tk.END, result_content)
        
    def close_app(self):
        """關閉應用"""
        if messagebox.askyesno("確認", "確定要關閉應用嗎？"):
            try:
                # 嘗試關閉瀏覽器
                if self.browser_started:
                    from mvp_grabber import driver
                    if driver:
                        driver.quit()
            except:
                pass
            self.root.destroy()
            
    def run(self):
        """運行 GUI"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🧪 啟動 AGES-KH-Bot 增強檢測測試 GUI")
    
    try:
        app = TestGUI()
        app.run()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")

if __name__ == "__main__":
    main()
