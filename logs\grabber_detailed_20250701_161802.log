2025-07-01 16:18:02,162 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_161802.log
2025-07-01 16:18:05,943 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:18:05,943 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:18:06,623 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 16:18:06,624 - DEBUG - chromedriver not found in PATH
2025-07-01 16:18:06,624 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:18:06,624 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:18:06,624 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 16:18:06,624 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:18:06,624 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:18:06,625 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:18:06,625 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:18:06,628 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 20664 using 0 to output -3
2025-07-01 16:18:07,137 - DEBUG - POST http://localhost:55194/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:18:07,138 - DEBUG - Starting new HTTP connection (1): localhost:55194
2025-07-01 16:18:07,668 - DEBUG - http://localhost:55194 "POST /session HTTP/1.1" 200 0
2025-07-01 16:18:07,668 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir20664_1470734591"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55199"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"a13bef38b22ccfc67c542c39f7842f46"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:07,669 - DEBUG - Finished Request
2025-07-01 16:18:07,669 - DEBUG - POST http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:18:09,126 - DEBUG - http://localhost:55194 "POST /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:09,127 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:09,127 - DEBUG - Finished Request
2025-07-01 16:18:09,127 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:18:09,128 - DEBUG - POST http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:18:09,134 - DEBUG - http://localhost:55194 "POST /session/a13bef38b22ccfc67c542c39f7842f46/execute/sync HTTP/1.1" 200 0
2025-07-01 16:18:09,134 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:09,134 - DEBUG - Finished Request
2025-07-01 16:18:09,134 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:18:09,135 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:09,168 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:09,168 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:09,168 - DEBUG - Finished Request
2025-07-01 16:18:10,169 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:10,177 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:10,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:10,177 - DEBUG - Finished Request
2025-07-01 16:18:11,178 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:11,185 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:11,185 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:11,185 - DEBUG - Finished Request
2025-07-01 16:18:12,186 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:12,194 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:12,194 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:12,194 - DEBUG - Finished Request
2025-07-01 16:18:13,195 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:13,202 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:13,202 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:13,202 - DEBUG - Finished Request
2025-07-01 16:18:14,203 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:14,209 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:14,210 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:14,210 - DEBUG - Finished Request
2025-07-01 16:18:15,211 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:15,218 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:15,218 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:15,218 - DEBUG - Finished Request
2025-07-01 16:18:16,220 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:16,227 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:16,227 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:16,227 - DEBUG - Finished Request
2025-07-01 16:18:17,228 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:17,234 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:17,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:17,234 - DEBUG - Finished Request
2025-07-01 16:18:18,236 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:18,243 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:18,243 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:18,243 - DEBUG - Finished Request
2025-07-01 16:18:19,244 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:19,250 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 200 0
2025-07-01 16:18:19,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:19,251 - DEBUG - Finished Request
2025-07-01 16:18:20,252 - DEBUG - GET http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46/url {}
2025-07-01 16:18:20,254 - DEBUG - http://localhost:55194 "GET /session/a13bef38b22ccfc67c542c39f7842f46/url HTTP/1.1" 404 0
2025-07-01 16:18:20,254 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:20,255 - DEBUG - Finished Request
2025-07-01 16:18:20,256 - DEBUG - DELETE http://localhost:55194/session/a13bef38b22ccfc67c542c39f7842f46 {}
2025-07-01 16:18:20,284 - DEBUG - http://localhost:55194 "DELETE /session/a13bef38b22ccfc67c542c39f7842f46 HTTP/1.1" 200 0
2025-07-01 16:18:20,284 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:18:20,285 - DEBUG - Finished Request
