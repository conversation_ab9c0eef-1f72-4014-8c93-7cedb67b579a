2025-07-01 16:12:23,233 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_161223.log
2025-07-01 16:12:32,554 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:12:32,555 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:12:32,608 - DEBUG - chromedriver not found in PATH
2025-07-01 16:12:32,609 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:12:32,609 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:12:32,609 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:12:32,609 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:12:32,609 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:12:32,609 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:12:32,613 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 19312 using 0 to output -3
2025-07-01 16:12:33,131 - DEBUG - POST http://localhost:55028/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:12:33,131 - DEBUG - Starting new HTTP connection (1): localhost:55028
2025-07-01 16:12:33,661 - DEBUG - http://localhost:55028 "POST /session HTTP/1.1" 200 0
2025-07-01 16:12:33,661 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir19312_1298715131"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55031"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"00f68acd6798a7356ff94e29d4ad0b26"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:33,661 - DEBUG - Finished Request
2025-07-01 16:12:33,662 - DEBUG - POST http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:12:35,223 - DEBUG - http://localhost:55028 "POST /session/00f68acd6798a7356ff94e29d4ad0b26/url HTTP/1.1" 200 0
2025-07-01 16:12:35,223 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:35,223 - DEBUG - Finished Request
2025-07-01 16:12:35,224 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:12:35,224 - DEBUG - POST http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:12:35,230 - DEBUG - http://localhost:55028 "POST /session/00f68acd6798a7356ff94e29d4ad0b26/execute/sync HTTP/1.1" 200 0
2025-07-01 16:12:35,231 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:35,231 - DEBUG - Finished Request
2025-07-01 16:12:35,231 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:12:35,231 - DEBUG - GET http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26/url {}
2025-07-01 16:12:35,263 - DEBUG - http://localhost:55028 "GET /session/00f68acd6798a7356ff94e29d4ad0b26/url HTTP/1.1" 200 0
2025-07-01 16:12:35,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:35,263 - DEBUG - Finished Request
2025-07-01 16:12:36,264 - DEBUG - GET http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26/url {}
2025-07-01 16:12:36,271 - DEBUG - http://localhost:55028 "GET /session/00f68acd6798a7356ff94e29d4ad0b26/url HTTP/1.1" 200 0
2025-07-01 16:12:36,271 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:36,271 - DEBUG - Finished Request
2025-07-01 16:12:37,272 - DEBUG - GET http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26/url {}
2025-07-01 16:12:37,278 - DEBUG - http://localhost:55028 "GET /session/00f68acd6798a7356ff94e29d4ad0b26/url HTTP/1.1" 200 0
2025-07-01 16:12:37,279 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:37,279 - DEBUG - Finished Request
2025-07-01 16:12:37,412 - DEBUG - DELETE http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26 {}
2025-07-01 16:12:37,464 - DEBUG - http://localhost:55028 "DELETE /session/00f68acd6798a7356ff94e29d4ad0b26 HTTP/1.1" 200 0
2025-07-01 16:12:37,464 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:12:37,464 - DEBUG - Finished Request
2025-07-01 16:12:38,289 - DEBUG - DELETE http://localhost:55028/session/00f68acd6798a7356ff94e29d4ad0b26 {}
2025-07-01 16:12:38,289 - DEBUG - Starting new HTTP connection (1): localhost:55028
