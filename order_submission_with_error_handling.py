"""
整合錯誤處理的訂單提交示例
展示如何在主程序中使用錯誤處理模組
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from error_handler import ErrorHandler

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('order_submission.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OrderSubmissionBot:
    """整合錯誤處理的訂單提交機器人"""
    
    def __init__(self):
        self.driver = None
        self.logger = logging.getLogger(__name__)
        self.error_handler = None
        self.max_retries = 3
        self.retry_count = 0
    
    def setup_driver(self):
        """設置瀏覽器驅動"""
        try:
            options = webdriver.ChromeOptions()
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 初始化錯誤處理器
            self.error_handler = ErrorHandler(self.driver, self.logger)
            
            self.logger.info("瀏覽器驅動設置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"設置瀏覽器驅動失敗: {e}")
            return False
    
    def submit_order_with_error_handling(self, captcha_code: str) -> dict:
        """
        提交訂單並處理可能的錯誤
        
        Args:
            captcha_code: 驗證碼
            
        Returns:
            dict: 提交結果
        """
        result = {
            "success": False,
            "message": "",
            "error_info": None,
            "retry_count": self.retry_count
        }
        
        try:
            # 1. 填入驗證碼
            self.logger.info("填入驗證碼...")
            captcha_input = self.driver.find_element(By.CSS_SELECTOR, "input[name*='captcha']")
            captcha_input.clear()
            captcha_input.send_keys(captcha_code)
            
            # 2. 點擊送出按鈕
            self.logger.info("點擊送出按鈕...")
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('送出'), input[type='submit']")
            submit_button.click()
            
            # 3. 等待一小段時間讓頁面反應
            time.sleep(2)
            
            # 4. 檢查是否有錯誤彈窗
            self.logger.info("檢查錯誤彈窗...")
            error_info = self.error_handler.check_for_error_popup(timeout=5)
            
            if error_info:
                # 有錯誤，處理錯誤
                result["error_info"] = error_info
                result["message"] = f"檢測到錯誤: {error_info['description']}"
                
                # 根據錯誤類型決定是否重試
                if self._should_retry(error_info):
                    self.logger.info(f"錯誤可重試，開始處理: {error_info['type']}")
                    
                    # 處理錯誤
                    if self.error_handler.handle_error(error_info):
                        result["message"] += " - 錯誤已處理，可以重試"
                        result["can_retry"] = True
                    else:
                        result["message"] += " - 錯誤處理失敗，需要人工介入"
                        result["can_retry"] = False
                else:
                    result["message"] += " - 錯誤不可重試，需要人工介入"
                    result["can_retry"] = False
            else:
                # 沒有錯誤彈窗，檢查是否提交成功
                if self._check_submission_success():
                    result["success"] = True
                    result["message"] = "訂單提交成功！"
                    self.logger.info("✅ 訂單提交成功！")
                else:
                    result["message"] = "未檢測到明確的成功或失敗信號"
                    self.logger.warning("⚠️ 提交狀態不明確")
            
        except Exception as e:
            result["message"] = f"提交過程中發生異常: {e}"
            self.logger.error(f"提交訂單時發生異常: {e}")
        
        return result
    
    def _should_retry(self, error_info: dict) -> bool:
        """判斷錯誤是否可以重試"""
        retryable_actions = ["wait_and_retry", "retry_or_manual"]
        return (
            error_info.get("action") in retryable_actions and 
            self.retry_count < self.max_retries
        )
    
    def _check_submission_success(self) -> bool:
        """檢查訂單是否提交成功"""
        try:
            # 檢查成功指標（需要根據實際頁面調整）
            success_indicators = [
                "提交成功",
                "預約完成", 
                "申請已送出"
            ]
            
            page_text = self.driver.page_source
            for indicator in success_indicators:
                if indicator in page_text:
                    return True
            
            # 檢查是否回到列表頁面
            if "進廠確認管理" in self.driver.title:
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"檢查提交成功狀態時發生異常: {e}")
            return False
    
    def automated_submission_with_retry(self, captcha_code: str) -> dict:
        """
        自動提交訂單並在遇到可重試錯誤時自動重試
        
        Args:
            captcha_code: 驗證碼
            
        Returns:
            dict: 最終提交結果
        """
        self.retry_count = 0
        
        while self.retry_count <= self.max_retries:
            self.logger.info(f"第 {self.retry_count + 1} 次嘗試提交訂單...")
            
            result = self.submit_order_with_error_handling(captcha_code)
            
            if result["success"]:
                # 提交成功，結束重試
                return result
            
            if not result.get("can_retry", False):
                # 不可重試的錯誤，結束重試
                self.logger.error(f"遇到不可重試的錯誤: {result['message']}")
                return result
            
            # 可以重試，增加重試計數
            self.retry_count += 1
            
            if self.retry_count <= self.max_retries:
                wait_time = 5 * self.retry_count  # 遞增等待時間
                self.logger.info(f"等待 {wait_time} 秒後進行第 {self.retry_count + 1} 次重試...")
                time.sleep(wait_time)
            else:
                self.logger.error("已達到最大重試次數")
                result["message"] += f" - 已重試 {self.max_retries} 次"
                break
        
        return result
    
    def cleanup(self):
        """清理資源"""
        if self.driver:
            self.driver.quit()
            self.logger.info("瀏覽器已關閉")

def main():
    """主程序示例"""
    bot = OrderSubmissionBot()
    
    try:
        # 設置瀏覽器
        if not bot.setup_driver():
            print("❌ 瀏覽器設置失敗")
            return
        
        # 這裡應該包含導航到訂單頁面的代碼
        # bot.driver.get("https://your-order-page-url")
        
        # 示例：提交訂單
        captcha_code = input("請輸入驗證碼: ")
        
        result = bot.automated_submission_with_retry(captcha_code)
        
        # 輸出結果
        if result["success"]:
            print("🎉 訂單提交成功！")
        else:
            print(f"❌ 訂單提交失敗: {result['message']}")
            if result.get("error_info"):
                print(f"錯誤詳情: {result['error_info']}")
    
    finally:
        bot.cleanup()

if __name__ == "__main__":
    main()
