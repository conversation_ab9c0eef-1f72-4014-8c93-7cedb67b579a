2025-06-28 18:38:21,575 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250628_183821.log
2025-06-28 18:38:21,577 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-28 18:38:21,577 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-28 18:38:21,640 - DEBUG - chromedriver not found in PATH
2025-06-28 18:38:21,640 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 18:38:21,640 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-28 18:38:21,641 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-28 18:38:21,641 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-28 18:38:21,641 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-28 18:38:21,641 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 18:38:21,645 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 20304 using 0 to output -3
2025-06-28 18:38:22,179 - DEBUG - POST http://localhost:51873/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-28 18:38:22,179 - DEBUG - Starting new HTTP connection (1): localhost:51873
2025-06-28 18:38:22,724 - DEBUG - http://localhost:51873 "POST /session HTTP/1.1" 200 0
2025-06-28 18:38:22,725 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir20304_1524617113"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51879"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"857bf0b55fced130c961d899c6577467"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:22,725 - DEBUG - Finished Request
2025-06-28 18:38:22,725 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-28 18:38:24,659 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:24,660 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:24,660 - DEBUG - Finished Request
2025-06-28 18:38:24,660 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:38:24,660 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:38:24,666 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:38:24,667 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:24,667 - DEBUG - Finished Request
2025-06-28 18:38:24,667 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:38:24,667 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:24,676 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:24,677 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:24,677 - DEBUG - Finished Request
2025-06-28 18:38:25,677 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:25,687 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:25,687 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:25,688 - DEBUG - Finished Request
2025-06-28 18:38:26,689 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:26,694 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:26,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:26,695 - DEBUG - Finished Request
2025-06-28 18:38:27,695 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:27,704 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:27,704 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:27,705 - DEBUG - Finished Request
2025-06-28 18:38:28,705 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:28,711 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:28,712 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:28,712 - DEBUG - Finished Request
2025-06-28 18:38:29,713 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:29,719 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:29,719 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:29,720 - DEBUG - Finished Request
2025-06-28 18:38:30,720 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:30,729 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:30,729 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:30,729 - DEBUG - Finished Request
2025-06-28 18:38:31,730 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:31,738 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:31,738 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:31,738 - DEBUG - Finished Request
2025-06-28 18:38:32,739 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:32,748 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:32,748 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:32,749 - DEBUG - Finished Request
2025-06-28 18:38:33,750 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:33,760 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:33,761 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:33,761 - DEBUG - Finished Request
2025-06-28 18:38:34,762 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:34,769 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:34,769 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:34,769 - DEBUG - Finished Request
2025-06-28 18:38:35,770 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:35,779 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:35,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:35,780 - DEBUG - Finished Request
2025-06-28 18:38:36,782 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:36,789 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:36,790 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:36,790 - DEBUG - Finished Request
2025-06-28 18:38:37,791 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:37,800 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:37,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:37,801 - DEBUG - Finished Request
2025-06-28 18:38:38,802 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:38,810 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:38,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:38,811 - DEBUG - Finished Request
2025-06-28 18:38:39,812 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:39,819 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:39,819 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:39,820 - DEBUG - Finished Request
2025-06-28 18:38:40,821 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:40,828 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:40,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:40,829 - DEBUG - Finished Request
2025-06-28 18:38:41,830 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:41,836 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:41,837 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:41,837 - DEBUG - Finished Request
2025-06-28 18:38:42,839 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:42,846 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:42,846 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:42,847 - DEBUG - Finished Request
2025-06-28 18:38:43,848 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:43,856 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:43,857 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:43,857 - DEBUG - Finished Request
2025-06-28 18:38:44,859 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:44,867 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:44,868 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:44,868 - DEBUG - Finished Request
2025-06-28 18:38:45,869 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:45,877 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:45,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:45,878 - DEBUG - Finished Request
2025-06-28 18:38:46,878 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:46,889 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:46,890 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:46,890 - DEBUG - Finished Request
2025-06-28 18:38:47,891 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:47,899 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:47,899 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:47,900 - DEBUG - Finished Request
2025-06-28 18:38:48,901 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:48,909 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:48,910 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:48,910 - DEBUG - Finished Request
2025-06-28 18:38:49,911 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:49,919 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:49,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:49,920 - DEBUG - Finished Request
2025-06-28 18:38:50,921 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:50,929 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:50,930 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:50,930 - DEBUG - Finished Request
2025-06-28 18:38:51,931 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:51,938 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:51,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:51,939 - DEBUG - Finished Request
2025-06-28 18:38:52,940 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:52,977 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:52,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:52,978 - DEBUG - Finished Request
2025-06-28 18:38:53,979 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:53,985 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:53,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:53,986 - DEBUG - Finished Request
2025-06-28 18:38:54,987 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:54,996 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:54,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:54,997 - DEBUG - Finished Request
2025-06-28 18:38:55,997 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:56,005 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:56,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:56,006 - DEBUG - Finished Request
2025-06-28 18:38:57,008 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:57,015 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:57,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:57,016 - DEBUG - Finished Request
2025-06-28 18:38:58,016 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:58,023 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:58,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:58,024 - DEBUG - Finished Request
2025-06-28 18:38:59,025 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:38:59,031 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:38:59,032 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:38:59,032 - DEBUG - Finished Request
2025-06-28 18:39:00,033 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:00,039 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:00,039 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:00,040 - DEBUG - Finished Request
2025-06-28 18:39:01,041 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:01,048 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:01,048 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:01,049 - DEBUG - Finished Request
2025-06-28 18:39:02,050 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:02,058 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:02,059 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:02,059 - DEBUG - Finished Request
2025-06-28 18:39:03,060 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:03,067 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:03,067 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:03,067 - DEBUG - Finished Request
2025-06-28 18:39:04,068 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:04,075 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:04,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:04,075 - DEBUG - Finished Request
2025-06-28 18:39:05,077 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:05,085 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:05,085 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:05,086 - DEBUG - Finished Request
2025-06-28 18:39:06,087 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:06,093 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:06,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:06,093 - DEBUG - Finished Request
2025-06-28 18:39:07,094 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:07,101 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:07,102 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:07,103 - DEBUG - Finished Request
2025-06-28 18:39:08,104 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:08,113 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:08,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:08,114 - DEBUG - Finished Request
2025-06-28 18:39:09,115 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:09,123 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:09,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:09,124 - DEBUG - Finished Request
2025-06-28 18:39:10,125 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:10,133 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:10,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:10,134 - DEBUG - Finished Request
2025-06-28 18:39:11,134 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:11,141 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:11,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:11,142 - DEBUG - Finished Request
2025-06-28 18:39:12,143 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:12,150 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:12,150 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:12,151 - DEBUG - Finished Request
2025-06-28 18:39:13,152 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:13,159 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:13,160 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:13,160 - DEBUG - Finished Request
2025-06-28 18:39:14,161 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:14,169 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:14,169 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:14,170 - DEBUG - Finished Request
2025-06-28 18:39:15,171 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:15,180 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:15,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:15,181 - DEBUG - Finished Request
2025-06-28 18:39:15,782 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:39:15,782 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:39:15,789 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:15,790 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:15,790 - DEBUG - Finished Request
2025-06-28 18:39:15,790 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:39:15,811 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:15,818 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:15,819 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:15,819 - DEBUG - Finished Request
2025-06-28 18:39:16,183 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:16,191 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:16,191 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:16,192 - DEBUG - Finished Request
2025-06-28 18:39:16,325 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:16,333 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:16,333 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:16,333 - DEBUG - Finished Request
2025-06-28 18:39:16,841 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:16,848 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:16,850 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:16,850 - DEBUG - Finished Request
2025-06-28 18:39:17,192 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:17,202 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:17,202 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:17,202 - DEBUG - Finished Request
2025-06-28 18:39:17,353 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:17,358 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:17,359 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:17,359 - DEBUG - Finished Request
2025-06-28 18:39:17,868 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:17,875 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:17,876 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:17,876 - DEBUG - Finished Request
2025-06-28 18:39:18,203 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:18,214 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:18,215 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:18,215 - DEBUG - Finished Request
2025-06-28 18:39:18,382 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:18,390 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:18,390 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:18,390 - DEBUG - Finished Request
2025-06-28 18:39:18,900 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:18,908 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:18,908 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:18,908 - DEBUG - Finished Request
2025-06-28 18:39:19,215 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:19,220 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:19,221 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:19,221 - DEBUG - Finished Request
2025-06-28 18:39:19,416 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:19,423 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:19,423 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:19,423 - DEBUG - Finished Request
2025-06-28 18:39:19,925 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:19,929 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:19,929 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:19,930 - DEBUG - Finished Request
2025-06-28 18:39:20,222 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:20,229 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:20,230 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:20,230 - DEBUG - Finished Request
2025-06-28 18:39:20,433 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:20,439 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:20,439 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:20,439 - DEBUG - Finished Request
2025-06-28 18:39:20,947 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:20,955 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:20,955 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:20,955 - DEBUG - Finished Request
2025-06-28 18:39:21,231 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:21,238 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:21,239 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:21,239 - DEBUG - Finished Request
2025-06-28 18:39:21,461 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:21,468 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:21,468 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:21,468 - DEBUG - Finished Request
2025-06-28 18:39:21,971 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:21,978 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:21,979 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:21,979 - DEBUG - Finished Request
2025-06-28 18:39:22,240 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:22,248 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:22,248 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:22,248 - DEBUG - Finished Request
2025-06-28 18:39:22,480 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:22,488 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:22,488 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:22,488 - DEBUG - Finished Request
2025-06-28 18:39:22,997 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:23,005 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:23,005 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:23,005 - DEBUG - Finished Request
2025-06-28 18:39:23,249 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:23,258 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:23,258 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:23,258 - DEBUG - Finished Request
2025-06-28 18:39:23,512 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:23,523 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:23,523 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:23,523 - DEBUG - Finished Request
2025-06-28 18:39:24,024 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:24,032 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:24,033 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:24,033 - DEBUG - Finished Request
2025-06-28 18:39:24,259 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:24,267 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:24,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:24,267 - DEBUG - Finished Request
2025-06-28 18:39:24,538 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:24,546 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:24,546 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:24,546 - DEBUG - Finished Request
2025-06-28 18:39:25,054 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:25,062 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:25,063 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:25,063 - DEBUG - Finished Request
2025-06-28 18:39:25,268 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:25,277 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:25,277 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:25,277 - DEBUG - Finished Request
2025-06-28 18:39:25,567 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:25,576 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:25,577 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:25,577 - DEBUG - Finished Request
2025-06-28 18:39:26,078 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:26,086 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:26,087 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:26,087 - DEBUG - Finished Request
2025-06-28 18:39:26,278 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:26,286 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:26,286 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:26,286 - DEBUG - Finished Request
2025-06-28 18:39:26,596 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:26,604 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:26,604 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:26,604 - DEBUG - Finished Request
2025-06-28 18:39:27,108 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:27,117 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:27,117 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:27,117 - DEBUG - Finished Request
2025-06-28 18:39:27,287 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:27,295 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:27,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:27,296 - DEBUG - Finished Request
2025-06-28 18:39:27,619 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:27,627 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:27,628 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:27,628 - DEBUG - Finished Request
2025-06-28 18:39:28,137 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:28,145 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:28,145 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:28,145 - DEBUG - Finished Request
2025-06-28 18:39:28,297 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:28,305 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:28,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:28,305 - DEBUG - Finished Request
2025-06-28 18:39:28,652 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:28,661 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:28,662 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:28,662 - DEBUG - Finished Request
2025-06-28 18:39:29,169 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:29,178 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:29,178 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:29,178 - DEBUG - Finished Request
2025-06-28 18:39:29,306 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:29,335 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:29,335 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:29,336 - DEBUG - Finished Request
2025-06-28 18:39:29,686 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:29,694 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:29,694 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:29,694 - DEBUG - Finished Request
2025-06-28 18:39:30,197 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:30,206 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:30,206 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:30,206 - DEBUG - Finished Request
2025-06-28 18:39:30,337 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:30,346 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:30,346 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:30,346 - DEBUG - Finished Request
2025-06-28 18:39:30,709 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:30,719 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:30,719 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:30,719 - DEBUG - Finished Request
2025-06-28 18:39:31,223 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:31,233 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:31,233 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:31,234 - DEBUG - Finished Request
2025-06-28 18:39:31,347 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:31,356 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:31,357 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:31,357 - DEBUG - Finished Request
2025-06-28 18:39:31,737 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:31,748 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:31,748 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:31,749 - DEBUG - Finished Request
2025-06-28 18:39:32,252 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:32,261 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:32,261 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:32,261 - DEBUG - Finished Request
2025-06-28 18:39:32,360 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:32,369 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:32,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:32,370 - DEBUG - Finished Request
2025-06-28 18:39:32,764 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:32,773 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:32,773 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:32,773 - DEBUG - Finished Request
2025-06-28 18:39:33,279 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:33,304 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:33,304 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:33,304 - DEBUG - Finished Request
2025-06-28 18:39:33,371 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:33,380 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:33,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:33,380 - DEBUG - Finished Request
2025-06-28 18:39:33,810 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:33,819 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:33,819 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:33,819 - DEBUG - Finished Request
2025-06-28 18:39:34,322 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:34,331 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:34,331 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:34,331 - DEBUG - Finished Request
2025-06-28 18:39:34,381 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:34,389 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:34,389 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:34,390 - DEBUG - Finished Request
2025-06-28 18:39:34,839 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:34,848 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:34,848 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:34,849 - DEBUG - Finished Request
2025-06-28 18:39:35,354 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:35,362 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:35,362 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:35,362 - DEBUG - Finished Request
2025-06-28 18:39:35,391 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:35,400 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:35,401 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:35,401 - DEBUG - Finished Request
2025-06-28 18:39:35,865 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:35,876 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:35,876 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:35,877 - DEBUG - Finished Request
2025-06-28 18:39:36,381 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:36,390 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:36,391 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:36,391 - DEBUG - Finished Request
2025-06-28 18:39:36,402 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:36,409 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:36,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:36,410 - DEBUG - Finished Request
2025-06-28 18:39:36,895 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:36,904 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:36,904 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:36,904 - DEBUG - Finished Request
2025-06-28 18:39:37,410 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:37,411 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:37,412 - DEBUG - Starting new HTTP connection (2): localhost:51873
2025-06-28 18:39:37,420 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:37,421 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:37,421 - DEBUG - Finished Request
2025-06-28 18:39:37,424 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:37,425 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-28 18:39:37,425 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:37,425 - DEBUG - Finished Request
2025-06-28 18:39:37,926 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:37,934 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:37,935 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:37,935 - DEBUG - Finished Request
2025-06-28 18:39:38,426 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:38,434 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:38,434 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:38,435 - DEBUG - Finished Request
2025-06-28 18:39:38,441 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:38,448 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:38,448 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:38,449 - DEBUG - Finished Request
2025-06-28 18:39:38,959 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:38,967 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:38,967 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:38,967 - DEBUG - Finished Request
2025-06-28 18:39:39,436 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:39,449 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:39,450 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:39,450 - DEBUG - Finished Request
2025-06-28 18:39:39,480 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:39,492 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:39,492 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:39,493 - DEBUG - Finished Request
2025-06-28 18:39:40,002 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:40,009 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:40,009 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:40,010 - DEBUG - Finished Request
2025-06-28 18:39:40,451 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:40,458 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:40,458 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:40,459 - DEBUG - Finished Request
2025-06-28 18:39:40,512 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:40,520 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:40,521 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:40,521 - DEBUG - Finished Request
2025-06-28 18:39:41,025 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:41,033 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:41,033 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:41,034 - DEBUG - Finished Request
2025-06-28 18:39:41,459 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:41,466 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:41,467 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:41,467 - DEBUG - Finished Request
2025-06-28 18:39:41,539 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:41,547 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:41,547 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:41,547 - DEBUG - Finished Request
2025-06-28 18:39:42,055 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:42,062 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:42,063 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:42,063 - DEBUG - Finished Request
2025-06-28 18:39:42,468 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:42,475 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:42,476 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:42,476 - DEBUG - Finished Request
2025-06-28 18:39:42,570 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:42,577 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:42,578 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:42,578 - DEBUG - Finished Request
2025-06-28 18:39:43,087 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:43,096 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:43,096 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:43,096 - DEBUG - Finished Request
2025-06-28 18:39:43,476 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:43,488 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:43,489 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:43,489 - DEBUG - Finished Request
2025-06-28 18:39:43,597 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:43,607 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:43,608 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:43,608 - DEBUG - Finished Request
2025-06-28 18:39:44,110 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:44,117 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:44,118 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:44,118 - DEBUG - Finished Request
2025-06-28 18:39:44,490 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:44,497 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:44,497 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:44,498 - DEBUG - Finished Request
2025-06-28 18:39:44,620 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:44,628 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:44,628 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:44,628 - DEBUG - Finished Request
2025-06-28 18:39:45,134 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:45,141 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:45,142 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:45,142 - DEBUG - Finished Request
2025-06-28 18:39:45,499 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:45,510 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:45,510 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:45,511 - DEBUG - Finished Request
2025-06-28 18:39:45,651 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:45,658 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:45,659 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:45,659 - DEBUG - Finished Request
2025-06-28 18:39:46,163 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:46,171 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:46,171 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:46,171 - DEBUG - Finished Request
2025-06-28 18:39:46,512 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:46,520 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:46,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:46,521 - DEBUG - Finished Request
2025-06-28 18:39:46,677 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:46,686 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:46,686 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:46,687 - DEBUG - Finished Request
2025-06-28 18:39:47,192 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:47,201 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:47,201 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:47,202 - DEBUG - Finished Request
2025-06-28 18:39:47,523 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:47,529 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:47,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:47,530 - DEBUG - Finished Request
2025-06-28 18:39:47,707 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:47,713 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:47,713 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:47,714 - DEBUG - Finished Request
2025-06-28 18:39:48,218 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:48,226 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:48,226 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:48,226 - DEBUG - Finished Request
2025-06-28 18:39:48,531 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:48,539 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:48,539 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:48,540 - DEBUG - Finished Request
2025-06-28 18:39:48,731 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:48,738 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:48,739 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:48,739 - DEBUG - Finished Request
2025-06-28 18:39:49,241 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:49,248 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:49,249 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:49,249 - DEBUG - Finished Request
2025-06-28 18:39:49,541 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:49,547 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:49,548 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:49,548 - DEBUG - Finished Request
2025-06-28 18:39:49,751 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:49,758 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:49,758 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:49,758 - DEBUG - Finished Request
2025-06-28 18:39:50,261 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:50,267 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:50,267 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:50,268 - DEBUG - Finished Request
2025-06-28 18:39:50,549 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:50,556 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:50,556 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:50,557 - DEBUG - Finished Request
2025-06-28 18:39:50,777 - DEBUG - POST http://localhost:51873/session/857bf0b55fced130c961d899c6577467/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:39:50,783 - DEBUG - http://localhost:51873 "POST /session/857bf0b55fced130c961d899c6577467/execute/sync HTTP/1.1" 200 0
2025-06-28 18:39:50,784 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:50,784 - DEBUG - Finished Request
2025-06-28 18:39:51,557 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:51,564 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:51,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:51,565 - DEBUG - Finished Request
2025-06-28 18:39:52,566 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:52,573 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:52,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:52,574 - DEBUG - Finished Request
2025-06-28 18:39:53,575 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:53,580 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:53,581 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:53,581 - DEBUG - Finished Request
2025-06-28 18:39:54,581 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:54,590 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:54,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:54,590 - DEBUG - Finished Request
2025-06-28 18:39:55,590 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:55,597 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:55,597 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:55,597 - DEBUG - Finished Request
2025-06-28 18:39:56,598 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:56,604 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:56,604 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:56,604 - DEBUG - Finished Request
2025-06-28 18:39:57,605 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:57,612 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:57,613 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:57,613 - DEBUG - Finished Request
2025-06-28 18:39:58,614 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:58,621 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:58,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:58,621 - DEBUG - Finished Request
2025-06-28 18:39:59,622 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:39:59,630 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:39:59,631 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:39:59,631 - DEBUG - Finished Request
2025-06-28 18:40:00,632 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:00,640 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:00,641 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:00,641 - DEBUG - Finished Request
2025-06-28 18:40:01,642 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:01,649 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:01,650 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:01,650 - DEBUG - Finished Request
2025-06-28 18:40:02,651 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:02,659 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:02,660 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:02,660 - DEBUG - Finished Request
2025-06-28 18:40:03,661 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:03,669 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:03,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:03,669 - DEBUG - Finished Request
2025-06-28 18:40:04,670 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:04,679 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:04,680 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:04,680 - DEBUG - Finished Request
2025-06-28 18:40:05,681 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:05,692 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:05,692 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:05,692 - DEBUG - Finished Request
2025-06-28 18:40:06,693 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:06,700 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:06,701 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:06,701 - DEBUG - Finished Request
2025-06-28 18:40:07,702 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:07,708 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:07,708 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:07,708 - DEBUG - Finished Request
2025-06-28 18:40:08,709 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:08,717 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:08,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:08,717 - DEBUG - Finished Request
2025-06-28 18:40:09,718 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:09,727 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:09,727 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:09,727 - DEBUG - Finished Request
2025-06-28 18:40:10,728 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:10,735 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:10,735 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:10,735 - DEBUG - Finished Request
2025-06-28 18:40:11,736 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:11,745 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:11,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:11,745 - DEBUG - Finished Request
2025-06-28 18:40:12,746 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:12,754 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:12,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:12,754 - DEBUG - Finished Request
2025-06-28 18:40:13,755 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:13,765 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:13,765 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:13,765 - DEBUG - Finished Request
2025-06-28 18:40:14,766 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:14,776 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:14,777 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:14,777 - DEBUG - Finished Request
2025-06-28 18:40:15,778 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:15,786 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:15,786 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:15,786 - DEBUG - Finished Request
2025-06-28 18:40:16,787 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:16,794 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:16,795 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:16,795 - DEBUG - Finished Request
2025-06-28 18:40:17,796 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:17,806 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:17,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:17,807 - DEBUG - Finished Request
2025-06-28 18:40:18,808 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:18,817 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:18,817 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:18,817 - DEBUG - Finished Request
2025-06-28 18:40:19,818 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:19,828 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:19,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:19,828 - DEBUG - Finished Request
2025-06-28 18:40:20,830 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:20,840 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:20,841 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:20,841 - DEBUG - Finished Request
2025-06-28 18:40:21,842 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:21,850 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:21,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:21,851 - DEBUG - Finished Request
2025-06-28 18:40:22,853 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:22,862 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:22,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:22,863 - DEBUG - Finished Request
2025-06-28 18:40:23,864 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:23,872 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:23,872 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:23,873 - DEBUG - Finished Request
2025-06-28 18:40:24,874 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:24,884 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:24,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:24,885 - DEBUG - Finished Request
2025-06-28 18:40:25,886 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:25,893 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:25,894 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:25,895 - DEBUG - Finished Request
2025-06-28 18:40:26,895 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:26,904 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:26,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:26,905 - DEBUG - Finished Request
2025-06-28 18:40:27,906 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:27,916 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:27,916 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:27,917 - DEBUG - Finished Request
2025-06-28 18:40:28,918 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:28,927 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:28,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:28,928 - DEBUG - Finished Request
2025-06-28 18:40:29,929 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:29,939 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:29,940 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:29,940 - DEBUG - Finished Request
2025-06-28 18:40:30,942 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:30,950 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:30,950 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:30,951 - DEBUG - Finished Request
2025-06-28 18:40:31,952 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:31,962 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:31,963 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:31,963 - DEBUG - Finished Request
2025-06-28 18:40:32,964 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:32,972 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:32,972 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:32,973 - DEBUG - Finished Request
2025-06-28 18:40:33,974 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:33,986 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:33,986 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:33,987 - DEBUG - Finished Request
2025-06-28 18:40:34,987 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:34,996 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:34,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:34,997 - DEBUG - Finished Request
2025-06-28 18:40:35,998 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:36,007 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:36,008 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:36,008 - DEBUG - Finished Request
2025-06-28 18:40:37,009 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:37,018 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:37,018 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:37,018 - DEBUG - Finished Request
2025-06-28 18:40:38,020 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:38,029 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:38,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:38,030 - DEBUG - Finished Request
2025-06-28 18:40:39,030 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:39,038 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:39,038 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:39,039 - DEBUG - Finished Request
2025-06-28 18:40:40,040 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:40,050 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:40,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:40,051 - DEBUG - Finished Request
2025-06-28 18:40:41,052 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:41,062 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:41,062 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:41,063 - DEBUG - Finished Request
2025-06-28 18:40:42,064 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:42,072 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:42,072 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:42,073 - DEBUG - Finished Request
2025-06-28 18:40:43,074 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:43,085 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:43,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:43,086 - DEBUG - Finished Request
2025-06-28 18:40:44,087 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:44,097 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:44,098 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:44,098 - DEBUG - Finished Request
2025-06-28 18:40:45,098 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:45,107 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:45,107 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:45,108 - DEBUG - Finished Request
2025-06-28 18:40:46,109 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:46,118 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:46,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:46,118 - DEBUG - Finished Request
2025-06-28 18:40:47,119 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:47,127 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:47,128 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:47,128 - DEBUG - Finished Request
2025-06-28 18:40:48,130 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:48,141 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:48,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:48,142 - DEBUG - Finished Request
2025-06-28 18:40:49,143 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:49,152 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:49,152 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:49,153 - DEBUG - Finished Request
2025-06-28 18:40:50,153 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:50,162 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:50,163 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:50,163 - DEBUG - Finished Request
2025-06-28 18:40:51,164 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:51,176 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:51,176 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:51,177 - DEBUG - Finished Request
2025-06-28 18:40:52,177 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:52,187 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:52,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:52,187 - DEBUG - Finished Request
2025-06-28 18:40:53,188 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:53,197 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:53,198 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:53,199 - DEBUG - Finished Request
2025-06-28 18:40:54,200 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:54,208 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:54,208 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:54,209 - DEBUG - Finished Request
2025-06-28 18:40:55,210 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:55,223 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:55,223 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:55,224 - DEBUG - Finished Request
2025-06-28 18:40:56,225 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:56,234 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:56,235 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:56,235 - DEBUG - Finished Request
2025-06-28 18:40:57,236 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:57,244 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:57,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:57,245 - DEBUG - Finished Request
2025-06-28 18:40:58,246 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:58,255 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:58,256 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:58,256 - DEBUG - Finished Request
2025-06-28 18:40:59,257 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:40:59,267 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:40:59,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:40:59,268 - DEBUG - Finished Request
2025-06-28 18:41:00,269 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:00,279 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:00,279 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:00,280 - DEBUG - Finished Request
2025-06-28 18:41:01,281 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:01,293 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:01,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:01,294 - DEBUG - Finished Request
2025-06-28 18:41:02,295 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:02,304 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:02,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:02,305 - DEBUG - Finished Request
2025-06-28 18:41:03,306 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:03,316 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:03,316 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:03,317 - DEBUG - Finished Request
2025-06-28 18:41:04,318 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:04,327 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:04,327 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:04,328 - DEBUG - Finished Request
2025-06-28 18:41:05,330 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:05,339 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:05,339 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:05,340 - DEBUG - Finished Request
2025-06-28 18:41:06,340 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:06,350 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:06,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:06,351 - DEBUG - Finished Request
2025-06-28 18:41:07,352 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:07,370 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:07,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:07,371 - DEBUG - Finished Request
2025-06-28 18:41:08,373 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:08,379 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:08,379 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:08,379 - DEBUG - Finished Request
2025-06-28 18:41:09,381 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:09,388 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:09,389 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:09,389 - DEBUG - Finished Request
2025-06-28 18:41:10,391 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:10,400 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:10,401 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:10,401 - DEBUG - Finished Request
2025-06-28 18:41:11,402 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:11,412 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:11,413 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:11,413 - DEBUG - Finished Request
2025-06-28 18:41:12,414 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:12,423 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:12,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:12,424 - DEBUG - Finished Request
2025-06-28 18:41:13,425 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:13,436 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:13,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:13,437 - DEBUG - Finished Request
2025-06-28 18:41:14,438 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:14,448 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:14,449 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:14,449 - DEBUG - Finished Request
2025-06-28 18:41:15,450 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:15,462 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:15,462 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:15,463 - DEBUG - Finished Request
2025-06-28 18:41:16,464 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:16,473 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:16,473 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:16,474 - DEBUG - Finished Request
2025-06-28 18:41:17,475 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:17,484 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:17,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:17,485 - DEBUG - Finished Request
2025-06-28 18:41:18,486 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:18,496 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:18,497 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:18,497 - DEBUG - Finished Request
2025-06-28 18:41:19,499 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:19,510 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:19,511 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:19,511 - DEBUG - Finished Request
2025-06-28 18:41:20,512 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:20,521 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:20,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:20,522 - DEBUG - Finished Request
2025-06-28 18:41:21,524 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:21,534 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:21,534 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:21,535 - DEBUG - Finished Request
2025-06-28 18:41:22,535 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:22,544 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:22,544 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:22,544 - DEBUG - Finished Request
2025-06-28 18:41:23,545 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:23,555 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:23,555 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:23,556 - DEBUG - Finished Request
2025-06-28 18:41:24,558 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:24,567 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:24,567 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:24,567 - DEBUG - Finished Request
2025-06-28 18:41:25,568 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:25,578 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:25,578 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:25,579 - DEBUG - Finished Request
2025-06-28 18:41:26,580 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:26,590 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:26,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:26,590 - DEBUG - Finished Request
2025-06-28 18:41:27,592 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:27,600 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:27,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:27,600 - DEBUG - Finished Request
2025-06-28 18:41:28,602 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:28,610 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:28,610 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:28,610 - DEBUG - Finished Request
2025-06-28 18:41:29,612 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:29,620 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:29,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:29,620 - DEBUG - Finished Request
2025-06-28 18:41:30,621 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:30,630 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:30,630 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:30,630 - DEBUG - Finished Request
2025-06-28 18:41:31,631 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:31,639 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:31,640 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:31,640 - DEBUG - Finished Request
2025-06-28 18:41:32,640 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:32,646 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:32,647 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:32,647 - DEBUG - Finished Request
2025-06-28 18:41:33,648 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:33,655 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:33,655 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:33,655 - DEBUG - Finished Request
2025-06-28 18:41:34,656 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:34,664 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:34,665 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:34,665 - DEBUG - Finished Request
2025-06-28 18:41:35,666 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:35,675 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:35,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:35,675 - DEBUG - Finished Request
2025-06-28 18:41:36,676 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:36,684 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:36,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:36,684 - DEBUG - Finished Request
2025-06-28 18:41:37,685 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:37,691 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:37,692 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:37,692 - DEBUG - Finished Request
2025-06-28 18:41:38,693 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:38,700 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:38,700 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:38,700 - DEBUG - Finished Request
2025-06-28 18:41:39,700 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:39,709 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:39,711 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:39,711 - DEBUG - Finished Request
2025-06-28 18:41:40,711 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:40,718 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:40,718 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:40,718 - DEBUG - Finished Request
2025-06-28 18:41:41,718 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:41,726 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:41,726 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:41,726 - DEBUG - Finished Request
2025-06-28 18:41:42,727 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:42,734 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:42,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:42,734 - DEBUG - Finished Request
2025-06-28 18:41:43,736 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:43,745 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:43,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:43,745 - DEBUG - Finished Request
2025-06-28 18:41:44,746 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:44,752 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:44,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:44,752 - DEBUG - Finished Request
2025-06-28 18:41:45,753 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:45,762 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:45,762 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:45,762 - DEBUG - Finished Request
2025-06-28 18:41:46,763 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:46,780 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:46,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:46,780 - DEBUG - Finished Request
2025-06-28 18:41:47,781 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:47,789 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:47,789 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:47,789 - DEBUG - Finished Request
2025-06-28 18:41:48,790 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:48,798 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:48,799 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:48,799 - DEBUG - Finished Request
2025-06-28 18:41:49,800 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:49,809 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:49,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:49,810 - DEBUG - Finished Request
2025-06-28 18:41:50,812 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:50,823 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:50,824 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:50,824 - DEBUG - Finished Request
2025-06-28 18:41:51,826 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:51,835 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:51,836 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:51,836 - DEBUG - Finished Request
2025-06-28 18:41:52,837 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:52,846 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:52,846 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:52,847 - DEBUG - Finished Request
2025-06-28 18:41:53,848 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:53,858 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:53,859 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:53,859 - DEBUG - Finished Request
2025-06-28 18:41:54,860 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:54,869 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:54,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:54,870 - DEBUG - Finished Request
2025-06-28 18:41:55,871 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:55,882 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:55,882 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:55,882 - DEBUG - Finished Request
2025-06-28 18:41:56,883 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:56,893 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:56,893 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:56,894 - DEBUG - Finished Request
2025-06-28 18:41:57,895 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:57,905 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:57,905 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:57,906 - DEBUG - Finished Request
2025-06-28 18:41:58,907 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:58,915 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:58,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:58,916 - DEBUG - Finished Request
2025-06-28 18:41:59,917 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:41:59,927 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:41:59,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:41:59,928 - DEBUG - Finished Request
2025-06-28 18:42:00,929 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:00,939 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:00,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:00,940 - DEBUG - Finished Request
2025-06-28 18:42:01,940 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:01,948 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:01,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:01,949 - DEBUG - Finished Request
2025-06-28 18:42:02,950 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:02,958 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:02,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:02,959 - DEBUG - Finished Request
2025-06-28 18:42:03,961 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:03,970 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:03,971 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:03,971 - DEBUG - Finished Request
2025-06-28 18:42:04,972 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:04,986 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:04,987 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:04,988 - DEBUG - Finished Request
2025-06-28 18:42:05,988 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:05,997 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:05,998 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:05,998 - DEBUG - Finished Request
2025-06-28 18:42:07,000 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:07,009 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:07,009 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:07,009 - DEBUG - Finished Request
2025-06-28 18:42:08,010 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:08,021 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:08,021 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:08,021 - DEBUG - Finished Request
2025-06-28 18:42:09,023 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:09,031 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:09,031 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:09,032 - DEBUG - Finished Request
2025-06-28 18:42:10,033 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:10,044 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:10,044 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:10,045 - DEBUG - Finished Request
2025-06-28 18:42:11,046 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:11,055 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:11,055 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:11,056 - DEBUG - Finished Request
2025-06-28 18:42:12,057 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:12,066 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:12,066 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:12,067 - DEBUG - Finished Request
2025-06-28 18:42:13,068 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:13,078 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:13,078 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:13,079 - DEBUG - Finished Request
2025-06-28 18:42:14,080 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:14,088 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:14,088 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:14,089 - DEBUG - Finished Request
2025-06-28 18:42:15,090 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:15,100 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:15,101 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:15,101 - DEBUG - Finished Request
2025-06-28 18:42:16,102 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:16,111 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:16,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:16,113 - DEBUG - Finished Request
2025-06-28 18:42:17,113 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:17,122 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:17,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:17,123 - DEBUG - Finished Request
2025-06-28 18:42:18,124 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:18,133 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:18,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:18,134 - DEBUG - Finished Request
2025-06-28 18:42:19,135 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:19,144 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:19,145 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:19,145 - DEBUG - Finished Request
2025-06-28 18:42:20,146 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:20,156 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:20,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:20,157 - DEBUG - Finished Request
2025-06-28 18:42:21,157 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:21,166 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:21,166 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:21,167 - DEBUG - Finished Request
2025-06-28 18:42:22,169 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:22,178 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:22,178 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:22,178 - DEBUG - Finished Request
2025-06-28 18:42:23,179 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:23,188 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:23,188 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:23,188 - DEBUG - Finished Request
2025-06-28 18:42:24,189 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:24,198 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:24,198 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:24,199 - DEBUG - Finished Request
2025-06-28 18:42:25,201 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:25,209 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:25,209 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:25,209 - DEBUG - Finished Request
2025-06-28 18:42:26,210 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:26,219 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:26,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:26,220 - DEBUG - Finished Request
2025-06-28 18:42:27,221 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:27,230 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:27,231 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:27,231 - DEBUG - Finished Request
2025-06-28 18:42:28,232 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:28,242 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:28,242 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:28,243 - DEBUG - Finished Request
2025-06-28 18:42:29,244 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:29,253 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:29,254 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:29,254 - DEBUG - Finished Request
2025-06-28 18:42:30,254 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:30,263 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:30,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:30,264 - DEBUG - Finished Request
2025-06-28 18:42:31,264 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:31,275 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:31,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:31,276 - DEBUG - Finished Request
2025-06-28 18:42:32,277 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:32,287 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:32,287 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:32,287 - DEBUG - Finished Request
2025-06-28 18:42:33,288 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:33,296 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:33,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:33,296 - DEBUG - Finished Request
2025-06-28 18:42:34,298 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:34,309 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:34,310 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:34,310 - DEBUG - Finished Request
2025-06-28 18:42:35,312 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:35,324 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:35,324 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:35,325 - DEBUG - Finished Request
2025-06-28 18:42:36,326 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:36,337 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:36,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:36,337 - DEBUG - Finished Request
2025-06-28 18:42:37,338 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:37,348 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:37,349 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:37,349 - DEBUG - Finished Request
2025-06-28 18:42:38,350 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:38,359 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:38,359 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:38,360 - DEBUG - Finished Request
2025-06-28 18:42:39,362 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:39,370 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:39,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:39,371 - DEBUG - Finished Request
2025-06-28 18:42:40,372 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:40,381 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:40,381 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:40,382 - DEBUG - Finished Request
2025-06-28 18:42:41,383 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:41,392 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:41,392 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:41,393 - DEBUG - Finished Request
2025-06-28 18:42:42,394 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:42,402 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:42,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:42,403 - DEBUG - Finished Request
2025-06-28 18:42:43,404 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:43,412 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:43,412 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:43,412 - DEBUG - Finished Request
2025-06-28 18:42:44,413 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:44,422 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:44,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:44,422 - DEBUG - Finished Request
2025-06-28 18:42:45,423 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:45,433 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:45,433 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:45,433 - DEBUG - Finished Request
2025-06-28 18:42:46,434 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:46,443 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:46,443 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:46,444 - DEBUG - Finished Request
2025-06-28 18:42:47,444 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:47,453 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:47,453 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:47,453 - DEBUG - Finished Request
2025-06-28 18:42:48,455 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:48,466 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:48,466 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:48,466 - DEBUG - Finished Request
2025-06-28 18:42:49,467 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:49,476 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:49,477 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:49,477 - DEBUG - Finished Request
2025-06-28 18:42:50,478 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:50,487 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:50,487 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:50,487 - DEBUG - Finished Request
2025-06-28 18:42:51,490 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:51,502 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:51,502 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:51,503 - DEBUG - Finished Request
2025-06-28 18:42:52,504 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:52,512 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:52,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:52,513 - DEBUG - Finished Request
2025-06-28 18:42:53,514 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:53,521 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:53,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:53,522 - DEBUG - Finished Request
2025-06-28 18:42:54,523 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:54,530 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:54,530 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:54,531 - DEBUG - Finished Request
2025-06-28 18:42:55,532 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:55,540 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:55,540 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:55,541 - DEBUG - Finished Request
2025-06-28 18:42:56,543 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:56,550 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:56,550 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:56,551 - DEBUG - Finished Request
2025-06-28 18:42:57,551 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:57,558 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:57,559 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:57,559 - DEBUG - Finished Request
2025-06-28 18:42:58,560 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:58,566 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:58,566 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:58,566 - DEBUG - Finished Request
2025-06-28 18:42:59,567 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:42:59,582 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:42:59,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:42:59,583 - DEBUG - Finished Request
2025-06-28 18:43:00,584 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:00,593 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:00,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:00,593 - DEBUG - Finished Request
2025-06-28 18:43:01,594 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:01,602 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:01,602 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:01,602 - DEBUG - Finished Request
2025-06-28 18:43:02,604 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:02,613 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:02,614 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:02,614 - DEBUG - Finished Request
2025-06-28 18:43:03,615 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:03,623 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:03,624 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:03,624 - DEBUG - Finished Request
2025-06-28 18:43:04,625 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:04,633 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:04,634 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:04,634 - DEBUG - Finished Request
2025-06-28 18:43:05,635 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:05,643 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:05,643 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:05,644 - DEBUG - Finished Request
2025-06-28 18:43:06,645 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:06,653 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:06,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:06,654 - DEBUG - Finished Request
2025-06-28 18:43:07,655 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:07,664 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:07,665 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:07,665 - DEBUG - Finished Request
2025-06-28 18:43:08,666 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:08,674 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:08,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:08,675 - DEBUG - Finished Request
2025-06-28 18:43:09,676 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:09,685 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:09,685 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:09,686 - DEBUG - Finished Request
2025-06-28 18:43:10,687 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:10,695 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:10,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:10,695 - DEBUG - Finished Request
2025-06-28 18:43:11,697 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:11,707 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:11,707 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:11,708 - DEBUG - Finished Request
2025-06-28 18:43:12,709 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:12,716 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:12,716 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:12,717 - DEBUG - Finished Request
2025-06-28 18:43:13,717 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:13,725 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:13,725 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:13,726 - DEBUG - Finished Request
2025-06-28 18:43:14,727 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:14,736 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:14,736 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:14,737 - DEBUG - Finished Request
2025-06-28 18:43:15,739 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:15,749 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:15,750 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:15,750 - DEBUG - Finished Request
2025-06-28 18:43:16,750 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:16,759 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:16,759 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:16,760 - DEBUG - Finished Request
2025-06-28 18:43:17,761 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:17,770 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:17,770 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:17,770 - DEBUG - Finished Request
2025-06-28 18:43:18,771 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:18,779 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:18,779 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:18,780 - DEBUG - Finished Request
2025-06-28 18:43:19,781 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:19,789 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:19,789 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:19,790 - DEBUG - Finished Request
2025-06-28 18:43:20,791 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:20,799 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:20,800 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:20,800 - DEBUG - Finished Request
2025-06-28 18:43:21,801 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:21,809 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:21,809 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:21,809 - DEBUG - Finished Request
2025-06-28 18:43:22,810 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:22,819 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:22,820 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:22,820 - DEBUG - Finished Request
2025-06-28 18:43:23,821 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:23,828 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:23,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:23,828 - DEBUG - Finished Request
2025-06-28 18:43:24,829 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:24,838 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:24,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:24,839 - DEBUG - Finished Request
2025-06-28 18:43:25,840 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:25,847 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:25,848 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:25,848 - DEBUG - Finished Request
2025-06-28 18:43:26,849 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:26,857 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:26,857 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:26,857 - DEBUG - Finished Request
2025-06-28 18:43:27,858 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:27,866 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:27,866 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:27,867 - DEBUG - Finished Request
2025-06-28 18:43:28,868 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:28,875 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:28,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:28,876 - DEBUG - Finished Request
2025-06-28 18:43:29,877 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:29,885 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:29,886 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:29,886 - DEBUG - Finished Request
2025-06-28 18:43:30,887 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:30,895 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:30,895 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:30,895 - DEBUG - Finished Request
2025-06-28 18:43:31,897 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:31,906 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:31,906 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:31,907 - DEBUG - Finished Request
2025-06-28 18:43:32,907 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:32,914 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:32,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:32,915 - DEBUG - Finished Request
2025-06-28 18:43:33,916 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:33,924 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:33,924 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:33,924 - DEBUG - Finished Request
2025-06-28 18:43:34,926 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:34,934 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:34,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:34,935 - DEBUG - Finished Request
2025-06-28 18:43:35,936 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:35,945 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:35,945 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:35,946 - DEBUG - Finished Request
2025-06-28 18:43:36,947 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:36,954 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:36,954 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:36,954 - DEBUG - Finished Request
2025-06-28 18:43:37,955 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:37,964 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:37,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:37,965 - DEBUG - Finished Request
2025-06-28 18:43:38,966 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:38,974 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:38,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:38,975 - DEBUG - Finished Request
2025-06-28 18:43:39,976 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:39,983 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:39,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:39,983 - DEBUG - Finished Request
2025-06-28 18:43:40,985 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:40,992 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:40,992 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:40,992 - DEBUG - Finished Request
2025-06-28 18:43:41,994 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:42,003 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:42,003 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:42,003 - DEBUG - Finished Request
2025-06-28 18:43:43,005 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:43,013 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:43,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:43,014 - DEBUG - Finished Request
2025-06-28 18:43:44,015 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:44,025 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:44,025 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:44,026 - DEBUG - Finished Request
2025-06-28 18:43:45,028 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:45,037 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:45,037 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:45,037 - DEBUG - Finished Request
2025-06-28 18:43:46,038 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:46,046 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:46,046 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:46,046 - DEBUG - Finished Request
2025-06-28 18:43:47,048 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:47,058 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:47,058 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:47,059 - DEBUG - Finished Request
2025-06-28 18:43:48,060 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:48,069 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:48,069 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:48,069 - DEBUG - Finished Request
2025-06-28 18:43:49,071 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:49,080 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:49,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:49,080 - DEBUG - Finished Request
2025-06-28 18:43:50,082 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:50,092 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:50,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:50,093 - DEBUG - Finished Request
2025-06-28 18:43:51,094 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:51,101 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:51,102 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:51,102 - DEBUG - Finished Request
2025-06-28 18:43:52,103 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:52,111 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:52,111 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:52,112 - DEBUG - Finished Request
2025-06-28 18:43:53,113 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:53,120 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:53,121 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:53,121 - DEBUG - Finished Request
2025-06-28 18:43:54,122 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:54,131 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:54,131 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:54,131 - DEBUG - Finished Request
2025-06-28 18:43:55,133 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:55,141 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:55,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:55,142 - DEBUG - Finished Request
2025-06-28 18:43:56,143 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:56,149 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:56,150 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:56,150 - DEBUG - Finished Request
2025-06-28 18:43:57,151 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:57,160 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:57,161 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:57,161 - DEBUG - Finished Request
2025-06-28 18:43:58,161 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:58,169 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:58,169 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:58,170 - DEBUG - Finished Request
2025-06-28 18:43:59,172 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:43:59,181 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:43:59,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:43:59,182 - DEBUG - Finished Request
2025-06-28 18:44:00,183 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:00,192 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:00,192 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:00,193 - DEBUG - Finished Request
2025-06-28 18:44:01,195 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:01,203 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:01,203 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:01,203 - DEBUG - Finished Request
2025-06-28 18:44:02,206 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:02,214 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:02,215 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:02,215 - DEBUG - Finished Request
2025-06-28 18:44:03,215 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:03,223 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:03,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:03,224 - DEBUG - Finished Request
2025-06-28 18:44:04,225 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:04,234 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:04,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:04,235 - DEBUG - Finished Request
2025-06-28 18:44:05,235 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:05,243 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:05,244 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:05,244 - DEBUG - Finished Request
2025-06-28 18:44:06,245 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:06,253 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:06,253 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:06,254 - DEBUG - Finished Request
2025-06-28 18:44:07,256 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:07,264 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:07,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:07,264 - DEBUG - Finished Request
2025-06-28 18:44:08,265 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:08,273 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:08,274 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:08,275 - DEBUG - Finished Request
2025-06-28 18:44:09,276 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:09,282 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:09,283 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:09,283 - DEBUG - Finished Request
2025-06-28 18:44:10,284 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:10,293 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:10,294 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:10,294 - DEBUG - Finished Request
2025-06-28 18:44:11,295 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:11,304 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:11,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:11,305 - DEBUG - Finished Request
2025-06-28 18:44:12,306 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:12,314 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:12,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:12,315 - DEBUG - Finished Request
2025-06-28 18:44:13,316 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:13,325 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:13,325 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:13,325 - DEBUG - Finished Request
2025-06-28 18:44:14,326 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:14,334 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:14,334 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:14,335 - DEBUG - Finished Request
2025-06-28 18:44:15,335 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:15,344 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:15,344 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:15,345 - DEBUG - Finished Request
2025-06-28 18:44:16,346 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:16,353 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:16,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:16,353 - DEBUG - Finished Request
2025-06-28 18:44:17,355 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:17,363 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:17,364 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:17,364 - DEBUG - Finished Request
2025-06-28 18:44:18,366 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:18,376 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:18,376 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:18,376 - DEBUG - Finished Request
2025-06-28 18:44:19,377 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:19,385 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:19,386 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:19,386 - DEBUG - Finished Request
2025-06-28 18:44:20,388 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:20,395 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:20,396 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:20,396 - DEBUG - Finished Request
2025-06-28 18:44:21,398 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:21,407 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:21,407 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:21,408 - DEBUG - Finished Request
2025-06-28 18:44:22,409 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:22,417 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:22,417 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:22,417 - DEBUG - Finished Request
2025-06-28 18:44:23,418 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:23,426 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:23,426 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:23,426 - DEBUG - Finished Request
2025-06-28 18:44:24,428 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:24,435 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:24,435 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:24,436 - DEBUG - Finished Request
2025-06-28 18:44:25,437 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:25,446 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:25,447 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:25,447 - DEBUG - Finished Request
2025-06-28 18:44:26,448 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:26,456 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:26,457 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:26,457 - DEBUG - Finished Request
2025-06-28 18:44:27,458 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:27,466 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:27,467 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:27,467 - DEBUG - Finished Request
2025-06-28 18:44:28,468 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:28,476 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:28,476 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:28,476 - DEBUG - Finished Request
2025-06-28 18:44:29,477 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:29,485 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:29,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:29,485 - DEBUG - Finished Request
2025-06-28 18:44:30,487 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:30,496 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:30,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:30,496 - DEBUG - Finished Request
2025-06-28 18:44:31,497 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:31,505 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:31,505 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:31,505 - DEBUG - Finished Request
2025-06-28 18:44:32,506 - DEBUG - GET http://localhost:51873/session/857bf0b55fced130c961d899c6577467/url {}
2025-06-28 18:44:32,513 - DEBUG - http://localhost:51873 "GET /session/857bf0b55fced130c961d899c6577467/url HTTP/1.1" 200 0
2025-06-28 18:44:32,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:44:32,514 - DEBUG - Finished Request
