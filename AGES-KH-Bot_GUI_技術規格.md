# AGES-KH-Bot GUI 技術規格文檔

## 📋 文檔信息
- **創建日期**: 2025-07-01
- **版本**: v1.5.0 技術規格
- **狀態**: 正式規格
- **用途**: 開發參考、測試驗證、維護指南

## 🎯 **GUI 詳細技術規格**

### **GUI#01 - 觸發時間設定**

#### **基本資訊**
- **視窗類型**: `tkinter.simpledialog.askstring`
- **視窗大小**: 系統預設 (約 300x150)
- **模態視窗**: 是
- **父視窗**: 隱藏的臨時根視窗

#### **介面元素**
| 元素類型 | 數量 | 內容 | 功能 |
|----------|------|------|------|
| 標題 | 1 | "觸發時間設定 (支援毫秒)" | 視窗標題 |
| 提示文字 | 1 | "請輸入本次搶單觸發時間 (格式 HH:MM:SS.sss)：" | 輸入說明 |
| 輸入框 | 1 | 預設值: "09:30:00.001" | 時間輸入 |
| 按鈕 | 2 | [確定] [取消] | 確認/取消 |

#### **跳轉邏輯**
```
[確定] → 返回輸入的時間字串 → 主程式繼續
[取消] → 返回預設值 "09:30:00.001" → 主程式繼續
[X關閉] → 返回預設值 → 主程式繼續
```

#### **驗證規則**
- **時間格式**: HH:MM:SS.sss (支援毫秒)
- **範圍檢查**: 無 (由主程式處理)
- **空值處理**: 使用預設值

---

### **GUI#02 - 準備提示視窗**

#### **基本資訊**
- **視窗類型**: `tkinter.Toplevel`
- **視窗大小**: 400x180 (固定)
- **可調整大小**: 否
- **置頂顯示**: 是

#### **介面元素**
| 元素類型 | 數量 | 內容 | 功能 |
|----------|------|------|------|
| 主標題 | 1 | "即將執行以下搶單任務：" | 說明文字 |
| 任務顯示區 | 1 | 固定高度 60px | 任務列表容器 |
| 任務項目 | 1-3 | "訂單: XXX \| 瀏覽器: XXX" | 任務詳情 |
| 提醒文字 | 1 | "確認無誤後，點擊「啟動瀏覽器」開始搶單" | 操作提醒 |
| 按鈕 | 2 | [啟動瀏覽器] [取消] | 操作按鈕 |
| 原型標示 | 1 | "🎨 原型展示模式 - 簡化版 GUI#02" | 原型標記 |

#### **按鈕規格**
| 按鈕 | 寬度 | 字體 | 背景色 | 文字色 | 間距 |
|------|------|------|--------|--------|------|
| 啟動瀏覽器 | width=12 | Arial 10 bold | #4CAF50 | white | padx=5 |
| 取消 | width=12 | Arial 10 | #f44336 | white | padx=5 |

#### **跳轉邏輯**
```
[啟動瀏覽器] → 關閉 GUI#02 → 跳轉到 GUI#05
[取消] → 關閉 GUI#02 → 程式結束
[X關閉] → 等同於 [取消]
```

#### **任務顯示規則**
- **最大數量**: 3筆 (平台限制：單一瀏覽器單一分頁)
- **顯示格式**: "訂單: {order_number} | 瀏覽器: {browser}"
- **顏色**: 綠色 (fg="green")
- **字體**: Arial 9

---

### **GUI#04 - 主搶單程式 (GrabberGUI)**

#### **基本資訊**
- **視窗類型**: `tkinter.Tk` 或 `tkinter.Toplevel`
- **視窗大小**: 參考 v1.5.X 主程式
- **功能定位**: 主管理介面

#### **功能模組**
| 模組 | 功能 | 按鈕/元件 |
|------|------|-----------|
| 觸發時間設定 | 調用 GUI#01 | [觸發時間設定] |
| RTT 設定 | RTT 參數配置 | [RTT 設定] |
| 任務管理 | 載入/顯示/編輯任務 | [載入任務] [編輯任務] |
| 執行控制 | 開始搶單流程 | [開始執行] |

#### **跳轉邏輯**
```
[觸發時間設定] → 調用 GUI#01 → 返回 GUI#04
[開始執行] → 跳轉到 GUI#02
其他按鈕 → 在 GUI#04 內部處理
```

#### **狀態**
- **原型狀態**: 參考 v1.5.X 主程式，不再修改原型
- **實作狀態**: 已在主程式中實作

---

### **GUI#05 - 操作指南**

#### **基本資訊**
- **視窗類型**: `tkinter.Toplevel`
- **視窗大小**: 700x500 (帶滾動條)
- **可調整大小**: 是
- **置頂顯示**: 是

#### **介面元素**
| 元素類型 | 數量 | 內容 | 功能 |
|----------|------|------|------|
| 標題 | 1 | "AGES-KH 操作指南" | 視窗標題 |
| 滾動區域 | 1 | 主要內容容器 | 內容顯示 |
| 操作步驟 | 多個 | 詳細操作說明 | 用戶指導 |
| 時間資訊 | 1 | 觸發時間、當前時間等 | 時間提醒 |
| 按鈕 | 2 | [✅ 準備完成] [❌ 取消操作] | 操作控制 |

#### **按鈕規格**
| 按鈕 | 字體 | 背景色 | 文字色 | 尺寸 |
|------|------|--------|--------|------|
| ✅ 準備完成 | Arial 11 bold | #4CAF50 | white | padx=20, pady=10 |
| ❌ 取消操作 | Arial 11 bold | #f44336 | white | padx=20, pady=10 |

#### **跳轉邏輯**
```
[✅ 準備完成] → 關閉 GUI#05 → 程式自動點擊編輯按鈕 → 跳轉到 GUI#09
[❌ 取消操作] → 關閉 GUI#05 → 程式結束
[X關閉] → 等同於 [❌ 取消操作]
```

---

### **GUI#09 - 驗證碼輸入提醒**

#### **基本資訊**
- **視窗類型**: `tkinter.Toplevel`
- **視窗大小**: 700x400
- **可調整大小**: 否
- **置頂顯示**: 是

#### **介面元素**
| 元素類型 | 數量 | 內容 | 功能 |
|----------|------|------|------|
| 主標題 | 1 | "🔐 請在修改進廠確認單中輸入驗證碼" | 主要提醒 |
| 操作說明 | 1 | 4步驟操作指南 | 用戶指導 |
| 時間提醒 | 1 | "⏰ 請在觸發時間前 5 分鐘內完成驗證碼輸入" | 時間警告 |
| 狀態欄 | 1 | 定位狀態、檢測結果、元素統計 | 系統狀態 |
| 按鈕 | 2 | [已輸入驗證碼-正式送單] [已輸入驗證碼-模擬送單] | 模式選擇 |

#### **狀態欄規格**
| 狀態項目 | 格式 | 示例 |
|----------|------|------|
| 定位狀態 | "📍 定位狀態: ✅ {狀態描述}" | "📍 定位狀態: ✅ 修改進廠確認單" |
| 檢測結果 | "🔍 檢測結果: ✅ {項目1} \| ✅ {項目2}" | "🔍 檢測結果: ✅ 找到驗證碼輸入框 \| ✅ 找到送出按鈕" |
| 元素統計 | "📊 元素統計: 按鈕 {數量} 個 \| 輸入框 {數量} 個" | "📊 元素統計: 按鈕 3 個 \| 輸入框 5 個" |

#### **按鈕規格**
| 按鈕 | 字體 | 背景色 | 文字色 | 尺寸 |
|------|------|--------|--------|------|
| 已輸入驗證碼-正式送單 | Arial 11 bold | #4CAF50 | white | padx=20, pady=10 |
| 已輸入驗證碼-模擬送單 | Arial 11 bold | #FF9800 | white | padx=20, pady=10 |

#### **跳轉邏輯**
```
[已輸入驗證碼-正式送單] → 設定模式為 'normal' → 跳轉到等待觸發時間 (v1.6.0 將為 GUI#10)
[已輸入驗證碼-模擬送單] → 設定模式為 'test' → 跳轉到等待觸發時間 (v1.6.0 將為 GUI#10)
[X關閉] → 程式異常結束
```

---

### **GUI#10 - 等待觸發時間 (v1.6.0 計劃)**

#### **基本資訊**
- **視窗類型**: `tkinter.Toplevel`
- **視窗大小**: 800x600
- **實時更新**: 是 (每秒更新)
- **置頂顯示**: 是

#### **介面元素 (設計規格)**
| 元素類型 | 數量 | 內容 | 功能 |
|----------|------|------|------|
| 主標題 | 1 | "⏰ 程式正在等待觸發時間" | 狀態說明 |
| 觸發時間 | 1 | "🎯 觸發時間: 🌅明日 09:30:00.001 📡用戶輸入" | 時間顯示 |
| 倒數計時 | 1 | "⏳ 倒數計時: 14:23:45" | 實時倒數 |
| 執行模式 | 1 | "🎮 執行模式: 🚀 正式送單" 或 "🧪 模擬送單" | 模式顯示 |
| 系統狀態 | 1 | 多項狀態檢測結果 | 狀態監控 |
| 階段進度 | 1 | 5階段執行進度 | 進度顯示 |
| 按鈕 | 2 | [緊急取消執行] [最小化視窗] | 控制按鈕 |

#### **跳轉邏輯 (設計)**
```
[緊急取消執行] → 中止搶單 → 跳轉到錯誤處理
[最小化視窗] → 縮小視窗，繼續執行
觸發時間到達 → 自動執行搶單 → 跳轉到 GUI#11
```

---

### **GUI#11 - 執行結果 (v1.7.0 計劃)**

#### **基本資訊**
- **視窗類型**: `tkinter.Toplevel`
- **視窗大小**: 600x400
- **模態視窗**: 是
- **置頂顯示**: 是

#### **介面元素 (設計規格)**
| 元素類型 | 數量 | 內容 | 功能 |
|----------|------|------|------|
| 結果標題 | 1 | "🎉 搶單執行完成" 或 "🧪 模擬送單執行完成" | 結果說明 |
| 執行結果 | 1 | "✅ 執行結果: 送出成功" | 結果狀態 |
| 時間分析 | 3 | 執行時間、目標時間、時間誤差 | 精度分析 |
| 執行摘要 | 1 | 訂單號、模式、按鈕點擊結果等 | 詳細摘要 |
| 按鈕 | 2 | [查看詳細日誌] [關閉程式] | 後續操作 |

#### **跳轉邏輯 (設計)**
```
[查看詳細日誌] → 開啟日誌檔案 → 返回 GUI#11
[關閉程式] → 程式完全結束
[X關閉] → 等同於 [關閉程式]
```

## 🔧 **技術實作要點**

### **共通規範**
- **字體標準**: Arial 系列
- **按鈕尺寸**: 統一使用 `width` 或 `padx/pady` 控制
- **顏色規範**: 
  - 成功/確認: #4CAF50 (綠色)
  - 警告/測試: #FF9800 (橙色)  
  - 錯誤/取消: #f44336 (紅色)
- **間距標準**: padx=5, pady=5 (小間距) | padx=20, pady=10 (大間距)

### **視窗管理**
- **模態視窗**: 使用 `attributes('-topmost', True)`
- **視窗關閉**: 統一使用 `window.protocol("WM_DELETE_WINDOW", callback)`
- **視窗居中**: 計算螢幕中心位置

### **錯誤處理**
- **異常捕獲**: 所有 GUI 函數都要有 try-except
- **錯誤顯示**: 使用 `messagebox.showerror`
- **日誌記錄**: 記錄 GUI 操作和錯誤

## 📊 **測試檢查清單**

### **GUI#01 測試項目**
- [ ] 預設值顯示正確
- [ ] 輸入驗證正常
- [ ] 確定/取消按鈕功能正常
- [ ] 返回值格式正確

### **GUI#02 測試項目**
- [ ] 視窗大小 400x180
- [ ] 任務顯示最多3筆
- [ ] 按鈕大小一致 (width=12)
- [ ] 按鈕居中靠近
- [ ] 跳轉邏輯正確

### **GUI#05 測試項目**
- [ ] 滾動條功能正常
- [ ] 按鈕樣式符合 GUI#09 標準
- [ ] 內容顯示完整
- [ ] 跳轉邏輯正確

### **GUI#09 測試項目**
- [ ] 狀態欄資訊準確
- [ ] 按鈕樣式一致
- [ ] 模式設定正確
- [ ] 跳轉邏輯正確

---
**本文檔為 AGES-KH-Bot GUI 開發的技術規格，所有實作都必須遵循此規格。**
