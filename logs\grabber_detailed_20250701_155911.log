2025-07-01 15:59:11,640 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_155911.log
2025-07-01 15:59:24,605 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 15:59:24,606 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 15:59:24,658 - DEBUG - chromedriver not found in PATH
2025-07-01 15:59:24,658 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 15:59:24,658 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 15:59:24,658 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 15:59:24,659 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 15:59:24,659 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 15:59:24,659 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 15:59:24,663 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 24532 using 0 to output -3
2025-07-01 15:59:25,187 - DEBUG - POST http://localhost:54729/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 15:59:25,188 - DEBUG - Starting new HTTP connection (1): localhost:54729
2025-07-01 15:59:25,721 - DEBUG - http://localhost:54729 "POST /session HTTP/1.1" 200 0
2025-07-01 15:59:25,721 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir24532_1828989825"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54732"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"d91a1183350ff0ec334cf46b54e7d398"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:25,721 - DEBUG - Finished Request
2025-07-01 15:59:25,722 - DEBUG - POST http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 15:59:27,362 - DEBUG - http://localhost:54729 "POST /session/d91a1183350ff0ec334cf46b54e7d398/url HTTP/1.1" 200 0
2025-07-01 15:59:27,363 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:27,363 - DEBUG - Finished Request
2025-07-01 15:59:27,363 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 15:59:27,363 - DEBUG - POST http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 15:59:27,373 - DEBUG - http://localhost:54729 "POST /session/d91a1183350ff0ec334cf46b54e7d398/execute/sync HTTP/1.1" 200 0
2025-07-01 15:59:27,374 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:27,374 - DEBUG - Finished Request
2025-07-01 15:59:27,374 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 15:59:27,375 - DEBUG - GET http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398/url {}
2025-07-01 15:59:27,426 - DEBUG - http://localhost:54729 "GET /session/d91a1183350ff0ec334cf46b54e7d398/url HTTP/1.1" 200 0
2025-07-01 15:59:27,426 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:27,426 - DEBUG - Finished Request
2025-07-01 15:59:28,427 - DEBUG - GET http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398/url {}
2025-07-01 15:59:28,437 - DEBUG - http://localhost:54729 "GET /session/d91a1183350ff0ec334cf46b54e7d398/url HTTP/1.1" 200 0
2025-07-01 15:59:28,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:28,438 - DEBUG - Finished Request
2025-07-01 15:59:29,438 - DEBUG - GET http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398/url {}
2025-07-01 15:59:29,443 - DEBUG - http://localhost:54729 "GET /session/d91a1183350ff0ec334cf46b54e7d398/url HTTP/1.1" 200 0
2025-07-01 15:59:29,443 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:29,444 - DEBUG - Finished Request
2025-07-01 15:59:30,445 - DEBUG - GET http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398/url {}
2025-07-01 15:59:30,449 - DEBUG - http://localhost:54729 "GET /session/d91a1183350ff0ec334cf46b54e7d398/url HTTP/1.1" 200 0
2025-07-01 15:59:30,449 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:30,451 - DEBUG - Finished Request
2025-07-01 15:59:31,247 - DEBUG - DELETE http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398 {}
2025-07-01 15:59:31,289 - DEBUG - http://localhost:54729 "DELETE /session/d91a1183350ff0ec334cf46b54e7d398 HTTP/1.1" 200 0
2025-07-01 15:59:31,289 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:59:31,290 - DEBUG - Finished Request
2025-07-01 15:59:31,467 - DEBUG - DELETE http://localhost:54729/session/d91a1183350ff0ec334cf46b54e7d398 {}
2025-07-01 15:59:31,468 - DEBUG - Starting new HTTP connection (1): localhost:54729
