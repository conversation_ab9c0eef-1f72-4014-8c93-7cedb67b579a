#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尋找真正的編輯彈窗
Find Real Edit Dialog
"""

import os
import sys
import time
import tkinter as tk
from tkinter import messagebox, scrolledtext
from datetime import datetime
import threading

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class RealDialogFinderGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 尋找真正的編輯彈窗")
        self.root.geometry("1000x800")
        
        self.setup_ui()
        
    def setup_ui(self):
        """設置 UI"""
        # 標題
        title_label = tk.Label(self.root, text="🔍 尋找真正的編輯彈窗", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 說明
        info_text = """
此工具專門用於尋找真正的編輯彈窗位置。
請確保您已經點擊了編輯按鈕，編輯彈窗已經打開。
        """
        
        info_label = tk.Label(self.root, text=info_text, 
                             font=("Arial", 10), justify="center")
        info_label.pack(pady=5)
        
        # 按鈕區域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 檢測按鈕
        self.detect_btn = tk.Button(button_frame, text="🔍 深度掃描所有層級", 
                                   command=self.deep_scan_all_levels,
                                   font=("Arial", 12), bg="lightblue")
        self.detect_btn.pack(side="left", padx=10)
        
        # 檢測彈窗按鈕
        self.detect_modal_btn = tk.Button(button_frame, text="🎯 檢測彈窗結構", 
                                         command=self.detect_modal_structure,
                                         font=("Arial", 12), bg="lightgreen")
        self.detect_modal_btn.pack(side="left", padx=10)
        
        # 截圖按鈕
        self.screenshot_btn = tk.Button(button_frame, text="📸 截圖當前狀態", 
                                       command=self.take_screenshot,
                                       font=("Arial", 12), bg="lightyellow")
        self.screenshot_btn.pack(side="left", padx=10)
        
        # 關閉按鈕
        self.close_btn = tk.Button(button_frame, text="❌ 關閉", 
                                 command=self.close_app,
                                 font=("Arial", 12), bg="lightcoral")
        self.close_btn.pack(side="right", padx=10)
        
        # 日誌顯示區域
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(log_frame, text="掃描日誌:", font=("Arial", 12, "bold")).pack(anchor="w")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, width=120)
        self.log_text.pack(fill="both", expand=True)
        
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def deep_scan_all_levels(self):
        """深度掃描所有層級"""
        try:
            self.log("🔍 開始深度掃描所有層級...")
            self.detect_btn.config(state="disabled")
            
            def scan_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 1. 檢測主頁面
                    self.root.after(0, lambda: self.log("📍 檢測主頁面..."))
                    main_content = driver.execute_script("return document.body.innerText || '';")
                    main_buttons = driver.find_elements(By.TAG_NAME, "button")
                    
                    self.root.after(0, lambda: self.log(f"  主頁面內容長度: {len(main_content)}"))
                    self.root.after(0, lambda: self.log(f"  主頁面按鈕數量: {len(main_buttons)}"))
                    
                    # 2. 檢測所有 iframe
                    iframes = driver.find_elements(By.TAG_NAME, "iframe")
                    self.root.after(0, lambda: self.log(f"📊 檢測到 {len(iframes)} 個 iframe"))
                    
                    for i, iframe in enumerate(iframes):
                        try:
                            self.root.after(0, lambda i=i: self.log(f"\n🔍 掃描 iframe {i+1}..."))
                            
                            # 獲取 iframe 屬性
                            iframe_src = iframe.get_attribute('src') or 'N/A'
                            iframe_id = iframe.get_attribute('id') or 'N/A'
                            iframe_name = iframe.get_attribute('name') or 'N/A'
                            
                            self.root.after(0, lambda i=i, src=iframe_src, id=iframe_id, name=iframe_name:
                                           self.log(f"  iframe {i+1} 屬性: src='{src}' id='{id}' name='{name}'"))
                            
                            # 切換到 iframe
                            driver.switch_to.frame(iframe)
                            
                            # 檢測 iframe 內容
                            iframe_content = driver.execute_script("return document.body.innerText || '';")
                            iframe_html = driver.execute_script("return document.body.innerHTML || '';")
                            iframe_buttons = driver.find_elements(By.TAG_NAME, "button")
                            iframe_inputs = driver.find_elements(By.TAG_NAME, "input")
                            
                            self.root.after(0, lambda i=i: self.log(f"  iframe {i+1} 內容長度: {len(iframe_content)}"))
                            self.root.after(0, lambda i=i: self.log(f"  iframe {i+1} HTML 長度: {len(iframe_html)}"))
                            self.root.after(0, lambda i=i: self.log(f"  iframe {i+1} 按鈕數量: {len(iframe_buttons)}"))
                            self.root.after(0, lambda i=i: self.log(f"  iframe {i+1} 輸入框數量: {len(iframe_inputs)}"))
                            
                            # 檢測關鍵內容
                            edit_keywords = ['編輯進廠確認單', '送出', '取消', '驗證碼', 'Close']
                            found_keywords = [kw for kw in edit_keywords if kw in iframe_content]
                            
                            if found_keywords:
                                self.root.after(0, lambda i=i, kws=found_keywords: 
                                               self.log(f"  iframe {i+1} 包含關鍵字: {kws}"))
                            
                            # 檢測是否有嵌套的 iframe
                            nested_iframes = driver.find_elements(By.TAG_NAME, "iframe")
                            if nested_iframes:
                                self.root.after(0, lambda i=i, count=len(nested_iframes): 
                                               self.log(f"  iframe {i+1} 包含 {count} 個嵌套 iframe"))
                                
                                # 檢測嵌套 iframe
                                for j, nested_iframe in enumerate(nested_iframes):
                                    try:
                                        self.root.after(0, lambda i=i, j=j: 
                                                       self.log(f"    🔍 掃描嵌套 iframe {i+1}.{j+1}..."))
                                        
                                        driver.switch_to.frame(nested_iframe)
                                        
                                        nested_content = driver.execute_script("return document.body.innerText || '';")
                                        nested_buttons = driver.find_elements(By.TAG_NAME, "button")
                                        nested_inputs = driver.find_elements(By.TAG_NAME, "input")
                                        
                                        self.root.after(0, lambda i=i, j=j: 
                                                       self.log(f"    嵌套 iframe {i+1}.{j+1} 內容長度: {len(nested_content)}"))
                                        self.root.after(0, lambda i=i, j=j: 
                                                       self.log(f"    嵌套 iframe {i+1}.{j+1} 按鈕數量: {len(nested_buttons)}"))
                                        self.root.after(0, lambda i=i, j=j: 
                                                       self.log(f"    嵌套 iframe {i+1}.{j+1} 輸入框數量: {len(nested_inputs)}"))
                                        
                                        # 檢測嵌套 iframe 的關鍵內容
                                        nested_keywords = [kw for kw in edit_keywords if kw in nested_content]
                                        if nested_keywords:
                                            self.root.after(0, lambda i=i, j=j, kws=nested_keywords: 
                                                           self.log(f"    🎯 嵌套 iframe {i+1}.{j+1} 包含關鍵字: {kws}"))
                                        
                                        # 如果找到關鍵內容，詳細分析按鈕
                                        if nested_keywords and ('送出' in nested_keywords or '取消' in nested_keywords):
                                            self.root.after(0, lambda i=i, j=j: 
                                                           self.log(f"    🎯 在嵌套 iframe {i+1}.{j+1} 中分析按鈕..."))
                                            
                                            for k, btn in enumerate(nested_buttons[:10]):  # 只分析前10個
                                                try:
                                                    btn_text = btn.text.strip()
                                                    btn_value = btn.get_attribute('value') or ''
                                                    btn_class = btn.get_attribute('class') or ''
                                                    btn_visible = btn.is_displayed()
                                                    
                                                    self.root.after(0, lambda i=i, j=j, k=k, txt=btn_text, val=btn_value, 
                                                                   cls=btn_class, vis=btn_visible:
                                                                   self.log(f"      按鈕 {k+1}: '{txt}' value='{val}' class='{cls}' visible={vis}"))
                                                except:
                                                    pass
                                        
                                        # 切換回父 iframe
                                        driver.switch_to.parent_frame()
                                        
                                    except Exception as e:
                                        self.root.after(0, lambda i=i, j=j, e=str(e): 
                                                       self.log(f"    ❌ 嵌套 iframe {i+1}.{j+1} 檢測失敗: {e}"))
                                        try:
                                            driver.switch_to.parent_frame()
                                        except:
                                            pass
                            
                            # 切換回主頁面
                            driver.switch_to.default_content()
                            
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"❌ iframe {i+1} 檢測失敗: {e}"))
                            try:
                                driver.switch_to.default_content()
                            except:
                                pass
                    
                    self.root.after(0, lambda: self.log("\n✅ 深度掃描完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 掃描過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_btn.config(state="normal"))
            
            threading.Thread(target=scan_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動掃描失敗: {e}")
            self.detect_btn.config(state="normal")
    
    def detect_modal_structure(self):
        """檢測彈窗結構"""
        try:
            self.log("🎯 檢測彈窗結構...")
            self.detect_modal_btn.config(state="disabled")
            
            def modal_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 檢測各種彈窗結構
                    modal_selectors = [
                        ".ui-dialog",
                        ".modal",
                        ".popup",
                        ".overlay",
                        "[role='dialog']",
                        "[role='modal']",
                        ".swal2-popup",
                        ".sweet-alert",
                        ".dialog",
                        ".lightbox"
                    ]
                    
                    self.root.after(0, lambda: self.log("🔍 檢測彈窗容器..."))
                    
                    for selector in modal_selectors:
                        try:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            if elements:
                                self.root.after(0, lambda s=selector, c=len(elements): 
                                               self.log(f"  找到 {c} 個 {s} 彈窗"))
                                
                                for i, element in enumerate(elements):
                                    try:
                                        is_visible = element.is_displayed()
                                        element_text = element.text.strip()[:200]
                                        
                                        self.root.after(0, lambda s=selector, i=i, vis=is_visible, txt=element_text:
                                                       self.log(f"    {s} {i+1}: visible={vis} text='{txt}...'"))
                                    except:
                                        pass
                        except:
                            pass
                    
                    self.root.after(0, lambda: self.log("✅ 彈窗結構檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_modal_btn.config(state="normal"))
            
            threading.Thread(target=modal_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_modal_btn.config(state="normal")
    
    def take_screenshot(self):
        """截圖當前狀態"""
        try:
            self.log("📸 截圖當前狀態...")
            
            from mvp_grabber import driver
            
            if not driver:
                self.log("❌ 瀏覽器未啟動")
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            screenshot_path = f"screenshots/real_dialog_debug_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            
            driver.save_screenshot(screenshot_path)
            self.log(f"✅ 截圖已保存: {screenshot_path}")
            
        except Exception as e:
            self.log(f"❌ 截圖失敗: {e}")
    
    def close_app(self):
        """關閉應用"""
        self.root.destroy()
        
    def run(self):
        """運行 GUI"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🔍 啟動真正編輯彈窗尋找工具")
    
    try:
        app = RealDialogFinderGUI()
        app.run()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")

if __name__ == "__main__":
    main()
