2025-07-01 17:31:23,574 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_173123.log
2025-07-01 17:31:39,791 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 17:31:39,792 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 17:31:39,860 - DEBUG - chromedriver not found in PATH
2025-07-01 17:31:39,860 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:31:39,861 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 17:31:39,861 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 17:31:39,861 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 17:31:39,862 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 17:31:39,862 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:31:39,868 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 12336 using 0 to output -3
2025-07-01 17:31:40,384 - DEBUG - POST http://localhost:56334/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 17:31:40,385 - DEBUG - Starting new HTTP connection (1): localhost:56334
2025-07-01 17:31:40,921 - DEBUG - http://localhost:56334 "POST /session HTTP/1.1" 200 0
2025-07-01 17:31:40,921 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir12336_1261816740"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:56337"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"5d6f056814eb6b7206d27b2878e8e61e"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:40,921 - DEBUG - Finished Request
2025-07-01 17:31:40,922 - DEBUG - POST http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 17:31:42,858 - DEBUG - http://localhost:56334 "POST /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:42,858 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:42,858 - DEBUG - Finished Request
2025-07-01 17:31:42,858 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 17:31:42,859 - DEBUG - POST http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 17:31:42,865 - DEBUG - http://localhost:56334 "POST /session/5d6f056814eb6b7206d27b2878e8e61e/execute/sync HTTP/1.1" 200 0
2025-07-01 17:31:42,866 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:42,866 - DEBUG - Finished Request
2025-07-01 17:31:42,866 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 17:31:42,866 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:42,898 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:42,898 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:42,898 - DEBUG - Finished Request
2025-07-01 17:31:43,899 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:43,908 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:43,909 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:43,909 - DEBUG - Finished Request
2025-07-01 17:31:44,911 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:44,916 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:44,916 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:44,916 - DEBUG - Finished Request
2025-07-01 17:31:45,917 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:45,924 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:45,924 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:45,924 - DEBUG - Finished Request
2025-07-01 17:31:46,925 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:46,931 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:46,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:46,931 - DEBUG - Finished Request
2025-07-01 17:31:47,932 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:47,939 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:47,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:47,939 - DEBUG - Finished Request
2025-07-01 17:31:48,941 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:48,949 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:48,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:48,949 - DEBUG - Finished Request
2025-07-01 17:31:49,950 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:49,958 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:49,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:49,959 - DEBUG - Finished Request
2025-07-01 17:31:50,960 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:50,969 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:50,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:50,969 - DEBUG - Finished Request
2025-07-01 17:31:51,970 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:51,977 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:51,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:51,978 - DEBUG - Finished Request
2025-07-01 17:31:52,979 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:52,987 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:52,988 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:52,988 - DEBUG - Finished Request
2025-07-01 17:31:53,989 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:53,997 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:53,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:53,997 - DEBUG - Finished Request
2025-07-01 17:31:54,998 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:55,006 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:55,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:55,006 - DEBUG - Finished Request
2025-07-01 17:31:56,007 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:56,015 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:56,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:56,015 - DEBUG - Finished Request
2025-07-01 17:31:57,017 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:57,026 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:57,026 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:57,026 - DEBUG - Finished Request
2025-07-01 17:31:58,027 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:58,033 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:58,033 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:58,033 - DEBUG - Finished Request
2025-07-01 17:31:59,034 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:31:59,041 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:31:59,042 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:31:59,042 - DEBUG - Finished Request
2025-07-01 17:32:00,043 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:00,050 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:00,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:00,051 - DEBUG - Finished Request
2025-07-01 17:32:01,051 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:01,059 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:01,060 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:01,060 - DEBUG - Finished Request
2025-07-01 17:32:02,061 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:02,067 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:02,067 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:02,068 - DEBUG - Finished Request
2025-07-01 17:32:03,069 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:03,076 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:03,076 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:03,076 - DEBUG - Finished Request
2025-07-01 17:32:04,077 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:04,083 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:04,083 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:04,084 - DEBUG - Finished Request
2025-07-01 17:32:05,085 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:05,091 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:05,091 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:05,092 - DEBUG - Finished Request
2025-07-01 17:32:06,092 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:06,099 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:06,099 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:06,099 - DEBUG - Finished Request
2025-07-01 17:32:07,100 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:07,106 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:07,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:07,106 - DEBUG - Finished Request
2025-07-01 17:32:08,108 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:08,115 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:08,115 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:08,115 - DEBUG - Finished Request
2025-07-01 17:32:09,116 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:09,123 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:09,124 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:09,124 - DEBUG - Finished Request
2025-07-01 17:32:10,125 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:10,131 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:10,131 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:10,131 - DEBUG - Finished Request
2025-07-01 17:32:11,133 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:11,139 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:11,139 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:11,139 - DEBUG - Finished Request
2025-07-01 17:32:12,141 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:12,147 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:12,147 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:12,148 - DEBUG - Finished Request
2025-07-01 17:32:13,149 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:13,153 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:13,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:13,154 - DEBUG - Finished Request
2025-07-01 17:32:14,155 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:14,441 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:14,442 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:14,442 - DEBUG - Finished Request
2025-07-01 17:32:15,443 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:15,449 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:15,449 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:15,449 - DEBUG - Finished Request
2025-07-01 17:32:16,450 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:16,456 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:16,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:16,457 - DEBUG - Finished Request
2025-07-01 17:32:17,457 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:17,463 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:17,463 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:17,464 - DEBUG - Finished Request
2025-07-01 17:32:18,466 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:18,471 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:18,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:18,471 - DEBUG - Finished Request
2025-07-01 17:32:19,472 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:19,479 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:19,479 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:19,480 - DEBUG - Finished Request
2025-07-01 17:32:20,481 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:20,486 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:20,486 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:20,486 - DEBUG - Finished Request
2025-07-01 17:32:21,487 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:21,492 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:21,493 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:21,493 - DEBUG - Finished Request
2025-07-01 17:32:22,494 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:22,498 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:22,499 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:22,499 - DEBUG - Finished Request
2025-07-01 17:32:23,500 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:23,506 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:23,506 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:23,506 - DEBUG - Finished Request
2025-07-01 17:32:24,507 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:24,512 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:24,512 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:24,513 - DEBUG - Finished Request
2025-07-01 17:32:25,514 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:25,521 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:25,522 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:25,522 - DEBUG - Finished Request
2025-07-01 17:32:26,523 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:26,529 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:26,530 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:26,530 - DEBUG - Finished Request
2025-07-01 17:32:27,531 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:27,538 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:27,538 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:27,538 - DEBUG - Finished Request
2025-07-01 17:32:28,539 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:28,546 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:28,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:28,546 - DEBUG - Finished Request
2025-07-01 17:32:29,547 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:29,554 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:29,554 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:29,554 - DEBUG - Finished Request
2025-07-01 17:32:30,555 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:30,561 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:30,561 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:30,561 - DEBUG - Finished Request
2025-07-01 17:32:31,562 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:31,568 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:31,568 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:31,568 - DEBUG - Finished Request
2025-07-01 17:32:32,569 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:32,575 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:32,575 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:32,575 - DEBUG - Finished Request
2025-07-01 17:32:33,576 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:33,582 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:33,583 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:33,583 - DEBUG - Finished Request
2025-07-01 17:32:34,584 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:34,590 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:34,591 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:34,591 - DEBUG - Finished Request
2025-07-01 17:32:35,592 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:35,599 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:35,599 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:35,599 - DEBUG - Finished Request
2025-07-01 17:32:36,600 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:36,606 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:36,606 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:36,607 - DEBUG - Finished Request
2025-07-01 17:32:37,608 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:37,615 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:37,615 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:37,615 - DEBUG - Finished Request
2025-07-01 17:32:38,616 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:38,624 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:38,624 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:38,625 - DEBUG - Finished Request
2025-07-01 17:32:39,625 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:39,632 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:39,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:39,633 - DEBUG - Finished Request
2025-07-01 17:32:40,634 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:40,640 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:40,641 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:40,641 - DEBUG - Finished Request
2025-07-01 17:32:41,641 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:41,649 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:41,649 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:41,649 - DEBUG - Finished Request
2025-07-01 17:32:42,650 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:42,658 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:42,658 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:42,658 - DEBUG - Finished Request
2025-07-01 17:32:43,658 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:43,666 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:43,667 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:43,667 - DEBUG - Finished Request
2025-07-01 17:32:44,668 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:44,675 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:44,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:44,675 - DEBUG - Finished Request
2025-07-01 17:32:45,676 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:45,682 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:45,682 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:45,682 - DEBUG - Finished Request
2025-07-01 17:32:46,683 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:46,690 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:46,690 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:46,690 - DEBUG - Finished Request
2025-07-01 17:32:47,691 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:47,698 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:47,698 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:47,699 - DEBUG - Finished Request
2025-07-01 17:32:48,700 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:48,707 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:48,707 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:48,707 - DEBUG - Finished Request
2025-07-01 17:32:49,708 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:49,713 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:49,713 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:49,713 - DEBUG - Finished Request
2025-07-01 17:32:50,714 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:50,720 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:50,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:50,720 - DEBUG - Finished Request
2025-07-01 17:32:51,722 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:51,728 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:51,728 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:51,729 - DEBUG - Finished Request
2025-07-01 17:32:52,729 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:52,735 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:52,736 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:52,736 - DEBUG - Finished Request
2025-07-01 17:32:53,737 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:53,745 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:53,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:53,745 - DEBUG - Finished Request
2025-07-01 17:32:54,746 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:54,754 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:54,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:54,754 - DEBUG - Finished Request
2025-07-01 17:32:55,754 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:55,760 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:55,760 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:55,760 - DEBUG - Finished Request
2025-07-01 17:32:56,760 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:56,766 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:56,766 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:56,766 - DEBUG - Finished Request
2025-07-01 17:32:57,767 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:57,774 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:57,774 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:57,774 - DEBUG - Finished Request
2025-07-01 17:32:58,776 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:58,782 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:58,782 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:58,782 - DEBUG - Finished Request
2025-07-01 17:32:59,783 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:32:59,788 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:32:59,788 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:32:59,789 - DEBUG - Finished Request
2025-07-01 17:33:00,789 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:00,798 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:00,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:00,798 - DEBUG - Finished Request
2025-07-01 17:33:01,799 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:01,807 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:01,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:01,807 - DEBUG - Finished Request
2025-07-01 17:33:02,808 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:02,813 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:02,814 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:02,814 - DEBUG - Finished Request
2025-07-01 17:33:03,815 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:03,822 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:03,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:03,822 - DEBUG - Finished Request
2025-07-01 17:33:04,823 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:04,830 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:04,830 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:04,830 - DEBUG - Finished Request
2025-07-01 17:33:05,831 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:05,838 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:05,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:05,839 - DEBUG - Finished Request
2025-07-01 17:33:06,839 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:06,844 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:06,844 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:06,845 - DEBUG - Finished Request
2025-07-01 17:33:07,846 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:07,852 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:07,852 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:07,852 - DEBUG - Finished Request
2025-07-01 17:33:08,853 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:08,858 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:08,858 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:08,858 - DEBUG - Finished Request
2025-07-01 17:33:09,860 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:09,866 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:09,867 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:09,867 - DEBUG - Finished Request
2025-07-01 17:33:10,867 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:10,874 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:10,874 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:10,875 - DEBUG - Finished Request
2025-07-01 17:33:11,876 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:11,882 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:11,882 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:11,882 - DEBUG - Finished Request
2025-07-01 17:33:12,884 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:12,888 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:12,889 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:12,889 - DEBUG - Finished Request
2025-07-01 17:33:13,890 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:13,896 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:13,897 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:13,897 - DEBUG - Finished Request
2025-07-01 17:33:14,898 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:14,904 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:14,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:14,904 - DEBUG - Finished Request
2025-07-01 17:33:15,905 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:15,912 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:15,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:15,913 - DEBUG - Finished Request
2025-07-01 17:33:16,913 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:16,920 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:16,920 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:16,921 - DEBUG - Finished Request
2025-07-01 17:33:17,922 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:17,928 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:17,928 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:17,928 - DEBUG - Finished Request
2025-07-01 17:33:18,929 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:18,933 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:18,934 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:18,934 - DEBUG - Finished Request
2025-07-01 17:33:19,935 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:19,941 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:19,941 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:19,941 - DEBUG - Finished Request
2025-07-01 17:33:20,941 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:20,949 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:20,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:20,950 - DEBUG - Finished Request
2025-07-01 17:33:21,951 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:21,956 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:21,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:21,956 - DEBUG - Finished Request
2025-07-01 17:33:22,957 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:22,962 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:22,963 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:22,963 - DEBUG - Finished Request
2025-07-01 17:33:23,964 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:23,970 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:23,970 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:23,970 - DEBUG - Finished Request
2025-07-01 17:33:24,971 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:24,978 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:24,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:24,978 - DEBUG - Finished Request
2025-07-01 17:33:25,979 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:25,985 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:25,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:25,985 - DEBUG - Finished Request
2025-07-01 17:33:26,986 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:26,992 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:26,992 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:26,992 - DEBUG - Finished Request
2025-07-01 17:33:27,994 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:28,002 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:28,002 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:28,002 - DEBUG - Finished Request
2025-07-01 17:33:29,003 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:29,012 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:29,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:29,012 - DEBUG - Finished Request
2025-07-01 17:33:30,013 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:30,020 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:30,020 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:30,020 - DEBUG - Finished Request
2025-07-01 17:33:31,021 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:31,029 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:31,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:31,030 - DEBUG - Finished Request
2025-07-01 17:33:32,031 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:32,040 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:32,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:32,040 - DEBUG - Finished Request
2025-07-01 17:33:33,041 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:33,048 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:33,049 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:33,049 - DEBUG - Finished Request
2025-07-01 17:33:34,050 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:34,059 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:34,059 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:34,059 - DEBUG - Finished Request
2025-07-01 17:33:35,060 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:35,070 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:35,070 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:35,071 - DEBUG - Finished Request
2025-07-01 17:33:36,071 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:36,080 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:36,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:36,081 - DEBUG - Finished Request
2025-07-01 17:33:37,081 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:37,090 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:37,090 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:37,091 - DEBUG - Finished Request
2025-07-01 17:33:38,092 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:38,100 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:38,100 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:38,101 - DEBUG - Finished Request
2025-07-01 17:33:39,102 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:39,111 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:39,111 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:39,112 - DEBUG - Finished Request
2025-07-01 17:33:40,113 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:40,122 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:40,122 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:40,122 - DEBUG - Finished Request
2025-07-01 17:33:41,123 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:41,131 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:41,131 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:41,132 - DEBUG - Finished Request
2025-07-01 17:33:42,133 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:42,140 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:42,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:42,141 - DEBUG - Finished Request
2025-07-01 17:33:43,142 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:43,150 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:43,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:43,151 - DEBUG - Finished Request
2025-07-01 17:33:44,153 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:44,161 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:44,161 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:44,162 - DEBUG - Finished Request
2025-07-01 17:33:45,163 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:45,170 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:45,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:45,171 - DEBUG - Finished Request
2025-07-01 17:33:46,173 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:46,179 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:46,180 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:46,180 - DEBUG - Finished Request
2025-07-01 17:33:47,182 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:47,190 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:47,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:47,190 - DEBUG - Finished Request
2025-07-01 17:33:48,191 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:48,201 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:48,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:48,201 - DEBUG - Finished Request
2025-07-01 17:33:49,202 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:49,211 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:49,212 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:49,212 - DEBUG - Finished Request
2025-07-01 17:33:50,213 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:50,221 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:50,221 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:50,221 - DEBUG - Finished Request
2025-07-01 17:33:51,222 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:51,230 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:51,230 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:51,230 - DEBUG - Finished Request
2025-07-01 17:33:52,232 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:52,239 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:52,240 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:52,240 - DEBUG - Finished Request
2025-07-01 17:33:53,241 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:53,250 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:53,251 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:53,251 - DEBUG - Finished Request
2025-07-01 17:33:54,252 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:54,259 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:54,260 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:54,260 - DEBUG - Finished Request
2025-07-01 17:33:55,260 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:55,267 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:55,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:55,267 - DEBUG - Finished Request
2025-07-01 17:33:56,268 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:56,275 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:56,275 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:56,275 - DEBUG - Finished Request
2025-07-01 17:33:57,277 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:57,283 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:57,284 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:57,284 - DEBUG - Finished Request
2025-07-01 17:33:58,285 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:58,293 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:58,294 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:58,294 - DEBUG - Finished Request
2025-07-01 17:33:59,295 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:33:59,302 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:33:59,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:33:59,303 - DEBUG - Finished Request
2025-07-01 17:34:00,305 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:00,311 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:00,311 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:00,311 - DEBUG - Finished Request
2025-07-01 17:34:01,312 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:01,320 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:01,321 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:01,321 - DEBUG - Finished Request
2025-07-01 17:34:02,322 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:02,331 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:02,331 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:02,331 - DEBUG - Finished Request
2025-07-01 17:34:03,332 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:03,339 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:03,340 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:03,340 - DEBUG - Finished Request
2025-07-01 17:34:04,340 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:04,348 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:04,349 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:04,349 - DEBUG - Finished Request
2025-07-01 17:34:05,350 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:05,358 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:05,359 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:05,359 - DEBUG - Finished Request
2025-07-01 17:34:06,361 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:06,369 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:06,369 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:06,370 - DEBUG - Finished Request
2025-07-01 17:34:07,370 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:07,381 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:07,381 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:07,381 - DEBUG - Finished Request
2025-07-01 17:34:08,383 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:08,391 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:08,391 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:08,392 - DEBUG - Finished Request
2025-07-01 17:34:09,392 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:09,400 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:09,400 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:09,400 - DEBUG - Finished Request
2025-07-01 17:34:10,401 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:10,408 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:10,408 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:10,409 - DEBUG - Finished Request
2025-07-01 17:34:11,410 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:11,419 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:11,420 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:11,420 - DEBUG - Finished Request
2025-07-01 17:34:12,421 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:12,500 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:12,501 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:12,501 - DEBUG - Finished Request
2025-07-01 17:34:13,501 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:13,509 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 200 0
2025-07-01 17:34:13,509 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:13,510 - DEBUG - Finished Request
2025-07-01 17:34:14,510 - DEBUG - GET http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e/url {}
2025-07-01 17:34:14,512 - DEBUG - http://localhost:56334 "GET /session/5d6f056814eb6b7206d27b2878e8e61e/url HTTP/1.1" 404 0
2025-07-01 17:34:14,513 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:14,513 - DEBUG - Finished Request
2025-07-01 17:34:14,514 - DEBUG - DELETE http://localhost:56334/session/5d6f056814eb6b7206d27b2878e8e61e {}
2025-07-01 17:34:14,614 - DEBUG - http://localhost:56334 "DELETE /session/5d6f056814eb6b7206d27b2878e8e61e HTTP/1.1" 200 0
2025-07-01 17:34:14,614 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:34:14,615 - DEBUG - Finished Request
