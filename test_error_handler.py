"""
錯誤處理模組測試腳本
用於測試 SweetAlert2 彈窗檢測和處理功能
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from error_handler import ErrorHandler

class TestErrorHandler(unittest.TestCase):
    """錯誤處理器測試類"""
    
    def setUp(self):
        """測試前準備"""
        self.mock_driver = Mock()
        self.mock_logger = Mock()
        self.error_handler = <PERSON>rror<PERSON>and<PERSON>(self.mock_driver, self.mock_logger)
    
    def test_analyze_time_not_open_error(self):
        """測試預約時間未開放錯誤分析"""
        error_text = "送出失敗：尚未開放 2025/07/04 預約進廠，請於 9:30 後預約。（現在時間：2025-06-27 09:11:50）"
        
        result = self.error_handler._analyze_error_type(error_text)
        
        self.assertEqual(result["type"], "time_not_open")
        self.assertEqual(result["action"], "wait_and_retry")
        self.assertEqual(result["confidence"], "high")
        self.assertIn("預約時間未開放", result["description"])
    
    def test_analyze_factory_full_error(self):
        """測試工廠名額已滿錯誤分析"""
        error_text = "送出失敗：M1：仁武廠選擇的進廠數量已滿，請選擇其他廠。"
        
        result = self.error_handler._analyze_error_type(error_text)
        
        self.assertEqual(result["type"], "factory_full")
        self.assertEqual(result["action"], "switch_factory")
        self.assertEqual(result["confidence"], "high")
        self.assertIn("工廠名額已滿", result["description"])
    
    def test_analyze_general_failure_error(self):
        """測試一般送出失敗錯誤分析"""
        error_text = "送出失敗：系統暫時無法處理您的請求。"
        
        result = self.error_handler._analyze_error_type(error_text)
        
        self.assertEqual(result["type"], "general_failure")
        self.assertEqual(result["action"], "retry_or_manual")
        self.assertEqual(result["confidence"], "high")
    
    def test_analyze_unknown_error(self):
        """測試未知錯誤分析"""
        error_text = "這是一個未知的錯誤訊息"
        
        result = self.error_handler._analyze_error_type(error_text)
        
        self.assertEqual(result["type"], "unknown")
        self.assertEqual(result["action"], "manual_intervention")
        self.assertEqual(result["confidence"], "low")
    
    @patch('time.sleep')
    def test_handle_time_not_open_error(self, mock_sleep):
        """測試處理預約時間未開放錯誤"""
        error_info = {
            "type": "time_not_open",
            "action": "wait_and_retry",
            "pattern": {"wait_time": 60}
        }
        
        # Mock _close_popup 方法
        self.error_handler._close_popup = Mock(return_value=True)
        
        result = self.error_handler._handle_time_not_open(error_info)
        
        self.assertTrue(result)
        self.error_handler._close_popup.assert_called_once()
        mock_sleep.assert_called_once_with(60)
    
    def test_close_popup_with_confirm_button(self):
        """測試使用確認按鈕關閉彈窗"""
        # 設置 mock 元素
        mock_button = Mock()
        mock_button.is_displayed.return_value = True
        
        self.mock_driver.find_element.return_value = mock_button
        
        result = self.error_handler._close_popup()
        
        self.assertTrue(result)
        mock_button.click.assert_called_once()
    
    def test_error_patterns_configuration(self):
        """測試錯誤模式配置"""
        patterns = self.error_handler.error_patterns
        
        # 檢查必要的錯誤模式是否存在
        self.assertIn("time_not_open", patterns)
        self.assertIn("factory_full", patterns)
        self.assertIn("general_failure", patterns)
        
        # 檢查每個模式是否有必要的字段
        for pattern_name, pattern in patterns.items():
            self.assertIn("keywords", pattern)
            self.assertIn("action", pattern)
            self.assertIn("description", pattern)
            self.assertIsInstance(pattern["keywords"], list)
            self.assertTrue(len(pattern["keywords"]) > 0)

class TestErrorHandlerIntegration(unittest.TestCase):
    """錯誤處理器整合測試"""
    
    def setUp(self):
        """測試前準備"""
        self.mock_driver = Mock()
        self.error_handler = ErrorHandler(self.mock_driver)
    
    def test_full_error_detection_flow(self):
        """測試完整的錯誤檢測流程"""
        # 模擬找到彈窗元素
        mock_popup = Mock()
        mock_text_element = Mock()
        mock_text_element.text = "送出失敗：尚未開放 2025/07/04 預約進廠，請於 9:30 後預約。"
        
        mock_popup.find_element.return_value = mock_text_element
        
        # 模擬 WebDriverWait
        with patch('error_handler.WebDriverWait') as mock_wait:
            mock_wait.return_value.until.return_value = mock_popup
            
            result = self.error_handler.check_for_error_popup()
            
            self.assertIsNotNone(result)
            self.assertEqual(result["type"], "time_not_open")
            self.assertIn("raw_text", result)
            self.assertIn("timestamp", result)

def run_error_handler_tests():
    """運行錯誤處理器測試"""
    print("🧪 開始運行錯誤處理器測試...")
    
    # 創建測試套件
    test_suite = unittest.TestSuite()
    
    # 添加測試案例
    test_suite.addTest(unittest.makeSuite(TestErrorHandler))
    test_suite.addTest(unittest.makeSuite(TestErrorHandlerIntegration))
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 輸出結果
    if result.wasSuccessful():
        print("✅ 所有測試通過！")
        return True
    else:
        print("❌ 部分測試失敗")
        print(f"失敗數量: {len(result.failures)}")
        print(f"錯誤數量: {len(result.errors)}")
        return False

if __name__ == "__main__":
    # 運行測試
    success = run_error_handler_tests()
    
    # 如果測試失敗，退出程序並返回錯誤代碼
    if not success:
        sys.exit(1)
    
    print("\n🎉 錯誤處理模組測試完成！")
