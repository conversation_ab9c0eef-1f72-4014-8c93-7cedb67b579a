2025-07-01 10:50:15,599 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_105015.log
2025-07-01 10:50:45,929 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 10:50:45,929 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 10:50:46,602 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 10:50:46,602 - DEBUG - chromedriver not found in PATH
2025-07-01 10:50:46,602 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 10:50:46,602 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 10:50:46,602 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 10:50:46,603 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 10:50:46,603 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 10:50:46,603 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 10:50:46,603 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 10:50:46,608 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 12496 using 0 to output -3
2025-07-01 10:50:47,116 - DEBUG - POST http://localhost:52013/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 10:50:47,117 - DEBUG - Starting new HTTP connection (1): localhost:52013
2025-07-01 10:50:47,633 - DEBUG - http://localhost:52013 "POST /session HTTP/1.1" 200 0
2025-07-01 10:50:47,633 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir12496_1664934199"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:52018"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"641a56c6a71192a10c943ced373cf729"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:47,633 - DEBUG - Finished Request
2025-07-01 10:50:47,634 - DEBUG - POST http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 10:50:48,591 - DEBUG - http://localhost:52013 "POST /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:48,591 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:48,592 - DEBUG - Finished Request
2025-07-01 10:50:48,592 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 10:50:48,592 - DEBUG - POST http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 10:50:48,615 - DEBUG - http://localhost:52013 "POST /session/641a56c6a71192a10c943ced373cf729/execute/sync HTTP/1.1" 200 0
2025-07-01 10:50:48,615 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:48,616 - DEBUG - Finished Request
2025-07-01 10:50:48,616 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 10:50:48,617 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:48,660 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:48,661 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:48,661 - DEBUG - Finished Request
2025-07-01 10:50:49,662 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:49,667 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:49,668 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:49,668 - DEBUG - Finished Request
2025-07-01 10:50:50,669 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:50,674 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:50,674 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:50,675 - DEBUG - Finished Request
2025-07-01 10:50:51,675 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:51,681 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:51,681 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:51,681 - DEBUG - Finished Request
2025-07-01 10:50:52,682 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:52,688 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:52,688 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:52,688 - DEBUG - Finished Request
2025-07-01 10:50:53,689 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:53,695 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:53,696 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:53,696 - DEBUG - Finished Request
2025-07-01 10:50:54,697 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:54,702 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:54,703 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:54,703 - DEBUG - Finished Request
2025-07-01 10:50:55,703 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:55,709 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:55,709 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:55,710 - DEBUG - Finished Request
2025-07-01 10:50:56,711 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:56,717 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:56,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:56,718 - DEBUG - Finished Request
2025-07-01 10:50:57,718 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:57,726 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:57,726 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:57,727 - DEBUG - Finished Request
2025-07-01 10:50:58,728 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:58,735 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:58,736 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:58,736 - DEBUG - Finished Request
2025-07-01 10:50:59,736 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:50:59,743 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:50:59,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:50:59,743 - DEBUG - Finished Request
2025-07-01 10:51:00,744 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:51:00,751 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:51:00,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:51:00,752 - DEBUG - Finished Request
2025-07-01 10:51:01,753 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:51:01,759 - DEBUG - http://localhost:52013 "GET /session/641a56c6a71192a10c943ced373cf729/url HTTP/1.1" 200 0
2025-07-01 10:51:01,759 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:51:01,759 - DEBUG - Finished Request
2025-07-01 10:51:02,715 - DEBUG - DELETE http://localhost:52013/session/641a56c6a71192a10c943ced373cf729 {}
2025-07-01 10:51:02,761 - DEBUG - GET http://localhost:52013/session/641a56c6a71192a10c943ced373cf729/url {}
2025-07-01 10:51:02,761 - DEBUG - Starting new HTTP connection (2): localhost:52013
2025-07-01 10:51:02,768 - DEBUG - http://localhost:52013 "DELETE /session/641a56c6a71192a10c943ced373cf729 HTTP/1.1" 200 0
2025-07-01 10:51:02,768 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 10:51:02,769 - DEBUG - Finished Request
2025-07-01 10:51:02,805 - DEBUG - Incremented Retry for (url='/session/641a56c6a71192a10c943ced373cf729/url'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-01 10:51:02,806 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 10:51:02,806 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /session/641a56c6a71192a10c943ced373cf729/url
2025-07-01 10:51:02,806 - DEBUG - Resetting dropped connection: localhost
