2025-06-30 18:57:53,648 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_185753.log
2025-06-30 18:58:04,406 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 18:58:04,406 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 18:58:04,468 - DEBUG - chromedriver not found in PATH
2025-06-30 18:58:04,469 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:58:04,469 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 18:58:04,469 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 18:58:04,469 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 18:58:04,469 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 18:58:04,469 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:58:04,474 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 28092 using 0 to output -3
2025-06-30 18:58:04,997 - DEBUG - POST http://localhost:54132/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 18:58:04,998 - DEBUG - Starting new HTTP connection (1): localhost:54132
2025-06-30 18:58:05,515 - DEBUG - http://localhost:54132 "POST /session HTTP/1.1" 200 0
2025-06-30 18:58:05,516 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir28092_333714241"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54135"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"2e4ff9d96372a59df96a754125586861"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:05,516 - DEBUG - Finished Request
2025-06-30 18:58:05,517 - DEBUG - POST http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 18:58:06,540 - DEBUG - http://localhost:54132 "POST /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:06,540 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:06,541 - DEBUG - Finished Request
2025-06-30 18:58:06,541 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 18:58:06,542 - DEBUG - POST http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 18:58:06,552 - DEBUG - http://localhost:54132 "POST /session/2e4ff9d96372a59df96a754125586861/execute/sync HTTP/1.1" 200 0
2025-06-30 18:58:06,553 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:06,553 - DEBUG - Finished Request
2025-06-30 18:58:06,553 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 18:58:06,554 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:06,587 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:06,587 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:06,588 - DEBUG - Finished Request
2025-06-30 18:58:07,588 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:07,597 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:07,598 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:07,598 - DEBUG - Finished Request
2025-06-30 18:58:08,598 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:08,604 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:08,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:08,605 - DEBUG - Finished Request
2025-06-30 18:58:09,605 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:09,612 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:09,613 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:09,613 - DEBUG - Finished Request
2025-06-30 18:58:10,614 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:10,627 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:10,627 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:10,627 - DEBUG - Finished Request
2025-06-30 18:58:11,628 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:11,634 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:11,634 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:11,634 - DEBUG - Finished Request
2025-06-30 18:58:12,635 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:12,665 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:12,665 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:12,666 - DEBUG - Finished Request
2025-06-30 18:58:13,668 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:13,675 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:13,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:13,675 - DEBUG - Finished Request
2025-06-30 18:58:14,677 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:14,682 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:14,683 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:14,683 - DEBUG - Finished Request
2025-06-30 18:58:15,684 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:15,692 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:15,692 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:15,692 - DEBUG - Finished Request
2025-06-30 18:58:16,693 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:16,701 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:16,702 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:16,702 - DEBUG - Finished Request
2025-06-30 18:58:17,702 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:17,710 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:17,710 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:17,710 - DEBUG - Finished Request
2025-06-30 18:58:18,711 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:18,719 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:18,719 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:18,719 - DEBUG - Finished Request
2025-06-30 18:58:19,720 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:19,727 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:19,728 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:19,728 - DEBUG - Finished Request
2025-06-30 18:58:20,729 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:20,736 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:20,736 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:20,736 - DEBUG - Finished Request
2025-06-30 18:58:21,738 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:21,782 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:21,783 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:21,783 - DEBUG - Finished Request
2025-06-30 18:58:22,783 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:22,791 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:22,791 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:22,791 - DEBUG - Finished Request
2025-06-30 18:58:23,792 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:23,800 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:23,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:23,801 - DEBUG - Finished Request
2025-06-30 18:58:24,801 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:24,808 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:24,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:24,809 - DEBUG - Finished Request
2025-06-30 18:58:25,810 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:25,816 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:25,816 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:25,817 - DEBUG - Finished Request
2025-06-30 18:58:26,817 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:26,824 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:26,824 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:26,824 - DEBUG - Finished Request
2025-06-30 18:58:27,825 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:27,832 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:27,832 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:27,832 - DEBUG - Finished Request
2025-06-30 18:58:28,832 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:28,841 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:28,841 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:28,842 - DEBUG - Finished Request
2025-06-30 18:58:29,842 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:29,850 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:29,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:29,851 - DEBUG - Finished Request
2025-06-30 18:58:30,853 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:30,861 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:30,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:30,861 - DEBUG - Finished Request
2025-06-30 18:58:31,862 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:31,869 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:31,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:31,870 - DEBUG - Finished Request
2025-06-30 18:58:32,871 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:32,878 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:32,878 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:32,878 - DEBUG - Finished Request
2025-06-30 18:58:33,880 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:33,888 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:33,888 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:33,888 - DEBUG - Finished Request
2025-06-30 18:58:34,889 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:34,897 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:34,898 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:34,898 - DEBUG - Finished Request
2025-06-30 18:58:35,899 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:35,906 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:35,906 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:35,906 - DEBUG - Finished Request
2025-06-30 18:58:36,907 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:36,915 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:36,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:36,915 - DEBUG - Finished Request
2025-06-30 18:58:37,916 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:37,927 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:37,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:37,927 - DEBUG - Finished Request
2025-06-30 18:58:38,928 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:38,936 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:38,936 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:38,937 - DEBUG - Finished Request
2025-06-30 18:58:39,938 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:39,945 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:39,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:39,946 - DEBUG - Finished Request
2025-06-30 18:58:40,947 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:40,955 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:40,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:40,955 - DEBUG - Finished Request
2025-06-30 18:58:41,956 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:41,965 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:41,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:41,966 - DEBUG - Finished Request
2025-06-30 18:58:42,966 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:42,975 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:42,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:42,976 - DEBUG - Finished Request
2025-06-30 18:58:43,977 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:43,986 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:43,986 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:43,987 - DEBUG - Finished Request
2025-06-30 18:58:44,987 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:44,994 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:44,995 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:44,995 - DEBUG - Finished Request
2025-06-30 18:58:45,996 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:46,004 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:46,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:46,004 - DEBUG - Finished Request
2025-06-30 18:58:47,006 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:47,015 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:47,016 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:47,016 - DEBUG - Finished Request
2025-06-30 18:58:48,017 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:48,025 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:48,026 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:48,026 - DEBUG - Finished Request
2025-06-30 18:58:49,027 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:49,034 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:49,035 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:49,035 - DEBUG - Finished Request
2025-06-30 18:58:50,036 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:50,043 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:50,043 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:50,044 - DEBUG - Finished Request
2025-06-30 18:58:51,045 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:51,053 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:51,054 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:51,054 - DEBUG - Finished Request
2025-06-30 18:58:52,056 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:52,066 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:52,066 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:52,067 - DEBUG - Finished Request
2025-06-30 18:58:53,067 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:53,078 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:53,078 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:53,079 - DEBUG - Finished Request
2025-06-30 18:58:54,080 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:54,093 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:54,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:54,094 - DEBUG - Finished Request
2025-06-30 18:58:55,094 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:55,472 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:55,475 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:55,477 - DEBUG - Finished Request
2025-06-30 18:58:56,478 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:56,486 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:56,486 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:56,487 - DEBUG - Finished Request
2025-06-30 18:58:57,488 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:57,495 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:57,495 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:57,496 - DEBUG - Finished Request
2025-06-30 18:58:58,496 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:58,505 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:58,505 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:58,505 - DEBUG - Finished Request
2025-06-30 18:58:59,506 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:58:59,514 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:58:59,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:58:59,515 - DEBUG - Finished Request
2025-06-30 18:59:00,515 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:00,525 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:00,525 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:00,526 - DEBUG - Finished Request
2025-06-30 18:59:01,527 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:01,536 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:01,536 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:01,536 - DEBUG - Finished Request
2025-06-30 18:59:02,538 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:02,547 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:02,548 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:02,548 - DEBUG - Finished Request
2025-06-30 18:59:03,549 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:03,557 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:03,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:03,557 - DEBUG - Finished Request
2025-06-30 18:59:04,558 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:04,569 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:04,569 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:04,570 - DEBUG - Finished Request
2025-06-30 18:59:05,570 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:05,579 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:05,579 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:05,580 - DEBUG - Finished Request
2025-06-30 18:59:06,580 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:06,587 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:06,588 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:06,588 - DEBUG - Finished Request
2025-06-30 18:59:07,589 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:07,596 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:07,596 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:07,596 - DEBUG - Finished Request
2025-06-30 18:59:08,597 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:08,605 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:08,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:08,605 - DEBUG - Finished Request
2025-06-30 18:59:09,606 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:09,613 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:09,613 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:09,613 - DEBUG - Finished Request
2025-06-30 18:59:10,614 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:10,621 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:10,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:10,621 - DEBUG - Finished Request
2025-06-30 18:59:11,622 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:11,629 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:11,629 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:11,629 - DEBUG - Finished Request
2025-06-30 18:59:12,630 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:12,637 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:12,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:12,637 - DEBUG - Finished Request
2025-06-30 18:59:13,638 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:13,644 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:13,644 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:13,645 - DEBUG - Finished Request
2025-06-30 18:59:14,646 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:14,653 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:14,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:14,654 - DEBUG - Finished Request
2025-06-30 18:59:15,654 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:15,661 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:15,662 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:15,662 - DEBUG - Finished Request
2025-06-30 18:59:16,663 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:16,670 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:16,670 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:16,671 - DEBUG - Finished Request
2025-06-30 18:59:17,671 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:17,678 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:17,679 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:17,679 - DEBUG - Finished Request
2025-06-30 18:59:18,680 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:18,690 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:18,690 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:18,691 - DEBUG - Finished Request
2025-06-30 18:59:19,692 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:19,699 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:19,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:19,699 - DEBUG - Finished Request
2025-06-30 18:59:20,700 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:20,707 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:20,707 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:20,707 - DEBUG - Finished Request
2025-06-30 18:59:21,708 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:21,714 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:21,715 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:21,715 - DEBUG - Finished Request
2025-06-30 18:59:22,716 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:22,725 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:22,725 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:22,725 - DEBUG - Finished Request
2025-06-30 18:59:23,726 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:23,732 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:23,732 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:23,732 - DEBUG - Finished Request
2025-06-30 18:59:24,733 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:24,739 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:24,739 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:24,739 - DEBUG - Finished Request
2025-06-30 18:59:25,741 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:25,749 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:25,749 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:25,750 - DEBUG - Finished Request
2025-06-30 18:59:26,750 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:26,758 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:26,758 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:26,759 - DEBUG - Finished Request
2025-06-30 18:59:27,760 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:27,767 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:27,767 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:27,767 - DEBUG - Finished Request
2025-06-30 18:59:28,768 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:28,774 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:28,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:28,775 - DEBUG - Finished Request
2025-06-30 18:59:29,776 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:29,783 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:29,784 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:29,784 - DEBUG - Finished Request
2025-06-30 18:59:30,786 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:30,793 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:30,793 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:30,793 - DEBUG - Finished Request
2025-06-30 18:59:31,794 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:31,802 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:31,802 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:31,802 - DEBUG - Finished Request
2025-06-30 18:59:32,803 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:32,810 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:32,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:32,810 - DEBUG - Finished Request
2025-06-30 18:59:33,811 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:33,818 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:33,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:33,819 - DEBUG - Finished Request
2025-06-30 18:59:34,819 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:34,826 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:34,826 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:34,826 - DEBUG - Finished Request
2025-06-30 18:59:35,828 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:35,835 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:35,836 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:35,836 - DEBUG - Finished Request
2025-06-30 18:59:36,837 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:36,846 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:36,846 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:36,847 - DEBUG - Finished Request
2025-06-30 18:59:37,847 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:37,855 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:37,855 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:37,855 - DEBUG - Finished Request
2025-06-30 18:59:38,856 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:38,865 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:38,865 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:38,865 - DEBUG - Finished Request
2025-06-30 18:59:39,866 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:39,873 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:39,873 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:39,874 - DEBUG - Finished Request
2025-06-30 18:59:40,874 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:40,882 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:40,882 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:40,882 - DEBUG - Finished Request
2025-06-30 18:59:41,883 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:41,892 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:41,893 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:41,893 - DEBUG - Finished Request
2025-06-30 18:59:42,893 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:42,901 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:42,901 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:42,901 - DEBUG - Finished Request
2025-06-30 18:59:43,902 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:43,910 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:43,910 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:43,910 - DEBUG - Finished Request
2025-06-30 18:59:44,912 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:44,918 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:44,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:44,919 - DEBUG - Finished Request
2025-06-30 18:59:45,919 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:45,926 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:45,926 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:45,926 - DEBUG - Finished Request
2025-06-30 18:59:46,928 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:46,935 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:46,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:46,935 - DEBUG - Finished Request
2025-06-30 18:59:47,936 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:47,942 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:47,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:47,943 - DEBUG - Finished Request
2025-06-30 18:59:48,944 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:48,951 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:48,951 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:48,951 - DEBUG - Finished Request
2025-06-30 18:59:49,952 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:49,959 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:49,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:49,960 - DEBUG - Finished Request
2025-06-30 18:59:50,961 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:50,968 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:50,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:50,969 - DEBUG - Finished Request
2025-06-30 18:59:51,971 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:51,981 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:51,981 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:51,982 - DEBUG - Finished Request
2025-06-30 18:59:52,983 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:52,990 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:52,990 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:52,990 - DEBUG - Finished Request
2025-06-30 18:59:53,991 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:53,998 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:53,998 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:53,998 - DEBUG - Finished Request
2025-06-30 18:59:54,999 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:55,007 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:55,008 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:55,008 - DEBUG - Finished Request
2025-06-30 18:59:56,009 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:56,017 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:56,017 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:56,017 - DEBUG - Finished Request
2025-06-30 18:59:57,018 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:57,024 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:57,025 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:57,025 - DEBUG - Finished Request
2025-06-30 18:59:58,026 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:58,034 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:58,035 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:58,035 - DEBUG - Finished Request
2025-06-30 18:59:59,035 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 18:59:59,042 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 18:59:59,042 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:59:59,042 - DEBUG - Finished Request
2025-06-30 19:00:00,043 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:00,052 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:00,052 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:00,052 - DEBUG - Finished Request
2025-06-30 19:00:01,053 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:01,062 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:01,063 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:01,063 - DEBUG - Finished Request
2025-06-30 19:00:02,064 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:02,073 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:02,073 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:02,074 - DEBUG - Finished Request
2025-06-30 19:00:03,074 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:03,083 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:03,083 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:03,084 - DEBUG - Finished Request
2025-06-30 19:00:04,085 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:04,094 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:04,094 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:04,094 - DEBUG - Finished Request
2025-06-30 19:00:05,095 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:05,104 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:05,105 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:05,105 - DEBUG - Finished Request
2025-06-30 19:00:06,105 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:06,113 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:06,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:06,113 - DEBUG - Finished Request
2025-06-30 19:00:07,115 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:07,122 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:07,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:07,123 - DEBUG - Finished Request
2025-06-30 19:00:08,125 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:08,133 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:08,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:08,134 - DEBUG - Finished Request
2025-06-30 19:00:09,135 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:09,143 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:09,143 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:09,143 - DEBUG - Finished Request
2025-06-30 19:00:10,145 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:10,154 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:10,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:10,154 - DEBUG - Finished Request
2025-06-30 19:00:11,155 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:11,164 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:11,165 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:11,165 - DEBUG - Finished Request
2025-06-30 19:00:12,166 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:12,175 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:12,175 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:12,175 - DEBUG - Finished Request
2025-06-30 19:00:13,176 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:13,184 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:13,184 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:13,184 - DEBUG - Finished Request
2025-06-30 19:00:14,186 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:14,196 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:14,197 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:14,197 - DEBUG - Finished Request
2025-06-30 19:00:15,198 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:15,206 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:15,207 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:15,207 - DEBUG - Finished Request
2025-06-30 19:00:16,208 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:16,217 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:16,217 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:16,218 - DEBUG - Finished Request
2025-06-30 19:00:17,219 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:17,226 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:17,227 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:17,227 - DEBUG - Finished Request
2025-06-30 19:00:18,229 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:18,237 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:18,238 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:18,238 - DEBUG - Finished Request
2025-06-30 19:00:19,238 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:19,247 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:19,247 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:19,247 - DEBUG - Finished Request
2025-06-30 19:00:20,248 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:20,255 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:20,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:20,255 - DEBUG - Finished Request
2025-06-30 19:00:21,256 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:21,264 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:21,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:21,264 - DEBUG - Finished Request
2025-06-30 19:00:22,266 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:22,273 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:22,274 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:22,274 - DEBUG - Finished Request
2025-06-30 19:00:23,274 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:23,281 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:23,282 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:23,282 - DEBUG - Finished Request
2025-06-30 19:00:24,284 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:24,291 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:24,292 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:24,292 - DEBUG - Finished Request
2025-06-30 19:00:25,292 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:25,300 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:25,301 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:25,301 - DEBUG - Finished Request
2025-06-30 19:00:26,302 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:26,310 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:26,310 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:26,310 - DEBUG - Finished Request
2025-06-30 19:00:27,312 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:27,320 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:27,321 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:27,321 - DEBUG - Finished Request
2025-06-30 19:00:28,322 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:28,330 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:28,331 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:28,331 - DEBUG - Finished Request
2025-06-30 19:00:29,332 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:29,341 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:29,341 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:29,341 - DEBUG - Finished Request
2025-06-30 19:00:30,343 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:30,351 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:30,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:30,352 - DEBUG - Finished Request
2025-06-30 19:00:31,352 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:31,359 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:31,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:31,360 - DEBUG - Finished Request
2025-06-30 19:00:32,361 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:32,370 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:32,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:32,370 - DEBUG - Finished Request
2025-06-30 19:00:33,371 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:33,378 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:33,379 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:33,379 - DEBUG - Finished Request
2025-06-30 19:00:34,380 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:34,387 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:34,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:34,388 - DEBUG - Finished Request
2025-06-30 19:00:35,389 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:35,396 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:35,397 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:35,397 - DEBUG - Finished Request
2025-06-30 19:00:36,397 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:36,405 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:36,405 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:36,406 - DEBUG - Finished Request
2025-06-30 19:00:37,407 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:37,414 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:37,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:37,415 - DEBUG - Finished Request
2025-06-30 19:00:38,417 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:38,425 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:38,425 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:38,425 - DEBUG - Finished Request
2025-06-30 19:00:39,427 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:39,436 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:39,436 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:39,436 - DEBUG - Finished Request
2025-06-30 19:00:40,437 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:40,446 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:40,446 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:40,447 - DEBUG - Finished Request
2025-06-30 19:00:41,448 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:41,455 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:41,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:41,456 - DEBUG - Finished Request
2025-06-30 19:00:42,457 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:42,464 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:42,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:42,465 - DEBUG - Finished Request
2025-06-30 19:00:43,466 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:43,473 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:43,473 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:43,473 - DEBUG - Finished Request
2025-06-30 19:00:44,475 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:44,484 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:44,484 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:44,484 - DEBUG - Finished Request
2025-06-30 19:00:45,485 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:45,495 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:45,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:45,496 - DEBUG - Finished Request
2025-06-30 19:00:46,497 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:46,504 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:46,505 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:46,505 - DEBUG - Finished Request
2025-06-30 19:00:47,506 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:47,513 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:47,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:47,514 - DEBUG - Finished Request
2025-06-30 19:00:48,514 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:48,523 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:48,523 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:48,523 - DEBUG - Finished Request
2025-06-30 19:00:49,524 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:49,536 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:49,536 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:49,537 - DEBUG - Finished Request
2025-06-30 19:00:50,538 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:50,546 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:50,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:50,547 - DEBUG - Finished Request
2025-06-30 19:00:51,547 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:51,555 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:51,556 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:51,556 - DEBUG - Finished Request
2025-06-30 19:00:52,557 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:52,565 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:52,566 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:52,566 - DEBUG - Finished Request
2025-06-30 19:00:53,566 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:53,573 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:53,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:53,574 - DEBUG - Finished Request
2025-06-30 19:00:54,575 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:54,583 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:54,583 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:54,584 - DEBUG - Finished Request
2025-06-30 19:00:55,585 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:55,592 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:55,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:55,593 - DEBUG - Finished Request
2025-06-30 19:00:56,594 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:56,601 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:56,602 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:56,602 - DEBUG - Finished Request
2025-06-30 19:00:57,603 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:57,610 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:57,610 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:57,611 - DEBUG - Finished Request
2025-06-30 19:00:58,612 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:58,620 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:58,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:58,621 - DEBUG - Finished Request
2025-06-30 19:00:59,622 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:00:59,629 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:00:59,630 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:00:59,630 - DEBUG - Finished Request
2025-06-30 19:01:00,630 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:00,638 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:00,638 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:00,639 - DEBUG - Finished Request
2025-06-30 19:01:01,640 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:01,649 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:01,649 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:01,649 - DEBUG - Finished Request
2025-06-30 19:01:02,650 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:02,658 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:02,658 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:02,658 - DEBUG - Finished Request
2025-06-30 19:01:03,659 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:03,666 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:03,666 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:03,667 - DEBUG - Finished Request
2025-06-30 19:01:04,668 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:04,675 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:04,676 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:04,676 - DEBUG - Finished Request
2025-06-30 19:01:05,677 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:05,684 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:05,685 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:05,685 - DEBUG - Finished Request
2025-06-30 19:01:06,686 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:06,694 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:06,694 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:06,694 - DEBUG - Finished Request
2025-06-30 19:01:07,695 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:07,703 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:07,703 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:07,703 - DEBUG - Finished Request
2025-06-30 19:01:08,705 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:08,713 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:08,714 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:08,714 - DEBUG - Finished Request
2025-06-30 19:01:09,715 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:09,723 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:09,723 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:09,723 - DEBUG - Finished Request
2025-06-30 19:01:10,725 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:10,732 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:10,732 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:10,732 - DEBUG - Finished Request
2025-06-30 19:01:11,733 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:11,742 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:11,742 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:11,742 - DEBUG - Finished Request
2025-06-30 19:01:12,743 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:12,750 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:12,750 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:12,750 - DEBUG - Finished Request
2025-06-30 19:01:13,751 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:13,760 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:13,760 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:13,761 - DEBUG - Finished Request
2025-06-30 19:01:14,761 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:14,772 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:14,772 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:14,773 - DEBUG - Finished Request
2025-06-30 19:01:15,774 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:15,782 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:15,783 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:15,783 - DEBUG - Finished Request
2025-06-30 19:01:16,784 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:16,791 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:16,792 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:16,792 - DEBUG - Finished Request
2025-06-30 19:01:17,793 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:17,800 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:17,800 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:17,800 - DEBUG - Finished Request
2025-06-30 19:01:18,801 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:18,808 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:18,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:18,808 - DEBUG - Finished Request
2025-06-30 19:01:19,809 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:19,816 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:19,816 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:19,816 - DEBUG - Finished Request
2025-06-30 19:01:20,817 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:20,824 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:20,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:20,825 - DEBUG - Finished Request
2025-06-30 19:01:21,826 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:21,834 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:21,834 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:21,835 - DEBUG - Finished Request
2025-06-30 19:01:22,835 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:22,843 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:22,843 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:22,843 - DEBUG - Finished Request
2025-06-30 19:01:23,844 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:23,851 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:23,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:23,852 - DEBUG - Finished Request
2025-06-30 19:01:24,853 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:24,860 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:24,860 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:24,860 - DEBUG - Finished Request
2025-06-30 19:01:25,861 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:25,868 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:25,868 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:25,868 - DEBUG - Finished Request
2025-06-30 19:01:26,869 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:26,876 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:26,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:26,877 - DEBUG - Finished Request
2025-06-30 19:01:27,878 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:27,886 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:27,886 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:27,886 - DEBUG - Finished Request
2025-06-30 19:01:28,887 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:28,895 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:28,895 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:28,895 - DEBUG - Finished Request
2025-06-30 19:01:29,897 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:29,905 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:29,906 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:29,906 - DEBUG - Finished Request
2025-06-30 19:01:30,907 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:30,915 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:30,916 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:30,916 - DEBUG - Finished Request
2025-06-30 19:01:31,917 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:31,925 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:31,925 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:31,925 - DEBUG - Finished Request
2025-06-30 19:01:32,926 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:32,935 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:32,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:32,935 - DEBUG - Finished Request
2025-06-30 19:01:33,937 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:33,945 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:33,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:33,946 - DEBUG - Finished Request
2025-06-30 19:01:34,947 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:34,955 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:34,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:34,955 - DEBUG - Finished Request
2025-06-30 19:01:35,957 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:35,965 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:35,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:35,966 - DEBUG - Finished Request
2025-06-30 19:01:36,966 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:36,975 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:36,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:36,976 - DEBUG - Finished Request
2025-06-30 19:01:37,977 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:37,984 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:37,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:37,985 - DEBUG - Finished Request
2025-06-30 19:01:38,986 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:38,994 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:38,995 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:38,995 - DEBUG - Finished Request
2025-06-30 19:01:39,996 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:40,003 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:40,003 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:40,003 - DEBUG - Finished Request
2025-06-30 19:01:41,005 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:41,013 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:41,013 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:41,013 - DEBUG - Finished Request
2025-06-30 19:01:42,015 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:42,024 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:42,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:42,025 - DEBUG - Finished Request
2025-06-30 19:01:43,025 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:43,035 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:43,035 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:43,035 - DEBUG - Finished Request
2025-06-30 19:01:44,036 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:44,044 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:44,044 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:44,044 - DEBUG - Finished Request
2025-06-30 19:01:45,046 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:45,055 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:45,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:45,056 - DEBUG - Finished Request
2025-06-30 19:01:46,056 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:46,065 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:46,066 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:46,066 - DEBUG - Finished Request
2025-06-30 19:01:47,068 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:47,077 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:47,077 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:47,077 - DEBUG - Finished Request
2025-06-30 19:01:48,078 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:48,085 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:48,085 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:48,086 - DEBUG - Finished Request
2025-06-30 19:01:49,087 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:49,095 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:49,095 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:49,095 - DEBUG - Finished Request
2025-06-30 19:01:50,097 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:50,106 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:50,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:50,106 - DEBUG - Finished Request
2025-06-30 19:01:51,107 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:51,117 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:51,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:51,117 - DEBUG - Finished Request
2025-06-30 19:01:52,118 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:52,125 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:52,125 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:52,125 - DEBUG - Finished Request
2025-06-30 19:01:53,126 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:53,134 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:53,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:53,134 - DEBUG - Finished Request
2025-06-30 19:01:54,135 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:54,143 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:54,143 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:54,143 - DEBUG - Finished Request
2025-06-30 19:01:55,144 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:55,153 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:55,153 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:55,154 - DEBUG - Finished Request
2025-06-30 19:01:56,154 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:56,163 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:56,163 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:56,163 - DEBUG - Finished Request
2025-06-30 19:01:57,164 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:57,172 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:57,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:57,172 - DEBUG - Finished Request
2025-06-30 19:01:58,174 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:58,182 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:58,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:58,182 - DEBUG - Finished Request
2025-06-30 19:01:59,183 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:01:59,192 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:01:59,192 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:01:59,192 - DEBUG - Finished Request
2025-06-30 19:02:00,194 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:00,202 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:00,203 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:00,203 - DEBUG - Finished Request
2025-06-30 19:02:01,203 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:01,212 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:01,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:01,213 - DEBUG - Finished Request
2025-06-30 19:02:02,214 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:02,222 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:02,223 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:02,223 - DEBUG - Finished Request
2025-06-30 19:02:03,223 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:03,230 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:03,231 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:03,231 - DEBUG - Finished Request
2025-06-30 19:02:04,233 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:04,242 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:04,243 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:04,243 - DEBUG - Finished Request
2025-06-30 19:02:05,243 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:05,250 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:05,251 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:05,251 - DEBUG - Finished Request
2025-06-30 19:02:06,251 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:06,264 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:06,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:06,265 - DEBUG - Finished Request
2025-06-30 19:02:07,265 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:07,274 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:07,274 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:07,275 - DEBUG - Finished Request
2025-06-30 19:02:08,276 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:08,284 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:08,284 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:08,284 - DEBUG - Finished Request
2025-06-30 19:02:09,285 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:09,292 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:09,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:09,293 - DEBUG - Finished Request
2025-06-30 19:02:10,294 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:10,302 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:10,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:10,303 - DEBUG - Finished Request
2025-06-30 19:02:11,304 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:11,311 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:11,312 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:11,312 - DEBUG - Finished Request
2025-06-30 19:02:12,314 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:12,322 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:12,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:12,322 - DEBUG - Finished Request
2025-06-30 19:02:13,323 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:13,332 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:13,333 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:13,333 - DEBUG - Finished Request
2025-06-30 19:02:14,334 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:14,342 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:14,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:14,342 - DEBUG - Finished Request
2025-06-30 19:02:15,343 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:15,350 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:15,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:15,351 - DEBUG - Finished Request
2025-06-30 19:02:16,352 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:16,361 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:16,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:16,362 - DEBUG - Finished Request
2025-06-30 19:02:17,363 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:17,371 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:17,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:17,371 - DEBUG - Finished Request
2025-06-30 19:02:18,372 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:18,380 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:18,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:18,380 - DEBUG - Finished Request
2025-06-30 19:02:19,381 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:19,388 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:19,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:19,389 - DEBUG - Finished Request
2025-06-30 19:02:20,389 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:20,398 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:20,398 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:20,398 - DEBUG - Finished Request
2025-06-30 19:02:21,398 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:21,406 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:21,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:21,407 - DEBUG - Finished Request
2025-06-30 19:02:22,407 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:22,415 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:22,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:22,416 - DEBUG - Finished Request
2025-06-30 19:02:23,417 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:23,425 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:23,426 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:23,426 - DEBUG - Finished Request
2025-06-30 19:02:24,427 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:24,434 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:24,435 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:24,435 - DEBUG - Finished Request
2025-06-30 19:02:25,436 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:25,444 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:25,445 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:25,445 - DEBUG - Finished Request
2025-06-30 19:02:26,446 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:26,455 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:26,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:26,455 - DEBUG - Finished Request
2025-06-30 19:02:27,456 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:27,464 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:27,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:27,465 - DEBUG - Finished Request
2025-06-30 19:02:28,466 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:28,474 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:28,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:28,475 - DEBUG - Finished Request
2025-06-30 19:02:29,476 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:29,484 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:29,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:29,485 - DEBUG - Finished Request
2025-06-30 19:02:30,486 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:30,495 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:30,495 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:30,495 - DEBUG - Finished Request
2025-06-30 19:02:31,496 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:31,505 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:31,505 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:31,505 - DEBUG - Finished Request
2025-06-30 19:02:32,506 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:32,514 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:32,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:32,515 - DEBUG - Finished Request
2025-06-30 19:02:33,516 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:33,524 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:33,524 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:33,524 - DEBUG - Finished Request
2025-06-30 19:02:34,525 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:34,533 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:34,533 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:34,534 - DEBUG - Finished Request
2025-06-30 19:02:35,535 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:35,544 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:35,544 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:35,544 - DEBUG - Finished Request
2025-06-30 19:02:36,546 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:36,557 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:36,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:36,558 - DEBUG - Finished Request
2025-06-30 19:02:37,559 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:37,565 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:37,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:37,566 - DEBUG - Finished Request
2025-06-30 19:02:38,566 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:38,575 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:38,575 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:38,576 - DEBUG - Finished Request
2025-06-30 19:02:39,576 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:39,585 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:39,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:39,586 - DEBUG - Finished Request
2025-06-30 19:02:40,587 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:40,595 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:40,595 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:40,596 - DEBUG - Finished Request
2025-06-30 19:02:41,597 - DEBUG - GET http://localhost:54132/session/2e4ff9d96372a59df96a754125586861/url {}
2025-06-30 19:02:41,605 - DEBUG - http://localhost:54132 "GET /session/2e4ff9d96372a59df96a754125586861/url HTTP/1.1" 200 0
2025-06-30 19:02:41,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:41,605 - DEBUG - Finished Request
2025-06-30 19:02:42,415 - DEBUG - DELETE http://localhost:54132/session/2e4ff9d96372a59df96a754125586861 {}
2025-06-30 19:02:42,467 - DEBUG - http://localhost:54132 "DELETE /session/2e4ff9d96372a59df96a754125586861 HTTP/1.1" 200 0
2025-06-30 19:02:42,467 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:02:42,467 - DEBUG - Finished Request
2025-06-30 19:02:42,622 - DEBUG - DELETE http://localhost:54132/session/2e4ff9d96372a59df96a754125586861 {}
2025-06-30 19:02:42,623 - DEBUG - Starting new HTTP connection (1): localhost:54132
