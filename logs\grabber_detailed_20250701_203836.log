2025-07-01 20:38:36,606 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_203836.log
2025-07-01 20:38:39,538 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 20:38:39,538 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 20:38:39,599 - DEBUG - chromedriver not found in PATH
2025-07-01 20:38:39,599 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 20:38:39,600 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 20:38:39,600 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 20:38:39,600 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 20:38:39,600 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 20:38:39,600 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 20:38:39,604 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 24524 using 0 to output -3
2025-07-01 20:38:40,117 - DEBUG - POST http://localhost:60044/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 20:38:40,117 - DEBUG - Starting new HTTP connection (1): localhost:60044
2025-07-01 20:38:40,653 - DEBUG - http://localhost:60044 "POST /session HTTP/1.1" 200 0
2025-07-01 20:38:40,654 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir24524_647175950"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:60047"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"94fed91a1b5a960562cf1dda7822d9bb"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:40,654 - DEBUG - Finished Request
2025-07-01 20:38:40,655 - DEBUG - POST http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 20:38:42,630 - DEBUG - http://localhost:60044 "POST /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:42,630 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:42,630 - DEBUG - Finished Request
2025-07-01 20:38:42,630 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 20:38:42,631 - DEBUG - POST http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 20:38:42,637 - DEBUG - http://localhost:60044 "POST /session/94fed91a1b5a960562cf1dda7822d9bb/execute/sync HTTP/1.1" 200 0
2025-07-01 20:38:42,637 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:42,637 - DEBUG - Finished Request
2025-07-01 20:38:42,638 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 20:38:42,638 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:42,672 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:42,672 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:42,672 - DEBUG - Finished Request
2025-07-01 20:38:43,673 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:43,683 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:43,683 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:43,683 - DEBUG - Finished Request
2025-07-01 20:38:44,683 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:44,691 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:44,691 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:44,691 - DEBUG - Finished Request
2025-07-01 20:38:45,693 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:45,699 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:45,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:45,699 - DEBUG - Finished Request
2025-07-01 20:38:46,700 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:46,706 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:46,706 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:46,707 - DEBUG - Finished Request
2025-07-01 20:38:47,708 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:47,726 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:47,726 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:47,726 - DEBUG - Finished Request
2025-07-01 20:38:48,727 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:48,735 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:48,735 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:48,735 - DEBUG - Finished Request
2025-07-01 20:38:49,737 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:49,745 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:49,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:49,746 - DEBUG - Finished Request
2025-07-01 20:38:50,747 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:50,753 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:50,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:50,755 - DEBUG - Finished Request
2025-07-01 20:38:51,756 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:51,765 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:51,766 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:51,766 - DEBUG - Finished Request
2025-07-01 20:38:52,767 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:52,774 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:52,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:52,775 - DEBUG - Finished Request
2025-07-01 20:38:53,776 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:53,785 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:53,785 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:53,785 - DEBUG - Finished Request
2025-07-01 20:38:54,785 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:54,792 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:54,792 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:54,793 - DEBUG - Finished Request
2025-07-01 20:38:55,794 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:55,800 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:55,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:55,801 - DEBUG - Finished Request
2025-07-01 20:38:56,802 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:56,808 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:56,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:56,808 - DEBUG - Finished Request
2025-07-01 20:38:57,809 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:57,816 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:57,817 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:57,817 - DEBUG - Finished Request
2025-07-01 20:38:58,818 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:58,825 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:58,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:58,826 - DEBUG - Finished Request
2025-07-01 20:38:59,826 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:38:59,833 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:38:59,833 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:38:59,833 - DEBUG - Finished Request
2025-07-01 20:39:00,834 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:00,842 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:00,842 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:00,842 - DEBUG - Finished Request
2025-07-01 20:39:01,843 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:01,849 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:01,849 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:01,850 - DEBUG - Finished Request
2025-07-01 20:39:02,850 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:02,857 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:02,858 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:02,858 - DEBUG - Finished Request
2025-07-01 20:39:03,860 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:03,867 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:03,867 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:03,868 - DEBUG - Finished Request
2025-07-01 20:39:04,869 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:04,877 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:04,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:04,877 - DEBUG - Finished Request
2025-07-01 20:39:05,878 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:05,884 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:05,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:05,885 - DEBUG - Finished Request
2025-07-01 20:39:06,886 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:06,892 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:06,892 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:06,892 - DEBUG - Finished Request
2025-07-01 20:39:07,893 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:07,900 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:07,900 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:07,900 - DEBUG - Finished Request
2025-07-01 20:39:08,901 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:08,906 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:08,906 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:08,907 - DEBUG - Finished Request
2025-07-01 20:39:09,908 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:09,915 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:09,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:09,915 - DEBUG - Finished Request
2025-07-01 20:39:10,916 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:10,922 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:10,922 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:10,922 - DEBUG - Finished Request
2025-07-01 20:39:11,923 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:11,930 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:11,930 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:11,931 - DEBUG - Finished Request
2025-07-01 20:39:12,932 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:12,938 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:12,938 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:12,938 - DEBUG - Finished Request
2025-07-01 20:39:13,939 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:13,945 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:13,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:13,946 - DEBUG - Finished Request
2025-07-01 20:39:14,947 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:14,956 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:14,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:14,956 - DEBUG - Finished Request
2025-07-01 20:39:15,957 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:15,965 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:15,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:15,965 - DEBUG - Finished Request
2025-07-01 20:39:16,966 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:16,974 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:16,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:16,974 - DEBUG - Finished Request
2025-07-01 20:39:17,975 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:18,623 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:18,623 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:18,623 - DEBUG - Finished Request
2025-07-01 20:39:19,624 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:19,631 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:19,631 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:19,631 - DEBUG - Finished Request
2025-07-01 20:39:20,632 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:20,637 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:20,638 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:20,638 - DEBUG - Finished Request
2025-07-01 20:39:21,639 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:21,644 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:21,645 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:21,645 - DEBUG - Finished Request
2025-07-01 20:39:22,646 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:22,652 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:22,652 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:22,652 - DEBUG - Finished Request
2025-07-01 20:39:23,653 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:23,661 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:23,661 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:23,662 - DEBUG - Finished Request
2025-07-01 20:39:24,662 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:24,668 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:24,668 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:24,668 - DEBUG - Finished Request
2025-07-01 20:39:25,668 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:25,786 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:25,789 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:25,796 - DEBUG - Finished Request
2025-07-01 20:39:26,799 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:26,806 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:26,806 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:26,806 - DEBUG - Finished Request
2025-07-01 20:39:27,808 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:27,814 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:27,814 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:27,814 - DEBUG - Finished Request
2025-07-01 20:39:28,816 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:28,821 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:28,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:28,822 - DEBUG - Finished Request
2025-07-01 20:39:29,822 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:29,829 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:29,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:29,829 - DEBUG - Finished Request
2025-07-01 20:39:30,830 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:30,836 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:30,837 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:30,837 - DEBUG - Finished Request
2025-07-01 20:39:31,839 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:31,844 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:31,844 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:31,845 - DEBUG - Finished Request
2025-07-01 20:39:32,846 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:32,927 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:32,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:32,927 - DEBUG - Finished Request
2025-07-01 20:39:33,928 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:33,943 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:33,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:33,943 - DEBUG - Finished Request
2025-07-01 20:39:34,944 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:34,950 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:34,950 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:34,951 - DEBUG - Finished Request
2025-07-01 20:39:35,951 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:35,959 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:35,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:35,959 - DEBUG - Finished Request
2025-07-01 20:39:36,960 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:36,966 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:36,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:36,966 - DEBUG - Finished Request
2025-07-01 20:39:37,968 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:37,975 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:37,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:37,975 - DEBUG - Finished Request
2025-07-01 20:39:38,976 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:38,982 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:38,982 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:38,982 - DEBUG - Finished Request
2025-07-01 20:39:39,984 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:39,991 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:39,991 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:39,991 - DEBUG - Finished Request
2025-07-01 20:39:40,992 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:40,999 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:40,999 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:40,999 - DEBUG - Finished Request
2025-07-01 20:39:42,001 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:42,006 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:42,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:42,007 - DEBUG - Finished Request
2025-07-01 20:39:43,007 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:43,014 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:43,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:43,015 - DEBUG - Finished Request
2025-07-01 20:39:44,015 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:44,022 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:44,022 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:44,022 - DEBUG - Finished Request
2025-07-01 20:39:45,023 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:45,030 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:45,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:45,031 - DEBUG - Finished Request
2025-07-01 20:39:46,033 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:46,039 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:46,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:46,040 - DEBUG - Finished Request
2025-07-01 20:39:47,041 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:47,047 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:47,047 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:47,048 - DEBUG - Finished Request
2025-07-01 20:39:48,049 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:48,056 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:48,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:48,056 - DEBUG - Finished Request
2025-07-01 20:39:49,057 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:49,064 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:49,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:49,064 - DEBUG - Finished Request
2025-07-01 20:39:50,066 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:50,075 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:50,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:50,075 - DEBUG - Finished Request
2025-07-01 20:39:51,076 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:51,081 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:51,081 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:51,081 - DEBUG - Finished Request
2025-07-01 20:39:52,083 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:52,091 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:52,091 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:52,092 - DEBUG - Finished Request
2025-07-01 20:39:53,092 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:53,098 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:53,098 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:53,098 - DEBUG - Finished Request
2025-07-01 20:39:54,099 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:54,104 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:54,105 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:54,105 - DEBUG - Finished Request
2025-07-01 20:39:55,106 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:55,113 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:55,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:55,113 - DEBUG - Finished Request
2025-07-01 20:39:56,114 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:56,118 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:56,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:56,120 - DEBUG - Finished Request
2025-07-01 20:39:57,120 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:57,130 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:57,131 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:57,131 - DEBUG - Finished Request
2025-07-01 20:39:58,132 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:58,139 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:58,140 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:58,140 - DEBUG - Finished Request
2025-07-01 20:39:59,141 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:39:59,148 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:39:59,149 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:39:59,149 - DEBUG - Finished Request
2025-07-01 20:40:00,149 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:00,156 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:00,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:00,156 - DEBUG - Finished Request
2025-07-01 20:40:01,156 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:01,161 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:01,161 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:01,162 - DEBUG - Finished Request
2025-07-01 20:40:02,163 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:02,172 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:02,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:02,172 - DEBUG - Finished Request
2025-07-01 20:40:03,173 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:03,179 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:03,180 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:03,180 - DEBUG - Finished Request
2025-07-01 20:40:04,181 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:04,189 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:04,189 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:04,189 - DEBUG - Finished Request
2025-07-01 20:40:05,191 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:05,198 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:05,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:05,199 - DEBUG - Finished Request
2025-07-01 20:40:06,199 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:06,208 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:06,208 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:06,208 - DEBUG - Finished Request
2025-07-01 20:40:07,209 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:07,215 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:07,215 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:07,215 - DEBUG - Finished Request
2025-07-01 20:40:08,216 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:08,231 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:08,231 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:08,232 - DEBUG - Finished Request
2025-07-01 20:40:09,232 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:09,239 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:09,239 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:09,239 - DEBUG - Finished Request
2025-07-01 20:40:10,240 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:10,249 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:10,249 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:10,249 - DEBUG - Finished Request
2025-07-01 20:40:11,250 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:11,255 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:11,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:11,255 - DEBUG - Finished Request
2025-07-01 20:40:12,256 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:12,264 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:12,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:12,265 - DEBUG - Finished Request
2025-07-01 20:40:13,266 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:13,275 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:13,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:13,276 - DEBUG - Finished Request
2025-07-01 20:40:14,278 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:14,287 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:14,288 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:14,288 - DEBUG - Finished Request
2025-07-01 20:40:15,289 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:15,298 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:15,298 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:15,298 - DEBUG - Finished Request
2025-07-01 20:40:16,299 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:16,306 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:16,307 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:16,307 - DEBUG - Finished Request
2025-07-01 20:40:17,308 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:17,317 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:17,318 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:17,318 - DEBUG - Finished Request
2025-07-01 20:40:18,319 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:18,328 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:18,328 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:18,328 - DEBUG - Finished Request
2025-07-01 20:40:19,329 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:19,339 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:19,340 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:19,340 - DEBUG - Finished Request
2025-07-01 20:40:20,342 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:20,349 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:20,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:20,350 - DEBUG - Finished Request
2025-07-01 20:40:21,351 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:21,359 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:21,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:21,360 - DEBUG - Finished Request
2025-07-01 20:40:22,361 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:22,368 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:22,368 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:22,369 - DEBUG - Finished Request
2025-07-01 20:40:23,370 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:23,377 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:23,377 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:23,378 - DEBUG - Finished Request
2025-07-01 20:40:24,379 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:24,388 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 200 0
2025-07-01 20:40:24,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:24,389 - DEBUG - Finished Request
2025-07-01 20:40:25,389 - DEBUG - GET http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb/url {}
2025-07-01 20:40:25,391 - DEBUG - http://localhost:60044 "GET /session/94fed91a1b5a960562cf1dda7822d9bb/url HTTP/1.1" 404 0
2025-07-01 20:40:25,391 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:25,392 - DEBUG - Finished Request
2025-07-01 20:40:25,393 - DEBUG - DELETE http://localhost:60044/session/94fed91a1b5a960562cf1dda7822d9bb {}
2025-07-01 20:40:25,470 - DEBUG - http://localhost:60044 "DELETE /session/94fed91a1b5a960562cf1dda7822d9bb HTTP/1.1" 200 0
2025-07-01 20:40:25,471 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:40:25,471 - DEBUG - Finished Request
