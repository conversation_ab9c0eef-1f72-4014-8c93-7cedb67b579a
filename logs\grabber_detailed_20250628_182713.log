2025-06-28 18:27:13,486 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250628_182713.log
2025-06-28 18:27:13,487 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-28 18:27:13,487 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-28 18:27:13,543 - DEBUG - chromedriver not found in PATH
2025-06-28 18:27:13,544 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 18:27:13,544 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-28 18:27:13,544 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-28 18:27:13,544 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-28 18:27:13,544 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-28 18:27:13,544 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 18:27:13,547 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 29048 using 0 to output -3
2025-06-28 18:27:14,069 - DEBUG - POST http://localhost:51646/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-28 18:27:14,070 - DEBUG - Starting new HTTP connection (1): localhost:51646
2025-06-28 18:27:14,615 - DEBUG - http://localhost:51646 "POST /session HTTP/1.1" 200 0
2025-06-28 18:27:14,615 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir29048_1605892560"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51649"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"65dcde82692ba647bf604c51aa0b8bcd"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:14,616 - DEBUG - Finished Request
2025-06-28 18:27:14,616 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-28 18:27:16,472 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:16,472 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:16,473 - DEBUG - Finished Request
2025-06-28 18:27:16,473 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:27:16,473 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:27:16,479 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:27:16,480 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:16,480 - DEBUG - Finished Request
2025-06-28 18:27:16,480 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:27:16,480 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:16,487 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:16,487 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:16,487 - DEBUG - Finished Request
2025-06-28 18:27:17,488 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:17,495 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:17,495 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:17,496 - DEBUG - Finished Request
2025-06-28 18:27:18,497 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:18,505 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:18,505 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:18,505 - DEBUG - Finished Request
2025-06-28 18:27:19,506 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:19,512 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:19,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:19,513 - DEBUG - Finished Request
2025-06-28 18:27:20,514 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:20,520 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:20,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:20,521 - DEBUG - Finished Request
2025-06-28 18:27:21,522 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:21,530 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:21,530 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:21,530 - DEBUG - Finished Request
2025-06-28 18:27:22,532 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:22,539 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:22,539 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:22,540 - DEBUG - Finished Request
2025-06-28 18:27:23,541 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:23,550 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:23,550 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:23,550 - DEBUG - Finished Request
2025-06-28 18:27:24,552 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:24,560 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:24,561 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:24,561 - DEBUG - Finished Request
2025-06-28 18:27:25,562 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:25,570 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:25,570 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:25,571 - DEBUG - Finished Request
2025-06-28 18:27:26,572 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:26,580 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:26,580 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:26,581 - DEBUG - Finished Request
2025-06-28 18:27:27,582 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:27,588 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:27,589 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:27,589 - DEBUG - Finished Request
2025-06-28 18:27:28,589 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:28,599 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:28,599 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:28,599 - DEBUG - Finished Request
2025-06-28 18:27:29,600 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:29,607 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:29,607 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:29,607 - DEBUG - Finished Request
2025-06-28 18:27:30,609 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:30,617 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:30,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:30,618 - DEBUG - Finished Request
2025-06-28 18:27:31,619 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:31,625 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:31,626 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:31,626 - DEBUG - Finished Request
2025-06-28 18:27:32,627 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:32,637 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:32,638 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:32,638 - DEBUG - Finished Request
2025-06-28 18:27:33,639 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:33,646 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:33,647 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:33,647 - DEBUG - Finished Request
2025-06-28 18:27:34,648 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:34,656 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:34,657 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:34,657 - DEBUG - Finished Request
2025-06-28 18:27:35,658 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:35,666 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:35,666 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:35,667 - DEBUG - Finished Request
2025-06-28 18:27:36,669 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:36,678 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:36,678 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:36,679 - DEBUG - Finished Request
2025-06-28 18:27:37,680 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:37,691 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:37,691 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:37,691 - DEBUG - Finished Request
2025-06-28 18:27:38,693 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:38,699 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:38,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:38,700 - DEBUG - Finished Request
2025-06-28 18:27:39,701 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:39,712 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:39,713 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:39,713 - DEBUG - Finished Request
2025-06-28 18:27:40,714 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:40,722 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:40,723 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:40,723 - DEBUG - Finished Request
2025-06-28 18:27:41,724 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:41,732 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:41,732 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:41,733 - DEBUG - Finished Request
2025-06-28 18:27:42,733 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:42,742 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:42,742 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:42,742 - DEBUG - Finished Request
2025-06-28 18:27:43,743 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:43,752 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:43,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:43,753 - DEBUG - Finished Request
2025-06-28 18:27:44,754 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:44,761 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:44,762 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:44,762 - DEBUG - Finished Request
2025-06-28 18:27:45,763 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:45,771 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:45,771 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:45,771 - DEBUG - Finished Request
2025-06-28 18:27:46,772 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:46,781 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:46,781 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:46,782 - DEBUG - Finished Request
2025-06-28 18:27:47,783 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:47,791 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:47,791 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:47,792 - DEBUG - Finished Request
2025-06-28 18:27:48,794 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:48,802 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:48,802 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:48,803 - DEBUG - Finished Request
2025-06-28 18:27:49,805 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:49,812 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:49,812 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:49,813 - DEBUG - Finished Request
2025-06-28 18:27:50,813 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:50,821 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:50,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:50,822 - DEBUG - Finished Request
2025-06-28 18:27:51,823 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:51,830 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:51,830 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:51,830 - DEBUG - Finished Request
2025-06-28 18:27:52,832 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:52,839 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:52,840 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:52,840 - DEBUG - Finished Request
2025-06-28 18:27:53,841 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:53,849 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:53,850 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:53,850 - DEBUG - Finished Request
2025-06-28 18:27:54,850 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:54,859 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:54,860 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:54,860 - DEBUG - Finished Request
2025-06-28 18:27:55,861 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:55,866 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:55,867 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:55,867 - DEBUG - Finished Request
2025-06-28 18:27:56,868 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:56,874 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:56,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:56,875 - DEBUG - Finished Request
2025-06-28 18:27:57,876 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:57,883 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:57,883 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:57,883 - DEBUG - Finished Request
2025-06-28 18:27:58,884 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:58,907 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:58,908 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:58,908 - DEBUG - Finished Request
2025-06-28 18:27:59,908 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:27:59,917 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:27:59,917 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:27:59,918 - DEBUG - Finished Request
2025-06-28 18:28:00,919 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:00,926 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:00,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:00,927 - DEBUG - Finished Request
2025-06-28 18:28:01,928 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:01,936 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:01,937 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:01,937 - DEBUG - Finished Request
2025-06-28 18:28:02,938 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:02,946 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:02,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:02,946 - DEBUG - Finished Request
2025-06-28 18:28:03,947 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:03,955 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:03,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:03,955 - DEBUG - Finished Request
2025-06-28 18:28:04,956 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:04,965 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:04,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:04,966 - DEBUG - Finished Request
2025-06-28 18:28:05,967 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:05,976 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:05,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:05,977 - DEBUG - Finished Request
2025-06-28 18:28:06,978 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:06,985 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:06,986 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:06,986 - DEBUG - Finished Request
2025-06-28 18:28:07,987 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:07,995 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:07,995 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:07,995 - DEBUG - Finished Request
2025-06-28 18:28:08,997 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:09,006 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:09,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:09,007 - DEBUG - Finished Request
2025-06-28 18:28:10,008 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:10,017 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:10,018 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:10,018 - DEBUG - Finished Request
2025-06-28 18:28:11,019 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:11,026 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:11,027 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:11,027 - DEBUG - Finished Request
2025-06-28 18:28:12,028 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:12,035 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:12,036 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:12,036 - DEBUG - Finished Request
2025-06-28 18:28:13,037 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:13,046 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:13,046 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:13,046 - DEBUG - Finished Request
2025-06-28 18:28:14,048 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:14,055 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:14,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:14,056 - DEBUG - Finished Request
2025-06-28 18:28:15,058 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:15,066 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:15,066 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:15,066 - DEBUG - Finished Request
2025-06-28 18:28:16,068 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:16,075 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:16,076 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:16,076 - DEBUG - Finished Request
2025-06-28 18:28:17,076 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:17,084 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:17,084 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:17,085 - DEBUG - Finished Request
2025-06-28 18:28:18,085 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:18,092 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:18,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:18,093 - DEBUG - Finished Request
2025-06-28 18:28:19,094 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:19,105 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:19,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:19,106 - DEBUG - Finished Request
2025-06-28 18:28:20,106 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:20,116 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:20,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:20,117 - DEBUG - Finished Request
2025-06-28 18:28:21,119 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:21,129 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:21,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:21,129 - DEBUG - Finished Request
2025-06-28 18:28:22,130 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:22,140 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:22,140 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:22,141 - DEBUG - Finished Request
2025-06-28 18:28:23,141 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:23,150 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:23,150 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:23,150 - DEBUG - Finished Request
2025-06-28 18:28:24,151 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:24,160 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:24,160 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:24,160 - DEBUG - Finished Request
2025-06-28 18:28:25,161 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:25,171 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:25,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:25,172 - DEBUG - Finished Request
2025-06-28 18:28:26,173 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:26,181 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:26,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:26,181 - DEBUG - Finished Request
2025-06-28 18:28:27,182 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:27,189 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:27,189 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:27,190 - DEBUG - Finished Request
2025-06-28 18:28:28,191 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:28,200 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:28,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:28,201 - DEBUG - Finished Request
2025-06-28 18:28:29,201 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:29,210 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:29,211 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:29,211 - DEBUG - Finished Request
2025-06-28 18:28:30,212 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:30,223 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:30,223 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:30,224 - DEBUG - Finished Request
2025-06-28 18:28:31,224 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:31,234 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:31,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:31,235 - DEBUG - Finished Request
2025-06-28 18:28:32,236 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:32,245 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:32,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:32,246 - DEBUG - Finished Request
2025-06-28 18:28:33,246 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:33,255 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:33,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:33,255 - DEBUG - Finished Request
2025-06-28 18:28:34,256 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:34,266 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:34,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:34,267 - DEBUG - Finished Request
2025-06-28 18:28:35,268 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:35,276 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:35,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:35,277 - DEBUG - Finished Request
2025-06-28 18:28:36,277 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:36,287 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:36,287 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:36,288 - DEBUG - Finished Request
2025-06-28 18:28:37,289 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:37,300 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:37,300 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:37,301 - DEBUG - Finished Request
2025-06-28 18:28:38,302 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:38,311 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:38,311 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:38,311 - DEBUG - Finished Request
2025-06-28 18:28:39,312 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:39,321 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:39,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:39,322 - DEBUG - Finished Request
2025-06-28 18:28:40,323 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:40,331 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:40,331 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:40,332 - DEBUG - Finished Request
2025-06-28 18:28:41,332 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:41,342 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:41,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:41,343 - DEBUG - Finished Request
2025-06-28 18:28:42,344 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:42,353 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:42,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:42,353 - DEBUG - Finished Request
2025-06-28 18:28:43,355 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:43,364 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:43,364 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:43,364 - DEBUG - Finished Request
2025-06-28 18:28:44,365 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:44,374 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:44,374 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:44,375 - DEBUG - Finished Request
2025-06-28 18:28:45,376 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:45,385 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:45,386 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:45,386 - DEBUG - Finished Request
2025-06-28 18:28:46,387 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:46,395 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:46,396 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:46,396 - DEBUG - Finished Request
2025-06-28 18:28:47,397 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:47,405 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:47,405 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:47,405 - DEBUG - Finished Request
2025-06-28 18:28:48,406 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:48,413 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:48,413 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:48,414 - DEBUG - Finished Request
2025-06-28 18:28:48,711 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:28:48,712 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:28:48,720 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:48,721 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:48,721 - DEBUG - Finished Request
2025-06-28 18:28:48,721 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:28:48,741 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:48,748 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:48,749 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:48,749 - DEBUG - Finished Request
2025-06-28 18:28:49,256 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:49,263 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:49,263 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:49,263 - DEBUG - Finished Request
2025-06-28 18:28:49,415 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:49,422 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:49,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:49,422 - DEBUG - Finished Request
2025-06-28 18:28:49,770 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:49,777 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:49,777 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:49,777 - DEBUG - Finished Request
2025-06-28 18:28:50,281 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:50,288 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:50,288 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:50,288 - DEBUG - Finished Request
2025-06-28 18:28:50,423 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:50,430 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:50,430 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:50,430 - DEBUG - Finished Request
2025-06-28 18:28:50,794 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:50,801 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:50,801 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:50,801 - DEBUG - Finished Request
2025-06-28 18:28:51,311 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:51,318 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:51,318 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:51,318 - DEBUG - Finished Request
2025-06-28 18:28:51,431 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:51,438 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:51,438 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:51,439 - DEBUG - Finished Request
2025-06-28 18:28:51,828 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:51,837 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:51,838 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:51,838 - DEBUG - Finished Request
2025-06-28 18:28:52,347 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:52,355 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:52,355 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:52,356 - DEBUG - Finished Request
2025-06-28 18:28:52,440 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:52,448 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:52,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:52,448 - DEBUG - Finished Request
2025-06-28 18:28:52,866 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:52,874 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:52,874 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:52,875 - DEBUG - Finished Request
2025-06-28 18:28:53,383 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:53,391 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:53,391 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:53,391 - DEBUG - Finished Request
2025-06-28 18:28:53,449 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:53,457 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:53,457 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:53,457 - DEBUG - Finished Request
2025-06-28 18:28:53,898 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:53,906 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:53,906 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:53,906 - DEBUG - Finished Request
2025-06-28 18:28:54,413 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:54,421 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:54,421 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:54,422 - DEBUG - Finished Request
2025-06-28 18:28:54,459 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:54,466 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:54,466 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:54,466 - DEBUG - Finished Request
2025-06-28 18:28:54,930 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:54,938 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:54,938 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:54,939 - DEBUG - Finished Request
2025-06-28 18:28:55,447 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:55,454 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:55,455 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:55,455 - DEBUG - Finished Request
2025-06-28 18:28:55,467 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:55,474 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:55,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:55,474 - DEBUG - Finished Request
2025-06-28 18:28:55,961 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:55,970 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:55,970 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:55,971 - DEBUG - Finished Request
2025-06-28 18:28:56,475 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:56,477 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:56,478 - DEBUG - Starting new HTTP connection (2): localhost:51646
2025-06-28 18:28:56,483 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:56,483 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:56,484 - DEBUG - Finished Request
2025-06-28 18:28:56,487 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:56,487 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-28 18:28:56,488 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:56,488 - DEBUG - Finished Request
2025-06-28 18:28:56,994 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:57,005 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:57,006 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:57,007 - DEBUG - Finished Request
2025-06-28 18:28:57,486 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:57,494 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:57,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:57,495 - DEBUG - Finished Request
2025-06-28 18:28:57,522 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:57,528 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:57,528 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:57,528 - DEBUG - Finished Request
2025-06-28 18:28:58,030 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:58,036 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:58,037 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:58,037 - DEBUG - Finished Request
2025-06-28 18:28:58,496 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:58,502 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:58,502 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:58,503 - DEBUG - Finished Request
2025-06-28 18:28:58,541 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:58,555 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:58,556 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:58,556 - DEBUG - Finished Request
2025-06-28 18:28:59,071 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:59,079 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:59,079 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:59,080 - DEBUG - Finished Request
2025-06-28 18:28:59,504 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:28:59,511 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:28:59,511 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:59,511 - DEBUG - Finished Request
2025-06-28 18:28:59,583 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:28:59,591 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:28:59,591 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:28:59,592 - DEBUG - Finished Request
2025-06-28 18:29:00,100 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:00,109 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:00,109 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:00,110 - DEBUG - Finished Request
2025-06-28 18:29:00,512 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:00,519 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:00,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:00,520 - DEBUG - Finished Request
2025-06-28 18:29:00,615 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:00,622 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:00,622 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:00,623 - DEBUG - Finished Request
2025-06-28 18:29:01,130 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:01,140 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:01,141 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:01,142 - DEBUG - Finished Request
2025-06-28 18:29:01,520 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:01,526 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:01,527 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:01,527 - DEBUG - Finished Request
2025-06-28 18:29:01,654 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:01,660 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:01,661 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:01,661 - DEBUG - Finished Request
2025-06-28 18:29:02,169 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:02,176 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:02,177 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:02,177 - DEBUG - Finished Request
2025-06-28 18:29:02,529 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:02,535 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:02,535 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:02,535 - DEBUG - Finished Request
2025-06-28 18:29:02,681 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:02,687 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:02,687 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:02,688 - DEBUG - Finished Request
2025-06-28 18:29:03,196 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:03,203 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:03,203 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:03,204 - DEBUG - Finished Request
2025-06-28 18:29:03,536 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:03,544 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:03,545 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:03,545 - DEBUG - Finished Request
2025-06-28 18:29:03,713 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:03,721 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:03,721 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:03,722 - DEBUG - Finished Request
2025-06-28 18:29:04,231 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:04,238 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:04,239 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:04,239 - DEBUG - Finished Request
2025-06-28 18:29:04,546 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:04,553 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:04,554 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:04,554 - DEBUG - Finished Request
2025-06-28 18:29:04,743 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:04,751 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:04,751 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:04,752 - DEBUG - Finished Request
2025-06-28 18:29:05,260 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:05,268 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:05,269 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:05,269 - DEBUG - Finished Request
2025-06-28 18:29:05,555 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:05,561 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:05,561 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:05,561 - DEBUG - Finished Request
2025-06-28 18:29:05,775 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:05,783 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:05,783 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:05,783 - DEBUG - Finished Request
2025-06-28 18:29:06,292 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:06,299 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:06,300 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:06,300 - DEBUG - Finished Request
2025-06-28 18:29:06,562 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:06,571 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:06,572 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:06,572 - DEBUG - Finished Request
2025-06-28 18:29:06,807 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:06,816 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:06,816 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:06,817 - DEBUG - Finished Request
2025-06-28 18:29:07,324 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:07,332 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:07,332 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:07,332 - DEBUG - Finished Request
2025-06-28 18:29:07,574 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:07,583 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:07,583 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:07,583 - DEBUG - Finished Request
2025-06-28 18:29:07,843 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:07,850 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:07,851 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:07,851 - DEBUG - Finished Request
2025-06-28 18:29:08,359 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:08,368 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:08,368 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:08,369 - DEBUG - Finished Request
2025-06-28 18:29:08,584 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:08,591 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:08,592 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:08,593 - DEBUG - Finished Request
2025-06-28 18:29:08,873 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:08,880 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:08,881 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:08,881 - DEBUG - Finished Request
2025-06-28 18:29:09,391 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:09,400 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:09,400 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:09,401 - DEBUG - Finished Request
2025-06-28 18:29:09,594 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:09,603 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:09,604 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:09,604 - DEBUG - Finished Request
2025-06-28 18:29:09,907 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:09,916 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:09,916 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:09,917 - DEBUG - Finished Request
2025-06-28 18:29:10,427 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:10,434 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:10,434 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:10,435 - DEBUG - Finished Request
2025-06-28 18:29:10,605 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:10,614 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:10,614 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:10,614 - DEBUG - Finished Request
2025-06-28 18:29:10,939 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:10,948 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:10,948 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:10,949 - DEBUG - Finished Request
2025-06-28 18:29:11,456 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:11,463 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:11,464 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:11,464 - DEBUG - Finished Request
2025-06-28 18:29:11,616 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:11,624 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:11,624 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:11,624 - DEBUG - Finished Request
2025-06-28 18:29:11,971 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:11,977 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:11,978 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:11,978 - DEBUG - Finished Request
2025-06-28 18:29:12,488 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:12,498 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:12,498 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:12,499 - DEBUG - Finished Request
2025-06-28 18:29:12,625 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:12,634 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:12,634 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:12,635 - DEBUG - Finished Request
2025-06-28 18:29:13,007 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:13,013 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:13,014 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:13,014 - DEBUG - Finished Request
2025-06-28 18:29:13,521 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:13,537 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:13,538 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:13,538 - DEBUG - Finished Request
2025-06-28 18:29:13,635 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:13,643 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:13,644 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:13,644 - DEBUG - Finished Request
2025-06-28 18:29:14,052 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:14,059 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:14,059 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:14,059 - DEBUG - Finished Request
2025-06-28 18:29:14,572 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:14,579 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:14,579 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:14,580 - DEBUG - Finished Request
2025-06-28 18:29:14,646 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:14,654 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:14,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:14,654 - DEBUG - Finished Request
2025-06-28 18:29:15,086 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:15,094 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:15,094 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:15,095 - DEBUG - Finished Request
2025-06-28 18:29:15,604 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:15,612 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:15,612 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:15,612 - DEBUG - Finished Request
2025-06-28 18:29:15,655 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:15,662 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:15,663 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:15,663 - DEBUG - Finished Request
2025-06-28 18:29:16,122 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:16,129 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:16,129 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:16,129 - DEBUG - Finished Request
2025-06-28 18:29:16,638 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:16,649 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:16,649 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:16,650 - DEBUG - Finished Request
2025-06-28 18:29:16,663 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:16,671 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:16,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:16,672 - DEBUG - Finished Request
2025-06-28 18:29:17,158 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:17,167 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:17,167 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:17,168 - DEBUG - Finished Request
2025-06-28 18:29:17,674 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:17,678 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:17,679 - DEBUG - Starting new HTTP connection (3): localhost:51646
2025-06-28 18:29:17,684 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:17,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:17,685 - DEBUG - Finished Request
2025-06-28 18:29:17,687 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:17,687 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-28 18:29:17,688 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:17,688 - DEBUG - Finished Request
2025-06-28 18:29:18,198 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:18,207 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:18,207 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:18,207 - DEBUG - Finished Request
2025-06-28 18:29:18,686 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:18,693 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:18,693 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:18,693 - DEBUG - Finished Request
2025-06-28 18:29:18,716 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:18,722 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:18,723 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:18,723 - DEBUG - Finished Request
2025-06-28 18:29:19,229 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:19,237 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:19,237 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:19,238 - DEBUG - Finished Request
2025-06-28 18:29:19,695 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:19,702 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:19,702 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:19,703 - DEBUG - Finished Request
2025-06-28 18:29:19,744 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:19,751 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:19,751 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:19,751 - DEBUG - Finished Request
2025-06-28 18:29:20,257 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:20,265 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:20,265 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:20,265 - DEBUG - Finished Request
2025-06-28 18:29:20,703 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:20,711 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:20,711 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:20,711 - DEBUG - Finished Request
2025-06-28 18:29:20,773 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:20,780 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:20,781 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:20,781 - DEBUG - Finished Request
2025-06-28 18:29:21,290 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:21,298 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:21,298 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:21,299 - DEBUG - Finished Request
2025-06-28 18:29:21,713 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:21,721 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:21,721 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:21,721 - DEBUG - Finished Request
2025-06-28 18:29:21,804 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:21,812 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:21,812 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:21,813 - DEBUG - Finished Request
2025-06-28 18:29:22,322 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:22,330 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:22,330 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:22,331 - DEBUG - Finished Request
2025-06-28 18:29:22,722 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:22,730 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:22,731 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:22,731 - DEBUG - Finished Request
2025-06-28 18:29:22,838 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:22,845 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:22,846 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:22,846 - DEBUG - Finished Request
2025-06-28 18:29:23,351 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:23,361 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:23,361 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:23,362 - DEBUG - Finished Request
2025-06-28 18:29:23,732 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:23,740 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:23,740 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:23,740 - DEBUG - Finished Request
2025-06-28 18:29:23,874 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:23,881 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:23,881 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:23,881 - DEBUG - Finished Request
2025-06-28 18:29:24,388 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:24,395 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:24,395 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:24,395 - DEBUG - Finished Request
2025-06-28 18:29:24,741 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:24,750 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:24,750 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:24,751 - DEBUG - Finished Request
2025-06-28 18:29:24,903 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:24,909 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:24,910 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:24,910 - DEBUG - Finished Request
2025-06-28 18:29:25,420 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:25,426 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:25,426 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:25,426 - DEBUG - Finished Request
2025-06-28 18:29:25,752 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:25,759 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:25,759 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:25,759 - DEBUG - Finished Request
2025-06-28 18:29:25,934 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:25,940 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:25,940 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:25,941 - DEBUG - Finished Request
2025-06-28 18:29:26,449 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:26,457 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:26,458 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:26,458 - DEBUG - Finished Request
2025-06-28 18:29:26,761 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:26,769 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:26,769 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:26,769 - DEBUG - Finished Request
2025-06-28 18:29:26,968 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:26,975 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:26,976 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:26,976 - DEBUG - Finished Request
2025-06-28 18:29:27,484 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:27,490 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:27,491 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:27,491 - DEBUG - Finished Request
2025-06-28 18:29:27,770 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:27,777 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:27,778 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:27,779 - DEBUG - Finished Request
2025-06-28 18:29:27,997 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:28,006 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:28,006 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:28,007 - DEBUG - Finished Request
2025-06-28 18:29:28,516 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:28,524 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:28,524 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:28,525 - DEBUG - Finished Request
2025-06-28 18:29:28,779 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:28,787 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:28,787 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:28,788 - DEBUG - Finished Request
2025-06-28 18:29:29,029 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:29,037 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:29,037 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:29,038 - DEBUG - Finished Request
2025-06-28 18:29:29,539 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:29,551 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:29,551 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:29,552 - DEBUG - Finished Request
2025-06-28 18:29:29,789 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:29,797 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:29,797 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:29,798 - DEBUG - Finished Request
2025-06-28 18:29:30,065 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:30,073 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:30,073 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:30,074 - DEBUG - Finished Request
2025-06-28 18:29:30,575 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:30,581 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:30,582 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:30,582 - DEBUG - Finished Request
2025-06-28 18:29:30,799 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:30,807 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:30,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:30,808 - DEBUG - Finished Request
2025-06-28 18:29:31,094 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:31,100 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:31,100 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:31,101 - DEBUG - Finished Request
2025-06-28 18:29:31,607 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:31,616 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:31,616 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:31,617 - DEBUG - Finished Request
2025-06-28 18:29:31,809 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:31,817 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:31,817 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:31,817 - DEBUG - Finished Request
2025-06-28 18:29:32,124 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:32,132 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:32,132 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:32,133 - DEBUG - Finished Request
2025-06-28 18:29:32,637 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:32,646 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:32,647 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:32,647 - DEBUG - Finished Request
2025-06-28 18:29:32,818 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:32,825 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:32,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:32,825 - DEBUG - Finished Request
2025-06-28 18:29:33,147 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:33,154 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:33,154 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:33,155 - DEBUG - Finished Request
2025-06-28 18:29:33,660 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:33,667 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:33,667 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:33,667 - DEBUG - Finished Request
2025-06-28 18:29:33,826 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:33,835 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:33,835 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:33,836 - DEBUG - Finished Request
2025-06-28 18:29:34,179 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:34,188 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:34,188 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:34,189 - DEBUG - Finished Request
2025-06-28 18:29:34,694 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:34,702 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:34,702 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:34,703 - DEBUG - Finished Request
2025-06-28 18:29:34,837 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:34,843 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:34,843 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:34,843 - DEBUG - Finished Request
2025-06-28 18:29:35,209 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:35,217 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:35,217 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:35,218 - DEBUG - Finished Request
2025-06-28 18:29:35,732 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:35,741 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:35,741 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:35,742 - DEBUG - Finished Request
2025-06-28 18:29:35,844 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:35,852 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:35,852 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:35,852 - DEBUG - Finished Request
2025-06-28 18:29:36,249 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:36,257 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:36,257 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:36,258 - DEBUG - Finished Request
2025-06-28 18:29:36,766 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:36,774 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:36,774 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:36,775 - DEBUG - Finished Request
2025-06-28 18:29:36,854 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:36,862 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:36,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:36,863 - DEBUG - Finished Request
2025-06-28 18:29:37,282 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:37,291 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:37,291 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:37,292 - DEBUG - Finished Request
2025-06-28 18:29:37,799 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:37,809 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:37,809 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:37,809 - DEBUG - Finished Request
2025-06-28 18:29:37,864 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:37,871 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:37,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:37,871 - DEBUG - Finished Request
2025-06-28 18:29:38,312 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:38,319 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:38,319 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:38,319 - DEBUG - Finished Request
2025-06-28 18:29:38,835 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:29:38,843 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:29:38,843 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:38,844 - DEBUG - Finished Request
2025-06-28 18:29:38,873 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:38,881 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:38,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:38,881 - DEBUG - Finished Request
2025-06-28 18:29:39,883 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:39,890 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:39,890 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:39,890 - DEBUG - Finished Request
2025-06-28 18:29:40,891 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:40,897 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:40,899 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:40,899 - DEBUG - Finished Request
2025-06-28 18:29:41,900 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:41,911 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:41,911 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:41,911 - DEBUG - Finished Request
2025-06-28 18:29:42,912 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:42,922 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:42,922 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:42,922 - DEBUG - Finished Request
2025-06-28 18:29:43,924 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:43,931 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:43,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:43,931 - DEBUG - Finished Request
2025-06-28 18:29:44,932 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:44,939 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:44,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:44,939 - DEBUG - Finished Request
2025-06-28 18:29:45,940 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:45,950 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:45,951 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:45,951 - DEBUG - Finished Request
2025-06-28 18:29:46,952 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:46,961 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:46,962 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:46,962 - DEBUG - Finished Request
2025-06-28 18:29:47,963 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:47,971 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:47,971 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:47,971 - DEBUG - Finished Request
2025-06-28 18:29:48,972 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:48,979 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:48,979 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:48,980 - DEBUG - Finished Request
2025-06-28 18:29:49,981 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:49,989 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:49,990 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:49,990 - DEBUG - Finished Request
2025-06-28 18:29:50,990 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:50,997 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:50,998 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:50,998 - DEBUG - Finished Request
2025-06-28 18:29:51,998 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:52,006 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:52,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:52,006 - DEBUG - Finished Request
2025-06-28 18:29:53,007 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:53,015 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:53,016 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:53,016 - DEBUG - Finished Request
2025-06-28 18:29:54,017 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:54,026 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:54,026 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:54,026 - DEBUG - Finished Request
2025-06-28 18:29:55,027 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:55,038 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:55,039 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:55,039 - DEBUG - Finished Request
2025-06-28 18:29:56,039 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:56,051 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:56,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:56,051 - DEBUG - Finished Request
2025-06-28 18:29:57,051 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:57,061 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:57,062 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:57,062 - DEBUG - Finished Request
2025-06-28 18:29:58,063 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:58,074 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:58,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:58,075 - DEBUG - Finished Request
2025-06-28 18:29:59,076 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:29:59,086 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:29:59,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:29:59,087 - DEBUG - Finished Request
2025-06-28 18:30:00,088 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:00,099 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:00,100 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:00,100 - DEBUG - Finished Request
2025-06-28 18:30:01,101 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:01,110 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:01,110 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:01,110 - DEBUG - Finished Request
2025-06-28 18:30:02,111 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:02,121 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:02,122 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:02,122 - DEBUG - Finished Request
2025-06-28 18:30:03,124 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:03,135 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:03,136 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:03,136 - DEBUG - Finished Request
2025-06-28 18:30:04,137 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:04,146 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:04,147 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:04,147 - DEBUG - Finished Request
2025-06-28 18:30:05,148 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:05,156 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:05,157 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:05,157 - DEBUG - Finished Request
2025-06-28 18:30:06,158 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:06,166 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:06,167 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:06,167 - DEBUG - Finished Request
2025-06-28 18:30:07,168 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:07,179 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:07,179 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:07,179 - DEBUG - Finished Request
2025-06-28 18:30:08,180 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:08,189 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:08,189 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:08,190 - DEBUG - Finished Request
2025-06-28 18:30:09,192 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:09,202 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:09,202 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:09,202 - DEBUG - Finished Request
2025-06-28 18:30:10,203 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:10,212 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:10,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:10,213 - DEBUG - Finished Request
2025-06-28 18:30:11,214 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:11,225 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:11,226 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:11,226 - DEBUG - Finished Request
2025-06-28 18:30:12,227 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:12,237 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:12,237 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:12,237 - DEBUG - Finished Request
2025-06-28 18:30:13,238 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:13,247 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:13,247 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:13,247 - DEBUG - Finished Request
2025-06-28 18:30:14,248 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:14,256 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:14,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:14,258 - DEBUG - Finished Request
2025-06-28 18:30:15,259 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:15,268 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:15,269 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:15,269 - DEBUG - Finished Request
2025-06-28 18:30:16,270 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:16,281 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:16,281 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:16,281 - DEBUG - Finished Request
2025-06-28 18:30:17,282 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:17,291 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:17,291 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:17,292 - DEBUG - Finished Request
2025-06-28 18:30:18,293 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:18,302 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:18,302 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:18,302 - DEBUG - Finished Request
2025-06-28 18:30:18,976 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:18,984 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:18,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:18,984 - DEBUG - Finished Request
2025-06-28 18:30:18,984 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/title {}
2025-06-28 18:30:18,992 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/title HTTP/1.1" 200 0
2025-06-28 18:30:18,993 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:18,993 - DEBUG - Finished Request
2025-06-28 18:30:19,005 - DEBUG - POST http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync {'script': "return document.body.innerText || '';", 'args': []}
2025-06-28 18:30:19,013 - DEBUG - http://localhost:51646 "POST /session/65dcde82692ba647bf604c51aa0b8bcd/execute/sync HTTP/1.1" 200 0
2025-06-28 18:30:19,014 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/28 18:27:44\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:19,014 - DEBUG - Finished Request
2025-06-28 18:30:19,304 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:19,314 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:19,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:19,314 - DEBUG - Finished Request
2025-06-28 18:30:20,315 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:20,324 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:20,324 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:20,324 - DEBUG - Finished Request
2025-06-28 18:30:21,326 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:21,336 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:21,336 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:21,337 - DEBUG - Finished Request
2025-06-28 18:30:22,339 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:22,346 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:22,346 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:22,347 - DEBUG - Finished Request
2025-06-28 18:30:23,348 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:23,358 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:23,358 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:23,358 - DEBUG - Finished Request
2025-06-28 18:30:24,359 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:24,367 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:24,368 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:24,368 - DEBUG - Finished Request
2025-06-28 18:30:25,369 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:25,378 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:25,378 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:25,378 - DEBUG - Finished Request
2025-06-28 18:30:26,379 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:26,387 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:26,387 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:26,387 - DEBUG - Finished Request
2025-06-28 18:30:27,388 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:27,397 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:27,397 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:27,398 - DEBUG - Finished Request
2025-06-28 18:30:28,398 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:28,406 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:28,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:28,407 - DEBUG - Finished Request
2025-06-28 18:30:29,408 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:29,414 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:29,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:29,415 - DEBUG - Finished Request
2025-06-28 18:30:30,416 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:30,425 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:30,425 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:30,425 - DEBUG - Finished Request
2025-06-28 18:30:31,426 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:31,436 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:31,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:31,437 - DEBUG - Finished Request
2025-06-28 18:30:32,439 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:32,448 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:32,449 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:32,449 - DEBUG - Finished Request
2025-06-28 18:30:33,450 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:33,458 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:33,459 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:33,459 - DEBUG - Finished Request
2025-06-28 18:30:34,461 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:34,471 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:34,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:34,472 - DEBUG - Finished Request
2025-06-28 18:30:35,473 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:35,480 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:35,481 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:35,481 - DEBUG - Finished Request
2025-06-28 18:30:36,483 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:36,493 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:36,493 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:36,494 - DEBUG - Finished Request
2025-06-28 18:30:37,495 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:37,506 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:37,507 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:37,507 - DEBUG - Finished Request
2025-06-28 18:30:38,509 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:38,519 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:38,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:38,520 - DEBUG - Finished Request
2025-06-28 18:30:39,520 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:39,530 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:39,531 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:39,531 - DEBUG - Finished Request
2025-06-28 18:30:40,532 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:40,541 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:40,542 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:40,542 - DEBUG - Finished Request
2025-06-28 18:30:41,543 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:41,553 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:41,553 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:41,553 - DEBUG - Finished Request
2025-06-28 18:30:42,554 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:42,563 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:42,564 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:42,564 - DEBUG - Finished Request
2025-06-28 18:30:43,565 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:43,574 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:43,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:43,575 - DEBUG - Finished Request
2025-06-28 18:30:44,575 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:44,585 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:44,586 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:44,586 - DEBUG - Finished Request
2025-06-28 18:30:45,588 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:45,597 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:45,598 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:45,599 - DEBUG - Finished Request
2025-06-28 18:30:46,600 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:46,609 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:46,610 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:46,610 - DEBUG - Finished Request
2025-06-28 18:30:47,611 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:47,620 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:47,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:47,621 - DEBUG - Finished Request
2025-06-28 18:30:48,622 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:48,632 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:48,632 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:48,633 - DEBUG - Finished Request
2025-06-28 18:30:49,634 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:49,644 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:49,644 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:49,644 - DEBUG - Finished Request
2025-06-28 18:30:50,645 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:50,656 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:50,656 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:50,657 - DEBUG - Finished Request
2025-06-28 18:30:51,657 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:51,666 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:51,667 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:51,667 - DEBUG - Finished Request
2025-06-28 18:30:52,668 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:52,678 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:52,679 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:52,679 - DEBUG - Finished Request
2025-06-28 18:30:53,679 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:53,688 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:53,688 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:53,688 - DEBUG - Finished Request
2025-06-28 18:30:54,690 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:54,699 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:54,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:54,699 - DEBUG - Finished Request
2025-06-28 18:30:55,701 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:55,710 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:55,710 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:55,710 - DEBUG - Finished Request
2025-06-28 18:30:56,711 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:56,720 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:56,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:56,720 - DEBUG - Finished Request
2025-06-28 18:30:57,721 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:57,730 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:57,730 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:57,731 - DEBUG - Finished Request
2025-06-28 18:30:58,732 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:58,742 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:58,742 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:58,742 - DEBUG - Finished Request
2025-06-28 18:30:59,743 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:30:59,753 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:30:59,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:30:59,754 - DEBUG - Finished Request
2025-06-28 18:31:00,755 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:00,764 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:00,764 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:00,764 - DEBUG - Finished Request
2025-06-28 18:31:01,766 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:01,777 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:01,777 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:01,778 - DEBUG - Finished Request
2025-06-28 18:31:02,778 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:02,788 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:02,788 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:02,788 - DEBUG - Finished Request
2025-06-28 18:31:03,789 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:03,798 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:03,799 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:03,800 - DEBUG - Finished Request
2025-06-28 18:31:04,801 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:04,810 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:04,811 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:04,811 - DEBUG - Finished Request
2025-06-28 18:31:05,812 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:05,821 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:05,821 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:05,821 - DEBUG - Finished Request
2025-06-28 18:31:06,822 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:06,830 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:06,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:06,831 - DEBUG - Finished Request
2025-06-28 18:31:07,832 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:07,841 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:07,841 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:07,842 - DEBUG - Finished Request
2025-06-28 18:31:08,842 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:08,851 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:08,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:08,851 - DEBUG - Finished Request
2025-06-28 18:31:09,852 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:09,860 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:09,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:09,861 - DEBUG - Finished Request
2025-06-28 18:31:10,862 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:10,870 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:10,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:10,871 - DEBUG - Finished Request
2025-06-28 18:31:11,872 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:11,882 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:11,883 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:11,883 - DEBUG - Finished Request
2025-06-28 18:31:12,884 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:12,891 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:12,891 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:12,893 - DEBUG - Finished Request
2025-06-28 18:31:13,893 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:13,901 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:13,901 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:13,901 - DEBUG - Finished Request
2025-06-28 18:31:14,903 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:14,912 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:14,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:14,912 - DEBUG - Finished Request
2025-06-28 18:31:15,914 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:15,922 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:15,923 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:15,923 - DEBUG - Finished Request
2025-06-28 18:31:16,924 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:16,933 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:16,933 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:16,934 - DEBUG - Finished Request
2025-06-28 18:31:17,934 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:17,957 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:17,957 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:17,957 - DEBUG - Finished Request
2025-06-28 18:31:18,958 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:18,967 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:18,967 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:18,968 - DEBUG - Finished Request
2025-06-28 18:31:19,969 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:19,977 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:19,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:19,978 - DEBUG - Finished Request
2025-06-28 18:31:20,979 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:20,988 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:20,988 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:20,988 - DEBUG - Finished Request
2025-06-28 18:31:21,989 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:21,997 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:21,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:21,998 - DEBUG - Finished Request
2025-06-28 18:31:22,999 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:23,007 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:23,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:23,008 - DEBUG - Finished Request
2025-06-28 18:31:24,009 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:24,018 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:24,018 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:24,018 - DEBUG - Finished Request
2025-06-28 18:31:25,019 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:25,029 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:25,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:25,029 - DEBUG - Finished Request
2025-06-28 18:31:26,030 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:26,039 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:26,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:26,040 - DEBUG - Finished Request
2025-06-28 18:31:27,041 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:27,051 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:27,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:27,052 - DEBUG - Finished Request
2025-06-28 18:31:28,053 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:28,065 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:28,065 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:28,065 - DEBUG - Finished Request
2025-06-28 18:31:29,066 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:29,075 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:29,076 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:29,076 - DEBUG - Finished Request
2025-06-28 18:31:30,077 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:30,087 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:30,088 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:30,088 - DEBUG - Finished Request
2025-06-28 18:31:31,088 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:31,100 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:31,100 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:31,100 - DEBUG - Finished Request
2025-06-28 18:31:32,101 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:32,109 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:32,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:32,110 - DEBUG - Finished Request
2025-06-28 18:31:33,111 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:33,121 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:33,121 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:33,121 - DEBUG - Finished Request
2025-06-28 18:31:34,123 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:34,132 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:34,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:34,133 - DEBUG - Finished Request
2025-06-28 18:31:35,134 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:35,142 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:35,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:35,143 - DEBUG - Finished Request
2025-06-28 18:31:36,143 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:36,153 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:36,153 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:36,153 - DEBUG - Finished Request
2025-06-28 18:31:37,154 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:37,162 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:37,163 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:37,163 - DEBUG - Finished Request
2025-06-28 18:31:38,164 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:38,173 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:38,174 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:38,174 - DEBUG - Finished Request
2025-06-28 18:31:39,175 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:39,185 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:39,185 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:39,185 - DEBUG - Finished Request
2025-06-28 18:31:40,186 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:40,196 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:40,197 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:40,197 - DEBUG - Finished Request
2025-06-28 18:31:41,198 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:41,207 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:41,207 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:41,207 - DEBUG - Finished Request
2025-06-28 18:31:42,208 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:42,218 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:42,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:42,219 - DEBUG - Finished Request
2025-06-28 18:31:43,220 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:43,228 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:43,228 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:43,228 - DEBUG - Finished Request
2025-06-28 18:31:44,228 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:44,239 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:44,239 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:44,240 - DEBUG - Finished Request
2025-06-28 18:31:45,242 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:45,252 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:45,252 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:45,252 - DEBUG - Finished Request
2025-06-28 18:31:46,254 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:46,263 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:46,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:46,264 - DEBUG - Finished Request
2025-06-28 18:31:47,265 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:47,273 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:47,274 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:47,274 - DEBUG - Finished Request
2025-06-28 18:31:48,275 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:48,284 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:48,284 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:48,284 - DEBUG - Finished Request
2025-06-28 18:31:49,285 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:49,293 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:49,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:49,293 - DEBUG - Finished Request
2025-06-28 18:31:50,294 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:50,303 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:50,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:50,304 - DEBUG - Finished Request
2025-06-28 18:31:51,305 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:51,313 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:51,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:51,314 - DEBUG - Finished Request
2025-06-28 18:31:52,315 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:52,323 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:52,324 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:52,324 - DEBUG - Finished Request
2025-06-28 18:31:53,325 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:53,333 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:53,333 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:53,333 - DEBUG - Finished Request
2025-06-28 18:31:54,334 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:54,341 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:54,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:54,342 - DEBUG - Finished Request
2025-06-28 18:31:55,343 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:55,352 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:55,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:55,353 - DEBUG - Finished Request
2025-06-28 18:31:56,354 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:56,363 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:56,364 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:56,364 - DEBUG - Finished Request
2025-06-28 18:31:57,365 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:57,372 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:57,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:57,372 - DEBUG - Finished Request
2025-06-28 18:31:58,374 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:58,383 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:58,384 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:58,384 - DEBUG - Finished Request
2025-06-28 18:31:59,385 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:31:59,394 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:31:59,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:31:59,395 - DEBUG - Finished Request
2025-06-28 18:32:00,396 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:00,404 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:00,404 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:00,404 - DEBUG - Finished Request
2025-06-28 18:32:01,405 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:01,413 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:01,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:01,414 - DEBUG - Finished Request
2025-06-28 18:32:02,415 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:02,423 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:02,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:02,423 - DEBUG - Finished Request
2025-06-28 18:32:03,424 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:03,433 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:03,434 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:03,434 - DEBUG - Finished Request
2025-06-28 18:32:04,435 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:04,442 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:04,443 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:04,443 - DEBUG - Finished Request
2025-06-28 18:32:05,444 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:05,452 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:05,452 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:05,452 - DEBUG - Finished Request
2025-06-28 18:32:06,453 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:06,460 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:06,460 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:06,460 - DEBUG - Finished Request
2025-06-28 18:32:07,462 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:07,473 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:07,473 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:07,473 - DEBUG - Finished Request
2025-06-28 18:32:08,474 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:08,481 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:08,481 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:08,481 - DEBUG - Finished Request
2025-06-28 18:32:09,482 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:09,491 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:09,492 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:09,492 - DEBUG - Finished Request
2025-06-28 18:32:10,493 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:10,500 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:10,500 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:10,501 - DEBUG - Finished Request
2025-06-28 18:32:11,501 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:11,510 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:11,510 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:11,510 - DEBUG - Finished Request
2025-06-28 18:32:12,511 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:12,521 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:12,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:12,521 - DEBUG - Finished Request
2025-06-28 18:32:13,521 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:13,529 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:13,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:13,529 - DEBUG - Finished Request
2025-06-28 18:32:14,530 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:14,539 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:14,540 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:14,540 - DEBUG - Finished Request
2025-06-28 18:32:15,541 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:15,550 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:15,550 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:15,551 - DEBUG - Finished Request
2025-06-28 18:32:16,551 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:16,561 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:16,561 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:16,562 - DEBUG - Finished Request
2025-06-28 18:32:17,563 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:17,570 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:17,570 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:17,570 - DEBUG - Finished Request
2025-06-28 18:32:18,572 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:18,580 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:18,580 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:18,580 - DEBUG - Finished Request
2025-06-28 18:32:19,581 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:19,589 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:19,589 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:19,589 - DEBUG - Finished Request
2025-06-28 18:32:20,591 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:20,600 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:20,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:20,600 - DEBUG - Finished Request
2025-06-28 18:32:21,600 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:21,609 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:21,610 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:21,610 - DEBUG - Finished Request
2025-06-28 18:32:22,611 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:22,620 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:22,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:22,620 - DEBUG - Finished Request
2025-06-28 18:32:23,621 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:23,628 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:23,629 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:23,629 - DEBUG - Finished Request
2025-06-28 18:32:24,629 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:24,636 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:24,636 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:24,637 - DEBUG - Finished Request
2025-06-28 18:32:25,637 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:25,643 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:25,644 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:25,644 - DEBUG - Finished Request
2025-06-28 18:32:26,645 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:26,654 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:26,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:26,654 - DEBUG - Finished Request
2025-06-28 18:32:27,655 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:27,663 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:27,663 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:27,663 - DEBUG - Finished Request
2025-06-28 18:32:28,665 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:28,674 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:28,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:28,675 - DEBUG - Finished Request
2025-06-28 18:32:29,676 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:29,685 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:29,686 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:29,686 - DEBUG - Finished Request
2025-06-28 18:32:30,687 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:30,699 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:30,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:30,700 - DEBUG - Finished Request
2025-06-28 18:32:31,700 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:31,709 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:31,710 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:31,710 - DEBUG - Finished Request
2025-06-28 18:32:32,711 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:32,720 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:32,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:32,721 - DEBUG - Finished Request
2025-06-28 18:32:33,722 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:33,733 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:33,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:33,734 - DEBUG - Finished Request
2025-06-28 18:32:34,735 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:34,743 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:34,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:34,744 - DEBUG - Finished Request
2025-06-28 18:32:35,746 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:35,755 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:35,755 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:35,755 - DEBUG - Finished Request
2025-06-28 18:32:36,756 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:36,764 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:36,764 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:36,765 - DEBUG - Finished Request
2025-06-28 18:32:37,766 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:37,774 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:37,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:37,775 - DEBUG - Finished Request
2025-06-28 18:32:38,775 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:38,783 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:38,784 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:38,784 - DEBUG - Finished Request
2025-06-28 18:32:39,785 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:39,794 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:39,795 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:39,796 - DEBUG - Finished Request
2025-06-28 18:32:40,797 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:40,807 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:40,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:40,807 - DEBUG - Finished Request
2025-06-28 18:32:41,808 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:41,819 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:41,820 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:41,820 - DEBUG - Finished Request
2025-06-28 18:32:42,821 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:42,829 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:42,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:42,829 - DEBUG - Finished Request
2025-06-28 18:32:43,830 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:43,839 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:43,840 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:43,840 - DEBUG - Finished Request
2025-06-28 18:32:44,841 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:44,851 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:44,852 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:44,852 - DEBUG - Finished Request
2025-06-28 18:32:45,852 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:45,861 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:45,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:45,861 - DEBUG - Finished Request
2025-06-28 18:32:46,863 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:46,874 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:46,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:46,875 - DEBUG - Finished Request
2025-06-28 18:32:47,875 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:47,884 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:47,884 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:47,884 - DEBUG - Finished Request
2025-06-28 18:32:48,885 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:48,893 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:48,894 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:48,894 - DEBUG - Finished Request
2025-06-28 18:32:49,895 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:49,903 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:49,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:49,904 - DEBUG - Finished Request
2025-06-28 18:32:50,905 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:50,914 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:50,914 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:50,914 - DEBUG - Finished Request
2025-06-28 18:32:51,915 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:51,926 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:51,926 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:51,926 - DEBUG - Finished Request
2025-06-28 18:32:52,927 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:52,938 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:52,938 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:52,938 - DEBUG - Finished Request
2025-06-28 18:32:53,939 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:53,947 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:53,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:53,948 - DEBUG - Finished Request
2025-06-28 18:32:54,950 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:54,959 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:54,960 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:54,960 - DEBUG - Finished Request
2025-06-28 18:32:55,961 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:55,973 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:55,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:55,974 - DEBUG - Finished Request
2025-06-28 18:32:56,974 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:56,982 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:56,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:56,983 - DEBUG - Finished Request
2025-06-28 18:32:57,984 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:57,993 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:57,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:57,993 - DEBUG - Finished Request
2025-06-28 18:32:58,995 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:32:59,007 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:32:59,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:32:59,007 - DEBUG - Finished Request
2025-06-28 18:33:00,008 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:00,018 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:00,019 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:00,019 - DEBUG - Finished Request
2025-06-28 18:33:01,020 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:01,030 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:01,031 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:01,031 - DEBUG - Finished Request
2025-06-28 18:33:02,032 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:02,041 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:02,042 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:02,042 - DEBUG - Finished Request
2025-06-28 18:33:03,043 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:03,050 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:03,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:03,051 - DEBUG - Finished Request
2025-06-28 18:33:04,052 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:04,061 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:04,061 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:04,062 - DEBUG - Finished Request
2025-06-28 18:33:05,063 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:05,073 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:05,073 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:05,073 - DEBUG - Finished Request
2025-06-28 18:33:06,074 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:06,086 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:06,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:06,086 - DEBUG - Finished Request
2025-06-28 18:33:07,087 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:07,096 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:07,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:07,097 - DEBUG - Finished Request
2025-06-28 18:33:08,098 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:08,109 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:08,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:08,109 - DEBUG - Finished Request
2025-06-28 18:33:09,111 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:09,119 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:09,120 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:09,120 - DEBUG - Finished Request
2025-06-28 18:33:10,120 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:10,131 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:10,131 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:10,132 - DEBUG - Finished Request
2025-06-28 18:33:11,132 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:11,141 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:11,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:11,141 - DEBUG - Finished Request
2025-06-28 18:33:12,142 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:12,150 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:12,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:12,151 - DEBUG - Finished Request
2025-06-28 18:33:13,152 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:13,161 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:13,161 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:13,162 - DEBUG - Finished Request
2025-06-28 18:33:14,162 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:14,170 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:14,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:14,171 - DEBUG - Finished Request
2025-06-28 18:33:15,172 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:15,182 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:15,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:15,182 - DEBUG - Finished Request
2025-06-28 18:33:16,183 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:16,192 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:16,193 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:16,193 - DEBUG - Finished Request
2025-06-28 18:33:17,194 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:17,202 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:17,202 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:17,202 - DEBUG - Finished Request
2025-06-28 18:33:18,204 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:18,212 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:18,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:18,213 - DEBUG - Finished Request
2025-06-28 18:33:19,213 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:19,222 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:19,222 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:19,222 - DEBUG - Finished Request
2025-06-28 18:33:20,224 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:20,235 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:20,235 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:20,235 - DEBUG - Finished Request
2025-06-28 18:33:21,236 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:21,246 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:21,246 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:21,246 - DEBUG - Finished Request
2025-06-28 18:33:22,247 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:22,254 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:22,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:22,255 - DEBUG - Finished Request
2025-06-28 18:33:23,256 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:23,267 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:23,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:23,267 - DEBUG - Finished Request
2025-06-28 18:33:24,268 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:24,278 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:24,278 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:24,278 - DEBUG - Finished Request
2025-06-28 18:33:25,280 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:25,289 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:25,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:25,289 - DEBUG - Finished Request
2025-06-28 18:33:26,291 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:26,299 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:26,300 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:26,300 - DEBUG - Finished Request
2025-06-28 18:33:27,301 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:27,309 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:27,309 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:27,309 - DEBUG - Finished Request
2025-06-28 18:33:28,310 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:28,319 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:28,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:28,320 - DEBUG - Finished Request
2025-06-28 18:33:29,321 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:29,329 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:29,330 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:29,330 - DEBUG - Finished Request
2025-06-28 18:33:30,331 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:30,342 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:30,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:30,342 - DEBUG - Finished Request
2025-06-28 18:33:31,343 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:31,351 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:31,352 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:31,352 - DEBUG - Finished Request
2025-06-28 18:33:32,353 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:32,362 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:32,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:32,362 - DEBUG - Finished Request
2025-06-28 18:33:33,364 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:33,372 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:33,373 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:33,373 - DEBUG - Finished Request
2025-06-28 18:33:34,373 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:34,383 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:34,383 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:34,384 - DEBUG - Finished Request
2025-06-28 18:33:35,384 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:35,394 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:35,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:35,395 - DEBUG - Finished Request
2025-06-28 18:33:36,395 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:36,405 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:36,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:36,406 - DEBUG - Finished Request
2025-06-28 18:33:37,407 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:37,415 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:37,416 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:37,416 - DEBUG - Finished Request
2025-06-28 18:33:38,417 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:38,427 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:38,427 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:38,427 - DEBUG - Finished Request
2025-06-28 18:33:39,428 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:39,436 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:39,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:39,437 - DEBUG - Finished Request
2025-06-28 18:33:40,437 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:40,447 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:40,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:40,448 - DEBUG - Finished Request
2025-06-28 18:33:41,449 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:41,458 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:41,459 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:41,459 - DEBUG - Finished Request
2025-06-28 18:33:42,460 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:42,469 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:42,469 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:42,469 - DEBUG - Finished Request
2025-06-28 18:33:43,470 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:43,479 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:43,480 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:43,480 - DEBUG - Finished Request
2025-06-28 18:33:44,481 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:44,490 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:44,491 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:44,491 - DEBUG - Finished Request
2025-06-28 18:33:45,491 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:45,502 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:45,503 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:45,503 - DEBUG - Finished Request
2025-06-28 18:33:46,504 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:46,514 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:46,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:46,514 - DEBUG - Finished Request
2025-06-28 18:33:47,515 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:47,526 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:47,526 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:47,526 - DEBUG - Finished Request
2025-06-28 18:33:48,527 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:48,535 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:48,535 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:48,536 - DEBUG - Finished Request
2025-06-28 18:33:49,536 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:49,545 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:49,545 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:49,545 - DEBUG - Finished Request
2025-06-28 18:33:50,548 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:50,557 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:50,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:50,558 - DEBUG - Finished Request
2025-06-28 18:33:51,558 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:51,567 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:51,567 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:51,567 - DEBUG - Finished Request
2025-06-28 18:33:52,568 - DEBUG - GET http://localhost:51646/session/65dcde82692ba647bf604c51aa0b8bcd/url {}
2025-06-28 18:33:52,577 - DEBUG - http://localhost:51646 "GET /session/65dcde82692ba647bf604c51aa0b8bcd/url HTTP/1.1" 200 0
2025-06-28 18:33:52,577 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:33:52,577 - DEBUG - Finished Request
