#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AGES-KH-Bot GUI 原型展示程式
用於展示所有 GUI 介面原型，確認流程和設計符合使用者需求
類似 UI 原型圖的確認過程，不依賴主程式邏輯
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time

class GUITestProgram:
    """GUI 測試程式主類"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AGES-KH-Bot GUI 測試程式")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 測試結果記錄
        self.test_results = {}
        
        # 創建主界面
        self.create_main_interface()
        
    def create_main_interface(self):
        """創建主測試界面"""
        # 標題
        title_label = tk.Label(self.root, text="🧪 AGES-KH-Bot GUI 測試程式", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # 說明
        desc_label = tk.Label(self.root, 
                             text="此程式用於獨立測試所有 GUI 組件，確保內容和功能正確",
                             font=("Arial", 10))
        desc_label.pack(pady=10)
        
        # 創建測試按鈕框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
        # GUI 測試按鈕列表
        gui_tests = [
            ("GUI#01", "觸發時間設定", self.test_gui01),
            ("GUI#02", "準備提示視窗", self.test_gui02),
            ("GUI#03", "登入提示", self.test_gui03),
            ("GUI#04", "主搶單程式", self.test_gui04),
            ("GUI#05", "操作指南", self.test_gui05),
            ("GUI#06", "頁面確認對話框", self.test_gui06),
            ("GUI#07", "準備完成確認", self.test_gui07),
            ("GUI#08", "驗證碼輸入 (舊版)", self.test_gui08),
            ("GUI#09", "驗證碼輸入提醒", self.test_gui09),
            ("GUI#10", "等待觸發時間 (待實現)", self.test_gui10),
            ("GUI#11", "執行結果 (待實現)", self.test_gui11),
        ]
        
        # 創建測試按鈕
        for i, (gui_code, gui_name, test_func) in enumerate(gui_tests):
            row = i // 2
            col = i % 2
            
            btn_frame = tk.Frame(button_frame)
            btn_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
            
            # 測試按鈕
            test_btn = tk.Button(btn_frame, text=f"測試 {gui_code}",
                               command=test_func, font=("Arial", 10, "bold"),
                               bg="#2196F3", fg="white", padx=15, pady=8)
            test_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            # GUI 名稱標籤
            name_label = tk.Label(btn_frame, text=gui_name, font=("Arial", 9))
            name_label.pack(side=tk.LEFT)
            
            # 狀態指示器
            status_label = tk.Label(btn_frame, text="⚪ 未測試", font=("Arial", 8))
            status_label.pack(side=tk.RIGHT)
            
            # 保存引用以便更新狀態
            setattr(self, f"status_label_{gui_code.lower().replace('#', '')}", status_label)
        
        # 配置網格權重
        for i in range(2):
            button_frame.columnconfigure(i, weight=1)
        
        # 底部控制區域
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=20, fill=tk.X)
        
        # 全部測試按鈕
        test_all_btn = tk.Button(control_frame, text="🚀 執行全部測試",
                               command=self.test_all_guis, font=("Arial", 12, "bold"),
                               bg="#4CAF50", fg="white", padx=20, pady=10)
        test_all_btn.pack(side=tk.LEFT, padx=20)
        
        # 測試報告按鈕
        report_btn = tk.Button(control_frame, text="📊 生成測試報告",
                             command=self.generate_test_report, font=("Arial", 12, "bold"),
                             bg="#FF9800", fg="white", padx=20, pady=10)
        report_btn.pack(side=tk.LEFT, padx=10)
        
        # 退出按鈕
        exit_btn = tk.Button(control_frame, text="❌ 退出程式",
                           command=self.root.quit, font=("Arial", 12, "bold"),
                           bg="#F44336", fg="white", padx=20, pady=10)
        exit_btn.pack(side=tk.RIGHT, padx=20)
    
    def update_test_status(self, gui_code, status, result=None):
        """更新測試狀態"""
        status_label = getattr(self, f"status_label_{gui_code.lower().replace('#', '')}", None)
        if status_label:
            if status == "testing":
                status_label.config(text="🔄 測試中", fg="blue")
            elif status == "pass":
                status_label.config(text="✅ 通過", fg="green")
            elif status == "fail":
                status_label.config(text="❌ 失敗", fg="red")
            elif status == "skip":
                status_label.config(text="⚠️ 跳過", fg="orange")
        
        # 記錄測試結果
        self.test_results[gui_code] = {
            'status': status,
            'result': result,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def test_gui01(self):
        """測試 GUI#01 - 觸發時間設定"""
        self.update_test_status("GUI#01", "testing")
        try:
            # 嘗試導入並測試
            import mvp_grabber
            if hasattr(mvp_grabber, 'ask_trigger_time_gui'):
                # 在線程中執行以避免阻塞
                def test_thread():
                    try:
                        result = mvp_grabber.ask_trigger_time_gui()
                        self.update_test_status("GUI#01", "pass", result)
                    except Exception as e:
                        self.update_test_status("GUI#01", "fail", str(e))
                
                threading.Thread(target=test_thread, daemon=True).start()
            else:
                self.update_test_status("GUI#01", "fail", "函數不存在")
        except Exception as e:
            self.update_test_status("GUI#01", "fail", str(e))
    
    def test_gui02(self):
        """測試 GUI#02 - 準備提示視窗"""
        self.update_test_status("GUI#02", "testing")
        try:
            import mvp_grabber
            if hasattr(mvp_grabber, 'show_preparation_gui'):
                def test_thread():
                    try:
                        result = mvp_grabber.show_preparation_gui()
                        self.update_test_status("GUI#02", "pass", result)
                    except Exception as e:
                        self.update_test_status("GUI#02", "fail", str(e))
                
                threading.Thread(target=test_thread, daemon=True).start()
            else:
                self.update_test_status("GUI#02", "fail", "函數不存在")
        except Exception as e:
            self.update_test_status("GUI#02", "fail", str(e))
    
    def test_gui03(self):
        """測試 GUI#03 - 登入提示"""
        self.update_test_status("GUI#03", "testing")
        try:
            import mvp_grabber
            if hasattr(mvp_grabber, 'wait_for_user_operation_and_start_grabbing'):
                def test_thread():
                    try:
                        # 這個函數需要特殊處理，因為它會啟動瀏覽器
                        messagebox.showinfo("測試說明", 
                                          "GUI#03 需要啟動瀏覽器，請手動測試。\n"
                                          "此處標記為跳過。")
                        self.update_test_status("GUI#03", "skip", "需要手動測試")
                    except Exception as e:
                        self.update_test_status("GUI#03", "fail", str(e))
                
                threading.Thread(target=test_thread, daemon=True).start()
            else:
                self.update_test_status("GUI#03", "fail", "函數不存在")
        except Exception as e:
            self.update_test_status("GUI#03", "fail", str(e))
    
    def test_gui04(self):
        """測試 GUI#04 - 主搶單程式"""
        self.update_test_status("GUI#04", "testing")
        try:
            import mvp_grabber
            if hasattr(mvp_grabber, 'GrabberGUI'):
                messagebox.showinfo("測試說明", 
                                  "GUI#04 是主搶單程式，需要完整環境。\n"
                                  "此處標記為跳過，建議在主程式中測試。")
                self.update_test_status("GUI#04", "skip", "需要完整環境")
            else:
                self.update_test_status("GUI#04", "fail", "類不存在")
        except Exception as e:
            self.update_test_status("GUI#04", "fail", str(e))
    
    def test_gui05(self):
        """測試 GUI#05 - 操作指南"""
        self.update_test_status("GUI#05", "testing")
        messagebox.showinfo("測試說明", 
                          "GUI#05 是 GrabberGUI 類的方法，需要在主程式環境中測試。\n"
                          "此處標記為跳過。")
        self.update_test_status("GUI#05", "skip", "需要主程式環境")
    
    def test_gui06(self):
        """測試 GUI#06 - 頁面確認對話框"""
        self.update_test_status("GUI#06", "testing")
        messagebox.showinfo("測試說明", 
                          "GUI#06 是 GrabberGUI 類的方法，需要在主程式環境中測試。\n"
                          "此處標記為跳過。")
        self.update_test_status("GUI#06", "skip", "需要主程式環境")
    
    def test_gui07(self):
        """測試 GUI#07 - 準備完成確認"""
        self.update_test_status("GUI#07", "testing")
        try:
            import mvp_grabber
            if hasattr(mvp_grabber, 'wait_for_user_ready_confirmation'):
                def test_thread():
                    try:
                        result = mvp_grabber.wait_for_user_ready_confirmation()
                        self.update_test_status("GUI#07", "pass", result)
                    except Exception as e:
                        self.update_test_status("GUI#07", "fail", str(e))
                
                threading.Thread(target=test_thread, daemon=True).start()
            else:
                self.update_test_status("GUI#07", "fail", "函數不存在")
        except Exception as e:
            self.update_test_status("GUI#07", "fail", str(e))
    
    def test_gui08(self):
        """測試 GUI#08 - 驗證碼輸入 (舊版)"""
        self.update_test_status("GUI#08", "testing")
        messagebox.showwarning("注意", 
                             "GUI#08 是舊版本驗證碼輸入，計劃移除。\n"
                             "此處標記為跳過。")
        self.update_test_status("GUI#08", "skip", "舊版本，計劃移除")
    
    def test_gui09(self):
        """測試 GUI#09 - 驗證碼輸入提醒"""
        self.update_test_status("GUI#09", "testing")
        try:
            import mvp_grabber
            if hasattr(mvp_grabber, 'show_verification_reminder_gui'):
                def test_thread():
                    try:
                        # 創建模擬檢測結果
                        mock_detection = {
                            'submit_buttons': ['submit1', 'submit2'],
                            'cancel_buttons': ['cancel1'],
                            'other_buttons': ['other1', 'other2']
                        }
                        result = mvp_grabber.show_verification_reminder_gui(mock_detection)
                        self.update_test_status("GUI#09", "pass", result)
                    except Exception as e:
                        self.update_test_status("GUI#09", "fail", str(e))
                
                threading.Thread(target=test_thread, daemon=True).start()
            else:
                self.update_test_status("GUI#09", "fail", "函數不存在")
        except Exception as e:
            self.update_test_status("GUI#09", "fail", str(e))
    
    def test_gui10(self):
        """測試 GUI#10 - 等待觸發時間"""
        self.update_test_status("GUI#10", "testing")
        messagebox.showinfo("測試說明", 
                          "GUI#10 尚未實現。\n"
                          "此處標記為跳過。")
        self.update_test_status("GUI#10", "skip", "尚未實現")
    
    def test_gui11(self):
        """測試 GUI#11 - 執行結果"""
        self.update_test_status("GUI#11", "testing")
        messagebox.showinfo("測試說明", 
                          "GUI#11 尚未實現。\n"
                          "此處標記為跳過。")
        self.update_test_status("GUI#11", "skip", "尚未實現")
    
    def test_all_guis(self):
        """執行全部 GUI 測試"""
        messagebox.showinfo("全部測試", 
                          "將依序執行所有 GUI 測試。\n"
                          "請注意某些 GUI 需要手動操作。")
        
        # 依序執行測試
        test_functions = [
            self.test_gui01, self.test_gui02, self.test_gui03, self.test_gui04,
            self.test_gui05, self.test_gui06, self.test_gui07, self.test_gui08,
            self.test_gui09, self.test_gui10, self.test_gui11
        ]
        
        def run_all_tests():
            for i, test_func in enumerate(test_functions):
                print(f"執行測試 {i+1}/{len(test_functions)}")
                test_func()
                time.sleep(1)  # 間隔1秒
        
        threading.Thread(target=run_all_tests, daemon=True).start()
    
    def generate_test_report(self):
        """生成測試報告"""
        if not self.test_results:
            messagebox.showwarning("警告", "尚未執行任何測試！")
            return
        
        # 創建報告視窗
        report_window = tk.Toplevel(self.root)
        report_window.title("GUI 測試報告")
        report_window.geometry("600x400")
        
        # 報告內容
        report_text = tk.Text(report_window, wrap=tk.WORD, font=("Consolas", 10))
        report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 生成報告內容
        report_content = "# AGES-KH-Bot GUI 測試報告\n\n"
        report_content += f"測試時間: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 統計
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'pass')
        failed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'fail')
        skipped_tests = sum(1 for r in self.test_results.values() if r['status'] == 'skip')
        
        report_content += f"## 測試統計\n"
        report_content += f"- 總測試數: {total_tests}\n"
        report_content += f"- 通過: {passed_tests}\n"
        report_content += f"- 失敗: {failed_tests}\n"
        report_content += f"- 跳過: {skipped_tests}\n\n"
        
        # 詳細結果
        report_content += "## 詳細結果\n\n"
        for gui_code, result in self.test_results.items():
            status_icon = {"pass": "✅", "fail": "❌", "skip": "⚠️", "testing": "🔄"}
            icon = status_icon.get(result['status'], "❓")
            report_content += f"{icon} {gui_code}: {result['status'].upper()}\n"
            if result['result']:
                report_content += f"   結果: {result['result']}\n"
            report_content += f"   時間: {result['timestamp']}\n\n"
        
        report_text.insert(tk.END, report_content)
        report_text.config(state=tk.DISABLED)
    
    def run(self):
        """運行測試程式"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🧪 啟動 AGES-KH-Bot GUI 測試程式...")
    
    # 檢查是否能導入主程式
    try:
        import mvp_grabber
        print(f"✅ 成功導入主程式，版本: {getattr(mvp_grabber, '__VERSION__', 'Unknown')}")
    except ImportError as e:
        print(f"❌ 無法導入主程式: {e}")
        print("請確保 mvp_grabber.py 在同一目錄下")
    
    # 啟動測試程式
    app = GUITestProgram()
    app.run()
