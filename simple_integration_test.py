"""
簡單整合測試 - 驗證基本功能
"""

import sys
import os

def test_basic_imports():
    """測試基本模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        # 測試新模組
        from submission_result_detector import SubmissionResultDetector
        print("✅ SubmissionResultDetector 導入成功")
        
        from dom_inspector import DOMInspector  
        print("✅ DOMInspector 導入成功")
        
        # 測試主程式模組（不執行 GUI）
        import mvp_grabber
        print("✅ mvp_grabber 導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_config_files():
    """測試配置文件"""
    print("🔍 測試配置文件...")
    
    # 檢查 DOM 配置
    if os.path.exists("dom_elements_config.json"):
        import json
        try:
            with open("dom_elements_config.json", 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ DOM 配置文件格式正確")
        except json.JSONDecodeError:
            print("❌ DOM 配置文件格式錯誤")
            return False
    else:
        print("⚠️ DOM 配置文件不存在（首次運行時會創建）")
    
    # 檢查訂單文件
    if os.path.exists("orders/orders.csv"):
        print("✅ 訂單文件存在")
    else:
        print("⚠️ 訂單文件不存在")
    
    return True

def test_detector_initialization():
    """測試檢測器初始化"""
    print("🔍 測試檢測器初始化...")
    
    try:
        from unittest.mock import Mock
        from submission_result_detector import SubmissionResultDetector
        from dom_inspector import DOMInspector
        
        # Mock driver
        mock_driver = Mock()
        mock_logger = Mock()
        
        # 測試結果檢測器
        detector = SubmissionResultDetector(mock_driver, mock_logger)
        print("✅ SubmissionResultDetector 初始化成功")
        
        # 測試 DOM 檢查器
        inspector = DOMInspector()
        print("✅ DOMInspector 初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢測器初始化失敗: {e}")
        return False

def test_error_patterns():
    """測試錯誤模式識別"""
    print("🔍 測試錯誤模式識別...")
    
    try:
        from unittest.mock import Mock
        from submission_result_detector import SubmissionResultDetector
        
        mock_driver = Mock()
        detector = SubmissionResultDetector(mock_driver)
        
        # 測試時間未開放錯誤
        error_text1 = "送出失敗：尚未開放 2025/07/04 預約進廠，請於 9:30 後預約。"
        result1 = detector._analyze_popup_content(error_text1)
        assert result1["result_type"] == "time_not_open"
        print("✅ 時間未開放錯誤識別正確")

        # 測試工廠已滿錯誤
        error_text2 = "送出失敗：M1：仁武廠選擇的進廠數量已滿，請選擇其他廠。"
        result2 = detector._analyze_popup_content(error_text2)
        assert result2["result_type"] == "factory_full"
        print("✅ 工廠已滿錯誤識別正確")

        # 測試非失敗彈窗
        success_text = "預約申請已送出，請等待審核結果。"
        result3 = detector._analyze_popup_content(success_text)
        assert result3["result_type"] == "non_failure_popup"
        print("✅ 非失敗彈窗識別正確")
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤模式測試失敗: {e}")
        return False

def test_directory_structure():
    """測試目錄結構"""
    print("🔍 測試目錄結構...")
    
    required_dirs = ["orders", "results", "screenshots", "logs"]
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 創建目錄: {dir_name}")
        else:
            print(f"✅ 目錄存在: {dir_name}")
    
    return True

def test_version_info():
    """測試版本信息"""
    print("🔍 測試版本信息...")
    
    try:
        import mvp_grabber
        version = mvp_grabber.__VERSION__
        print(f"✅ 主程式版本: {version}")
        
        # 檢查是否為新版本
        if version >= "1.4.0":
            print("✅ 版本號正確（包含新功能）")
        else:
            print("⚠️ 版本號較舊，可能未包含最新功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 版本信息測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 AGES-KH-Bot 簡單整合測試")
    print("=" * 50)
    
    tests = [
        ("基本模組導入", test_basic_imports),
        ("配置文件檢查", test_config_files), 
        ("檢測器初始化", test_detector_initialization),
        ("錯誤模式識別", test_error_patterns),
        ("目錄結構檢查", test_directory_structure),
        ("版本信息檢查", test_version_info)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！系統已準備就緒")
        print("\n📋 下一步操作:")
        print("1. 確保 orders/orders.csv 包含今日任務")
        print("2. 運行 python mvp_grabber.py 啟動主程式")
        print("3. 在實際環境中測試完整搶單流程")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查問題")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
