#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終測試：專門檢測編輯彈窗中的送出按鈕
Final Test: Detect Submit Button in Edit Dialog
"""

import os
import sys
import time
import tkinter as tk
from tkinter import messagebox, scrolledtext
from datetime import datetime
import threading

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class FinalSubmitButtonTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 最終測試：送出按鈕檢測")
        self.root.geometry("1000x700")
        
        self.setup_ui()
        
    def setup_ui(self):
        """設置 UI"""
        # 標題
        title_label = tk.Label(self.root, text="🎯 最終測試：送出按鈕檢測", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 說明
        info_text = """
✅ 我們已經成功進入編輯彈窗：「編輯進廠確認單(E48B201611406250521)」
🎯 現在只需要找到送出按鈕！

請確保：
1. 瀏覽器已打開並登入
2. 已點擊編輯按鈕，編輯彈窗已打開
3. 可以看到編輯彈窗的標題和內容
        """
        
        info_label = tk.Label(self.root, text=info_text, 
                             font=("Arial", 10), justify="left")
        info_label.pack(pady=5)
        
        # 按鈕區域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 檢測按鈕
        self.detect_btn = tk.Button(button_frame, text="🔍 檢測送出按鈕", 
                                   command=self.detect_submit_button,
                                   font=("Arial", 12), bg="lightblue")
        self.detect_btn.pack(side="left", padx=10)
        
        # 等待載入按鈕
        self.wait_btn = tk.Button(button_frame, text="⏰ 等待內容載入", 
                                 command=self.wait_for_content,
                                 font=("Arial", 12), bg="lightgreen")
        self.wait_btn.pack(side="left", padx=10)
        
        # 截圖按鈕
        self.screenshot_btn = tk.Button(button_frame, text="📸 截圖", 
                                       command=self.take_screenshot,
                                       font=("Arial", 12), bg="lightyellow")
        self.screenshot_btn.pack(side="left", padx=10)
        
        # 關閉按鈕
        self.close_btn = tk.Button(button_frame, text="❌ 關閉", 
                                 command=self.close_app,
                                 font=("Arial", 12), bg="lightcoral")
        self.close_btn.pack(side="right", padx=10)
        
        # 日誌顯示區域
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(log_frame, text="檢測日誌:", font=("Arial", 12, "bold")).pack(anchor="w")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=25, width=120)
        self.log_text.pack(fill="both", expand=True)
        
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def wait_for_content(self):
        """等待內容載入"""
        try:
            self.log("⏰ 開始等待編輯彈窗內容完全載入...")
            self.wait_btn.config(state="disabled")
            
            def wait_thread():
                try:
                    from mvp_grabber import driver
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 等待15秒，每秒檢查一次內容變化
                    for i in range(15):
                        try:
                            current_text = driver.execute_script("return document.body.innerText || '';")
                            
                            # 檢查關鍵內容
                            has_title = '編輯進廠確認單' in current_text
                            has_form_content = any(keyword in current_text for keyword in 
                                                 ['驗證碼', '送出', '確認', '提交', '儲存', 'input', 'form'])
                            
                            self.root.after(0, lambda i=i, length=len(current_text), title=has_title, form=has_form_content:
                                           self.log(f"  等待 {i+1}/15 秒: 內容長度={length}, 有標題={title}, 有表單={form}"))
                            
                            if has_form_content:
                                self.root.after(0, lambda: self.log("✅ 檢測到表單內容，載入完成！"))
                                break
                                
                            time.sleep(1)
                            
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"  等待 {i+1} 檢查失敗: {e}"))
                            time.sleep(1)
                    
                    self.root.after(0, lambda: self.log("⏰ 等待完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 等待過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.wait_btn.config(state="normal"))
            
            threading.Thread(target=wait_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動等待失敗: {e}")
            self.wait_btn.config(state="normal")
    
    def detect_submit_button(self):
        """檢測送出按鈕"""
        try:
            self.log("🔍 開始檢測送出按鈕...")
            self.detect_btn.config(state="disabled")
            
            def detect_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 1. 檢查當前頁面內容
                    current_text = driver.execute_script("return document.body.innerText || '';")
                    self.root.after(0, lambda: self.log(f"📊 當前頁面內容長度: {len(current_text)}"))
                    
                    # 檢查是否在編輯彈窗中
                    if '編輯進廠確認單' in current_text:
                        self.root.after(0, lambda: self.log("✅ 確認在編輯彈窗中"))
                    else:
                        self.root.after(0, lambda: self.log("❌ 不在編輯彈窗中"))
                        return
                    
                    # 2. 檢測所有按鈕
                    all_buttons = driver.find_elements(By.TAG_NAME, "button")
                    all_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='submit'], input[type='button']")
                    all_clickable = driver.find_elements(By.CSS_SELECTOR, "[onclick], [role='button']")
                    
                    total_elements = len(all_buttons) + len(all_inputs) + len(all_clickable)
                    self.root.after(0, lambda: self.log(f"📊 檢測到可點擊元素: button={len(all_buttons)}, input={len(all_inputs)}, clickable={len(all_clickable)}, 總計={total_elements}"))
                    
                    # 3. 分析每個按鈕
                    submit_candidates = []
                    
                    for i, button in enumerate(all_buttons):
                        try:
                            text = button.text.strip()
                            value = button.get_attribute('value') or ''
                            onclick = button.get_attribute('onclick') or ''
                            class_name = button.get_attribute('class') or ''
                            is_visible = button.is_displayed()
                            
                            # 檢查是否是送出相關按鈕
                            all_text = f"{text} {value} {onclick} {class_name}".lower()
                            submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok']
                            
                            found_keywords = [kw for kw in submit_keywords if kw in all_text]
                            
                            self.root.after(0, lambda i=i, txt=text, val=value, vis=is_visible, kws=found_keywords:
                                           self.log(f"  按鈕 {i+1}: '{txt}' value='{val}' visible={vis} 匹配關鍵字={kws}"))
                            
                            if found_keywords and is_visible:
                                submit_candidates.append({
                                    'element': button,
                                    'text': text,
                                    'value': value,
                                    'keywords': found_keywords
                                })
                                
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"  按鈕 {i+1} 分析失敗: {e}"))
                    
                    # 4. 檢查 input 元素
                    for i, input_elem in enumerate(all_inputs):
                        try:
                            value = input_elem.get_attribute('value') or ''
                            input_type = input_elem.get_attribute('type') or ''
                            is_visible = input_elem.is_displayed()
                            
                            submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save']
                            found_keywords = [kw for kw in submit_keywords if kw in value.lower()]
                            
                            self.root.after(0, lambda i=i, val=value, typ=input_type, vis=is_visible, kws=found_keywords:
                                           self.log(f"  Input {i+1}: type='{typ}' value='{val}' visible={vis} 匹配關鍵字={kws}"))
                            
                            if found_keywords and is_visible:
                                submit_candidates.append({
                                    'element': input_elem,
                                    'text': '',
                                    'value': value,
                                    'keywords': found_keywords
                                })
                                
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"  Input {i+1} 分析失敗: {e}"))
                    
                    # 5. 總結結果
                    self.root.after(0, lambda: self.log(f"\n🎯 檢測結果總結:"))
                    self.root.after(0, lambda: self.log(f"  找到 {len(submit_candidates)} 個送出按鈕候選"))
                    
                    for i, candidate in enumerate(submit_candidates):
                        text_or_value = candidate['text'] or candidate['value']
                        self.root.after(0, lambda i=i, txt=text_or_value, kws=candidate['keywords']:
                                       self.log(f"  候選 {i+1}: '{txt}' 匹配關鍵字={kws}"))
                    
                    if not submit_candidates:
                        self.root.after(0, lambda: self.log("❌ 沒有找到送出按鈕"))
                        self.root.after(0, lambda: self.log("💡 建議："))
                        self.root.after(0, lambda: self.log("  1. 確保編輯彈窗完全載入"))
                        self.root.after(0, lambda: self.log("  2. 檢查是否需要先輸入驗證碼"))
                        self.root.after(0, lambda: self.log("  3. 等待更長時間讓內容載入"))
                    else:
                        self.root.after(0, lambda: self.log("✅ 找到送出按鈕候選！"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_btn.config(state="normal"))
            
            threading.Thread(target=detect_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_btn.config(state="normal")
    
    def take_screenshot(self):
        """截圖"""
        try:
            self.log("📸 截圖當前狀態...")
            
            from mvp_grabber import driver
            
            if not driver:
                self.log("❌ 瀏覽器未啟動")
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            screenshot_path = f"screenshots/final_test_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            
            driver.save_screenshot(screenshot_path)
            self.log(f"✅ 截圖已保存: {screenshot_path}")
            
        except Exception as e:
            self.log(f"❌ 截圖失敗: {e}")
    
    def close_app(self):
        """關閉應用"""
        self.root.destroy()
        
    def run(self):
        """運行 GUI"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🎯 啟動最終送出按鈕檢測測試")
    
    try:
        app = FinalSubmitButtonTest()
        app.run()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")

if __name__ == "__main__":
    main()
