2025-07-01 15:12:50,820 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_151250.log
2025-07-01 15:13:27,434 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 15:13:27,435 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 15:13:28,161 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 15:13:28,161 - DEBUG - chromedriver not found in PATH
2025-07-01 15:13:28,161 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 15:13:28,161 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 15:13:28,162 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 15:13:28,162 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 15:13:28,162 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 15:13:28,162 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 15:13:28,162 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 15:13:28,166 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 11432 using 0 to output -3
2025-07-01 15:13:28,686 - DEBUG - POST http://localhost:54087/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 15:13:28,687 - DEBUG - Starting new HTTP connection (1): localhost:54087
2025-07-01 15:13:29,293 - DEBUG - http://localhost:54087 "POST /session HTTP/1.1" 200 0
2025-07-01 15:13:29,293 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir11432_2041648992"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54092"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"e5b6b5fb65e01e285c7c90dfbb507102"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:29,293 - DEBUG - Finished Request
2025-07-01 15:13:29,294 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 15:13:30,640 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:30,640 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:30,640 - DEBUG - Finished Request
2025-07-01 15:13:30,641 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 15:13:30,641 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 15:13:30,650 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync HTTP/1.1" 200 0
2025-07-01 15:13:30,650 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:30,651 - DEBUG - Finished Request
2025-07-01 15:13:30,651 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 15:13:30,652 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:30,719 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:30,719 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:30,720 - DEBUG - Finished Request
2025-07-01 15:13:31,721 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:31,730 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:31,730 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:31,731 - DEBUG - Finished Request
2025-07-01 15:13:32,731 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:32,736 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:32,737 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:32,737 - DEBUG - Finished Request
2025-07-01 15:13:33,738 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:33,745 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:33,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:33,745 - DEBUG - Finished Request
2025-07-01 15:13:34,746 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:34,752 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:34,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:34,753 - DEBUG - Finished Request
2025-07-01 15:13:35,754 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:35,760 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:35,760 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:35,760 - DEBUG - Finished Request
2025-07-01 15:13:36,761 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:36,768 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:36,769 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:36,769 - DEBUG - Finished Request
2025-07-01 15:13:37,770 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:37,777 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:37,777 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:37,777 - DEBUG - Finished Request
2025-07-01 15:13:38,778 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:38,785 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:38,786 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:38,786 - DEBUG - Finished Request
2025-07-01 15:13:39,787 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:39,795 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:39,795 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:39,796 - DEBUG - Finished Request
2025-07-01 15:13:40,796 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:40,804 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:40,804 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:40,804 - DEBUG - Finished Request
2025-07-01 15:13:41,805 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:41,813 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:41,813 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:41,813 - DEBUG - Finished Request
2025-07-01 15:13:42,814 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:42,822 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:42,823 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:42,823 - DEBUG - Finished Request
2025-07-01 15:13:43,825 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:43,836 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:43,836 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:43,836 - DEBUG - Finished Request
2025-07-01 15:13:44,837 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:44,877 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:44,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:44,877 - DEBUG - Finished Request
2025-07-01 15:13:45,879 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:45,895 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:45,895 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:45,896 - DEBUG - Finished Request
2025-07-01 15:13:46,897 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:46,903 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:46,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:46,904 - DEBUG - Finished Request
2025-07-01 15:13:47,905 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:47,912 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:47,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:47,912 - DEBUG - Finished Request
2025-07-01 15:13:48,913 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:48,922 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:48,922 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:48,922 - DEBUG - Finished Request
2025-07-01 15:13:49,924 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:49,931 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:49,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:49,931 - DEBUG - Finished Request
2025-07-01 15:13:50,933 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:50,940 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:50,940 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:50,940 - DEBUG - Finished Request
2025-07-01 15:13:51,940 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:51,948 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:51,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:51,949 - DEBUG - Finished Request
2025-07-01 15:13:52,949 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:52,956 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:52,957 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:52,957 - DEBUG - Finished Request
2025-07-01 15:13:53,958 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:53,966 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:53,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:53,966 - DEBUG - Finished Request
2025-07-01 15:13:54,967 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:54,974 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:54,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:54,975 - DEBUG - Finished Request
2025-07-01 15:13:55,976 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:55,985 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:55,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:55,985 - DEBUG - Finished Request
2025-07-01 15:13:56,986 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:56,993 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:56,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:56,993 - DEBUG - Finished Request
2025-07-01 15:13:57,994 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:58,001 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:58,001 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:58,001 - DEBUG - Finished Request
2025-07-01 15:13:59,003 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:13:59,011 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:13:59,011 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:13:59,011 - DEBUG - Finished Request
2025-07-01 15:14:00,012 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:00,020 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:00,020 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:00,020 - DEBUG - Finished Request
2025-07-01 15:14:01,022 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:01,031 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:01,031 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:01,032 - DEBUG - Finished Request
2025-07-01 15:14:02,033 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:02,040 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:02,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:02,040 - DEBUG - Finished Request
2025-07-01 15:14:03,042 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:03,074 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:03,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:03,075 - DEBUG - Finished Request
2025-07-01 15:14:04,076 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:04,082 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:04,082 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:04,082 - DEBUG - Finished Request
2025-07-01 15:14:05,083 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:05,090 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:05,090 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:05,090 - DEBUG - Finished Request
2025-07-01 15:14:06,091 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:06,098 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:06,098 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:06,098 - DEBUG - Finished Request
2025-07-01 15:14:07,099 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:07,108 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:07,108 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:07,108 - DEBUG - Finished Request
2025-07-01 15:14:08,109 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:08,117 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:08,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:08,117 - DEBUG - Finished Request
2025-07-01 15:14:09,117 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:09,124 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:09,124 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:09,125 - DEBUG - Finished Request
2025-07-01 15:14:10,125 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:10,133 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:10,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:10,133 - DEBUG - Finished Request
2025-07-01 15:14:11,134 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:11,140 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:11,140 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:11,140 - DEBUG - Finished Request
2025-07-01 15:14:12,141 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:12,145 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:12,146 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:12,146 - DEBUG - Finished Request
2025-07-01 15:14:13,147 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:13,154 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:13,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:13,154 - DEBUG - Finished Request
2025-07-01 15:14:14,156 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:14,163 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:14,163 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:14,164 - DEBUG - Finished Request
2025-07-01 15:14:15,164 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:15,171 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:15,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:15,171 - DEBUG - Finished Request
2025-07-01 15:14:16,173 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:16,181 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:16,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:16,182 - DEBUG - Finished Request
2025-07-01 15:14:17,184 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:17,193 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:17,194 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:17,194 - DEBUG - Finished Request
2025-07-01 15:14:18,195 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:18,203 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:18,203 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:18,204 - DEBUG - Finished Request
2025-07-01 15:14:19,204 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:19,213 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:19,214 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:19,214 - DEBUG - Finished Request
2025-07-01 15:14:20,215 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:20,223 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:20,223 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:20,223 - DEBUG - Finished Request
2025-07-01 15:14:21,224 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:21,231 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:21,232 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:21,232 - DEBUG - Finished Request
2025-07-01 15:14:22,233 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:22,240 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:22,240 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:22,241 - DEBUG - Finished Request
2025-07-01 15:14:23,242 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:23,249 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:23,249 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:23,250 - DEBUG - Finished Request
2025-07-01 15:14:24,250 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:24,257 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:24,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:24,258 - DEBUG - Finished Request
2025-07-01 15:14:25,259 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:25,267 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:25,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:25,268 - DEBUG - Finished Request
2025-07-01 15:14:26,268 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:26,275 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:26,275 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:26,275 - DEBUG - Finished Request
2025-07-01 15:14:27,277 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:27,283 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:27,284 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:27,284 - DEBUG - Finished Request
2025-07-01 15:14:28,285 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:28,932 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:28,933 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:28,933 - DEBUG - Finished Request
2025-07-01 15:14:29,934 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:29,943 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:29,944 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:29,944 - DEBUG - Finished Request
2025-07-01 15:14:30,945 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:30,953 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:30,953 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:30,953 - DEBUG - Finished Request
2025-07-01 15:14:31,954 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:31,966 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:31,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:31,966 - DEBUG - Finished Request
2025-07-01 15:14:32,967 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:32,973 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:32,973 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:32,974 - DEBUG - Finished Request
2025-07-01 15:14:33,975 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:33,982 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:33,982 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:33,982 - DEBUG - Finished Request
2025-07-01 15:14:34,983 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:34,989 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:34,990 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:34,990 - DEBUG - Finished Request
2025-07-01 15:14:35,991 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:36,000 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:36,001 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:36,002 - DEBUG - Finished Request
2025-07-01 15:14:37,002 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:37,010 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:37,010 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:37,010 - DEBUG - Finished Request
2025-07-01 15:14:38,012 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:38,019 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:38,019 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:38,020 - DEBUG - Finished Request
2025-07-01 15:14:39,020 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:39,027 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:39,028 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:39,028 - DEBUG - Finished Request
2025-07-01 15:14:40,029 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:40,035 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:40,035 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:40,036 - DEBUG - Finished Request
2025-07-01 15:14:41,037 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:41,044 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:41,044 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:41,044 - DEBUG - Finished Request
2025-07-01 15:14:42,046 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:42,057 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:42,057 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:42,057 - DEBUG - Finished Request
2025-07-01 15:14:43,058 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:43,064 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:43,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:43,064 - DEBUG - Finished Request
2025-07-01 15:14:44,066 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:44,074 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:44,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:44,075 - DEBUG - Finished Request
2025-07-01 15:14:45,075 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:45,082 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:45,082 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:45,083 - DEBUG - Finished Request
2025-07-01 15:14:46,084 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:46,091 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:46,091 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:46,092 - DEBUG - Finished Request
2025-07-01 15:14:47,093 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:47,098 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:47,099 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:47,099 - DEBUG - Finished Request
2025-07-01 15:14:48,100 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:48,106 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:48,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,106 - DEBUG - Finished Request
2025-07-01 15:14:48,361 - INFO - 🎯 用戶點擊準備完成按鈕，開始詳細檢測...
2025-07-01 15:14:48,362 - INFO - 🔍 [用戶點擊準備完成] 開始記錄頁面內容...
2025-07-01 15:14:48,362 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:48,376 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:48,376 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,377 - DEBUG - Finished Request
2025-07-01 15:14:48,377 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/title {}
2025-07-01 15:14:48,383 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/title HTTP/1.1" 200 0
2025-07-01 15:14:48,384 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,384 - DEBUG - Finished Request
2025-07-01 15:14:48,384 - INFO - 🔍 [用戶點擊準備完成] 當前 URL: https://wmc.kcg.gov.tw/
2025-07-01 15:14:48,384 - INFO - 🔍 [用戶點擊準備完成] 頁面標題: 高雄市廢棄物調度中心
2025-07-01 15:14:48,385 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/source {}
2025-07-01 15:14:48,388 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/source HTTP/1.1" 200 0
2025-07-01 15:14:48,388 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '0ubmbGQKd3Ph_c256n27vxsZU1xvrymiLJrjqOI3Ow2kex7Ow311jvPOzSBGJsNcfYKWWH2U9CrxXb8MYAVhU_GRw7mBbDxncHEiPWk3j7A1:Qcoza4mZH3qC6ghPemX_lLan2-xcK7jsSWEXOspR2yKyrdieVpTAZdVUYFn-TKvDhn7CpFpnMtDCtgyrtbReSGQpIjBvb5rG3b93j279Aqc1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 15:14:29\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95841b54e9598285&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,389 - DEBUG - Finished Request
2025-07-01 15:14:48,389 - INFO - 🔍 [用戶點擊準備完成] page_source 長度: 8479
2025-07-01 15:14:48,389 - INFO - 🔍 [用戶點擊準備完成] page_source 包含 E48B: False
2025-07-01 15:14:48,390 - INFO - 🔍 [用戶點擊準備完成] page_source 包含目標訂單: False
2025-07-01 15:14:48,390 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-07-01 15:14:48,396 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync HTTP/1.1" 200 0
2025-07-01 15:14:48,397 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/07/01 15:14:29\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,397 - DEBUG - Finished Request
2025-07-01 15:14:48,397 - INFO - 🔍 [用戶點擊準備完成] innerText 長度: 97
2025-07-01 15:14:48,397 - INFO - 🔍 [用戶點擊準備完成] innerText 包含 E48B: False
2025-07-01 15:14:48,398 - INFO - 🔍 [用戶點擊準備完成] innerText 包含目標訂單: False
2025-07-01 15:14:48,398 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'tag name', 'value': 'table'}
2025-07-01 15:14:48,407 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,407 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,408 - DEBUG - Finished Request
2025-07-01 15:14:48,408 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'tag name', 'value': 'tr'}
2025-07-01 15:14:48,416 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,416 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,417 - DEBUG - Finished Request
2025-07-01 15:14:48,417 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個表格，0 個表格行
2025-07-01 15:14:48,417 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 15:14:48,428 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,428 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,428 - DEBUG - Finished Request
2025-07-01 15:14:48,429 - INFO - 🔍 [用戶點擊準備完成] 包含 'E48B' 的元素數量: 0
2025-07-01 15:14:48,429 - INFO - 🔍 [用戶點擊準備完成] innerText 前300字符:
2025-07-01 15:14:48,429 - INFO - 🔍 [用戶點擊準備完成]  環碩環保工程股份有限公司|郭炯宏 2025/07/01 15:14:29

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-07-01 15:14:48,429 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-07-01 15:14:48,438 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,439 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,439 - DEBUG - Finished Request
2025-07-01 15:14:48,439 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個編輯按鈕
2025-07-01 15:14:48,439 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'tag name', 'value': 'iframe'}
2025-07-01 15:14:48,448 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,449 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.253B3B3768BDF69EE1439EB5E350B289.d.D11162ED8BA89345D850208867DE56ED.e.38"}]} | headers=HTTPHeaderDict({'Content-Length': '128', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,449 - DEBUG - Finished Request
2025-07-01 15:14:48,449 - INFO - 🔍 [用戶點擊準備完成] 檢測到 1 個 iframe
2025-07-01 15:14:48,473 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.253B3B3768BDF69EE1439EB5E350B289.d.D11162ED8BA89345D850208867DE56ED.e.38'}, 'id']}
2025-07-01 15:14:48,481 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync HTTP/1.1" 200 0
2025-07-01 15:14:48,482 - DEBUG - Remote response: status=200 | data={"value":"frameid"} | headers=HTTPHeaderDict({'Content-Length': '19', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,482 - DEBUG - Finished Request
2025-07-01 15:14:48,482 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.253B3B3768BDF69EE1439EB5E350B289.d.D11162ED8BA89345D850208867DE56ED.e.38'}, 'src']}
2025-07-01 15:14:48,490 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync HTTP/1.1" 200 0
2025-07-01 15:14:48,490 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00"} | headers=HTTPHeaderDict({'Content-Length': '58', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,490 - DEBUG - Finished Request
2025-07-01 15:14:48,490 - INFO - 🔍 [用戶點擊準備完成] iframe 1: id='frameid', src='https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00'
2025-07-01 15:14:48,490 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/frame {'id': {'element-6066-11e4-a52e-4f735466cecf': 'f.253B3B3768BDF69EE1439EB5E350B289.d.D11162ED8BA89345D850208867DE56ED.e.38'}}
2025-07-01 15:14:48,510 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/frame HTTP/1.1" 200 0
2025-07-01 15:14:48,511 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,511 - DEBUG - Finished Request
2025-07-01 15:14:48,511 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-07-01 15:14:48,519 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync HTTP/1.1" 200 0
2025-07-01 15:14:48,519 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n7月\n8月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t20.49\t148.536\t0\t1177.474\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271670\t仁\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271669\t南\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271668\t仁\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271667\t南\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271666\t仁\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271665\t南\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271664\t仁\tKEP-2560\t一般清運\t6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271663\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191221\t南\t119-BR\t一般清運\t3.6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191220\t岡\t119-BR\t一般清運\t3.6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191219\t南\t121-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191218\t岡\t121-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191217\t南\tKEH-9230\t一般清運\t3.7\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191207\t南\t120-BR\t一般清運\t3.2\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191206\t仁\t120-BR\t一般清運\t3.2\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191204\t岡\t120-BR\t一般清運\t3.2\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121161\t仁\t937-N6\t一般清運\t4.9\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121160\t南\t937-N6\t一般清運\t4.9\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121157\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121156\t仁\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121155\t南\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121153\t南\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121152\t仁\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121151\t仁\tKED-9670\t一般清運\t5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121149\t仁\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121148\t南\tKEJ-5580\t一般清運\t7\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121146\t南\t121-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121145\t仁\t121-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121143\t岡\t121-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121142\t岡\t121-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121140\t仁\t121-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121137\t南\t121-BR\t一般清運\t3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061576\t岡\t117-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061575\t南\t117-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061574\t南\t937-N6\t一般清運\t4.9\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061573\t仁\t937-N6\t一般清運\t4.9\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061572\t岡\t937-N6\t一般清運\t4.9\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061571\t南\t937-N6\t一般清運\t4.9\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061567\t岡\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061566\t南\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270668\t岡\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270664\t南\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270660\t岡\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270659\t仁\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270655\t仁\tKEP-2808\t一般清運\t2\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405270654\t仁\t129-BR\t一般清運\t4.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270652\t岡\tKEP-2560\t一般清運\t6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405260922\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405260920\t南\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405260918\t仁\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405250276\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200980\t岡\tKEH-9230\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200979\t仁\tKEH-9230\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200978\t南\tKEH-9230\t一般清運\t3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200977\t南\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200976\t仁\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200975\t南\tKEB-6030\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200974\t岡\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200973\t仁\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200972\t南\tKEB-6030\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200971\t岡\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200970\t仁\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200969\t南\tKED-9670\t一般清運\t5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150349\t仁\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150344\t岡\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150341\t岡\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150339\t仁\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150336\t南\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150333\t南\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150331\t仁\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405121110\t南\t117-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405121109\t仁\t117-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405121106\t岡\t117-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020876\t南\t119-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020875\t仁\t119-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020874\t岡\t119-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020872\t岡\t119-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020871\t仁\t119-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404220584\t南\t119-BR\t一般清運\t3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404220582\t南\t120-BR\t一般清運\t3.2\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404150151\t仁\t120-BR\t一般清運\t3.2\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404150148\t岡\t120-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311318\t南\t129-BR\t一般清運\t4.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311308\t南\t129-BR\t一般清運\t4.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311301\t岡\t129-BR\t一般清運\t4.3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311297\t南\t129-BR\t一般清運\t4.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311260\t仁\t129-BR\t一般清運\t4.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611402171100\t南\t129-BR\t一般清運\t4\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402171091\t仁\tKEP-2560\t一般清運\t6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402101032\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060832\t南\tKEP-2560\t一般清運\t5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060792\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060773\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060772\t岡\tKEP-2560\t一般清運\t6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060764\t南\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060756\t仁\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060755\t岡\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060731\t岡\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060730\t仁\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060729\t南\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060728\t南\tKER-2807\t一般清運\t7\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060727\t仁\tKER-2807\t一般清運\t7\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060725\t岡\tKER-2807\t一般清運\t6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060724\t岡\tKER-2807\t一般清運\t7\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060723\t仁\tKER-2807\t一般清運\t7\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060722\t南\tKER-2807\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n顯示第 1 至 106 項結果，共 106 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '17502', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,520 - DEBUG - Finished Request
2025-07-01 15:14:48,521 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'tag name', 'value': 'table'}
2025-07-01 15:14:48,529 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,529 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.131"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.132"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,529 - DEBUG - Finished Request
2025-07-01 15:14:48,529 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'tag name', 'value': 'tr'}
2025-07-01 15:14:48,538 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,538 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.170"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.172"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.239"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.241"}]} | headers=HTTPHeaderDict({'Content-Length': '12873', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,539 - DEBUG - Finished Request
2025-07-01 15:14:48,540 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-07-01 15:14:48,554 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,554 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.242"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.243"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.277"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.278"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.279"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.280"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.281"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.282"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.283"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.284"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.285"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.286"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.287"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.288"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.289"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.290"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.291"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.292"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.293"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.294"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.295"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.296"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.297"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.298"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.299"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.300"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.301"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.302"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.303"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.304"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.305"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.306"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.307"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.308"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.309"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.310"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.311"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.312"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.313"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.314"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.315"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.316"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.317"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.318"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.319"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.320"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.321"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.322"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.323"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.324"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.325"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.326"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.327"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.328"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.329"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.330"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.331"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.332"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.333"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.334"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.335"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.336"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.337"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.338"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.339"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.340"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.341"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.342"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.343"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.344"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.345"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.346"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.347"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.348"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.349"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.350"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.351"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.352"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.353"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.354"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.355"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.356"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.357"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.358"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.359"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.360"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.361"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.362"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.363"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.364"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.365"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.366"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.367"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.368"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.369"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.370"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.371"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.372"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.373"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.374"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.375"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.376"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.377"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.378"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.379"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.380"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.381"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.382"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.383"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.384"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.385"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.386"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.387"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.388"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.389"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.390"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.391"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.392"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.393"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.394"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.395"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.396"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.397"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.398"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.399"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.400"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.401"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.402"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.403"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.404"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.405"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.406"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.407"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.408"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.409"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.410"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.411"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.412"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.413"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.414"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.415"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.416"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.417"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.418"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.419"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.420"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.421"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.422"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.423"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.424"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.425"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.426"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.427"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.428"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.429"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.430"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.431"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.432"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.433"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.434"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.435"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.436"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.437"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.438"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.439"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.440"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.441"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.442"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.443"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.444"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.445"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.446"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.447"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.448"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.449"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.450"}]} | headers=HTTPHeaderDict({'Content-Length': '24791', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,556 - DEBUG - Finished Request
2025-07-01 15:14:48,556 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 15:14:48,566 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:48,566 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.451"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.452"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.453"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.454"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.455"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.456"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.457"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.458"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.459"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.460"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.461"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.462"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.463"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.464"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.465"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.466"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.467"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.468"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.469"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.470"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.471"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.472"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.473"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.474"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.475"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.476"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.477"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.478"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.479"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.480"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.481"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.482"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.483"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.484"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.485"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.486"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.487"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.488"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.489"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.490"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.491"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.492"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.493"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.494"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.495"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.496"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.497"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.498"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.499"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.500"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.501"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.502"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.503"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.504"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.505"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.506"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.507"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.508"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.509"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.510"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.511"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.512"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.513"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.514"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.515"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.516"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.517"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.518"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.519"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.520"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.521"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.522"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.523"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.524"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.525"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.526"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.527"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.528"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.529"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.530"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.531"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.532"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.533"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.534"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.535"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.536"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.537"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.538"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.539"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.540"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.541"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.542"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.543"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.544"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.545"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.546"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.547"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.548"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.549"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.550"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.551"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.552"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.553"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.554"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.555"},{"element-6066-11e4-a52e-4f735466cecf":"f.E382F37D1721ACBEC211BA1C12BA3A17.d.F2F96228916FAC0FD363169966ABCB37.e.556"}]} | headers=HTTPHeaderDict({'Content-Length': '12519', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,568 - DEBUG - Finished Request
2025-07-01 15:14:48,568 - INFO - 🔍 [用戶點擊準備完成] iframe 1 內容:
2025-07-01 15:14:48,568 - INFO - 🔍 [用戶點擊準備完成]   - 文字長度: 9517
2025-07-01 15:14:48,568 - INFO - 🔍 [用戶點擊準備完成]   - 包含目標訂單: False
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - 包含 E48B: True
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - 表格數量: 2
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - 表格行數: 109
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - 編輯按鈕數量: 210
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - E48B 元素數量: 106
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - 內容前300字符: 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

7月
8月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	20.49	148.536	0	1177.474
每日開放查詢時日...
2025-07-01 15:14:48,569 - INFO - 🔍 [用戶點擊準備完成]   - 內容後300字符: ...		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611402060724	岡	KER-2807	一般清運	7	2025-07-08		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611402060723	仁	KER-2807	一般清運	7	2025-07-08		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611402060722	南	KER-2807	一般清運	6	2025-07-08		高南廠	0	一般	取消(刪除)
顯示第 1 至 106 項結果，共 106 項
上一頁
1
下一頁
2025-07-01 15:14:48,570 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/frame {'id': None}
2025-07-01 15:14:48,572 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/frame HTTP/1.1" 200 0
2025-07-01 15:14:48,572 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:48,572 - DEBUG - Finished Request
2025-07-01 15:14:48,572 - INFO - 🔍 [用戶點擊準備完成] iframe 1 檢測完成，已切換回主頁面
2025-07-01 15:14:49,108 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:49,115 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:49,115 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:49,115 - DEBUG - Finished Request
2025-07-01 15:14:50,117 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:50,125 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:50,126 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:50,126 - DEBUG - Finished Request
2025-07-01 15:14:50,574 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:50,582 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:50,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:50,582 - DEBUG - Finished Request
2025-07-01 15:14:50,583 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/title {}
2025-07-01 15:14:50,587 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/title HTTP/1.1" 200 0
2025-07-01 15:14:50,587 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:50,587 - DEBUG - Finished Request
2025-07-01 15:14:50,588 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//table'}
2025-07-01 15:14:50,595 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:50,595 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:50,595 - DEBUG - Finished Request
2025-07-01 15:14:50,595 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 15:14:50,603 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:50,603 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:50,603 - DEBUG - Finished Request
2025-07-01 15:14:50,603 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/source {}
2025-07-01 15:14:50,606 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/source HTTP/1.1" 200 0
2025-07-01 15:14:50,606 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '0ubmbGQKd3Ph_c256n27vxsZU1xvrymiLJrjqOI3Ow2kex7Ow311jvPOzSBGJsNcfYKWWH2U9CrxXb8MYAVhU_GRw7mBbDxncHEiPWk3j7A1:Qcoza4mZH3qC6ghPemX_lLan2-xcK7jsSWEXOspR2yKyrdieVpTAZdVUYFn-TKvDhn7CpFpnMtDCtgyrtbReSGQpIjBvb5rG3b93j279Aqc1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 15:14:29\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"c4989a9e3302da6b270e61467d2d91d1\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95841b54e9598285&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:50,607 - DEBUG - Finished Request
2025-07-01 15:14:51,127 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:51,133 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:51,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:51,133 - DEBUG - Finished Request
2025-07-01 15:14:52,134 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:52,140 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:52,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:52,141 - DEBUG - Finished Request
2025-07-01 15:14:52,609 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//table'}
2025-07-01 15:14:52,619 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:52,619 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:52,619 - DEBUG - Finished Request
2025-07-01 15:14:52,620 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 15:14:52,627 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:52,627 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:52,627 - DEBUG - Finished Request
2025-07-01 15:14:52,627 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/source {}
2025-07-01 15:14:52,630 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/source HTTP/1.1" 200 0
2025-07-01 15:14:52,630 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '0ubmbGQKd3Ph_c256n27vxsZU1xvrymiLJrjqOI3Ow2kex7Ow311jvPOzSBGJsNcfYKWWH2U9CrxXb8MYAVhU_GRw7mBbDxncHEiPWk3j7A1:Qcoza4mZH3qC6ghPemX_lLan2-xcK7jsSWEXOspR2yKyrdieVpTAZdVUYFn-TKvDhn7CpFpnMtDCtgyrtbReSGQpIjBvb5rG3b93j279Aqc1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 15:14:29\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"c4989a9e3302da6b270e61467d2d91d1\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95841b54e9598285&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:52,631 - DEBUG - Finished Request
2025-07-01 15:14:53,142 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:53,149 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:53,149 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:53,150 - DEBUG - Finished Request
2025-07-01 15:14:54,151 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:54,157 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:54,157 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:54,157 - DEBUG - Finished Request
2025-07-01 15:14:54,632 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//table'}
2025-07-01 15:14:54,640 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:54,640 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:54,640 - DEBUG - Finished Request
2025-07-01 15:14:54,640 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 15:14:54,646 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:14:54,647 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:54,647 - DEBUG - Finished Request
2025-07-01 15:14:54,647 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/source {}
2025-07-01 15:14:54,649 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/source HTTP/1.1" 200 0
2025-07-01 15:14:54,650 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '0ubmbGQKd3Ph_c256n27vxsZU1xvrymiLJrjqOI3Ow2kex7Ow311jvPOzSBGJsNcfYKWWH2U9CrxXb8MYAVhU_GRw7mBbDxncHEiPWk3j7A1:Qcoza4mZH3qC6ghPemX_lLan2-xcK7jsSWEXOspR2yKyrdieVpTAZdVUYFn-TKvDhn7CpFpnMtDCtgyrtbReSGQpIjBvb5rG3b93j279Aqc1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 15:14:29\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"c4989a9e3302da6b270e61467d2d91d1\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95841b54e9598285&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:54,650 - DEBUG - Finished Request
2025-07-01 15:14:55,158 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:55,166 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:55,166 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:55,167 - DEBUG - Finished Request
2025-07-01 15:14:56,167 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:56,176 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:56,176 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:56,176 - DEBUG - Finished Request
2025-07-01 15:14:57,177 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:57,185 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:57,185 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:57,185 - DEBUG - Finished Request
2025-07-01 15:14:58,186 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:58,192 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:58,192 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:58,192 - DEBUG - Finished Request
2025-07-01 15:14:59,193 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:14:59,199 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:14:59,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:14:59,199 - DEBUG - Finished Request
2025-07-01 15:15:00,201 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:00,208 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:00,208 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:00,208 - DEBUG - Finished Request
2025-07-01 15:15:01,210 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:01,216 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:01,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:01,216 - DEBUG - Finished Request
2025-07-01 15:15:02,217 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:02,227 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:02,227 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:02,228 - DEBUG - Finished Request
2025-07-01 15:15:03,229 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,237 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,238 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,238 - DEBUG - Finished Request
2025-07-01 15:15:03,745 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,750 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,751 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,751 - DEBUG - Finished Request
2025-07-01 15:15:03,751 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,756 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,757 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,757 - DEBUG - Finished Request
2025-07-01 15:15:03,757 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,762 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,762 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,762 - DEBUG - Finished Request
2025-07-01 15:15:03,762 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 15:15:03,773 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,773 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,774 - DEBUG - Finished Request
2025-07-01 15:15:03,774 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 15:15:03,781 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,781 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,781 - DEBUG - Finished Request
2025-07-01 15:15:03,781 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 15:15:03,788 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,788 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,788 - DEBUG - Finished Request
2025-07-01 15:15:03,789 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 15:15:03,796 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,796 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,796 - DEBUG - Finished Request
2025-07-01 15:15:03,796 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': '.btn-edit'}
2025-07-01 15:15:03,806 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,806 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,806 - DEBUG - Finished Request
2025-07-01 15:15:03,807 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'a[href*="edit"]'}
2025-07-01 15:15:03,816 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,816 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,816 - DEBUG - Finished Request
2025-07-01 15:15:03,817 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,822 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,822 - DEBUG - Finished Request
2025-07-01 15:15:03,822 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[name*="captcha"]'}
2025-07-01 15:15:03,832 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,832 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,832 - DEBUG - Finished Request
2025-07-01 15:15:03,833 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[id*="captcha"]'}
2025-07-01 15:15:03,842 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,842 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,843 - DEBUG - Finished Request
2025-07-01 15:15:03,843 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證"]'}
2025-07-01 15:15:03,853 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,853 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,853 - DEBUG - Finished Request
2025-07-01 15:15:03,853 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證碼"]'}
2025-07-01 15:15:03,862 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,862 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,862 - DEBUG - Finished Request
2025-07-01 15:15:03,862 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[name*="code"]'}
2025-07-01 15:15:03,872 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,872 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,873 - DEBUG - Finished Request
2025-07-01 15:15:03,873 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[name*="verify"]'}
2025-07-01 15:15:03,882 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,882 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,882 - DEBUG - Finished Request
2025-07-01 15:15:03,883 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="4"]'}
2025-07-01 15:15:03,891 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,891 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,892 - DEBUG - Finished Request
2025-07-01 15:15:03,892 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="5"]'}
2025-07-01 15:15:03,902 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,902 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,902 - DEBUG - Finished Request
2025-07-01 15:15:03,903 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,907 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,907 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,908 - DEBUG - Finished Request
2025-07-01 15:15:03,908 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'img[src*="captcha"]'}
2025-07-01 15:15:03,917 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,918 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,918 - DEBUG - Finished Request
2025-07-01 15:15:03,918 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'img[src*="verify"]'}
2025-07-01 15:15:03,928 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,929 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,929 - DEBUG - Finished Request
2025-07-01 15:15:03,929 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'img[alt*="驗證"]'}
2025-07-01 15:15:03,939 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,939 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,939 - DEBUG - Finished Request
2025-07-01 15:15:03,940 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'img[alt*="驗證碼"]'}
2025-07-01 15:15:03,951 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,951 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,951 - DEBUG - Finished Request
2025-07-01 15:15:03,951 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:03,955 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:03,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,956 - DEBUG - Finished Request
2025-07-01 15:15:03,956 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '確認取得驗證碼')]"}
2025-07-01 15:15:03,966 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,967 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,967 - DEBUG - Finished Request
2025-07-01 15:15:03,967 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-07-01 15:15:03,976 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,976 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,976 - DEBUG - Finished Request
2025-07-01 15:15:03,978 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-07-01 15:15:03,987 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:03,988 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:03,988 - DEBUG - Finished Request
2025-07-01 15:15:03,988 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': '.captcha-refresh'}
2025-07-01 15:15:04,006 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,006 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,006 - DEBUG - Finished Request
2025-07-01 15:15:04,007 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:04,012 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:04,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,012 - DEBUG - Finished Request
2025-07-01 15:15:04,012 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '送出')]"}
2025-07-01 15:15:04,022 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,023 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,023 - DEBUG - Finished Request
2025-07-01 15:15:04,023 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[value="送出"]'}
2025-07-01 15:15:04,036 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,036 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,036 - DEBUG - Finished Request
2025-07-01 15:15:04,036 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[type="submit"]'}
2025-07-01 15:15:04,049 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,049 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,049 - DEBUG - Finished Request
2025-07-01 15:15:04,049 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'button[type="submit"]'}
2025-07-01 15:15:04,061 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,061 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,061 - DEBUG - Finished Request
2025-07-01 15:15:04,062 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': '.btn-submit'}
2025-07-01 15:15:04,074 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,074 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,074 - DEBUG - Finished Request
2025-07-01 15:15:04,074 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:04,080 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:04,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,080 - DEBUG - Finished Request
2025-07-01 15:15:04,080 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': "//*[contains(text(), '取消')]"}
2025-07-01 15:15:04,092 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,092 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,092 - DEBUG - Finished Request
2025-07-01 15:15:04,092 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'input[value="取消"]'}
2025-07-01 15:15:04,102 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,102 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,102 - DEBUG - Finished Request
2025-07-01 15:15:04,103 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': '.btn-cancel'}
2025-07-01 15:15:04,112 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,112 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,113 - DEBUG - Finished Request
2025-07-01 15:15:04,113 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:04,117 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:04,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,118 - DEBUG - Finished Request
2025-07-01 15:15:04,118 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'table'}
2025-07-01 15:15:04,126 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,126 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,126 - DEBUG - Finished Request
2025-07-01 15:15:04,127 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': '.table'}
2025-07-01 15:15:04,136 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,137 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,137 - DEBUG - Finished Request
2025-07-01 15:15:04,137 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'tbody'}
2025-07-01 15:15:04,147 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,147 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,147 - DEBUG - Finished Request
2025-07-01 15:15:04,147 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'css selector', 'value': 'tr'}
2025-07-01 15:15:04,155 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 200 0
2025-07-01 15:15:04,155 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,155 - DEBUG - Finished Request
2025-07-01 15:15:04,156 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:04,166 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:04,166 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,167 - DEBUG - Finished Request
2025-07-01 15:15:04,238 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:04,245 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:04,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,246 - DEBUG - Finished Request
2025-07-01 15:15:04,668 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:04,678 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:04,679 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:04,679 - DEBUG - Finished Request
2025-07-01 15:15:05,179 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:05,188 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:05,188 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:05,189 - DEBUG - Finished Request
2025-07-01 15:15:05,246 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:05,253 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:05,253 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:05,253 - DEBUG - Finished Request
2025-07-01 15:15:05,690 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:05,704 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:05,704 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:05,704 - DEBUG - Finished Request
2025-07-01 15:15:06,205 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:06,214 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:06,214 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:06,215 - DEBUG - Finished Request
2025-07-01 15:15:06,254 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:06,262 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:06,262 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:06,262 - DEBUG - Finished Request
2025-07-01 15:15:06,715 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:06,725 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:06,725 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:06,726 - DEBUG - Finished Request
2025-07-01 15:15:07,227 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:07,238 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:07,238 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:07,238 - DEBUG - Finished Request
2025-07-01 15:15:07,263 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:07,269 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:07,270 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:07,270 - DEBUG - Finished Request
2025-07-01 15:15:07,739 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:07,750 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:07,750 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:07,750 - DEBUG - Finished Request
2025-07-01 15:15:08,251 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:08,262 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:08,262 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:08,262 - DEBUG - Finished Request
2025-07-01 15:15:08,270 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:08,276 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:08,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:08,276 - DEBUG - Finished Request
2025-07-01 15:15:08,764 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:08,776 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:08,776 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:08,776 - DEBUG - Finished Request
2025-07-01 15:15:09,278 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:09,278 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:09,279 - DEBUG - Starting new HTTP connection (2): localhost:54087
2025-07-01 15:15:09,289 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:09,289 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:09,289 - DEBUG - Finished Request
2025-07-01 15:15:09,293 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:09,293 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 15:15:09,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:09,294 - DEBUG - Finished Request
2025-07-01 15:15:09,791 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:09,803 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:09,803 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:09,803 - DEBUG - Finished Request
2025-07-01 15:15:10,295 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:10,304 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:10,304 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:10,304 - DEBUG - Starting new HTTP connection (3): localhost:54087
2025-07-01 15:15:10,304 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:10,305 - DEBUG - Finished Request
2025-07-01 15:15:10,336 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:10,336 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 15:15:10,336 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:10,336 - DEBUG - Finished Request
2025-07-01 15:15:10,838 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:10,848 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:10,849 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:10,849 - DEBUG - Finished Request
2025-07-01 15:15:11,306 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:11,314 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:11,315 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:11,315 - DEBUG - Finished Request
2025-07-01 15:15:11,350 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:11,359 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:11,359 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:11,359 - DEBUG - Finished Request
2025-07-01 15:15:11,860 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:11,870 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:11,870 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:11,870 - DEBUG - Finished Request
2025-07-01 15:15:12,316 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:12,323 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:12,323 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:12,323 - DEBUG - Finished Request
2025-07-01 15:15:12,371 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:12,380 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:12,381 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:12,381 - DEBUG - Finished Request
2025-07-01 15:15:12,882 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:12,891 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:12,891 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:12,891 - DEBUG - Finished Request
2025-07-01 15:15:13,326 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:13,334 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:13,334 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:13,334 - DEBUG - Finished Request
2025-07-01 15:15:13,392 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:13,402 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:13,402 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:13,402 - DEBUG - Finished Request
2025-07-01 15:15:13,904 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:13,917 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:13,917 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:13,917 - DEBUG - Finished Request
2025-07-01 15:15:14,335 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:14,343 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:14,343 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:14,343 - DEBUG - Finished Request
2025-07-01 15:15:14,418 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:14,427 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:14,427 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:14,427 - DEBUG - Finished Request
2025-07-01 15:15:14,928 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:14,940 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:14,941 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:14,941 - DEBUG - Finished Request
2025-07-01 15:15:15,344 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:15,352 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:15,352 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:15,352 - DEBUG - Finished Request
2025-07-01 15:15:15,441 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:15,451 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:15,451 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:15,451 - DEBUG - Finished Request
2025-07-01 15:15:15,952 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:15,962 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:15,962 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:15,963 - DEBUG - Finished Request
2025-07-01 15:15:16,353 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:16,360 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:16,361 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:16,361 - DEBUG - Finished Request
2025-07-01 15:15:16,464 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:16,474 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:16,474 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:16,474 - DEBUG - Finished Request
2025-07-01 15:15:16,975 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:16,984 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:16,985 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:16,985 - DEBUG - Finished Request
2025-07-01 15:15:17,362 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:17,368 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:17,368 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:17,369 - DEBUG - Finished Request
2025-07-01 15:15:17,486 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:17,496 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:17,496 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:17,496 - DEBUG - Finished Request
2025-07-01 15:15:17,997 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:18,007 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:18,007 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:18,008 - DEBUG - Finished Request
2025-07-01 15:15:18,369 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:18,376 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:18,376 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:18,376 - DEBUG - Finished Request
2025-07-01 15:15:18,508 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:18,517 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:18,517 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:18,517 - DEBUG - Finished Request
2025-07-01 15:15:19,018 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:19,029 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:19,029 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:19,029 - DEBUG - Finished Request
2025-07-01 15:15:19,377 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:19,384 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:19,384 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:19,384 - DEBUG - Finished Request
2025-07-01 15:15:19,531 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:19,543 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:19,544 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:19,544 - DEBUG - Finished Request
2025-07-01 15:15:20,044 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:20,056 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:20,056 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:20,056 - DEBUG - Finished Request
2025-07-01 15:15:20,385 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:20,394 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:20,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:20,394 - DEBUG - Finished Request
2025-07-01 15:15:20,557 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:20,572 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:20,572 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:20,572 - DEBUG - Finished Request
2025-07-01 15:15:21,073 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:21,083 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:21,083 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:21,084 - DEBUG - Finished Request
2025-07-01 15:15:21,396 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:21,403 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:21,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:21,403 - DEBUG - Finished Request
2025-07-01 15:15:21,584 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:21,595 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:21,595 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:21,595 - DEBUG - Finished Request
2025-07-01 15:15:22,096 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:22,109 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:22,109 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:22,109 - DEBUG - Finished Request
2025-07-01 15:15:22,403 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:22,410 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:22,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:22,411 - DEBUG - Finished Request
2025-07-01 15:15:22,611 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:22,623 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:22,623 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:22,623 - DEBUG - Finished Request
2025-07-01 15:15:23,124 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:23,134 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:23,134 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:23,135 - DEBUG - Finished Request
2025-07-01 15:15:23,412 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:23,422 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:23,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:23,422 - DEBUG - Finished Request
2025-07-01 15:15:23,635 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:23,647 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:23,648 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:23,648 - DEBUG - Finished Request
2025-07-01 15:15:24,148 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:24,158 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:24,158 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:24,158 - DEBUG - Finished Request
2025-07-01 15:15:24,422 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:24,429 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:24,429 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:24,429 - DEBUG - Finished Request
2025-07-01 15:15:24,659 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:24,669 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:24,670 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:24,670 - DEBUG - Finished Request
2025-07-01 15:15:25,171 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:25,182 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:25,183 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:25,183 - DEBUG - Finished Request
2025-07-01 15:15:25,430 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:25,438 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:25,438 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:25,438 - DEBUG - Finished Request
2025-07-01 15:15:25,683 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:25,692 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:25,692 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:25,692 - DEBUG - Finished Request
2025-07-01 15:15:26,194 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:26,206 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:26,206 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:26,206 - DEBUG - Finished Request
2025-07-01 15:15:26,439 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:26,447 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 200 0
2025-07-01 15:15:26,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:26,448 - DEBUG - Finished Request
2025-07-01 15:15:26,708 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:26,717 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:26,717 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:26,717 - DEBUG - Finished Request
2025-07-01 15:15:27,217 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 15:15:27,244 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:27,244 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:27,244 - DEBUG - Finished Request
2025-07-01 15:15:27,245 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 15:15:27,277 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/element HTTP/1.1" 404 0
2025-07-01 15:15:27,277 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b99fc]\n\t(No symbol) [0x0x7ff6918007df]\n\t(No symbol) [0x0x7ff691838a52]\n\t(No symbol) [0x0x7ff691833413]\n\t(No symbol) [0x0x7ff6918324d9]\n\t(No symbol) [0x0x7ff691785d55]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\t(No symbol) [0x0x7ff691784dca]\n\tGetHandleVerifier [0x0x7ff691df45e8+4238440]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:27,278 - DEBUG - Finished Request
2025-07-01 15:15:27,278 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 15:15:27,279 - DEBUG - http://localhost:54087 "POST /session/e5b6b5fb65e01e285c7c90dfbb507102/elements HTTP/1.1" 404 0
2025-07-01 15:15:27,279 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b99fc]\n\t(No symbol) [0x0x7ff6918007df]\n\t(No symbol) [0x0x7ff691838a52]\n\t(No symbol) [0x0x7ff691833413]\n\t(No symbol) [0x0x7ff6918324d9]\n\t(No symbol) [0x0x7ff691785d55]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\t(No symbol) [0x0x7ff691784dca]\n\tGetHandleVerifier [0x0x7ff691df45e8+4238440]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:27,279 - DEBUG - Finished Request
2025-07-01 15:15:27,448 - DEBUG - GET http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/url {}
2025-07-01 15:15:27,450 - DEBUG - http://localhost:54087 "GET /session/e5b6b5fb65e01e285c7c90dfbb507102/url HTTP/1.1" 404 0
2025-07-01 15:15:27,450 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b99fc]\n\t(No symbol) [0x0x7ff6918007df]\n\t(No symbol) [0x0x7ff691838a52]\n\t(No symbol) [0x0x7ff691833413]\n\t(No symbol) [0x0x7ff6918324d9]\n\t(No symbol) [0x0x7ff691785d55]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\t(No symbol) [0x0x7ff691784dca]\n\tGetHandleVerifier [0x0x7ff691df45e8+4238440]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:27,450 - DEBUG - Finished Request
2025-07-01 15:15:27,451 - DEBUG - DELETE http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102 {}
2025-07-01 15:15:27,452 - DEBUG - http://localhost:54087 "DELETE /session/e5b6b5fb65e01e285c7c90dfbb507102 HTTP/1.1" 200 0
2025-07-01 15:15:27,452 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:15:27,452 - DEBUG - Finished Request
2025-07-01 15:15:29,280 - DEBUG - POST http://localhost:54087/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync {'script': '\n            // 檢查頁面最終狀態\n            var bodyText = document.body.innerText || document.body.textCon...', 'args': []}
2025-07-01 15:15:29,280 - DEBUG - Starting new HTTP connection (1): localhost:54087
2025-07-01 15:15:33,343 - DEBUG - Incremented Retry for (url='/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-01 15:15:33,345 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A952CBE510>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync
2025-07-01 15:15:33,345 - DEBUG - Starting new HTTP connection (2): localhost:54087
2025-07-01 15:15:37,443 - DEBUG - Incremented Retry for (url='/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-01 15:15:37,444 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A952CCF950>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync
2025-07-01 15:15:37,444 - DEBUG - Starting new HTTP connection (3): localhost:54087
2025-07-01 15:15:41,504 - DEBUG - Incremented Retry for (url='/session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-01 15:15:41,505 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A952CFA210>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/e5b6b5fb65e01e285c7c90dfbb507102/execute/sync
2025-07-01 15:15:41,505 - DEBUG - Starting new HTTP connection (4): localhost:54087
2025-07-01 15:15:45,557 - ERROR - 尋找編輯按鈕失敗: 'NoneType' object has no attribute 'current_url'
