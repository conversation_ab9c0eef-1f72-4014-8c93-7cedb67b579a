"""
送出結果檢測器測試和使用示例
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os
import json

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from submission_result_detector import SubmissionResultDetector

class TestSubmissionResultDetector(unittest.TestCase):
    """送出結果檢測器測試"""
    
    def setUp(self):
        """測試前準備"""
        self.mock_driver = Mock()
        self.mock_logger = Mock()
        self.detector = SubmissionResultDetector(
            self.mock_driver, 
            self.mock_logger,
            screenshot_dir="test_screenshots"
        )
    
    def test_analyze_time_not_open_failure(self):
        """測試預約時間未開放失敗分析"""
        popup_text = "送出失敗：尚未開放 2025/07/04 預約進廠，請於 9:30 後預約。"
        
        result = self.detector._analyze_popup_content(popup_text)
        
        self.assertFalse(result["is_success"])
        self.assertEqual(result["result_type"], "time_not_open")
        self.assertIn("預約時間未開放", result["message"])
    
    def test_analyze_factory_full_failure(self):
        """測試工廠名額已滿失敗分析"""
        popup_text = "送出失敗：M1：仁武廠選擇的進廠數量已滿，請選擇其他廠。"
        
        result = self.detector._analyze_popup_content(popup_text)
        
        self.assertFalse(result["is_success"])
        self.assertEqual(result["result_type"], "factory_full")
        self.assertIn("工廠名額已滿", result["message"])
    
    def test_analyze_unknown_failure(self):
        """測試未知失敗分析"""
        popup_text = "送出失敗：系統維護中，請稍後再試。"

        result = self.detector._analyze_popup_content(popup_text)

        self.assertFalse(result["is_success"])
        self.assertEqual(result["result_type"], "general_failure")  # 修正：應該是 general_failure
        self.assertIn("一般送出失敗", result["message"])
    
    def test_analyze_non_failure_popup(self):
        """測試非失敗彈窗分析"""
        popup_text = "預約申請已送出，請等待審核結果。"
        
        result = self.detector._analyze_popup_content(popup_text)
        
        self.assertIsNone(result["is_success"])  # 不確定
        self.assertEqual(result["result_type"], "non_failure_popup")
        self.assertIn("需人工判讀", result["message"])
    
    def test_should_terminate_process(self):
        """測試是否應該終止程序"""
        # 生命週期結束的情況
        result_ended = {"lifecycle_ended": True}
        self.assertTrue(self.detector.should_terminate_process(result_ended))
        
        # 生命週期未結束的情況
        result_not_ended = {"lifecycle_ended": False}
        self.assertFalse(self.detector.should_terminate_process(result_not_ended))
    
    @patch('os.makedirs')
    @patch('submission_result_detector.datetime')
    def test_take_screenshot(self, mock_datetime, mock_makedirs):
        """測試截圖功能"""
        # Mock datetime
        mock_datetime.now.return_value.strftime.return_value = "20250627_101500"
        
        # Mock driver screenshot
        self.mock_driver.save_screenshot.return_value = True
        
        result = self.detector._take_screenshot("20250627_101500", "test_type")
        
        expected_path = os.path.join("test_screenshots", "submission_result_20250627_101500_test_type.png")
        self.assertEqual(result, expected_path)
        self.mock_driver.save_screenshot.assert_called_once_with(expected_path)

class TestSubmissionDetectorIntegration(unittest.TestCase):
    """整合測試"""
    
    def setUp(self):
        """測試前準備"""
        self.mock_driver = Mock()
        self.detector = SubmissionResultDetector(self.mock_driver)
    
    @patch('submission_result_detector.WebDriverWait')
    @patch.object(SubmissionResultDetector, '_take_screenshot')
    @patch.object(SubmissionResultDetector, '_save_result_log')
    def test_detect_failure_popup(self, mock_save_log, mock_screenshot, mock_wait):
        """測試檢測失敗彈窗的完整流程"""
        # Mock 彈窗元素
        mock_popup = Mock()
        mock_text_element = Mock()
        mock_text_element.text = "送出失敗：尚未開放 2025/07/04 預約進廠，請於 9:30 後預約。"
        mock_popup.find_element.return_value = mock_text_element
        
        # Mock WebDriverWait
        mock_wait.return_value.until.return_value = mock_popup
        
        # Mock 截圖和日誌
        mock_screenshot.return_value = "test_screenshot.png"
        mock_save_log.return_value = None
        
        result = self.detector.detect_submission_result()
        
        # 驗證結果
        self.assertTrue(result["lifecycle_ended"])
        self.assertFalse(result["is_success"])
        self.assertEqual(result["result_type"], "time_not_open")
        self.assertIn("預約時間未開放", result["message"])

def run_detector_tests():
    """運行檢測器測試"""
    print("🧪 開始運行送出結果檢測器測試...")
    
    # 創建測試套件
    test_suite = unittest.TestSuite()
    
    # 添加測試案例
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestSubmissionResultDetector))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestSubmissionDetectorIntegration))
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 輸出結果
    if result.wasSuccessful():
        print("✅ 所有測試通過！")
        return True
    else:
        print("❌ 部分測試失敗")
        print(f"失敗數量: {len(result.failures)}")
        print(f"錯誤數量: {len(result.errors)}")
        return False

# 使用示例
def example_usage():
    """使用示例"""
    print("\n📖 送出結果檢測器使用示例:")
    print("""
    # 在主程序中的使用方式
    from submission_result_detector import SubmissionResultDetector
    
    # 1. 初始化檢測器
    detector = SubmissionResultDetector(driver, logger)
    
    # 2. 送出訂單後檢測結果
    submit_button.click()  # 點擊送出按鈕
    
    # 3. 檢測結果
    result = detector.detect_submission_result(timeout=10)
    
    # 4. 根據結果決定是否終止程序
    if detector.should_terminate_process(result):
        print("搶單生命週期結束，程序終止")
        break
    
    # 5. 查看結果
    print(f"結果類型: {result['result_type']}")
    print(f"是否成功: {result['is_success']}")
    print(f"訊息: {result['message']}")
    print(f"截圖: {result['screenshot_path']}")
    """)

if __name__ == "__main__":
    # 運行測試
    success = run_detector_tests()
    
    # 顯示使用示例
    example_usage()
    
    # 如果測試失敗，退出程序並返回錯誤代碼
    if not success:
        sys.exit(1)
    
    print("\n🎉 送出結果檢測器測試完成！")
