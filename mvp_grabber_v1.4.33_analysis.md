# mvp_grabber_v1.4.33_backup.py 工作流程分析文檔

## 🚨 重要警告
**此文檔記錄已驗證通過的v1.4.33工作流程，任何修改都必須嚴格遵循此流程！**
**不得隨意更改已驗證的功能！**

## 📋 v1.4.33 完整工作流程

### 1. 啟動流程
```python
# 啟動瀏覽器並導航到首頁
driver.get("https://wmc.kcg.gov.tw/")

# 設置事件監控
setup_browser_event_monitoring()

# 啟動背景監控
start_driver_monitor()
```

### 2. 用戶手動操作階段 (關鍵！)
```python
# v1.4.33 的關鍵：等待用戶手動完成登入和導航
def wait_for_manual_login():
    """等待用戶手動登入並導航到訂單列表頁面"""
    
    # 顯示操作指引
    instructions = [
        "1. 瀏覽器已啟動，請手動完成以下操作：",
        "   • 登入您的帳號",
        "   • 輸入登入驗證碼", 
        "   • 導航到搶單頁面（進廠確認單列表）",
        "   • 確保能看到您要搶的訂單",
        "",
        "2. 完成上述操作後，點擊「準備完成」"
    ]
    
    # 等待用戶確認準備完成
    show_ready_confirmation_dialog()
```

### 3. 搶單執行流程
```python
def execute_order_grabbing(tasks):
    """執行搶單流程 - v1.4.33 已驗證版本"""
    
    for task in tasks:
        # 1. 掃描頁面 DOM 元素
        scan_result = scan_current_page_dom()
        
        # 2. 執行單個訂單搶單
        result = execute_single_order_grab(task)
```

### 4. 自動編輯按鈕點擊 (核心功能)
```python
def find_and_click_edit_button(order_id):
    """v1.4.33 已驗證的快速編輯按鈕點擊功能"""
    
    # 🚀 使用 JavaScript TreeWalker 快速搜尋策略
    click_script = f"""
    var walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    var node;
    while (node = walker.nextNode()) {{
        if (node.textContent.includes('{order_id}')) {{
            var row = node.parentElement.closest('tr');
            if (row) {{
                var editElements = row.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
                for (var i = 0; i < editElements.length; i++) {{
                    var el = editElements[i];
                    var text = (el.textContent || el.value || '').toLowerCase();
                    if (text.includes('編輯') || text.includes('edit')) {{
                        el.click();
                        return true;
                    }}
                }}
            }}
        }}
    }}
    return false;
    """
    
    clicked = driver.execute_script(click_script)
    
    if clicked:
        # 立即檢測彈窗打開
        dialog_detection_result = monitor_dialog_opening()
        return True
    
    return False
```

### 5. iframe 處理策略 (關鍵技術)
```python
def monitor_dialog_opening():
    """v1.4.33 已驗證的 iframe 檢測策略"""
    
    # 等待彈窗載入
    time.sleep(3)
    
    # 檢測 iframe 結構
    iframe_script = """
    // 檢測主頁面 iframe
    var iframes = document.querySelectorAll('iframe');
    var result = {
        iframeCount: iframes.length,
        hasEditDialog: false
    };
    
    // 檢查每個 iframe 內容
    for (var i = 0; i < iframes.length; i++) {
        try {
            var iframe = iframes[i];
            var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            if (iframeDoc) {
                var bodyText = iframeDoc.body.innerText || '';
                if (bodyText.includes('修改進廠確認單')) {
                    result.hasEditDialog = true;
                    result.editIframeIndex = i;
                    break;
                }
            }
        } catch (e) {
            // 跨域限制，忽略
        }
    }
    
    return result;
    """
    
    result = driver.execute_script(iframe_script)
    return result.get('hasEditDialog', False)
```

## 🔧 v1.4.33 vs 當前版本的關鍵差異

### ❌ 當前版本缺失的關鍵功能：

1. **缺少用戶手動操作等待階段**
   - v1.4.33：啟動瀏覽器 → 等待用戶手動登入和導航 → 用戶確認準備完成 → 開始搶單
   - 當前版本：啟動瀏覽器 → 直接開始搶單 (錯誤！)

2. **缺少準備完成確認機制**
   - v1.4.33：顯示操作指引 → 等待用戶點擊「準備完成」→ 開始搶單
   - 當前版本：無確認機制，直接執行

3. **工作流程順序錯誤**
   - v1.4.33：GUI#04 → 啟動瀏覽器 → 等待手動操作 → GUI#02 確認 → 搶單執行
   - 當前版本：GUI#04 → 啟動瀏覽器 → 直接搶單執行 (跳過了關鍵步驟)

## 🎯 修復方案

### 必須恢復的v1.4.33功能：

1. **恢復用戶手動操作等待機制**
2. **恢復準備完成確認對話框**  
3. **恢復正確的工作流程順序**
4. **保持已驗證的編輯按鈕點擊功能**
5. **保持已驗證的iframe檢測功能**

### 🚨 開發規則

1. **不得隨意修改已驗證功能**
2. **所有已驗證功能必須加上註解標記**
3. **任何修改都必須參考此文檔**
4. **優先恢復功能，再考慮優化**

## 📝 v1.4.33 關鍵代碼片段

### 啟動流程
```python
# ===== 已驗證功能：瀏覽器啟動 =====
def start_browser():
    """啟動瀏覽器 - v1.4.33 已驗證"""
    global driver, result_detector, dom_inspector
    
    try:
        # 啟動 Chrome
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        driver = webdriver.Chrome(options=options)
        
        # 導航到平台首頁
        driver.get("https://wmc.kcg.gov.tw/")
        
        # 設置事件監控
        setup_browser_event_monitoring()
        
        # 啟動背景監控
        start_driver_monitor()
        
        return True
    except Exception as e:
        print(f"[ERROR] 瀏覽器啟動失敗: {e}")
        return False
```

### 用戶操作等待
```python
# ===== 已驗證功能：用戶手動操作等待 =====
def wait_for_user_ready():
    """等待用戶手動完成登入和導航 - v1.4.33 已驗證"""
    
    # 顯示操作指引
    show_operation_guide()
    
    # 等待用戶確認
    ready = show_ready_confirmation()
    
    return ready
```

### 搶單執行
```python
# ===== 已驗證功能：搶單執行 =====
def execute_single_order_grab(task):
    """執行單個訂單搶單 - v1.4.33 已驗證"""
    
    order_id = task.get('order_id')
    
    # 1. 尋找並點擊編輯按鈕
    if not find_and_click_edit_button(order_id):
        return {"is_success": False, "message": "找不到編輯按鈕"}
    
    # 2. 等待編輯頁面載入
    time.sleep(3)
    
    # 3. 處理驗證碼
    captcha_code = handle_captcha_input()
    
    # 4. 等待觸發時間並提交
    wait_for_trigger_time_and_submit(task, captcha_code)
    
    return {"is_success": True}
```

## 🔄 下一步行動計劃

1. **立即恢復v1.4.33的用戶手動操作等待機制**
2. **恢復準備完成確認對話框**
3. **修正工作流程順序**
4. **測試完整流程**
5. **確保"修改進廠確認單"彈窗正常出現**
