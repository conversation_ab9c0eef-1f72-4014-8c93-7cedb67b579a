# Anti-Detection Enhancer for AGES-KH
# 針對 WAF (Web Application Firewall) 的防偵測強化
# Version: 1.1 - 支援多瀏覽器 (Chrome, Firefox, Edge)
# Author: Will Wang
#
# 因應 2025-06-23 公告：平台導入 WAF 監控網路封包，需強化防偵測機制

import random
import time
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService

# webdriver-manager imports
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

class AntiDetectionEnhancer:
    def __init__(self, user_profile_base: str = "xiaoming"):
        """
        初始化防偵測強化器
        user_profile_base: 使用者基礎名稱，用於建立一致的「個人特徵」
        """
        self.user_profile_base = user_profile_base

        # 為這個「使用者」建立一致的特徵
        self._establish_user_consistency()

        self.natural_ua_pools = {
            "chrome": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
            ],
            "firefox": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0"
            ],
            "edge": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0"
            ]
        }

    def _establish_user_consistency(self):
        """建立這個使用者的一致性特徵"""
        # 使用使用者名稱作為種子，確保特徵一致
        random.seed(hash(self.user_profile_base))

        # 固定的語系偏好（同一人不會每次都換語系設定）
        self.user_language_preference = "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"

        # 固定的視窗排列習慣（同一人有固定的視窗排列偏好）
        self.user_window_offset_x = random.randint(-5, 5)  # 個人 X 軸偏好
        self.user_window_offset_y = random.randint(5, 15)   # 個人 Y 軸偏好

        # 固定的視窗大小偏好
        self.user_size_preference = random.randint(-10, 10)  # 個人視窗大小偏好

        # 重置隨機種子
        random.seed()

        print(f"[INFO] 建立使用者 '{self.user_profile_base}' 的一致性特徵:")
        print(f"  - 語系偏好: {self.user_language_preference}")
        print(f"  - 視窗位置偏好: X偏移{self.user_window_offset_x}, Y偏移{self.user_window_offset_y}")
        print(f"  - 視窗大小偏好: {self.user_size_preference}")
        
    def prepare_browser_options(self, browser_type: str, profile_name: str, debug_mode: bool = False):
        """準備瀏覽器選項，支援 Chrome、Firefox、Edge"""
        browser_type = browser_type.lower()

        if browser_type == "chrome":
            return self._prepare_chrome_options(profile_name, debug_mode)
        elif browser_type == "firefox":
            return self._prepare_firefox_options(profile_name, debug_mode)
        elif browser_type == "edge":
            return self._prepare_edge_options(profile_name, debug_mode)
        else:
            raise ValueError(f"不支援的瀏覽器類型: {browser_type}")

    def _prepare_chrome_options(self, profile_name: str, debug_mode: bool = False):
        """準備 Chrome 選項，符合白皮書 v1.5 規範"""
        options = webdriver.ChromeOptions()

        # 基本防偵測設定
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # Profile 隔離 (白皮書要求)
        profile_path = f"profiles/{profile_name}_chrome"
        options.add_argument(f"--user-data-dir={profile_path}")

        # 語系設定 (使用使用者的固定偏好)
        options.add_argument(f"--lang={self.user_language_preference}")

        # UA 設定 (從池中選擇，但初始化後固定)
        ua = self.get_natural_ua_for_profile(profile_name, "chrome")
        options.add_argument(f"--user-agent={ua}")

        # 防 WAF 偵測的額外設定
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--disable-ipc-flooding-protection")

        # 模擬真實瀏覽器行為
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")
        options.add_argument("--disable-default-apps")

        # Debug 模式設定
        if debug_mode:
            options.add_argument("--start-maximized")
        else:
            options.add_argument("--disable-logging")
            options.add_argument("--disable-gpu-logging")
            options.add_argument("--silent")

        return options

    def _prepare_firefox_options(self, profile_name: str, debug_mode: bool = False):
        """準備 Firefox 選項"""
        options = webdriver.FirefoxOptions()

        # 基本防偵測設定
        options.set_preference("dom.webdriver.enabled", False)
        options.set_preference('useAutomationExtension', False)
        options.set_preference("general.platform.override", "Win32")

        # 語系設定 (使用使用者的固定偏好)
        options.set_preference("intl.accept_languages", self.user_language_preference)

        # UA 設定
        ua = self.get_natural_ua_for_profile(profile_name, "firefox")
        options.set_preference("general.useragent.override", ua)

        # Profile 路徑 (Firefox 使用不同的 profile 管理方式)
        # 這裡可以設定 profile 路徑，但 Firefox 的 profile 管理較複雜

        if not debug_mode:
            options.add_argument("--headless")  # 可選：無頭模式

        return options

    def _prepare_edge_options(self, profile_name: str, debug_mode: bool = False):
        """準備 Edge 選項"""
        options = webdriver.EdgeOptions()

        # Edge 基於 Chromium，設定類似 Chrome
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # Profile 隔離
        profile_path = f"profiles/{profile_name}_edge"
        options.add_argument(f"--user-data-dir={profile_path}")

        # 語系設定 (使用使用者的固定偏好)
        options.add_argument(f"--lang={self.user_language_preference}")

        # UA 設定
        ua = self.get_natural_ua_for_profile(profile_name, "edge")
        options.add_argument(f"--user-agent={ua}")

        # 防偵測設定
        options.add_argument("--disable-web-security")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")

        if debug_mode:
            options.add_argument("--start-maximized")
        else:
            options.add_argument("--disable-logging")
            options.add_argument("--silent")

        return options
        
    def get_natural_ua_for_profile(self, profile_name: str, browser_type: str) -> str:
        """為特定 profile 和瀏覽器類型取得固定的自然 UA"""
        browser_type = browser_type.lower()

        if browser_type not in self.natural_ua_pools:
            browser_type = "chrome"  # 預設使用 Chrome

        # 使用 profile 名稱和瀏覽器類型作為種子，確保同一組合總是得到相同 UA
        seed_string = f"{profile_name}_{browser_type}"
        random.seed(hash(seed_string))
        ua = random.choice(self.natural_ua_pools[browser_type])
        random.seed()  # 重置隨機種子
        return ua
        
    def set_natural_window_properties(self, driver, browser_index: int = 0):
        """設定自然的視窗屬性，體現個人習慣的一致性"""
        # 基於 1920x1080 螢幕的自然視窗配置
        base_configs = [
            {"width": 620, "height": 800, "x_base": 10},    # 左側視窗
            {"width": 640, "height": 820, "x_base": 650},   # 中間視窗
            {"width": 660, "height": 840, "x_base": 1300}   # 右側視窗
        ]

        if browser_index >= len(base_configs):
            browser_index = browser_index % len(base_configs)

        config = base_configs[browser_index]

        # 使用個人一致的偏好，而非完全隨機
        # 同一個人的視窗排列習慣是相對固定的
        width = config["width"] + self.user_size_preference + random.randint(-5, 5)
        height = config["height"] + self.user_size_preference + random.randint(-5, 5)
        x = config["x_base"] + self.user_window_offset_x + random.randint(-3, 3)
        y = self.user_window_offset_y + random.randint(-2, 2)

        # 確保視窗不會超出螢幕範圍
        width = max(600, min(width, 800))
        height = max(700, min(height, 900))
        x = max(0, min(x, 1920 - width))
        y = max(0, min(y, 1080 - height))

        # 設定視窗大小和位置
        driver.set_window_size(width, height)
        driver.set_window_position(x, y)

        print(f"[INFO] 視窗設定 (個人習慣): {width}x{height} at ({x}, {y})")
        
    def inject_natural_properties(self, driver):
        """注入自然瀏覽器屬性，體現同一設備的一致性"""
        # 隱藏 webdriver 屬性
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # 模擬自然的 plugins (同一台電腦的 plugins 應該一致)
        driver.execute_script("""
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {name: 'Chrome PDF Plugin', description: 'Portable Document Format'},
                    {name: 'Widevine Content Decryption Module', description: 'Enables Widevine licenses'},
                    {name: 'Native Client', description: 'Native Client'}
                ]
            });
        """)

        # 使用一致的語言設定 (同一人的語言偏好是固定的)
        languages = self.user_language_preference.split(',')
        languages = [lang.split(';')[0].strip() for lang in languages]

        driver.execute_script(f"""
            Object.defineProperty(navigator, 'languages', {{
                get: () => {languages}
            }});
        """)

        # 設定一致的時區 (同一台電腦的時區是固定的)
        driver.execute_script("""
            Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
                value: function() {
                    return {
                        locale: 'zh-TW',
                        timeZone: 'Asia/Taipei',
                        calendar: 'gregory',
                        numberingSystem: 'latn'
                    };
                }
            });
        """)

        # 設定一致的螢幕資訊 (同一台電腦的螢幕解析度是固定的)
        driver.execute_script("""
            Object.defineProperty(screen, 'width', {get: () => 1920});
            Object.defineProperty(screen, 'height', {get: () => 1080});
            Object.defineProperty(screen, 'availWidth', {get: () => 1920});
            Object.defineProperty(screen, 'availHeight', {get: () => 1040});
            Object.defineProperty(screen, 'colorDepth', {get: () => 24});
            Object.defineProperty(screen, 'pixelDepth', {get: () => 24});
        """)
        
    def simulate_natural_mouse_movement(self, driver, element, delay_range=(0.1, 0.3)):
        """模擬自然的滑鼠移動和點擊"""
        actions = ActionChains(driver)
        
        # 隨機偏移，模擬人手不精確
        offset_x = random.randint(-5, 5)
        offset_y = random.randint(-5, 5)
        
        # 移動到元素附近
        actions.move_to_element_with_offset(element, offset_x, offset_y)
        
        # 隨機停頓，模擬人類思考時間
        pause_time = random.uniform(*delay_range)
        actions.pause(pause_time)
        
        # 點擊
        actions.click()
        actions.perform()
        
        print(f"[INFO] 模擬點擊: 偏移({offset_x}, {offset_y}), 停頓{pause_time:.2f}秒")
        
    def add_random_human_delays(self, min_delay=0.5, max_delay=2.0):
        """加入隨機的人類延遲"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        print(f"[INFO] 人類延遲: {delay:.2f}秒")
        
    def create_enhanced_driver(self, browser_type: str, profile_name: str, browser_index: int = 0, debug_mode: bool = False):
        """建立強化防偵測的 WebDriver，支援多瀏覽器"""
        browser_type = browser_type.lower()
        print(f"[INFO] 建立強化防偵測 {browser_type.title()} WebDriver: {profile_name}")

        try:
            # 準備選項
            options = self.prepare_browser_options(browser_type, profile_name, debug_mode)

            # 建立對應的 driver
            if browser_type == "chrome":
                service = ChromeService(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
            elif browser_type == "firefox":
                service = FirefoxService(GeckoDriverManager().install())
                driver = webdriver.Firefox(service=service, options=options)
            elif browser_type == "edge":
                service = EdgeService(EdgeChromiumDriverManager().install())
                driver = webdriver.Edge(service=service, options=options)
            else:
                raise ValueError(f"不支援的瀏覽器類型: {browser_type}")

            # 設定視窗屬性
            self.set_natural_window_properties(driver, browser_index)

            # 注入自然屬性 (主要針對 Chrome 和 Edge)
            if browser_type in ["chrome", "edge"]:
                self.inject_natural_properties(driver)

            print(f"[INFO] {browser_type.title()} WebDriver 建立完成: {profile_name}")
            return driver

        except Exception as e:
            print(f"[ERROR] 建立 {browser_type.title()} WebDriver 失敗: {e}")
            return None
        
    def get_waf_safe_request_headers(self) -> dict:
        """取得對 WAF 安全的請求標頭"""
        return {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

# 使用範例
if __name__ == "__main__":
    # 建立「小明」這個使用者的防偵測強化器
    # 所有瀏覽器都會體現「小明」的一致性特徵
    enhancer = AntiDetectionEnhancer(user_profile_base="xiaoming")

    # 測試多瀏覽器 - 模擬小明在同一台電腦上使用三個不同瀏覽器
    browsers_to_test = [
        {"type": "chrome", "profile": "xiaoming_chrome", "index": 0},
        {"type": "firefox", "profile": "xiaoming_firefox", "index": 1},
        {"type": "edge", "profile": "xiaoming_edge", "index": 2}
    ]

    drivers = []

    try:
        for browser_config in browsers_to_test:
            print(f"\n測試 {browser_config['type'].title()} 瀏覽器...")

            # 建立強化的 WebDriver
            driver = enhancer.create_enhanced_driver(
                browser_type=browser_config['type'],
                profile_name=browser_config['profile'],
                browser_index=browser_config['index'],
                debug_mode=True
            )

            if driver:
                drivers.append(driver)

                # 導航到目標網站
                driver.get("https://wmc.kcg.gov.tw/")

                # 加入人類延遲
                enhancer.add_random_human_delays()

                print(f"[INFO] {browser_config['type'].title()} 防偵測強化完成")
            else:
                print(f"[ERROR] {browser_config['type'].title()} 啟動失敗")

        if drivers:
            print(f"\n[INFO] 成功啟動 {len(drivers)} 個瀏覽器")
            print("="*60)
            print("✅ 一致性特徵驗證：")
            print("  - 所有瀏覽器使用相同的語系偏好")
            print("  - 視窗排列體現個人習慣的一致性")
            print("  - 螢幕解析度、時區等設備特徵保持一致")
            print("  - 每個瀏覽器的 UA 固定但合理差異")
            print("\n這完美模擬了「小明在同一台電腦上使用不同瀏覽器」的真實情況")
            print("="*60)
            input("按 Enter 關閉所有瀏覽器...")

    except Exception as e:
        print(f"[ERROR] 發生錯誤: {e}")
    finally:
        # 關閉所有瀏覽器
        for driver in drivers:
            try:
                driver.quit()
            except:
                pass
