# AGES-KH 搶單主程式 GUI 流程說明

## 1. 主視窗流程

### 1.1 啟動流程
1. 顯示主視窗標題：「AGES-KH 搶單主程式 v1.3.2」
2. 顯示觸發時間設定區域
3. 顯示任務列表區域
4. 顯示操作按鈕區域

### 1.2 觸發時間設定
1. 預設值：09:30:00.001
2. 使用者可修改時間
3. 時間格式：HH:MM:SS.mmm
4. 支援毫秒級設定

### 1.3 任務列表顯示
1. 分為三個區塊：
   - 今日有效任務（符合今日或萬用條件）
   - 今日將執行任務（每個瀏覽器僅執行一筆）
   - 被忽略任務（不符合條件或重複的瀏覽器）
2. 每個任務顯示完整資訊：
   - 日期（date）
   - 訂單日期（order_date）
   - 訂單編號（order_id）
   - 瀏覽器（browser）
   - 使用者設定檔（user_profile）
   - 模型（model）
   - 觸發時間（trigger_time）

### 1.4 操作按鈕
1. 「載入任務」按鈕
   - 讀取 orders.csv
   - 更新任務列表顯示
2. 「RTT 設定」按鈕
   - 開啟 RTT 設定視窗
3. 「開始執行」按鈕
   - 檢查是否有有效任務
   - 檢查瀏覽器支援狀態
   - 啟動對應瀏覽器
4. 「退出」按鈕
   - 關閉所有瀏覽器
   - 結束程式

## 2. RTT 設定視窗流程

### 2.1 啟動流程
1. 從主視窗點擊「RTT 設定」按鈕開啟
2. 顯示 RTT 設定視窗標題：「RTT 設定」
3. 載入現有設定（如果有的話）

### 2.2 設定項目
1. 伺服器 URL
   - 預設值：https://www.google.com
   - 可自訂其他 URL
2. 採樣頻率（毫秒）
   - 預設值：100
   - 範圍：10-1000
3. 採樣時長（秒）
   - 預設值：60
   - 範圍：10-300
4. 目標時間
   - 預設值：09:30:00
   - 格式：HH:MM:SS
5. 模型選擇
   - 預設：Model A（移動平均）
   - 可擴展其他模型

### 2.3 操作按鈕
1. 「測試 RTT」按鈕
   - 啟動 RTT 測試執行緒
   - 顯示測試狀態
   - 更新測試結果
2. 「儲存設定」按鈕
   - 驗證所有設定
   - 儲存到設定檔
   - 關閉視窗
3. 「取消」按鈕
   - 放棄更改
   - 關閉視窗

### 2.4 RTT 測試流程
1. 測試前檢查
   - 驗證 URL 格式
   - 檢查網路連線
   - 確認設定值有效
2. 測試執行
   - 在主執行緒中啟動測試執行緒
   - 執行緒中只進行網路操作
   - 不直接操作 GUI 元件
3. 結果更新
   - 使用 after 方法在主執行緒更新 GUI
   - 顯示平均 RTT
   - 顯示原始數據
   - 更新狀態標籤

### 2.5 錯誤處理
1. 網路錯誤
   - 顯示錯誤訊息
   - 允許重試
2. 設定錯誤
   - 顯示錯誤訊息
   - 保持視窗開啟
3. 執行緒錯誤
   - 確保主執行緒安全
   - 正確處理 GUI 更新

## 3. 瀏覽器啟動流程

### 3.1 啟動前檢查
1. 檢查是否有有效任務
2. 檢查瀏覽器支援狀態
3. 檢查使用者設定檔存在

### 3.2 瀏覽器啟動
1. 根據任務設定啟動對應瀏覽器
2. 載入使用者設定檔
3. 開啟目標網頁

### 3.3 狀態監控
1. 顯示瀏覽器啟動狀態
2. 監控瀏覽器運行狀態
3. 處理異常情況

## 4. 錯誤處理與日誌

### 4.1 錯誤處理
1. 檔案讀取錯誤
2. 瀏覽器啟動錯誤
3. 網路連線錯誤
4. RTT 測試錯誤

### 4.2 日誌記錄
1. 操作日誌
2. 錯誤日誌
3. RTT 測試日誌
4. 瀏覽器操作日誌

## 5. 注意事項

### 5.1 GUI 執行緒安全
1. 所有 GUI 操作必須在主執行緒進行
2. 使用 after 方法更新 GUI
3. 避免在子執行緒直接操作 GUI 元件

### 5.2 資源管理
1. 正確關閉瀏覽器
2. 釋放系統資源
3. 清理暫存檔案

### 5.3 使用者體驗
1. 提供清晰的錯誤訊息
2. 保持介面響應性
3. 支援取消操作 