2025-07-01 07:40:29,506 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_074029.log
2025-07-01 07:40:45,317 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 07:40:45,317 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 07:40:45,381 - DEBUG - chromedriver not found in PATH
2025-07-01 07:40:45,381 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 07:40:45,381 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 07:40:45,382 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 07:40:45,382 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 07:40:45,382 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 07:40:45,383 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 07:40:45,388 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 8016 using 0 to output -3
2025-07-01 07:40:45,896 - DEBUG - POST http://localhost:61084/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 07:40:45,897 - DEBUG - Starting new HTTP connection (1): localhost:61084
2025-07-01 07:40:46,441 - DEBUG - http://localhost:61084 "POST /session HTTP/1.1" 200 0
2025-07-01 07:40:46,441 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir8016_670440018"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:61087"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"57b3d9e591474d1a65effba216ee81a3"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:46,441 - DEBUG - Finished Request
2025-07-01 07:40:46,442 - DEBUG - POST http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 07:40:47,782 - DEBUG - http://localhost:61084 "POST /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:47,782 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:47,782 - DEBUG - Finished Request
2025-07-01 07:40:47,782 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 07:40:47,783 - DEBUG - POST http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 07:40:47,791 - DEBUG - http://localhost:61084 "POST /session/57b3d9e591474d1a65effba216ee81a3/execute/sync HTTP/1.1" 200 0
2025-07-01 07:40:47,791 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:47,792 - DEBUG - Finished Request
2025-07-01 07:40:47,792 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 07:40:47,792 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:47,821 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:47,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:47,822 - DEBUG - Finished Request
2025-07-01 07:40:48,824 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:48,831 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:48,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:48,831 - DEBUG - Finished Request
2025-07-01 07:40:49,832 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:49,839 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:49,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:49,839 - DEBUG - Finished Request
2025-07-01 07:40:50,840 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:50,847 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:50,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:50,847 - DEBUG - Finished Request
2025-07-01 07:40:51,848 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:51,854 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:51,855 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:51,855 - DEBUG - Finished Request
2025-07-01 07:40:52,856 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:52,864 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:52,864 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:52,864 - DEBUG - Finished Request
2025-07-01 07:40:53,865 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:53,871 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:53,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:53,872 - DEBUG - Finished Request
2025-07-01 07:40:54,873 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:54,883 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:54,883 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:54,884 - DEBUG - Finished Request
2025-07-01 07:40:55,885 - DEBUG - GET http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3/url {}
2025-07-01 07:40:55,893 - DEBUG - http://localhost:61084 "GET /session/57b3d9e591474d1a65effba216ee81a3/url HTTP/1.1" 200 0
2025-07-01 07:40:55,893 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:55,893 - DEBUG - Finished Request
2025-07-01 07:40:56,438 - DEBUG - DELETE http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3 {}
2025-07-01 07:40:56,480 - DEBUG - http://localhost:61084 "DELETE /session/57b3d9e591474d1a65effba216ee81a3 HTTP/1.1" 200 0
2025-07-01 07:40:56,480 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:40:56,481 - DEBUG - Finished Request
2025-07-01 07:40:56,905 - DEBUG - DELETE http://localhost:61084/session/57b3d9e591474d1a65effba216ee81a3 {}
2025-07-01 07:40:56,905 - DEBUG - Starting new HTTP connection (1): localhost:61084
