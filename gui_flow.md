# AGES-KH 搶單主程式 GUI 流程文件

## 版本資訊
- **主程式版本**: v1.3.32
- **RTT 模組版本**: v1.3.0
- **最後更新**: 2024-06-18
- **作者**: Will Wang

## 程式架構

### 主程式 (mvp_grabber.py)
- **功能**: AGES-KH 搶單主程式，提供 GUI 介面
- **版本**: v1.3.32
- **GUI 尺寸**: 500x600 像素
- **主要區域**:
  - 今日任務區域（200px 高度，含捲動軸）
  - 有效任務區域（200px 高度，含捲動軸）
  - 按鈕區域（底部）

### RTT 模組 (rtt_predictor.py)
- **功能**: 網路延遲測試模組
- **版本**: v1.2.3
- **主要功能**:
  - RTT 採樣與統計分析
  - 錯誤率監控（error_count, error_rate）
  - 自動產生 log 與 CSV 檔案
  - 支援多模型架構（目前僅 Model A）
- **API**: get_rtt_result(), get_avg_rtt()
- **錯誤率指標**: 0-100%，用於網路健康度監控

## GUI 介面設計

### 視窗配置
```
┌─────────────────────────────────────┐
│            AGES-KH 搶單主程式        │
├─────────────────────────────────────┤
│ 今日任務（待執行）                   │
│ ┌─────────────────────────────────┐ │
│ │ 任務1: 搶單時間 09:00           │ │
│ │ 任務2: 搶單時間 14:30           │ │
│ │ ...                            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 有效任務（全部有效，含待執行與不執行） │
│ ┌─────────────────────────────────┐ │
│ │ 任務1: 搶單時間 09:00 (待執行)  │ │
│ │ 任務2: 搶單時間 14:30 (不執行)  │ │
│ │ ...                            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [開始搶單] [停止搶單] [設定]        │
└─────────────────────────────────────┘
```

### 任務顯示格式
- **今日任務**: 顯示待執行的任務
- **有效任務**: 顯示所有有效任務，包含狀態標示
- **欄位**: 任務編號、搶單時間、狀態

## 功能流程

### 1. 程式啟動
1. 載入設定檔 (rtt_config.json)
2. 初始化 GUI 介面
3. 載入今日任務與有效任務
4. 顯示任務清單

### 2. 搶單流程
1. 使用者點擊「開始搶單」
2. 系統檢查當前時間與任務時間
3. 執行 RTT 測試（透過 rtt_predictor.py）
4. 根據 RTT 結果決定搶單時機
5. 執行搶單操作
6. 更新任務狀態

### 3. RTT 測試流程
1. 呼叫 rtt_predictor.get_rtt_result()
2. 取得平均 RTT 與錯誤率
3. 記錄測試結果到 log 檔案
4. 回傳結果給主程式

## 設定檔格式

### rtt_config.json
```json
{
  "rtt_settings": {
    "freq": 2,
    "duration": 60,
    "server_url": "https://wmc.kcg.gov.tw/"
  },
  "tasks": [
    {
      "id": 1,
      "time": "09:00",
      "enabled": true
    }
  ]
}
```

## 錯誤處理

### 網路錯誤
- 透過 RTT 模組的 error_rate 監控網路狀況
- error_rate > 50% 時顯示警告
- error_rate = 100% 時暫停搶單

### 程式錯誤
- 記錄錯誤日誌
- 顯示錯誤訊息給使用者
- 保持程式穩定運行

## 版本更新紀錄

### 主程式 v1.3.32 (2024-06-18)
- 優化 GUI 版面配置
- 調整任務顯示區域高度
- 完善錯誤處理機制

### RTT 模組 v1.2.3 (2024-06-18)
- 新增錯誤率統計功能
- 修正 error_count 計算邏輯
- 完善錯誤處理機制
- 更新文件說明

### 歷史版本
- v1.3.31: GUI 版面調整
- v1.3.30: 新增任務管理功能
- v1.2.2: 新增 error_count 與 error_rate 欄位
- v1.2.0: 新增 get_rtt_result() API
- v1.1.0: 新增自動產生 log 與 CSV 檔案

## 技術規格

### 系統需求
- Python 3.7+
- tkinter (GUI)
- requests (網路請求)
- 網路連線

### 檔案結構
```
AGES-KH-Bot/
├── mvp_grabber.py          # 主程式
├── rtt_predictor.py        # RTT 模組
├── rtt_config.json         # 設定檔
├── gui_flow.md            # 本文件
├── logs/                  # 日誌目錄
│   ├── *.log             # RTT 測試日誌
│   └── *.csv             # RTT 原始數據
└── results/              # 結果目錄
    └── results.csv       # 搶單結果
```

## 聯絡資訊
- **開發者**: Will Wang
- **專案**: AGES-KH 搶單主程式
- **最後更新**: 2024-06-18 