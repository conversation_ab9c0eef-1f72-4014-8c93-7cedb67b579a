2025-06-30 19:11:53,641 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_191153.log
2025-06-30 19:12:04,961 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 19:12:04,961 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 19:12:05,024 - DEBUG - chromedriver not found in PATH
2025-06-30 19:12:05,024 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:12:05,024 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 19:12:05,024 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 19:12:05,024 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 19:12:05,024 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 19:12:05,025 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:12:05,029 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 29608 using 0 to output -3
2025-06-30 19:12:05,536 - DEBUG - POST http://localhost:54531/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 19:12:05,536 - DEBUG - Starting new HTTP connection (1): localhost:54531
2025-06-30 19:12:06,071 - DEBUG - http://localhost:54531 "POST /session HTTP/1.1" 200 0
2025-06-30 19:12:06,071 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir29608_1881582628"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54534"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"cb61723c197d3a6b13b4f4a68aaa7adc"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:06,072 - DEBUG - Finished Request
2025-06-30 19:12:06,072 - DEBUG - POST http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 19:12:07,914 - DEBUG - http://localhost:54531 "POST /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:07,914 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:07,915 - DEBUG - Finished Request
2025-06-30 19:12:07,915 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 19:12:07,915 - DEBUG - POST http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 19:12:07,921 - DEBUG - http://localhost:54531 "POST /session/cb61723c197d3a6b13b4f4a68aaa7adc/execute/sync HTTP/1.1" 200 0
2025-06-30 19:12:07,921 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:07,922 - DEBUG - Finished Request
2025-06-30 19:12:07,922 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 19:12:07,922 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:07,948 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:07,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:07,949 - DEBUG - Finished Request
2025-06-30 19:12:08,950 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:08,958 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:08,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:08,959 - DEBUG - Finished Request
2025-06-30 19:12:09,960 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:09,967 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:09,968 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:09,968 - DEBUG - Finished Request
2025-06-30 19:12:10,969 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:10,977 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:10,977 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:10,978 - DEBUG - Finished Request
2025-06-30 19:12:11,979 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:11,984 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:11,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:11,985 - DEBUG - Finished Request
2025-06-30 19:12:12,986 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:12,992 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:12,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:12,993 - DEBUG - Finished Request
2025-06-30 19:12:13,993 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:14,001 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:14,002 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:14,002 - DEBUG - Finished Request
2025-06-30 19:12:15,003 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:15,010 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:15,010 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:15,011 - DEBUG - Finished Request
2025-06-30 19:12:16,011 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:16,020 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:16,021 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:16,021 - DEBUG - Finished Request
2025-06-30 19:12:17,022 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:17,029 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:17,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:17,029 - DEBUG - Finished Request
2025-06-30 19:12:18,031 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:18,040 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:18,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:18,040 - DEBUG - Finished Request
2025-06-30 19:12:19,041 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:19,049 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:19,049 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:19,049 - DEBUG - Finished Request
2025-06-30 19:12:20,050 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:20,058 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:20,058 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:20,059 - DEBUG - Finished Request
2025-06-30 19:12:21,061 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:21,069 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:21,069 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:21,069 - DEBUG - Finished Request
2025-06-30 19:12:22,070 - DEBUG - GET http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc/url {}
2025-06-30 19:12:22,077 - DEBUG - http://localhost:54531 "GET /session/cb61723c197d3a6b13b4f4a68aaa7adc/url HTTP/1.1" 200 0
2025-06-30 19:12:22,077 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:22,078 - DEBUG - Finished Request
2025-06-30 19:12:22,775 - DEBUG - DELETE http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc {}
2025-06-30 19:12:22,822 - DEBUG - http://localhost:54531 "DELETE /session/cb61723c197d3a6b13b4f4a68aaa7adc HTTP/1.1" 200 0
2025-06-30 19:12:22,822 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:12:22,823 - DEBUG - Finished Request
2025-06-30 19:12:23,095 - DEBUG - DELETE http://localhost:54531/session/cb61723c197d3a6b13b4f4a68aaa7adc {}
2025-06-30 19:12:23,096 - DEBUG - Starting new HTTP connection (1): localhost:54531
