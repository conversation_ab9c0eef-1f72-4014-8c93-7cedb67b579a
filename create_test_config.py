# 建立測試配置檔
import json
from datetime import datetime

def create_test_config():
    timestamp = datetime.now()
    timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
    
    # 基於您的操作建立配置
    config = {
        'scan_info': {
            'timestamp': timestamp.isoformat(),
            'date': timestamp.strftime("%Y-%m-%d"),
            'time': timestamp.strftime("%H:%M:%S"),
            'browser_type': 'chrome',
            'url': 'https://wmc.kcg.gov.tw/',
            'total_elements_found': 5,
            'scan_method': 'fallback_after_manual_completion'
        },
        'elements': {
            'order_input': {
                'selector': 'input[name*="order"]',
                'tag': 'input',
                'text': '',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'search_button': {
                'selector': 'button[type="submit"]',
                'tag': 'button',
                'text': '查詢',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'edit_button': {
                'selector': 'a:contains("編輯")',
                'tag': 'a',
                'text': '編輯',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'captcha_input': {
                'selector': 'input[name*="captcha"]',
                'tag': 'input',
                'text': '',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'submit_button': {
                'selector': 'input[type="submit"]',
                'tag': 'input',
                'text': '送出',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            }
        }
    }
    
    # 儲存主檔案
    main_filename = 'dom_elements_config.json'
    backup_filename = f'dom_elements_config_{timestamp_str}.json'
    
    try:
        with open(main_filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            
        print(f"✅ 配置檔已建立:")
        print(f"  - 主檔案: {main_filename}")
        print(f"  - 備份檔: {backup_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 建立配置檔失敗: {e}")
        return False

if __name__ == "__main__":
    create_test_config()
