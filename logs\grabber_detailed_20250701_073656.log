2025-07-01 07:36:56,664 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_073656.log
2025-07-01 07:37:02,026 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 07:37:02,027 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 07:37:02,656 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 07:37:02,657 - DEBUG - chromedriver not found in PATH
2025-07-01 07:37:02,657 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 07:37:02,657 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 07:37:02,657 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 07:37:02,657 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 07:37:02,657 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 07:37:02,657 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 07:37:02,657 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 07:37:02,666 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 24516 using 0 to output -3
2025-07-01 07:37:03,183 - DEBUG - POST http://localhost:60929/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 07:37:03,183 - DEBUG - Starting new HTTP connection (1): localhost:60929
2025-07-01 07:37:03,747 - DEBUG - http://localhost:60929 "POST /session HTTP/1.1" 200 0
2025-07-01 07:37:03,748 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir24516_1910604858"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:60934"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"d64cd198c8c6431980b605fa5b263086"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:03,748 - DEBUG - Finished Request
2025-07-01 07:37:03,749 - DEBUG - POST http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 07:37:05,568 - DEBUG - http://localhost:60929 "POST /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:05,569 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:05,569 - DEBUG - Finished Request
2025-07-01 07:37:05,569 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 07:37:05,570 - DEBUG - POST http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 07:37:05,600 - DEBUG - http://localhost:60929 "POST /session/d64cd198c8c6431980b605fa5b263086/execute/sync HTTP/1.1" 200 0
2025-07-01 07:37:05,601 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:05,601 - DEBUG - Finished Request
2025-07-01 07:37:05,602 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 07:37:05,603 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:05,658 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:05,658 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:05,658 - DEBUG - Finished Request
2025-07-01 07:37:06,659 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:06,666 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:06,666 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:06,666 - DEBUG - Finished Request
2025-07-01 07:37:07,667 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:07,674 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:07,674 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:07,674 - DEBUG - Finished Request
2025-07-01 07:37:08,674 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:08,681 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:08,681 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:08,681 - DEBUG - Finished Request
2025-07-01 07:37:09,683 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:09,692 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:09,692 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:09,692 - DEBUG - Finished Request
2025-07-01 07:37:10,693 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:10,700 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:10,701 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:10,701 - DEBUG - Finished Request
2025-07-01 07:37:11,702 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:11,710 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:11,711 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:11,711 - DEBUG - Finished Request
2025-07-01 07:37:12,711 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:12,720 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:12,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:12,721 - DEBUG - Finished Request
2025-07-01 07:37:13,721 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:13,730 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:13,730 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:13,731 - DEBUG - Finished Request
2025-07-01 07:37:14,732 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:14,741 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:14,741 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:14,741 - DEBUG - Finished Request
2025-07-01 07:37:15,742 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:15,751 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:15,751 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:15,752 - DEBUG - Finished Request
2025-07-01 07:37:16,752 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:16,760 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:16,761 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:16,761 - DEBUG - Finished Request
2025-07-01 07:37:17,763 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:17,771 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:17,771 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:17,772 - DEBUG - Finished Request
2025-07-01 07:37:18,773 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:18,782 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:18,783 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:18,783 - DEBUG - Finished Request
2025-07-01 07:37:19,784 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:19,793 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:19,793 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:19,794 - DEBUG - Finished Request
2025-07-01 07:37:20,795 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:20,804 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:20,805 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:20,805 - DEBUG - Finished Request
2025-07-01 07:37:21,806 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:21,815 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:21,816 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:21,817 - DEBUG - Finished Request
2025-07-01 07:37:22,818 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:22,828 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:22,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:22,828 - DEBUG - Finished Request
2025-07-01 07:37:23,828 - DEBUG - GET http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086/url {}
2025-07-01 07:37:23,838 - DEBUG - http://localhost:60929 "GET /session/d64cd198c8c6431980b605fa5b263086/url HTTP/1.1" 200 0
2025-07-01 07:37:23,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:23,839 - DEBUG - Finished Request
2025-07-01 07:37:23,919 - DEBUG - DELETE http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086 {}
2025-07-01 07:37:23,955 - DEBUG - http://localhost:60929 "DELETE /session/d64cd198c8c6431980b605fa5b263086 HTTP/1.1" 200 0
2025-07-01 07:37:23,956 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 07:37:23,956 - DEBUG - Finished Request
2025-07-01 07:37:24,861 - DEBUG - DELETE http://localhost:60929/session/d64cd198c8c6431980b605fa5b263086 {}
2025-07-01 07:37:24,862 - DEBUG - Starting new HTTP connection (1): localhost:60929
