#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的搶單流程
驗證是否符合用戶期望的流程
"""

import os
import sys
import json
from datetime import datetime

def test_flow_understanding():
    """測試流程理解是否正確"""
    print("="*60)
    print("測試修正後的搶單流程理解")
    print("="*60)
    
    # 檢查關鍵函數是否存在
    try:
        import mvp_grabber
        
        required_functions = [
            'find_and_click_edit_button',
            'handle_captcha_input', 
            'wait_for_user_ready_confirmation',
            'execute_precise_submit',
            'execute_single_order_grab'
        ]
        
        print("🔍 檢查關鍵函數:")
        for func_name in required_functions:
            if hasattr(mvp_grabber, func_name):
                print(f"  ✅ {func_name}")
            else:
                print(f"  ❌ {func_name} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 導入失敗: {e}")
        return False

def test_order_search_logic():
    """測試訂單搜尋邏輯"""
    print("\n🔍 測試訂單搜尋邏輯:")
    
    try:
        import mvp_grabber
        import inspect
        
        # 檢查 find_and_click_edit_button 函數的實現
        source = inspect.getsource(mvp_grabber.find_and_click_edit_button)
        
        # 檢查是否包含自動搜尋邏輯
        if "自動尋找" in source:
            print("  ✅ 包含自動搜尋邏輯")
        else:
            print("  ❌ 缺少自動搜尋邏輯")
            
        # 檢查是否有詳細的調試信息
        if "DEBUG" in source and "找到" in source:
            print("  ✅ 包含詳細調試信息")
        else:
            print("  ❌ 缺少詳細調試信息")
            
        # 檢查是否支援多種選擇器
        if "row_selectors" in source and "edit_selectors" in source:
            print("  ✅ 支援多種選擇器策略")
        else:
            print("  ❌ 選擇器策略不足")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
        return False

def test_flow_sequence():
    """測試流程順序是否正確"""
    print("\n📋 測試流程順序:")
    
    try:
        import mvp_grabber
        import inspect
        
        # 檢查 execute_single_order_grab 函數的流程
        source = inspect.getsource(mvp_grabber.execute_single_order_grab)
        
        expected_steps = [
            "尋找並點擊編輯按鈕",
            "等待編輯頁面載入", 
            "處理驗證碼",
            "等待用戶確認準備完成",
            "根據 RTT 精確計算送出時機",
            "檢測送出結果",
            "記錄搶單結果"
        ]
        
        print("  檢查流程步驟:")
        for i, step in enumerate(expected_steps, 1):
            if f"步驟 {i}" in source and step in source:
                print(f"    ✅ 步驟 {i}: {step}")
            else:
                print(f"    ❌ 步驟 {i}: {step} - 缺失或不正確")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
        return False

def test_user_interaction_points():
    """測試用戶交互點"""
    print("\n👤 測試用戶交互點:")
    
    try:
        import mvp_grabber
        import inspect
        
        # 檢查驗證碼處理
        captcha_source = inspect.getsource(mvp_grabber.handle_captcha_input)
        if "人工輸入" in captcha_source or "手動" in captcha_source:
            print("  ✅ 驗證碼需要人工輸入")
        else:
            print("  ❌ 驗證碼處理不明確")
        
        # 檢查用戶確認
        if hasattr(mvp_grabber, 'wait_for_user_ready_confirmation'):
            confirm_source = inspect.getsource(mvp_grabber.wait_for_user_ready_confirmation)
            if "確認準備完成" in confirm_source:
                print("  ✅ 包含用戶確認步驟")
            else:
                print("  ❌ 用戶確認步驟不完整")
        else:
            print("  ❌ 缺少用戶確認函數")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
        return False

def test_rtt_integration():
    """測試 RTT 整合"""
    print("\n⚡ 測試 RTT 整合:")
    
    try:
        import mvp_grabber
        import inspect
        
        if hasattr(mvp_grabber, 'execute_precise_submit'):
            rtt_source = inspect.getsource(mvp_grabber.execute_precise_submit)
            
            if "RTT" in rtt_source and "補償" in rtt_source:
                print("  ✅ 包含 RTT 補償邏輯")
            else:
                print("  ❌ RTT 補償邏輯不完整")
                
            if "精確" in rtt_source and "觸發時間" in rtt_source:
                print("  ✅ 包含精確觸發邏輯")
            else:
                print("  ❌ 精確觸發邏輯不完整")
        else:
            print("  ❌ 缺少精確送出函數")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
        return False

def print_expected_flow():
    """打印期望的流程"""
    print("\n" + "="*60)
    print("期望的搶單流程 (根據用戶反饋)")
    print("="*60)
    
    flow_steps = [
        "1. 運行 python mvp_grabber.py",
        "2. 設定觸發時間，點擊「開始執行」", 
        "3. 手動登入並導航到搶單頁面",
        "4. 系統依據 orders.csv，找出 E48B201611405190953 訂單，自動點擊編輯按鈕",
        "5. 平台跳出編輯頁面",
        "6. 人工輸入驗證碼，然後點擊「準備完成」",
        "7. 程式依 RTT offset 在觸發時間點擊「送出」按鈕", 
        "8. 程式等待系統回覆結果",
        "9. 程式記錄本次搶單結果"
    ]
    
    for step in flow_steps:
        print(f"  {step}")
    
    print("\n關鍵點:")
    print("  • 系統應該自動找到訂單，不需要用戶 Ctrl+F")
    print("  • 驗證碼在編輯頁面輸入，不是登入時")
    print("  • 「準備完成」在驗證碼輸入後")
    print("  • RTT 補償在最終送出時應用")

def main():
    """主測試函數"""
    print("AGES-KH-Bot 流程修正驗證")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("流程理解", test_flow_understanding),
        ("訂單搜尋邏輯", test_order_search_logic), 
        ("流程順序", test_flow_sequence),
        ("用戶交互點", test_user_interaction_points),
        ("RTT 整合", test_rtt_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 測試: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print_expected_flow()
    
    print("\n" + "="*60)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 流程修正驗證通過！")
        return True
    else:
        print("⚠️ 部分測試失敗，需要進一步調整")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
