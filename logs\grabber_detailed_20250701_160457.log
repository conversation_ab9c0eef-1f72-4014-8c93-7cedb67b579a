2025-07-01 16:04:57,299 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_160457.log
2025-07-01 16:05:24,348 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:05:24,349 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:05:24,406 - DEBUG - chromedriver not found in PATH
2025-07-01 16:05:24,406 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:05:24,406 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:05:24,406 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:05:24,407 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:05:24,407 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:05:24,407 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:05:24,411 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 12252 using 0 to output -3
2025-07-01 16:05:24,932 - DEBUG - POST http://localhost:54863/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:05:24,933 - DEBUG - Starting new HTTP connection (1): localhost:54863
2025-07-01 16:05:25,581 - DEBUG - http://localhost:54863 "POST /session HTTP/1.1" 200 0
2025-07-01 16:05:25,582 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir12252_1438372829"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54866"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"a6319d84e5acbc2248ca2defc112ce16"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:25,582 - DEBUG - Finished Request
2025-07-01 16:05:25,583 - DEBUG - POST http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:05:26,946 - DEBUG - http://localhost:54863 "POST /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:26,947 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:26,947 - DEBUG - Finished Request
2025-07-01 16:05:26,947 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:05:26,947 - DEBUG - POST http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:05:26,954 - DEBUG - http://localhost:54863 "POST /session/a6319d84e5acbc2248ca2defc112ce16/execute/sync HTTP/1.1" 200 0
2025-07-01 16:05:26,955 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:26,955 - DEBUG - Finished Request
2025-07-01 16:05:26,955 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:05:26,955 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:26,988 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:26,988 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:26,989 - DEBUG - Finished Request
2025-07-01 16:05:27,990 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:27,997 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:27,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:27,998 - DEBUG - Finished Request
2025-07-01 16:05:28,998 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:29,004 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:29,005 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:29,005 - DEBUG - Finished Request
2025-07-01 16:05:30,006 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:30,012 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:30,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:30,012 - DEBUG - Finished Request
2025-07-01 16:05:31,013 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:31,021 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:31,021 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:31,021 - DEBUG - Finished Request
2025-07-01 16:05:32,022 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:32,029 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:32,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:32,029 - DEBUG - Finished Request
2025-07-01 16:05:33,030 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:33,039 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:33,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:33,040 - DEBUG - Finished Request
2025-07-01 16:05:34,040 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:34,050 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:34,050 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:34,050 - DEBUG - Finished Request
2025-07-01 16:05:35,053 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:35,061 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:35,061 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:35,061 - DEBUG - Finished Request
2025-07-01 16:05:36,062 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:36,070 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:36,070 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:36,070 - DEBUG - Finished Request
2025-07-01 16:05:37,071 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:37,080 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:37,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:37,080 - DEBUG - Finished Request
2025-07-01 16:05:38,081 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:38,089 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:38,090 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:38,090 - DEBUG - Finished Request
2025-07-01 16:05:39,091 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:39,098 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:39,098 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:39,099 - DEBUG - Finished Request
2025-07-01 16:05:40,099 - DEBUG - GET http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16/url {}
2025-07-01 16:05:40,107 - DEBUG - http://localhost:54863 "GET /session/a6319d84e5acbc2248ca2defc112ce16/url HTTP/1.1" 200 0
2025-07-01 16:05:40,107 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:40,107 - DEBUG - Finished Request
2025-07-01 16:05:40,756 - DEBUG - DELETE http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16 {}
2025-07-01 16:05:40,797 - DEBUG - http://localhost:54863 "DELETE /session/a6319d84e5acbc2248ca2defc112ce16 HTTP/1.1" 200 0
2025-07-01 16:05:40,798 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:05:40,798 - DEBUG - Finished Request
2025-07-01 16:05:41,118 - DEBUG - DELETE http://localhost:54863/session/a6319d84e5acbc2248ca2defc112ce16 {}
2025-07-01 16:05:41,118 - DEBUG - Starting new HTTP connection (1): localhost:54863
