#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滑鼠鍵盤追蹤測試
Mouse and Keyboard Tracking Test
"""

import os
import sys
import time
import tkinter as tk
from tkinter import messagebox, scrolledtext
from datetime import datetime
import threading
import json

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MouseKeyboardTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🖱️⌨️ 滑鼠鍵盤追蹤測試")
        self.root.geometry("1400x900")
        
        self.tracking_active = False
        self.event_log = []
        self.browser_started = False

        self.setup_ui()
        
    def setup_ui(self):
        """設置 UI"""
        # 標題
        title_label = tk.Label(self.root, text="🖱️⌨️ 滑鼠鍵盤追蹤測試", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 說明
        info_text = """
💡 絕佳想法！通過記錄您的操作來找到送出按鈕位置

測試流程：
1. 啟動瀏覽器並登入系統
2. 導航到編輯頁面並打開編輯彈窗
3. 啟動追蹤
4. 手動操作：輸入驗證碼 → 點擊送出按鈕
5. 停止追蹤並分析結果

這樣我們就能知道送出按鈕的確切位置和屬性！
        """
        
        info_label = tk.Label(self.root, text=info_text, 
                             font=("Arial", 10), justify="left")
        info_label.pack(pady=5)
        
        # 狀態顯示
        self.status_label = tk.Label(self.root, text="準備開始追蹤", 
                                   font=("Arial", 12), fg="blue")
        self.status_label.pack(pady=5)
        
        # 按鈕區域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)

        # 啟動瀏覽器按鈕
        self.start_browser_btn = tk.Button(button_frame, text="🌐 啟動瀏覽器",
                                          command=self.start_browser,
                                          font=("Arial", 12), bg="lightcyan")
        self.start_browser_btn.pack(side="left", padx=10)

        # 啟動追蹤按鈕
        self.start_tracking_btn = tk.Button(button_frame, text="🎯 啟動追蹤",
                                           command=self.start_tracking,
                                           font=("Arial", 12), bg="lightgreen",
                                           state="disabled")
        self.start_tracking_btn.pack(side="left", padx=10)
        
        # 停止追蹤按鈕
        self.stop_tracking_btn = tk.Button(button_frame, text="⏹️ 停止追蹤", 
                                          command=self.stop_tracking,
                                          font=("Arial", 12), bg="lightcoral",
                                          state="disabled")
        self.stop_tracking_btn.pack(side="left", padx=10)
        
        # 分析結果按鈕
        self.analyze_btn = tk.Button(button_frame, text="📊 分析結果", 
                                    command=self.analyze_results,
                                    font=("Arial", 12), bg="lightblue",
                                    state="disabled")
        self.analyze_btn.pack(side="left", padx=10)
        
        # 檢查瀏覽器狀態按鈕
        self.check_browser_btn = tk.Button(button_frame, text="🔍 檢查瀏覽器",
                                          command=self.check_browser_status,
                                          font=("Arial", 12), bg="lightgray")
        self.check_browser_btn.pack(side="left", padx=10)

        # 清除記錄按鈕
        self.clear_btn = tk.Button(button_frame, text="🗑️ 清除記錄",
                                  command=self.clear_log,
                                  font=("Arial", 12), bg="lightyellow")
        self.clear_btn.pack(side="left", padx=10)
        
        # 關閉按鈕
        self.close_btn = tk.Button(button_frame, text="❌ 關閉", 
                                 command=self.close_app,
                                 font=("Arial", 12), bg="lightgray")
        self.close_btn.pack(side="right", padx=10)
        
        # 日誌顯示區域
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(log_frame, text="追蹤日誌:", font=("Arial", 12, "bold")).pack(anchor="w")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=35, width=160)
        self.log_text.pack(fill="both", expand=True)
        
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def start_browser(self):
        """啟動瀏覽器"""
        try:
            self.log("🌐 啟動瀏覽器...")
            self.start_browser_btn.config(state="disabled")

            def browser_thread():
                try:
                    from mvp_grabber import start_browser

                    # 測試任務配置
                    test_task = {
                        'browser': 'chrome',
                        'order_id': 'TRACKING_TEST',
                        'trigger_time': '09:30:00.001',
                        'model': 'A'
                    }

                    if start_browser(test_task):
                        self.browser_started = True
                        self.root.after(0, lambda: self.on_browser_started())
                    else:
                        self.root.after(0, lambda: self.on_browser_failed())

                except Exception as e:
                    self.root.after(0, lambda: self.on_browser_error(str(e)))

            threading.Thread(target=browser_thread, daemon=True).start()

        except Exception as e:
            self.log(f"❌ 啟動瀏覽器失敗: {e}")
            self.start_browser_btn.config(state="normal")

    def on_browser_started(self):
        """瀏覽器啟動成功回調"""
        self.log("✅ 瀏覽器啟動成功")
        self.log("📋 請在瀏覽器中執行以下步驟：")
        self.log("  1. 登入系統")
        self.log("  2. 導航到進廠確認單列表")
        self.log("  3. 點擊編輯按鈕打開編輯彈窗")
        self.log("  4. 確保編輯彈窗完全打開")
        self.log("  5. 回到此工具點擊 '啟動追蹤'")

        self.start_tracking_btn.config(state="normal")
        self.status_label.config(text="瀏覽器已啟動，請手動導航到編輯彈窗", fg="green")

    def on_browser_failed(self):
        """瀏覽器啟動失敗回調"""
        self.log("❌ 瀏覽器啟動失敗")
        self.start_browser_btn.config(state="normal")
        self.status_label.config(text="瀏覽器啟動失敗", fg="red")

    def on_browser_error(self, error):
        """瀏覽器啟動錯誤回調"""
        self.log(f"❌ 瀏覽器啟動錯誤: {error}")
        self.start_browser_btn.config(state="normal")
        self.status_label.config(text="瀏覽器啟動錯誤", fg="red")

    def check_browser_status(self):
        """檢查瀏覽器狀態"""
        try:
            self.log("🔍 檢查瀏覽器狀態...")

            from mvp_grabber import driver

            if not driver:
                self.log("❌ 瀏覽器未啟動")
                self.browser_started = False
                self.start_browser_btn.config(state="normal")
                self.start_tracking_btn.config(state="disabled")
                self.status_label.config(text="瀏覽器未啟動", fg="red")
                return

            try:
                # 檢查瀏覽器是否還活著
                current_url = driver.current_url
                page_title = driver.title

                self.log(f"✅ 瀏覽器正常運行")
                self.log(f"📍 當前 URL: {current_url}")
                self.log(f"📍 頁面標題: {page_title}")

                # 檢查是否在編輯頁面
                page_content = driver.execute_script("return document.body.innerText || '';")

                if '編輯進廠確認單' in page_content:
                    self.log("✅ 檢測到編輯彈窗，可以開始追蹤")
                    self.status_label.config(text="瀏覽器正常，已在編輯頁面", fg="green")
                elif '進廠確認單' in page_content:
                    self.log("⚠️ 在進廠確認單頁面，請點擊編輯按鈕打開編輯彈窗")
                    self.status_label.config(text="請打開編輯彈窗", fg="orange")
                else:
                    self.log("⚠️ 不在進廠確認單頁面，請手動導航")
                    self.status_label.config(text="請導航到編輯頁面", fg="orange")

                self.browser_started = True
                self.start_tracking_btn.config(state="normal")

            except Exception as e:
                self.log(f"❌ 瀏覽器連接失敗: {e}")
                self.browser_started = False
                self.start_browser_btn.config(state="normal")
                self.start_tracking_btn.config(state="disabled")
                self.status_label.config(text="瀏覽器連接失敗", fg="red")

        except Exception as e:
            self.log(f"❌ 檢查瀏覽器狀態失敗: {e}")

    def start_tracking(self):
        """啟動追蹤"""
        try:
            if not self.browser_started:
                self.log("❌ 請先啟動瀏覽器")
                return

            self.log("🎯 啟動滑鼠鍵盤追蹤...")

            from mvp_grabber import driver, setup_browser_event_monitoring

            if not driver:
                self.log("❌ 瀏覽器連接失敗，請重新啟動瀏覽器")
                self.browser_started = False
                self.start_browser_btn.config(state="normal")
                return
            
            # 啟動瀏覽器事件監控
            setup_browser_event_monitoring()
            
            self.tracking_active = True
            self.event_log = []
            
            self.start_tracking_btn.config(state="disabled")
            self.stop_tracking_btn.config(state="normal")
            self.status_label.config(text="🔴 追蹤中...", fg="red")
            
            self.log("✅ 追蹤已啟動")
            self.log("📋 請在瀏覽器中執行以下操作：")
            self.log("  1. 確保編輯彈窗已打開")
            self.log("  2. 輸入驗證碼")
            self.log("  3. 點擊送出按鈕")
            self.log("  4. 回到此視窗點擊 '停止追蹤'")
            
            # 開始定期收集事件
            self.collect_events()
            
        except Exception as e:
            self.log(f"❌ 啟動追蹤失敗: {e}")
            
    def collect_events(self):
        """收集事件"""
        if not self.tracking_active:
            return
            
        try:
            from mvp_grabber import get_browser_event_log
            
            # 獲取瀏覽器事件
            browser_events = get_browser_event_log()
            
            # 只處理新事件
            new_events = browser_events[len(self.event_log):]
            
            for event in new_events:
                self.event_log.append(event)
                self.process_event(event)
            
            # 每500毫秒檢查一次
            if self.tracking_active:
                self.root.after(500, self.collect_events)
                
        except Exception as e:
            self.log(f"❌ 收集事件失敗: {e}")
            
    def process_event(self, event):
        """處理單個事件"""
        try:
            event_type = event.get('type', 'unknown')
            
            if event_type == 'click':
                target = event.get('target', {})
                coords = event.get('coordinates', {})
                
                tag_name = target.get('tagName', '')
                text = target.get('textContent', '')[:50]
                value = target.get('value', '')
                class_name = target.get('className', '')
                
                client_x = coords.get('clientX', 0)
                client_y = coords.get('clientY', 0)
                page_x = coords.get('pageX', 0)
                page_y = coords.get('pageY', 0)
                
                self.log(f"🖱️ 點擊: <{tag_name}> '{text}' value='{value}' class='{class_name}' 座標=({client_x},{client_y}) 頁面=({page_x},{page_y})")
                
            elif event_type == 'keydown':
                target = event.get('target', {})
                key = event.get('key', '')
                
                tag_name = target.get('tagName', '')
                target_type = target.get('type', '')
                value = target.get('value', '')[:20]
                
                self.log(f"⌨️ 按鍵: '{key}' 目標=<{tag_name} type='{target_type}'> 值='{value}'")
                
            elif event_type == 'input':
                target = event.get('target', {})
                
                tag_name = target.get('tagName', '')
                target_type = target.get('type', '')
                value = target.get('value', '')[:20]
                
                self.log(f"📝 輸入: <{tag_name} type='{target_type}'> 值='{value}'")
                
        except Exception as e:
            self.log(f"❌ 處理事件失敗: {e}")
            
    def stop_tracking(self):
        """停止追蹤"""
        try:
            self.tracking_active = False
            
            self.start_tracking_btn.config(state="normal")
            self.stop_tracking_btn.config(state="disabled")
            self.analyze_btn.config(state="normal")
            self.status_label.config(text="⏹️ 追蹤已停止", fg="blue")
            
            self.log(f"⏹️ 追蹤已停止，共記錄 {len(self.event_log)} 個事件")
            
        except Exception as e:
            self.log(f"❌ 停止追蹤失敗: {e}")
            
    def analyze_results(self):
        """分析結果"""
        try:
            self.log("\n📊 開始分析追蹤結果...")
            
            if not self.event_log:
                self.log("❌ 沒有事件記錄可分析")
                return
            
            # 分析事件類型
            event_types = {}
            click_events = []
            key_events = []
            input_events = []
            
            for event in self.event_log:
                event_type = event.get('type', 'unknown')
                event_types[event_type] = event_types.get(event_type, 0) + 1
                
                if event_type == 'click':
                    click_events.append(event)
                elif event_type == 'keydown':
                    key_events.append(event)
                elif event_type == 'input':
                    input_events.append(event)
            
            self.log(f"📊 事件統計:")
            for event_type, count in event_types.items():
                self.log(f"  {event_type}: {count} 個")
            
            # 分析點擊事件
            self.log(f"\n🖱️ 點擊事件分析 ({len(click_events)} 個):")
            for i, event in enumerate(click_events):
                target = event.get('target', {})
                coords = event.get('coordinates', {})
                
                tag_name = target.get('tagName', '')
                text = target.get('textContent', '')[:30]
                value = target.get('value', '')
                class_name = target.get('className', '')[:30]
                
                client_x = coords.get('clientX', 0)
                client_y = coords.get('clientY', 0)
                
                # 檢查是否可能是送出按鈕
                submit_indicators = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save']
                all_text = f"{text} {value} {class_name}".lower()
                found_indicators = [ind for ind in submit_indicators if ind in all_text]
                
                indicator_text = f" 🎯送出指標={found_indicators}" if found_indicators else ""
                
                self.log(f"  點擊 {i+1}: <{tag_name}> '{text}' value='{value}' class='{class_name}' 座標=({client_x},{client_y}){indicator_text}")
            
            # 分析鍵盤事件
            self.log(f"\n⌨️ 鍵盤事件分析 ({len(key_events)} 個):")
            for i, event in enumerate(key_events[-10:]):  # 只顯示最後10個
                key = event.get('key', '')
                target = event.get('target', {})
                target_type = target.get('type', '')
                value = target.get('value', '')[:10]
                
                self.log(f"  按鍵 {i+1}: '{key}' 目標類型='{target_type}' 值='{value}'")
            
            # 尋找可能的送出按鈕點擊
            self.log(f"\n🎯 送出按鈕候選分析:")
            submit_candidates = []
            
            for event in click_events:
                target = event.get('target', {})
                coords = event.get('coordinates', {})
                
                text = target.get('textContent', '')
                value = target.get('value', '')
                class_name = target.get('className', '')
                onclick = target.get('onclick', '')
                
                all_text = f"{text} {value} {class_name} {onclick}".lower()
                submit_indicators = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok']
                found_indicators = [ind for ind in submit_indicators if ind in all_text]
                
                if found_indicators or target.get('tagName') == 'BUTTON':
                    submit_candidates.append({
                        'event': event,
                        'indicators': found_indicators,
                        'coordinates': coords
                    })
            
            if submit_candidates:
                self.log(f"  找到 {len(submit_candidates)} 個送出按鈕候選:")
                for i, candidate in enumerate(submit_candidates):
                    event = candidate['event']
                    target = event.get('target', {})
                    coords = candidate['coordinates']
                    indicators = candidate['indicators']
                    
                    tag_name = target.get('tagName', '')
                    text = target.get('textContent', '')[:30]
                    value = target.get('value', '')
                    client_x = coords.get('clientX', 0)
                    client_y = coords.get('clientY', 0)
                    
                    self.log(f"    候選 {i+1}: <{tag_name}> '{text}' value='{value}' 座標=({client_x},{client_y}) 指標={indicators}")
            else:
                self.log("  ❌ 沒有找到明顯的送出按鈕候選")
            
            # 保存詳細分析結果
            self.save_analysis_results()
            
            self.log("\n✅ 分析完成")
            
        except Exception as e:
            self.log(f"❌ 分析失敗: {e}")
            
    def save_analysis_results(self):
        """保存分析結果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mouse_keyboard_analysis_{timestamp}.json"
            
            analysis_data = {
                'timestamp': timestamp,
                'total_events': len(self.event_log),
                'events': self.event_log
            }
            
            os.makedirs("analysis", exist_ok=True)
            filepath = os.path.join("analysis", filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)
            
            self.log(f"💾 分析結果已保存: {filepath}")
            
        except Exception as e:
            self.log(f"❌ 保存分析結果失敗: {e}")
            
    def clear_log(self):
        """清除記錄"""
        self.log_text.delete(1.0, tk.END)
        self.event_log = []
        self.analyze_btn.config(state="disabled")
        self.log("🗑️ 記錄已清除")
        
    def close_app(self):
        """關閉應用"""
        self.tracking_active = False
        self.root.destroy()
        
    def run(self):
        """運行 GUI"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🖱️⌨️ 啟動滑鼠鍵盤追蹤測試")
    
    try:
        app = MouseKeyboardTracker()
        app.run()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")

if __name__ == "__main__":
    main()
