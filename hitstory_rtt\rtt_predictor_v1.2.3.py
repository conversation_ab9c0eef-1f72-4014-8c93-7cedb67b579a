# rtt_predictor.py
# Version: 1.2.3
# Author: <PERSON> Wang
# Last Modified: 2024-06-18
# Description:
#   本模組負責在指定時間區間內對外部伺服器進行 RTT 採樣，回傳主要指標（如 avg_rtt、median_rtt 等）及原始數據。
#   支援多模型（目前僅 Model A），可單獨執行或由主程式調用。
#   執行結果自動產生 log 及 raw data 檔案，便於後續分析與追蹤。
#   設計邊界：本模組設計為單一目標單一模型單執行緒運作，若需多目標/多模型/多線程，請自行管理執行流程與資源。
#   log 檔案格式：
#     [時間 model=A avg_rtt=318ms samples=73/120 error_count=47 error_rate=39.17% sample_start=... sample_end=...]
#     未來可擴充欄位（如 device_name, user, target_url 等），皆以 key=value 方式記錄。
#   API 設計原則：
#     - get_rtt_result(model="A", ...) 回傳 dict，主指標名稱依 model 而定（如 avg_rtt、median_rtt...）。
#     - 主程式只需根據 model 取對應 key，彈性支援多模型。
#     - get_avg_rtt(...) 為舊API，僅支援 model="A"，回傳 (avg_rtt, rtts, timestamps)，內部呼叫 get_rtt_result。
#   外部調用範例：
#     from rtt_predictor import get_rtt_result
#     result = get_rtt_result(model="A", freq=2, duration=60, server_url="https://wmc.kcg.gov.tw/")
#     print(result["avg_rtt"])

import time
import datetime
import requests
import os
import sys
import csv
import threading
import json
from typing import Tuple, List, Dict, Optional
import socket
import uuid
import getpass

LOG_DIR = 'logs'
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

SAMPLE_START_TIME = None
SAMPLE_END_TIME = None

def get_rtt_result(model: str = "A",
                   freq: Optional[int] = None,
                   duration: Optional[int] = None,
                   server_url: Optional[str] = None,
                   sample_start_time: Optional[datetime.datetime] = None,
                   sample_end_time: Optional[datetime.datetime] = None) -> dict:
    """
    取得指定模型的 RTT 結果，回傳 dict，主指標名稱依 model 而定。
    Args:
        model: 預測模型（目前支援 "A"）
        freq: 採樣頻率（次/秒）
        duration: 採樣時長（秒）
        server_url: 目標伺服器 URL
        sample_start_time: 採樣開始時間（datetime，可選）
        sample_end_time: 採樣結束時間（datetime，可選）
    Returns:
        dict: 例如 {'model': 'A', 'avg_rtt': 318}
    """
    if model == "A":
        if freq is None:
            freq = 2
        if duration is None:
            duration = 60
        if server_url is None:
            server_url = "https://wmc.kcg.gov.tw/"
        rtts, timestamps, error_count = sample_rtt(server_url, freq, duration, sample_start_time, sample_end_time)
        avg_rtt = sum(rtts) / len(rtts) if rtts else 0
        return {"model": "A", "avg_rtt": avg_rtt, "error_count": error_count, "samples": len(rtts)}
    # 未來可擴充其他模型
    raise NotImplementedError(f"Model {model} not implemented.")

def get_avg_rtt(model: str = "A",
                freq: Optional[int] = None,
                duration: Optional[int] = None,
                server_url: Optional[str] = None,
                sample_start_time: Optional[datetime.datetime] = None,
                sample_end_time: Optional[datetime.datetime] = None) -> Tuple[float, List[float], List[datetime.datetime]]:
    """
    舊API，回傳 (avg_rtt, rtts, timestamps)，內部呼叫 get_rtt_result。
    """
    if model != "A":
        raise NotImplementedError("get_avg_rtt 只支援 model='A'")
    if freq is None:
        freq = 2
    if duration is None:
        duration = 60
    if server_url is None:
        server_url = "https://wmc.kcg.gov.tw/"
    rtts, timestamps, _ = sample_rtt(server_url, freq, duration, sample_start_time, sample_end_time)
    avg_rtt = sum(rtts) / len(rtts) if rtts else 0
    return avg_rtt, rtts, timestamps

def get_sample_basename():
    """
    產生本次採樣的唯一檔名基礎（依據採樣開始時間）。
    Returns: str
    """
    global SAMPLE_START_TIME
    if SAMPLE_START_TIME is None:
        SAMPLE_START_TIME = datetime.datetime.now()
    return SAMPLE_START_TIME.strftime("rtt_%Y%m%d_%H%M%S.%f")[:24]

def sample_rtt(server_url, freq=2, duration=60, sample_start_time=None, sample_end_time=None):
    """
    在指定時間區間內對 server_url 進行 RTT 採樣。
    Returns:
        (list[float], list[datetime.datetime], int): RTT 毫秒清單、對應時間戳、錯誤次數
    """
    global SAMPLE_START_TIME, SAMPLE_END_TIME
    now = datetime.datetime.now()
    if sample_start_time and now < sample_start_time:
        wait_sec = (sample_start_time - now).total_seconds()
        print(f"[INFO] 等待 {wait_sec:.2f} 秒後開始 RTT 採樣...")
        time.sleep(max(0, wait_sec))
        SAMPLE_START_TIME = sample_start_time
    else:
        SAMPLE_START_TIME = now
    rtts = []
    timestamps = []
    error_count = 0
    count = 0
    while True:
        ts = datetime.datetime.now()
        if sample_end_time and ts >= sample_end_time:
            break
        if duration and (ts - SAMPLE_START_TIME).total_seconds() >= duration:
            break
        start = time.time()
        try:
            requests.head(server_url, timeout=3)
        except Exception as e:
            error_count += 1
            print(f"[ERROR] RTT 請求失敗: {e} @ {ts}")
            continue
        rtt = (time.time() - start) * 1000  # ms
        rtts.append(rtt)
        timestamps.append(ts)
        count += 1
        time.sleep(1 / freq)
    SAMPLE_END_TIME = datetime.datetime.now()
    return rtts, timestamps, error_count

def get_mac():
    mac = uuid.getnode()
    return ':'.join(['{:02x}'.format((mac >> ele) & 0xff) for ele in range(40, -1, -8)])

def get_local_ip(target_url):
    try:
        # 取得本地IP（根據目標URL建立UDP連線，不會真的發送資料）
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect((socket.gethostbyname(socket.getfqdn(target_url.replace('https://','').replace('http://','').split('/')[0])), 80))
            ip = s.getsockname()[0]
        return ip
    except Exception:
        return ''

def get_target_ip(target_url):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            ip = socket.gethostbyname(target_url.replace('https://','').replace('http://','').split('/')[0])
        return ip
    except Exception:
        return ''

def write_raw_rtt_csv(rtts, timestamps, target_url=None):
    """
    將每筆 RTT 原始數據寫入 .csv 檔案。
    Args:
        rtts (list[float]): RTT 毫秒清單
        timestamps (list[datetime.datetime]): 對應時間戳
        target_url (str): 目標伺服器網址
    """
    global SAMPLE_START_TIME
    basename = get_sample_basename()
    csv_file = os.path.join(LOG_DIR, f"{basename}.csv")
    device_name = socket.gethostname()
    device_id = get_mac()
    user = getpass.getuser()
    local_ip = get_local_ip(target_url) if target_url else ''
    target_ip = get_target_ip(target_url) if target_url else ''
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(["seq", "timestamp", "device_name", "device_id", "user", "local_ip", "target_url", "target_ip", "rtt_ms"])
        for idx, (ts, rtt) in enumerate(zip(timestamps, rtts), 1):
            writer.writerow([
                idx,
                ts.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
                device_name,
                device_id,
                user,
                local_ip,
                target_url or '',
                target_ip,
                f"{rtt:.2f}"
            ])

def log_rtt(model, avg_rtt, sample_count, expected_count, sample_start, sample_end, error_count=0):
    """
    記錄本次採樣摘要資訊到 .log 檔案。
    Args:
        model (str): 使用的模型
        avg_rtt (float): 平均 RTT
        sample_count (int): 實際採樣筆數
        expected_count (int): 預計採樣筆數
        sample_start (datetime): 採樣開始時間
        sample_end (datetime): 採樣結束時間
        error_count (int): 錯誤次數
    """
    basename = get_sample_basename()
    log_file = os.path.join(LOG_DIR, f"{basename}.log")
    now = datetime.datetime.now().strftime("[%H:%M:%S.%f]")[:-3]
    error_rate = f"{(error_count/(sample_count+error_count)*100):.2f}%" if (sample_count+error_count)>0 else "0.00%"
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"{now} model={model} avg_rtt={int(avg_rtt)}ms samples={sample_count}/{sample_count+error_count} error_count={error_count} error_rate={error_rate} sample_start={sample_start.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} sample_end={sample_end.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}\n")

if __name__ == "__main__":
    try:
        # 直接呼叫 sample_rtt 取得實際 error_count
        rtts, timestamps, error_count = sample_rtt("https://wmc.kcg.gov.tw/", freq=2, duration=60)
        avg_rtt = sum(rtts) / len(rtts) if rtts else 0
        expected_count = int(60 * 2)  # 預設 duration=60, freq=2
        print(f"平均 RTT: {avg_rtt:.2f} ms")
        print(f"本次實際採樣 RTT 筆數：{len(rtts)} / {expected_count}")
        print(f"錯誤次數：{error_count}")
        print(f"採樣起訖：{SAMPLE_START_TIME.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} ~ {SAMPLE_END_TIME.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        # 新增：自動寫入 log 與 raw data
        write_raw_rtt_csv(rtts, timestamps, target_url="https://wmc.kcg.gov.tw/")
        log_rtt("A", avg_rtt, len(rtts), expected_count, SAMPLE_START_TIME, SAMPLE_END_TIME, error_count=error_count)
        sys.exit(0)
    except KeyboardInterrupt:
        print("使用者手動中斷")
        sys.exit(1)
    except Exception as e:
        print(f"發生錯誤: {e}")
        sys.exit(2) 