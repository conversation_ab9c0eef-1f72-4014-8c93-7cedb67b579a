2025-07-01 20:25:30,520 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_202530.log
2025-07-01 20:25:33,468 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 20:25:33,469 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 20:25:33,539 - DEBUG - chromedriver not found in PATH
2025-07-01 20:25:33,540 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 20:25:33,540 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 20:25:33,540 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 20:25:33,541 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 20:25:33,541 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 20:25:33,541 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 20:25:33,546 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 10488 using 0 to output -3
2025-07-01 20:25:34,073 - DEBUG - POST http://localhost:59856/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 20:25:34,074 - DEBUG - Starting new HTTP connection (1): localhost:59856
2025-07-01 20:25:34,619 - DEBUG - http://localhost:59856 "POST /session HTTP/1.1" 200 0
2025-07-01 20:25:34,619 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir10488_1152664362"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:59860"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"6618168b01c50643a55f07a4240bbea3"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:34,619 - DEBUG - Finished Request
2025-07-01 20:25:34,620 - DEBUG - POST http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 20:25:36,580 - DEBUG - http://localhost:59856 "POST /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:36,581 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:36,581 - DEBUG - Finished Request
2025-07-01 20:25:36,581 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 20:25:36,582 - DEBUG - POST http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 20:25:36,590 - DEBUG - http://localhost:59856 "POST /session/6618168b01c50643a55f07a4240bbea3/execute/sync HTTP/1.1" 200 0
2025-07-01 20:25:36,591 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:36,591 - DEBUG - Finished Request
2025-07-01 20:25:36,591 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 20:25:36,592 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:36,627 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:36,627 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:36,627 - DEBUG - Finished Request
2025-07-01 20:25:37,628 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:37,637 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:37,638 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:37,638 - DEBUG - Finished Request
2025-07-01 20:25:38,639 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:38,646 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:38,646 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:38,646 - DEBUG - Finished Request
2025-07-01 20:25:39,647 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:39,654 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:39,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:39,655 - DEBUG - Finished Request
2025-07-01 20:25:40,656 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:40,663 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:40,663 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:40,663 - DEBUG - Finished Request
2025-07-01 20:25:41,664 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:41,670 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:41,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:41,671 - DEBUG - Finished Request
2025-07-01 20:25:42,672 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:42,680 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:42,680 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:42,680 - DEBUG - Finished Request
2025-07-01 20:25:43,683 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:43,691 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:43,692 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:43,692 - DEBUG - Finished Request
2025-07-01 20:25:44,693 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:44,699 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:44,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:44,700 - DEBUG - Finished Request
2025-07-01 20:25:45,700 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:45,707 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:45,707 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:45,708 - DEBUG - Finished Request
2025-07-01 20:25:46,709 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:46,717 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:46,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:46,717 - DEBUG - Finished Request
2025-07-01 20:25:47,719 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:47,726 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:47,727 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:47,727 - DEBUG - Finished Request
2025-07-01 20:25:48,728 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:48,739 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:48,740 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:48,741 - DEBUG - Finished Request
2025-07-01 20:25:49,742 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:49,748 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:49,748 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:49,749 - DEBUG - Finished Request
2025-07-01 20:25:50,749 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:50,756 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:50,757 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:50,757 - DEBUG - Finished Request
2025-07-01 20:25:51,759 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:51,767 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:51,767 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:51,768 - DEBUG - Finished Request
2025-07-01 20:25:52,768 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:52,775 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:52,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:52,775 - DEBUG - Finished Request
2025-07-01 20:25:53,777 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:53,785 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:53,786 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:53,786 - DEBUG - Finished Request
2025-07-01 20:25:54,786 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:54,794 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:54,794 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:54,794 - DEBUG - Finished Request
2025-07-01 20:25:55,795 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:55,802 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:55,802 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:55,802 - DEBUG - Finished Request
2025-07-01 20:25:56,804 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:56,811 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:56,811 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:56,812 - DEBUG - Finished Request
2025-07-01 20:25:57,813 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:57,821 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:57,821 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:57,822 - DEBUG - Finished Request
2025-07-01 20:25:58,822 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:58,830 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:58,830 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:58,830 - DEBUG - Finished Request
2025-07-01 20:25:59,831 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:25:59,839 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:25:59,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:25:59,840 - DEBUG - Finished Request
2025-07-01 20:26:00,840 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:00,847 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:00,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:00,847 - DEBUG - Finished Request
2025-07-01 20:26:01,849 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:01,855 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:01,855 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:01,856 - DEBUG - Finished Request
2025-07-01 20:26:02,857 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:02,864 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:02,865 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:02,865 - DEBUG - Finished Request
2025-07-01 20:26:03,866 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:03,873 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:03,873 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:03,873 - DEBUG - Finished Request
2025-07-01 20:26:04,875 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:04,882 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:04,882 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:04,882 - DEBUG - Finished Request
2025-07-01 20:26:05,884 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:06,471 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:06,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:06,471 - DEBUG - Finished Request
2025-07-01 20:26:07,472 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:07,479 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:07,479 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:07,479 - DEBUG - Finished Request
2025-07-01 20:26:08,479 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:08,487 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:08,488 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:08,488 - DEBUG - Finished Request
2025-07-01 20:26:09,489 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:09,497 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:09,497 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:09,497 - DEBUG - Finished Request
2025-07-01 20:26:10,498 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:10,506 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:10,507 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:10,507 - DEBUG - Finished Request
2025-07-01 20:26:11,509 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:11,515 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:11,515 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:11,515 - DEBUG - Finished Request
2025-07-01 20:26:12,517 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:12,524 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:12,524 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:12,525 - DEBUG - Finished Request
2025-07-01 20:26:13,526 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:13,533 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:13,533 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:13,533 - DEBUG - Finished Request
2025-07-01 20:26:14,534 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:14,541 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:14,541 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:14,542 - DEBUG - Finished Request
2025-07-01 20:26:15,543 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:15,550 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:15,550 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:15,550 - DEBUG - Finished Request
2025-07-01 20:26:16,551 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:16,559 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:16,559 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:16,559 - DEBUG - Finished Request
2025-07-01 20:26:17,560 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:17,569 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:17,569 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:17,569 - DEBUG - Finished Request
2025-07-01 20:26:18,570 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:18,577 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:18,577 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:18,577 - DEBUG - Finished Request
2025-07-01 20:26:19,577 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:19,584 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:19,584 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:19,584 - DEBUG - Finished Request
2025-07-01 20:26:20,585 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:20,592 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:20,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:20,593 - DEBUG - Finished Request
2025-07-01 20:26:21,593 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:21,600 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:21,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:21,600 - DEBUG - Finished Request
2025-07-01 20:26:22,602 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:22,610 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:22,610 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:22,610 - DEBUG - Finished Request
2025-07-01 20:26:23,611 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:23,618 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:23,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:23,618 - DEBUG - Finished Request
2025-07-01 20:26:24,619 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:24,627 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:24,628 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:24,628 - DEBUG - Finished Request
2025-07-01 20:26:25,629 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:25,635 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:25,635 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:25,635 - DEBUG - Finished Request
2025-07-01 20:26:26,636 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:26,643 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:26,643 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:26,644 - DEBUG - Finished Request
2025-07-01 20:26:27,645 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:27,652 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:27,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:27,653 - DEBUG - Finished Request
2025-07-01 20:26:28,654 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:28,661 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:28,661 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:28,661 - DEBUG - Finished Request
2025-07-01 20:26:29,663 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:29,670 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:29,670 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:29,671 - DEBUG - Finished Request
2025-07-01 20:26:30,672 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:30,679 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:30,680 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:30,680 - DEBUG - Finished Request
2025-07-01 20:26:31,681 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:31,687 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:31,687 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:31,687 - DEBUG - Finished Request
2025-07-01 20:26:32,689 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:32,694 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:32,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:32,695 - DEBUG - Finished Request
2025-07-01 20:26:33,696 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:33,702 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:33,702 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:33,702 - DEBUG - Finished Request
2025-07-01 20:26:34,703 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:34,712 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:34,712 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:34,713 - DEBUG - Finished Request
2025-07-01 20:26:35,713 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:35,720 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:35,721 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:35,722 - DEBUG - Finished Request
2025-07-01 20:26:36,723 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:36,732 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:36,732 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:36,733 - DEBUG - Finished Request
2025-07-01 20:26:37,733 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:37,742 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:37,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:37,743 - DEBUG - Finished Request
2025-07-01 20:26:38,744 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:38,754 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:38,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:38,754 - DEBUG - Finished Request
2025-07-01 20:26:39,755 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:39,762 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:39,762 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:39,763 - DEBUG - Finished Request
2025-07-01 20:26:40,764 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:40,771 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:40,771 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:40,771 - DEBUG - Finished Request
2025-07-01 20:26:41,772 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:41,780 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:41,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:41,780 - DEBUG - Finished Request
2025-07-01 20:26:42,781 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:42,788 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:42,788 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:42,789 - DEBUG - Finished Request
2025-07-01 20:26:43,790 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:43,798 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:43,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:43,798 - DEBUG - Finished Request
2025-07-01 20:26:44,799 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:44,807 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:44,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:44,808 - DEBUG - Finished Request
2025-07-01 20:26:45,808 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:45,816 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:45,817 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:45,817 - DEBUG - Finished Request
2025-07-01 20:26:46,818 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:46,827 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:46,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:46,828 - DEBUG - Finished Request
2025-07-01 20:26:47,829 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:47,837 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:47,837 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:47,838 - DEBUG - Finished Request
2025-07-01 20:26:48,839 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:48,847 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:48,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:48,847 - DEBUG - Finished Request
2025-07-01 20:26:49,848 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:49,856 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:49,856 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:49,857 - DEBUG - Finished Request
2025-07-01 20:26:50,858 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:50,867 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:50,868 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:50,868 - DEBUG - Finished Request
2025-07-01 20:26:51,869 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:51,876 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:51,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:51,877 - DEBUG - Finished Request
2025-07-01 20:26:52,878 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:52,886 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:52,886 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:52,887 - DEBUG - Finished Request
2025-07-01 20:26:53,888 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:53,896 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:53,896 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:53,897 - DEBUG - Finished Request
2025-07-01 20:26:54,898 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:54,905 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:54,905 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:54,905 - DEBUG - Finished Request
2025-07-01 20:26:55,906 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:55,914 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:55,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:55,915 - DEBUG - Finished Request
2025-07-01 20:26:56,917 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:56,926 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:56,926 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:56,927 - DEBUG - Finished Request
2025-07-01 20:26:57,927 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:57,935 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:57,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:57,936 - DEBUG - Finished Request
2025-07-01 20:26:58,937 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:58,944 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:58,944 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:58,945 - DEBUG - Finished Request
2025-07-01 20:26:59,946 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:26:59,956 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:26:59,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:26:59,957 - DEBUG - Finished Request
2025-07-01 20:27:00,958 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:00,967 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:00,968 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:00,968 - DEBUG - Finished Request
2025-07-01 20:27:01,969 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:01,976 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:01,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:01,976 - DEBUG - Finished Request
2025-07-01 20:27:02,977 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:02,986 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:02,986 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:02,987 - DEBUG - Finished Request
2025-07-01 20:27:03,987 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:03,996 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:03,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:03,996 - DEBUG - Finished Request
2025-07-01 20:27:04,998 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:05,005 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:05,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:05,006 - DEBUG - Finished Request
2025-07-01 20:27:06,007 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:06,017 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:06,017 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:06,018 - DEBUG - Finished Request
2025-07-01 20:27:07,019 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:07,029 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:07,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:07,030 - DEBUG - Finished Request
2025-07-01 20:27:08,031 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:08,041 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:08,041 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:08,041 - DEBUG - Finished Request
2025-07-01 20:27:09,044 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:09,052 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:09,053 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:09,053 - DEBUG - Finished Request
2025-07-01 20:27:10,054 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:10,062 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:10,063 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:10,063 - DEBUG - Finished Request
2025-07-01 20:27:11,064 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:11,074 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:11,074 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:11,075 - DEBUG - Finished Request
2025-07-01 20:27:12,075 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:12,083 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:12,084 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:12,084 - DEBUG - Finished Request
2025-07-01 20:27:13,085 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:13,094 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:13,094 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:13,094 - DEBUG - Finished Request
2025-07-01 20:27:14,096 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:14,105 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:14,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:14,106 - DEBUG - Finished Request
2025-07-01 20:27:15,107 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:15,117 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:15,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:15,118 - DEBUG - Finished Request
2025-07-01 20:27:16,119 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:16,126 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:16,127 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:16,127 - DEBUG - Finished Request
2025-07-01 20:27:17,128 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:17,137 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:17,137 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:17,137 - DEBUG - Finished Request
2025-07-01 20:27:18,138 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:18,148 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:18,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:18,148 - DEBUG - Finished Request
2025-07-01 20:27:19,149 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:19,157 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:19,157 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:19,157 - DEBUG - Finished Request
2025-07-01 20:27:20,158 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:20,170 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:20,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:20,171 - DEBUG - Finished Request
2025-07-01 20:27:21,172 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:21,181 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:21,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:21,181 - DEBUG - Finished Request
2025-07-01 20:27:22,183 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:22,190 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:22,191 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:22,191 - DEBUG - Finished Request
2025-07-01 20:27:23,192 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:23,200 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:23,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:23,201 - DEBUG - Finished Request
2025-07-01 20:27:24,202 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:24,211 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:24,211 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:24,212 - DEBUG - Finished Request
2025-07-01 20:27:25,213 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:25,224 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:25,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:25,225 - DEBUG - Finished Request
2025-07-01 20:27:26,225 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:26,234 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:26,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:26,235 - DEBUG - Finished Request
2025-07-01 20:27:27,236 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:27,246 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:27,246 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:27,247 - DEBUG - Finished Request
2025-07-01 20:27:28,248 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:28,255 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:28,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:28,256 - DEBUG - Finished Request
2025-07-01 20:27:29,258 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:29,266 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:29,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:29,267 - DEBUG - Finished Request
2025-07-01 20:27:30,268 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:30,277 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:30,277 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:30,278 - DEBUG - Finished Request
2025-07-01 20:27:31,279 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:31,287 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:31,287 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:31,288 - DEBUG - Finished Request
2025-07-01 20:27:32,288 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:32,299 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:32,299 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:32,300 - DEBUG - Finished Request
2025-07-01 20:27:33,301 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:33,311 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:33,311 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:33,312 - DEBUG - Finished Request
2025-07-01 20:27:34,312 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:34,319 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:34,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:34,319 - DEBUG - Finished Request
2025-07-01 20:27:35,320 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:35,329 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:35,329 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:35,329 - DEBUG - Finished Request
2025-07-01 20:27:36,330 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:36,339 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:36,340 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:36,340 - DEBUG - Finished Request
2025-07-01 20:27:37,342 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:37,350 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:37,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:37,351 - DEBUG - Finished Request
2025-07-01 20:27:38,352 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:38,361 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:38,361 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:38,361 - DEBUG - Finished Request
2025-07-01 20:27:39,362 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:39,371 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:39,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:39,372 - DEBUG - Finished Request
2025-07-01 20:27:40,373 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:40,383 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:40,383 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:40,384 - DEBUG - Finished Request
2025-07-01 20:27:41,384 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:41,392 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:41,392 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:41,392 - DEBUG - Finished Request
2025-07-01 20:27:42,393 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:42,400 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:42,400 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:42,400 - DEBUG - Finished Request
2025-07-01 20:27:43,401 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:43,408 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:43,409 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:43,409 - DEBUG - Finished Request
2025-07-01 20:27:44,410 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:44,416 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 200 0
2025-07-01 20:27:44,416 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:44,416 - DEBUG - Finished Request
2025-07-01 20:27:45,417 - DEBUG - GET http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3/url {}
2025-07-01 20:27:45,419 - DEBUG - http://localhost:59856 "GET /session/6618168b01c50643a55f07a4240bbea3/url HTTP/1.1" 404 0
2025-07-01 20:27:45,419 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:45,420 - DEBUG - Finished Request
2025-07-01 20:27:45,420 - DEBUG - DELETE http://localhost:59856/session/6618168b01c50643a55f07a4240bbea3 {}
2025-07-01 20:27:45,451 - DEBUG - http://localhost:59856 "DELETE /session/6618168b01c50643a55f07a4240bbea3 HTTP/1.1" 200 0
2025-07-01 20:27:45,452 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:27:45,452 - DEBUG - Finished Request
