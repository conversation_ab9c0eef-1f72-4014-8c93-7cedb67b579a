2025-06-30 19:04:59,999 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_190459.log
2025-06-30 19:05:09,329 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 19:05:09,329 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 19:05:09,386 - DEBUG - chromedriver not found in PATH
2025-06-30 19:05:09,386 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:05:09,386 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 19:05:09,386 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 19:05:09,386 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 19:05:09,387 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 19:05:09,387 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:05:09,391 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 29400 using 0 to output -3
2025-06-30 19:05:09,925 - DEBUG - POST http://localhost:54311/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 19:05:09,926 - DEBUG - Starting new HTTP connection (1): localhost:54311
2025-06-30 19:05:10,462 - DEBUG - http://localhost:54311 "POST /session HTTP/1.1" 200 0
2025-06-30 19:05:10,463 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir29400_368091142"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54314"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"4599244f35a6770f2b1d7de2c0286cab"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:10,463 - DEBUG - Finished Request
2025-06-30 19:05:10,463 - DEBUG - POST http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 19:05:11,892 - DEBUG - http://localhost:54311 "POST /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:11,892 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:11,892 - DEBUG - Finished Request
2025-06-30 19:05:11,893 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 19:05:11,893 - DEBUG - POST http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 19:05:11,899 - DEBUG - http://localhost:54311 "POST /session/4599244f35a6770f2b1d7de2c0286cab/execute/sync HTTP/1.1" 200 0
2025-06-30 19:05:11,900 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:11,900 - DEBUG - Finished Request
2025-06-30 19:05:11,900 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 19:05:11,901 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:11,926 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:11,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:11,927 - DEBUG - Finished Request
2025-06-30 19:05:12,927 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:12,933 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:12,933 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:12,933 - DEBUG - Finished Request
2025-06-30 19:05:13,934 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:13,941 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:13,941 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:13,942 - DEBUG - Finished Request
2025-06-30 19:05:14,943 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:14,949 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:14,950 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:14,950 - DEBUG - Finished Request
2025-06-30 19:05:15,950 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:15,958 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:15,958 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:15,959 - DEBUG - Finished Request
2025-06-30 19:05:16,959 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:16,965 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:16,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:16,965 - DEBUG - Finished Request
2025-06-30 19:05:17,967 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:17,975 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:17,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:17,976 - DEBUG - Finished Request
2025-06-30 19:05:18,977 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:18,984 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:18,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:18,985 - DEBUG - Finished Request
2025-06-30 19:05:19,985 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:19,993 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:19,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:19,994 - DEBUG - Finished Request
2025-06-30 19:05:20,996 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:21,003 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:21,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:21,004 - DEBUG - Finished Request
2025-06-30 19:05:22,006 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:22,013 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:22,013 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:22,013 - DEBUG - Finished Request
2025-06-30 19:05:23,014 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:23,021 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:23,021 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:23,021 - DEBUG - Finished Request
2025-06-30 19:05:24,022 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:24,029 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:24,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:24,029 - DEBUG - Finished Request
2025-06-30 19:05:25,030 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:25,039 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:25,039 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:25,039 - DEBUG - Finished Request
2025-06-30 19:05:26,040 - DEBUG - GET http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab/url {}
2025-06-30 19:05:26,047 - DEBUG - http://localhost:54311 "GET /session/4599244f35a6770f2b1d7de2c0286cab/url HTTP/1.1" 200 0
2025-06-30 19:05:26,048 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:26,048 - DEBUG - Finished Request
2025-06-30 19:05:26,119 - DEBUG - DELETE http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab {}
2025-06-30 19:05:26,159 - DEBUG - http://localhost:54311 "DELETE /session/4599244f35a6770f2b1d7de2c0286cab HTTP/1.1" 200 0
2025-06-30 19:05:26,160 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:26,160 - DEBUG - Finished Request
2025-06-30 19:05:27,060 - DEBUG - DELETE http://localhost:54311/session/4599244f35a6770f2b1d7de2c0286cab {}
2025-06-30 19:05:27,061 - DEBUG - Starting new HTTP connection (1): localhost:54311
