2025-07-01 20:09:03,880 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_200903.log
2025-07-01 20:09:08,574 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 20:09:08,574 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 20:09:09,264 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 20:09:09,264 - DEBUG - chromedriver not found in PATH
2025-07-01 20:09:09,265 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 20:09:09,265 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 20:09:09,265 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 20:09:09,266 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 20:09:09,266 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 20:09:09,266 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 20:09:09,266 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 20:09:09,272 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 27668 using 0 to output -3
2025-07-01 20:09:09,791 - DEBUG - POST http://localhost:59557/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 20:09:09,792 - DEBUG - Starting new HTTP connection (1): localhost:59557
2025-07-01 20:09:10,412 - DEBUG - http://localhost:59557 "POST /session HTTP/1.1" 200 0
2025-07-01 20:09:10,413 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir27668_1947424409"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:59564"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"998097e586af8a61878391b5f54af016"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:10,414 - DEBUG - Finished Request
2025-07-01 20:09:10,415 - DEBUG - POST http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 20:09:11,667 - DEBUG - http://localhost:59557 "POST /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:11,667 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:11,668 - DEBUG - Finished Request
2025-07-01 20:09:11,668 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 20:09:11,669 - DEBUG - POST http://localhost:59557/session/998097e586af8a61878391b5f54af016/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 20:09:11,681 - DEBUG - http://localhost:59557 "POST /session/998097e586af8a61878391b5f54af016/execute/sync HTTP/1.1" 200 0
2025-07-01 20:09:11,681 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:11,682 - DEBUG - Finished Request
2025-07-01 20:09:11,682 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 20:09:11,683 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:11,739 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:11,739 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:11,739 - DEBUG - Finished Request
2025-07-01 20:09:12,740 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:12,756 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:12,757 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:12,757 - DEBUG - Finished Request
2025-07-01 20:09:13,758 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:13,765 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:13,766 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:13,766 - DEBUG - Finished Request
2025-07-01 20:09:14,766 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:14,773 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:14,774 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:14,774 - DEBUG - Finished Request
2025-07-01 20:09:15,775 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:15,782 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:15,782 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:15,783 - DEBUG - Finished Request
2025-07-01 20:09:16,783 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:16,790 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:16,791 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:16,791 - DEBUG - Finished Request
2025-07-01 20:09:17,793 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:17,801 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:17,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:17,801 - DEBUG - Finished Request
2025-07-01 20:09:18,803 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:18,833 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:18,833 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:18,834 - DEBUG - Finished Request
2025-07-01 20:09:19,834 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:19,841 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:19,841 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:19,842 - DEBUG - Finished Request
2025-07-01 20:09:20,843 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:20,851 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:20,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:20,852 - DEBUG - Finished Request
2025-07-01 20:09:21,852 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:21,862 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:21,863 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:21,863 - DEBUG - Finished Request
2025-07-01 20:09:22,864 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:22,884 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:22,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:22,885 - DEBUG - Finished Request
2025-07-01 20:09:23,886 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:23,923 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:23,923 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:23,924 - DEBUG - Finished Request
2025-07-01 20:09:24,925 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:24,931 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:24,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:24,932 - DEBUG - Finished Request
2025-07-01 20:09:25,932 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:25,952 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:25,953 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:25,954 - DEBUG - Finished Request
2025-07-01 20:09:26,961 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:26,969 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:26,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:26,970 - DEBUG - Finished Request
2025-07-01 20:09:27,970 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:27,981 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:27,981 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:27,982 - DEBUG - Finished Request
2025-07-01 20:09:28,983 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:28,990 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:28,991 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:28,991 - DEBUG - Finished Request
2025-07-01 20:09:29,992 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:29,999 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:29,999 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:30,000 - DEBUG - Finished Request
2025-07-01 20:09:31,000 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:31,007 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:31,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:31,007 - DEBUG - Finished Request
2025-07-01 20:09:32,008 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:32,016 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:32,017 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:32,017 - DEBUG - Finished Request
2025-07-01 20:09:33,019 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:33,025 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:33,026 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:33,026 - DEBUG - Finished Request
2025-07-01 20:09:34,026 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:34,033 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:34,033 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:34,033 - DEBUG - Finished Request
2025-07-01 20:09:35,034 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:35,040 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:35,041 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:35,041 - DEBUG - Finished Request
2025-07-01 20:09:36,042 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:36,048 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:36,048 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:36,048 - DEBUG - Finished Request
2025-07-01 20:09:37,049 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:37,056 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:37,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:37,056 - DEBUG - Finished Request
2025-07-01 20:09:38,057 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:38,063 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:38,063 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:38,064 - DEBUG - Finished Request
2025-07-01 20:09:39,064 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:39,071 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:39,071 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:39,072 - DEBUG - Finished Request
2025-07-01 20:09:40,072 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:40,079 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:40,079 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:40,079 - DEBUG - Finished Request
2025-07-01 20:09:41,080 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:41,086 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:41,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:41,086 - DEBUG - Finished Request
2025-07-01 20:09:42,087 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:42,094 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:42,095 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:42,095 - DEBUG - Finished Request
2025-07-01 20:09:43,095 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:43,101 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:43,101 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:43,101 - DEBUG - Finished Request
2025-07-01 20:09:44,103 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:44,108 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:44,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:44,109 - DEBUG - Finished Request
2025-07-01 20:09:45,110 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:45,116 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:45,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:45,117 - DEBUG - Finished Request
2025-07-01 20:09:46,117 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:46,124 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:46,125 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:46,125 - DEBUG - Finished Request
2025-07-01 20:09:47,126 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:47,132 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:47,132 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:47,132 - DEBUG - Finished Request
2025-07-01 20:09:48,133 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:48,140 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:48,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:48,141 - DEBUG - Finished Request
2025-07-01 20:09:49,142 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:49,148 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:49,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:49,149 - DEBUG - Finished Request
2025-07-01 20:09:50,149 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:50,154 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:50,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:50,154 - DEBUG - Finished Request
2025-07-01 20:09:51,156 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:51,162 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:51,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:51,163 - DEBUG - Finished Request
2025-07-01 20:09:52,164 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:52,171 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:52,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:52,171 - DEBUG - Finished Request
2025-07-01 20:09:53,172 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:53,178 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:53,178 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:53,179 - DEBUG - Finished Request
2025-07-01 20:09:54,180 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:54,186 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:54,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:54,187 - DEBUG - Finished Request
2025-07-01 20:09:55,188 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:55,194 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:55,194 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:55,194 - DEBUG - Finished Request
2025-07-01 20:09:56,195 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:56,202 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:56,202 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:56,202 - DEBUG - Finished Request
2025-07-01 20:09:57,204 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:57,210 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:57,210 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:57,210 - DEBUG - Finished Request
2025-07-01 20:09:58,211 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:58,216 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:58,217 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:58,217 - DEBUG - Finished Request
2025-07-01 20:09:59,218 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:09:59,226 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:09:59,226 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:09:59,226 - DEBUG - Finished Request
2025-07-01 20:10:00,227 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:00,728 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:00,728 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:00,728 - DEBUG - Finished Request
2025-07-01 20:10:01,730 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:01,736 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:01,736 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:01,736 - DEBUG - Finished Request
2025-07-01 20:10:02,737 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:02,744 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:02,744 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:02,745 - DEBUG - Finished Request
2025-07-01 20:10:03,746 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:03,751 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:03,751 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:03,752 - DEBUG - Finished Request
2025-07-01 20:10:04,752 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:04,758 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:04,758 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:04,758 - DEBUG - Finished Request
2025-07-01 20:10:05,759 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:05,765 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:05,765 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:05,765 - DEBUG - Finished Request
2025-07-01 20:10:06,766 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:06,772 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:06,773 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:06,773 - DEBUG - Finished Request
2025-07-01 20:10:07,776 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:07,783 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:07,783 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:07,783 - DEBUG - Finished Request
2025-07-01 20:10:08,784 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:08,790 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:08,790 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:08,790 - DEBUG - Finished Request
2025-07-01 20:10:09,791 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:09,798 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:09,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:09,798 - DEBUG - Finished Request
2025-07-01 20:10:10,799 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:10,806 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:10,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:10,807 - DEBUG - Finished Request
2025-07-01 20:10:11,808 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:11,814 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:11,815 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:11,815 - DEBUG - Finished Request
2025-07-01 20:10:12,815 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:12,823 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:12,823 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:12,823 - DEBUG - Finished Request
2025-07-01 20:10:13,825 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:13,829 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:13,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:13,830 - DEBUG - Finished Request
2025-07-01 20:10:14,830 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:14,835 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:14,836 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:14,836 - DEBUG - Finished Request
2025-07-01 20:10:15,837 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:15,844 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:15,844 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:15,844 - DEBUG - Finished Request
2025-07-01 20:10:16,845 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:16,851 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:16,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:16,852 - DEBUG - Finished Request
2025-07-01 20:10:17,852 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:17,858 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:17,858 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:17,858 - DEBUG - Finished Request
2025-07-01 20:10:18,859 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:18,866 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:18,867 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:18,867 - DEBUG - Finished Request
2025-07-01 20:10:19,868 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:19,875 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:19,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:19,876 - DEBUG - Finished Request
2025-07-01 20:10:20,876 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:20,883 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:20,884 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:20,884 - DEBUG - Finished Request
2025-07-01 20:10:21,884 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:21,891 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:21,891 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:21,891 - DEBUG - Finished Request
2025-07-01 20:10:22,892 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:22,899 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:22,899 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:22,900 - DEBUG - Finished Request
2025-07-01 20:10:23,900 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:23,905 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:23,905 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:23,906 - DEBUG - Finished Request
2025-07-01 20:10:24,907 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:24,914 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:24,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:24,915 - DEBUG - Finished Request
2025-07-01 20:10:25,915 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:25,922 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:25,922 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:25,922 - DEBUG - Finished Request
2025-07-01 20:10:26,922 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:26,928 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:26,928 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:26,929 - DEBUG - Finished Request
2025-07-01 20:10:27,929 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:27,934 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:27,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:27,935 - DEBUG - Finished Request
2025-07-01 20:10:28,936 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:28,942 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:28,942 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:28,942 - DEBUG - Finished Request
2025-07-01 20:10:29,943 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:29,948 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:29,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:29,949 - DEBUG - Finished Request
2025-07-01 20:10:30,949 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:30,955 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:30,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:30,955 - DEBUG - Finished Request
2025-07-01 20:10:31,956 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:31,962 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:31,963 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:31,963 - DEBUG - Finished Request
2025-07-01 20:10:32,964 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:32,968 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:32,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:32,969 - DEBUG - Finished Request
2025-07-01 20:10:33,969 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:33,976 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:33,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:33,976 - DEBUG - Finished Request
2025-07-01 20:10:34,978 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:34,983 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:34,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:34,984 - DEBUG - Finished Request
2025-07-01 20:10:35,984 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:35,990 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:35,990 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:35,990 - DEBUG - Finished Request
2025-07-01 20:10:36,991 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:36,998 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:36,998 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:36,998 - DEBUG - Finished Request
2025-07-01 20:10:37,999 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:38,006 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:38,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:38,006 - DEBUG - Finished Request
2025-07-01 20:10:39,007 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:39,012 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:39,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:39,013 - DEBUG - Finished Request
2025-07-01 20:10:40,014 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:40,024 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:40,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:40,025 - DEBUG - Finished Request
2025-07-01 20:10:41,025 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:41,031 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:41,031 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:41,032 - DEBUG - Finished Request
2025-07-01 20:10:42,033 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:42,040 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:42,040 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:42,040 - DEBUG - Finished Request
2025-07-01 20:10:43,041 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:43,048 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:43,049 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:43,049 - DEBUG - Finished Request
2025-07-01 20:10:44,050 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:44,058 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:44,059 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:44,059 - DEBUG - Finished Request
2025-07-01 20:10:45,060 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:45,067 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:45,067 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:45,067 - DEBUG - Finished Request
2025-07-01 20:10:46,068 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:46,077 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:46,078 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:46,078 - DEBUG - Finished Request
2025-07-01 20:10:47,079 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:47,086 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:47,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:47,086 - DEBUG - Finished Request
2025-07-01 20:10:48,087 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:48,097 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:48,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:48,097 - DEBUG - Finished Request
2025-07-01 20:10:49,098 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:49,105 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:49,105 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:49,105 - DEBUG - Finished Request
2025-07-01 20:10:50,106 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:50,113 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:50,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:50,114 - DEBUG - Finished Request
2025-07-01 20:10:51,115 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:51,123 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:51,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:51,124 - DEBUG - Finished Request
2025-07-01 20:10:52,124 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:52,133 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:52,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:52,133 - DEBUG - Finished Request
2025-07-01 20:10:53,133 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:53,139 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:53,140 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:53,140 - DEBUG - Finished Request
2025-07-01 20:10:54,141 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:54,148 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:54,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:54,148 - DEBUG - Finished Request
2025-07-01 20:10:55,148 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:55,159 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:55,159 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:55,159 - DEBUG - Finished Request
2025-07-01 20:10:56,159 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:56,167 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:56,168 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:56,168 - DEBUG - Finished Request
2025-07-01 20:10:57,169 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:57,177 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:57,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:57,177 - DEBUG - Finished Request
2025-07-01 20:10:58,179 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:58,186 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:58,186 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:58,186 - DEBUG - Finished Request
2025-07-01 20:10:59,187 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:10:59,194 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:10:59,195 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:10:59,195 - DEBUG - Finished Request
2025-07-01 20:11:00,196 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:00,231 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:00,231 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:00,232 - DEBUG - Finished Request
2025-07-01 20:11:01,233 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:01,241 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:01,241 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:01,242 - DEBUG - Finished Request
2025-07-01 20:11:02,242 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:02,250 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:02,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:02,250 - DEBUG - Finished Request
2025-07-01 20:11:03,251 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:03,260 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:03,260 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:03,260 - DEBUG - Finished Request
2025-07-01 20:11:04,261 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:04,268 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:04,268 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:04,269 - DEBUG - Finished Request
2025-07-01 20:11:05,269 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:05,276 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:05,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:05,276 - DEBUG - Finished Request
2025-07-01 20:11:06,277 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:06,285 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:06,286 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:06,286 - DEBUG - Finished Request
2025-07-01 20:11:07,286 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:07,295 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:07,295 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:07,295 - DEBUG - Finished Request
2025-07-01 20:11:08,295 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:08,302 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:08,302 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:08,302 - DEBUG - Finished Request
2025-07-01 20:11:09,303 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:09,310 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:09,310 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:09,310 - DEBUG - Finished Request
2025-07-01 20:11:10,311 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:10,319 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:10,320 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:10,320 - DEBUG - Finished Request
2025-07-01 20:11:11,321 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:11,328 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:11,328 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:11,328 - DEBUG - Finished Request
2025-07-01 20:11:12,329 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:12,337 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:12,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:12,337 - DEBUG - Finished Request
2025-07-01 20:11:13,338 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:13,344 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:13,345 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:13,345 - DEBUG - Finished Request
2025-07-01 20:11:14,346 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:14,355 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:14,355 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:14,356 - DEBUG - Finished Request
2025-07-01 20:11:15,358 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:15,367 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:15,368 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:15,368 - DEBUG - Finished Request
2025-07-01 20:11:16,368 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:16,384 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:16,385 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:16,385 - DEBUG - Finished Request
2025-07-01 20:11:17,386 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:17,393 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:17,393 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:17,394 - DEBUG - Finished Request
2025-07-01 20:11:18,395 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:18,403 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:18,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:18,404 - DEBUG - Finished Request
2025-07-01 20:11:19,405 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:19,413 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:19,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:19,414 - DEBUG - Finished Request
2025-07-01 20:11:20,415 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:20,422 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:20,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:20,423 - DEBUG - Finished Request
2025-07-01 20:11:21,424 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:21,432 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:21,433 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:21,433 - DEBUG - Finished Request
2025-07-01 20:11:22,434 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:22,442 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:22,442 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:22,442 - DEBUG - Finished Request
2025-07-01 20:11:23,444 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:23,453 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:23,453 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:23,454 - DEBUG - Finished Request
2025-07-01 20:11:24,455 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:24,465 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:24,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:24,466 - DEBUG - Finished Request
2025-07-01 20:11:25,466 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:25,475 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:25,475 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:25,476 - DEBUG - Finished Request
2025-07-01 20:11:26,477 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:26,486 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:26,486 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:26,487 - DEBUG - Finished Request
2025-07-01 20:11:27,487 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:27,495 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:27,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:27,496 - DEBUG - Finished Request
2025-07-01 20:11:28,498 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:28,509 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:28,509 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:28,510 - DEBUG - Finished Request
2025-07-01 20:11:29,511 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:29,520 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:29,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:29,521 - DEBUG - Finished Request
2025-07-01 20:11:30,522 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:30,530 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:30,530 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:30,531 - DEBUG - Finished Request
2025-07-01 20:11:31,532 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:31,542 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:31,542 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:31,543 - DEBUG - Finished Request
2025-07-01 20:11:32,544 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:32,553 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:32,554 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:32,554 - DEBUG - Finished Request
2025-07-01 20:11:33,555 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:33,562 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:33,562 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:33,562 - DEBUG - Finished Request
2025-07-01 20:11:34,564 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:34,574 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:34,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:34,575 - DEBUG - Finished Request
2025-07-01 20:11:35,575 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:35,582 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:35,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:35,583 - DEBUG - Finished Request
2025-07-01 20:11:36,584 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:36,592 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:36,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:36,593 - DEBUG - Finished Request
2025-07-01 20:11:37,595 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:37,607 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:37,608 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:37,608 - DEBUG - Finished Request
2025-07-01 20:11:38,609 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:38,618 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:38,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:38,619 - DEBUG - Finished Request
2025-07-01 20:11:39,620 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:39,627 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:39,628 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:39,628 - DEBUG - Finished Request
2025-07-01 20:11:40,629 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:40,638 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:40,638 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:40,639 - DEBUG - Finished Request
2025-07-01 20:11:41,640 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:41,654 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:41,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:41,655 - DEBUG - Finished Request
2025-07-01 20:11:42,655 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:42,663 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:42,663 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:42,664 - DEBUG - Finished Request
2025-07-01 20:11:43,665 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:43,674 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:43,674 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:43,674 - DEBUG - Finished Request
2025-07-01 20:11:44,675 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:44,685 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:44,685 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:44,685 - DEBUG - Finished Request
2025-07-01 20:11:45,686 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:45,696 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:45,697 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:45,697 - DEBUG - Finished Request
2025-07-01 20:11:46,698 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:46,706 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:46,706 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:46,707 - DEBUG - Finished Request
2025-07-01 20:11:47,708 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:47,716 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:47,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:47,717 - DEBUG - Finished Request
2025-07-01 20:11:48,718 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:48,727 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:48,728 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:48,729 - DEBUG - Finished Request
2025-07-01 20:11:49,729 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:49,738 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:49,738 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:49,738 - DEBUG - Finished Request
2025-07-01 20:11:50,739 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:50,747 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:50,748 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:50,748 - DEBUG - Finished Request
2025-07-01 20:11:51,749 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:51,758 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:51,759 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:51,759 - DEBUG - Finished Request
2025-07-01 20:11:52,760 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:52,775 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:52,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:52,776 - DEBUG - Finished Request
2025-07-01 20:11:53,777 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:53,787 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:53,787 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:53,787 - DEBUG - Finished Request
2025-07-01 20:11:54,788 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:54,796 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:54,797 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:54,797 - DEBUG - Finished Request
2025-07-01 20:11:55,798 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:55,807 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:55,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:55,808 - DEBUG - Finished Request
2025-07-01 20:11:56,809 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:56,819 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:56,819 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:56,819 - DEBUG - Finished Request
2025-07-01 20:11:57,821 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:57,829 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:57,830 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:57,830 - DEBUG - Finished Request
2025-07-01 20:11:58,831 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:58,840 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:58,840 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:58,841 - DEBUG - Finished Request
2025-07-01 20:11:59,842 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:11:59,851 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:11:59,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:11:59,851 - DEBUG - Finished Request
2025-07-01 20:12:00,853 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:00,861 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:00,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:00,862 - DEBUG - Finished Request
2025-07-01 20:12:01,864 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:01,872 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:01,872 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:01,873 - DEBUG - Finished Request
2025-07-01 20:12:02,874 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:02,883 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:02,884 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:02,884 - DEBUG - Finished Request
2025-07-01 20:12:03,885 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:03,895 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:03,895 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:03,895 - DEBUG - Finished Request
2025-07-01 20:12:04,896 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:04,905 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:04,906 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:04,906 - DEBUG - Finished Request
2025-07-01 20:12:05,907 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:05,918 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:05,918 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:05,919 - DEBUG - Finished Request
2025-07-01 20:12:06,919 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:06,927 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:06,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:06,927 - DEBUG - Finished Request
2025-07-01 20:12:07,928 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:07,937 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:07,937 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:07,938 - DEBUG - Finished Request
2025-07-01 20:12:08,939 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:08,949 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:08,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:08,950 - DEBUG - Finished Request
2025-07-01 20:12:09,951 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:09,962 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:09,962 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:09,963 - DEBUG - Finished Request
2025-07-01 20:12:10,964 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:10,973 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:10,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:10,975 - DEBUG - Finished Request
2025-07-01 20:12:11,976 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:11,985 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:11,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:11,985 - DEBUG - Finished Request
2025-07-01 20:12:12,986 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:12,995 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:12,995 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:12,996 - DEBUG - Finished Request
2025-07-01 20:12:13,997 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:14,008 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:14,009 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:14,009 - DEBUG - Finished Request
2025-07-01 20:12:15,010 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:15,019 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:15,019 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:15,020 - DEBUG - Finished Request
2025-07-01 20:12:16,020 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:16,029 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:16,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:16,030 - DEBUG - Finished Request
2025-07-01 20:12:17,031 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:17,041 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:17,041 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:17,041 - DEBUG - Finished Request
2025-07-01 20:12:18,042 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:18,050 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:18,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:18,051 - DEBUG - Finished Request
2025-07-01 20:12:19,052 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:19,061 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:19,062 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:19,062 - DEBUG - Finished Request
2025-07-01 20:12:20,063 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:20,074 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:20,074 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:20,074 - DEBUG - Finished Request
2025-07-01 20:12:21,076 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:21,086 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:21,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:21,087 - DEBUG - Finished Request
2025-07-01 20:12:22,088 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:22,097 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:22,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:22,098 - DEBUG - Finished Request
2025-07-01 20:12:23,099 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:23,108 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:23,108 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:23,109 - DEBUG - Finished Request
2025-07-01 20:12:24,110 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:24,118 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:24,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:24,119 - DEBUG - Finished Request
2025-07-01 20:12:25,120 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:25,129 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:25,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:25,130 - DEBUG - Finished Request
2025-07-01 20:12:26,132 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:26,140 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:26,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:26,141 - DEBUG - Finished Request
2025-07-01 20:12:27,142 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:27,151 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:27,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:27,151 - DEBUG - Finished Request
2025-07-01 20:12:28,152 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:28,160 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:28,160 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:28,161 - DEBUG - Finished Request
2025-07-01 20:12:29,162 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:29,169 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:29,169 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:29,170 - DEBUG - Finished Request
2025-07-01 20:12:30,171 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:30,182 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:30,183 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:30,183 - DEBUG - Finished Request
2025-07-01 20:12:31,185 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:31,195 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:31,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:31,196 - DEBUG - Finished Request
2025-07-01 20:12:32,198 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:32,207 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:32,207 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:32,208 - DEBUG - Finished Request
2025-07-01 20:12:33,209 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:33,218 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:33,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:33,220 - DEBUG - Finished Request
2025-07-01 20:12:34,220 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:34,230 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:34,230 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:34,231 - DEBUG - Finished Request
2025-07-01 20:12:35,231 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:35,240 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:35,241 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:35,241 - DEBUG - Finished Request
2025-07-01 20:12:36,242 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:36,251 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:36,251 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:36,251 - DEBUG - Finished Request
2025-07-01 20:12:37,253 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:37,262 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:37,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:37,263 - DEBUG - Finished Request
2025-07-01 20:12:38,264 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:38,272 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:38,273 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:38,273 - DEBUG - Finished Request
2025-07-01 20:12:39,274 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:39,282 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:39,282 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:39,282 - DEBUG - Finished Request
2025-07-01 20:12:40,283 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:40,291 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:40,292 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:40,292 - DEBUG - Finished Request
2025-07-01 20:12:41,293 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:41,302 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:41,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:41,303 - DEBUG - Finished Request
2025-07-01 20:12:42,304 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:42,313 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:42,313 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:42,313 - DEBUG - Finished Request
2025-07-01 20:12:43,314 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:43,322 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:43,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:43,324 - DEBUG - Finished Request
2025-07-01 20:12:44,325 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:44,335 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:44,335 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:44,335 - DEBUG - Finished Request
2025-07-01 20:12:45,336 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:45,346 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:45,346 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:45,346 - DEBUG - Finished Request
2025-07-01 20:12:46,347 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:46,356 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:46,357 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:46,357 - DEBUG - Finished Request
2025-07-01 20:12:47,358 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:47,366 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:47,367 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:47,367 - DEBUG - Finished Request
2025-07-01 20:12:48,368 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:48,378 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:48,378 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:48,379 - DEBUG - Finished Request
2025-07-01 20:12:49,379 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:49,389 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:49,389 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:49,389 - DEBUG - Finished Request
2025-07-01 20:12:50,390 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:50,398 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:50,399 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:50,399 - DEBUG - Finished Request
2025-07-01 20:12:51,400 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:51,409 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:51,409 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:51,410 - DEBUG - Finished Request
2025-07-01 20:12:52,411 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:52,419 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:52,419 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:52,420 - DEBUG - Finished Request
2025-07-01 20:12:53,421 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:53,430 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:53,430 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:53,431 - DEBUG - Finished Request
2025-07-01 20:12:54,432 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:54,443 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:54,444 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:54,444 - DEBUG - Finished Request
2025-07-01 20:12:55,446 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:55,454 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:55,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:55,455 - DEBUG - Finished Request
2025-07-01 20:12:56,457 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:56,466 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:56,466 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:56,466 - DEBUG - Finished Request
2025-07-01 20:12:57,467 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:57,475 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:57,476 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:57,476 - DEBUG - Finished Request
2025-07-01 20:12:58,477 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:58,485 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:58,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:58,486 - DEBUG - Finished Request
2025-07-01 20:12:59,487 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:12:59,496 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:12:59,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:12:59,496 - DEBUG - Finished Request
2025-07-01 20:13:00,498 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:00,508 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:00,508 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:00,509 - DEBUG - Finished Request
2025-07-01 20:13:01,510 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:01,518 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:01,518 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:01,519 - DEBUG - Finished Request
2025-07-01 20:13:02,520 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:02,528 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:02,528 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:02,528 - DEBUG - Finished Request
2025-07-01 20:13:03,529 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:03,537 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:03,538 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:03,538 - DEBUG - Finished Request
2025-07-01 20:13:04,539 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:04,547 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:04,547 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:04,547 - DEBUG - Finished Request
2025-07-01 20:13:05,548 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:05,558 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:05,558 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:05,559 - DEBUG - Finished Request
2025-07-01 20:13:06,560 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:06,570 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:06,570 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:06,570 - DEBUG - Finished Request
2025-07-01 20:13:07,571 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:07,579 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:07,580 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:07,580 - DEBUG - Finished Request
2025-07-01 20:13:08,581 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:08,590 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:08,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:08,590 - DEBUG - Finished Request
2025-07-01 20:13:09,592 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:09,601 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:09,601 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:09,602 - DEBUG - Finished Request
2025-07-01 20:13:10,603 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:10,611 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:10,612 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:10,612 - DEBUG - Finished Request
2025-07-01 20:13:11,612 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:11,621 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:11,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:11,622 - DEBUG - Finished Request
2025-07-01 20:13:12,623 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:12,634 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:12,634 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:12,634 - DEBUG - Finished Request
2025-07-01 20:13:13,636 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:13,644 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:13,644 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:13,645 - DEBUG - Finished Request
2025-07-01 20:13:14,646 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:14,654 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:14,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:14,655 - DEBUG - Finished Request
2025-07-01 20:13:15,657 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:15,667 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:15,668 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:15,668 - DEBUG - Finished Request
2025-07-01 20:13:16,669 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:16,678 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:16,678 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:16,679 - DEBUG - Finished Request
2025-07-01 20:13:17,680 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:17,689 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:17,690 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:17,690 - DEBUG - Finished Request
2025-07-01 20:13:18,691 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:18,700 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:18,700 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:18,701 - DEBUG - Finished Request
2025-07-01 20:13:19,701 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:19,709 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:19,709 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:19,709 - DEBUG - Finished Request
2025-07-01 20:13:20,710 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:20,718 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:20,718 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:20,719 - DEBUG - Finished Request
2025-07-01 20:13:21,720 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:21,729 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:21,729 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:21,729 - DEBUG - Finished Request
2025-07-01 20:13:22,731 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:22,742 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:22,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:22,743 - DEBUG - Finished Request
2025-07-01 20:13:23,745 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:23,754 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:23,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:23,754 - DEBUG - Finished Request
2025-07-01 20:13:24,755 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:24,764 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:24,764 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:24,765 - DEBUG - Finished Request
2025-07-01 20:13:25,765 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:25,774 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:25,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:25,775 - DEBUG - Finished Request
2025-07-01 20:13:26,776 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:26,786 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:26,786 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:26,787 - DEBUG - Finished Request
2025-07-01 20:13:27,787 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:27,797 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:27,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:27,798 - DEBUG - Finished Request
2025-07-01 20:13:28,800 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:28,809 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:28,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:28,810 - DEBUG - Finished Request
2025-07-01 20:13:29,811 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:29,819 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:29,819 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:29,819 - DEBUG - Finished Request
2025-07-01 20:13:30,821 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:30,830 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:30,830 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:30,830 - DEBUG - Finished Request
2025-07-01 20:13:31,831 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:31,839 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:31,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:31,840 - DEBUG - Finished Request
2025-07-01 20:13:32,841 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:32,851 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:32,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:32,852 - DEBUG - Finished Request
2025-07-01 20:13:33,853 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:33,863 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:33,863 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:33,864 - DEBUG - Finished Request
2025-07-01 20:13:34,865 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:34,873 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:34,873 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:34,874 - DEBUG - Finished Request
2025-07-01 20:13:35,875 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:35,884 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:35,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:35,885 - DEBUG - Finished Request
2025-07-01 20:13:36,886 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:36,896 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:36,896 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:36,896 - DEBUG - Finished Request
2025-07-01 20:13:37,898 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:37,906 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:37,907 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:37,907 - DEBUG - Finished Request
2025-07-01 20:13:38,908 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:38,918 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:38,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:38,919 - DEBUG - Finished Request
2025-07-01 20:13:39,920 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:39,931 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:39,932 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:39,932 - DEBUG - Finished Request
2025-07-01 20:13:40,933 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:40,944 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:40,944 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:40,945 - DEBUG - Finished Request
2025-07-01 20:13:41,946 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:41,954 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:41,954 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:41,955 - DEBUG - Finished Request
2025-07-01 20:13:42,956 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:42,965 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:42,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:42,966 - DEBUG - Finished Request
2025-07-01 20:13:43,967 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:43,976 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:43,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:43,976 - DEBUG - Finished Request
2025-07-01 20:13:44,977 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:44,984 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:44,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:44,985 - DEBUG - Finished Request
2025-07-01 20:13:45,985 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:45,994 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:45,994 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:45,995 - DEBUG - Finished Request
2025-07-01 20:13:46,996 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:47,005 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:47,005 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:47,006 - DEBUG - Finished Request
2025-07-01 20:13:48,007 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:48,014 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:48,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:48,015 - DEBUG - Finished Request
2025-07-01 20:13:49,016 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:49,024 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:49,025 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:49,025 - DEBUG - Finished Request
2025-07-01 20:13:50,027 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:50,035 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:50,036 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:50,036 - DEBUG - Finished Request
2025-07-01 20:13:51,038 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:51,047 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:51,047 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:51,048 - DEBUG - Finished Request
2025-07-01 20:13:52,048 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:52,058 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:52,058 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:52,058 - DEBUG - Finished Request
2025-07-01 20:13:53,060 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:53,070 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:53,070 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:53,070 - DEBUG - Finished Request
2025-07-01 20:13:54,071 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:54,080 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:54,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:54,081 - DEBUG - Finished Request
2025-07-01 20:13:55,082 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:55,091 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:55,091 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:55,091 - DEBUG - Finished Request
2025-07-01 20:13:56,092 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:56,101 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:56,102 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:56,102 - DEBUG - Finished Request
2025-07-01 20:13:57,103 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:57,112 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:57,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:57,113 - DEBUG - Finished Request
2025-07-01 20:13:58,114 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:58,123 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:58,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:58,123 - DEBUG - Finished Request
2025-07-01 20:13:59,125 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:13:59,133 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:13:59,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:13:59,134 - DEBUG - Finished Request
2025-07-01 20:14:00,135 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:00,145 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:00,145 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:00,145 - DEBUG - Finished Request
2025-07-01 20:14:01,146 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:01,154 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:01,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:01,155 - DEBUG - Finished Request
2025-07-01 20:14:02,156 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:02,165 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:02,165 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:02,166 - DEBUG - Finished Request
2025-07-01 20:14:03,167 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:03,175 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:03,175 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:03,175 - DEBUG - Finished Request
2025-07-01 20:14:04,176 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:04,184 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:04,185 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:04,185 - DEBUG - Finished Request
2025-07-01 20:14:05,186 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:05,194 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:05,194 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:05,194 - DEBUG - Finished Request
2025-07-01 20:14:06,196 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:06,204 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:06,204 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:06,205 - DEBUG - Finished Request
2025-07-01 20:14:07,206 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:07,216 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:07,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:07,217 - DEBUG - Finished Request
2025-07-01 20:14:08,217 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:08,225 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:08,226 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:08,226 - DEBUG - Finished Request
2025-07-01 20:14:09,227 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:09,236 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:09,236 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:09,236 - DEBUG - Finished Request
2025-07-01 20:14:10,238 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:10,246 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:10,246 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:10,247 - DEBUG - Finished Request
2025-07-01 20:14:11,248 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:11,257 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:11,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:11,258 - DEBUG - Finished Request
2025-07-01 20:14:12,258 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:12,268 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:12,269 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:12,269 - DEBUG - Finished Request
2025-07-01 20:14:13,270 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:13,278 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:13,278 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:13,278 - DEBUG - Finished Request
2025-07-01 20:14:14,280 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:14,287 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:14,288 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:14,288 - DEBUG - Finished Request
2025-07-01 20:14:15,290 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:15,302 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:15,302 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:15,302 - DEBUG - Finished Request
2025-07-01 20:14:16,303 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:16,313 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:16,313 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:16,314 - DEBUG - Finished Request
2025-07-01 20:14:17,314 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:17,326 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:17,326 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:17,327 - DEBUG - Finished Request
2025-07-01 20:14:18,328 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:18,335 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:18,335 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:18,336 - DEBUG - Finished Request
2025-07-01 20:14:19,337 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:19,345 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:19,345 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:19,345 - DEBUG - Finished Request
2025-07-01 20:14:20,347 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:20,357 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:20,357 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:20,358 - DEBUG - Finished Request
2025-07-01 20:14:21,358 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:21,367 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:21,367 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:21,367 - DEBUG - Finished Request
2025-07-01 20:14:22,369 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:22,378 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:22,378 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:22,378 - DEBUG - Finished Request
2025-07-01 20:14:23,380 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:23,391 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:23,391 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:23,392 - DEBUG - Finished Request
2025-07-01 20:14:24,392 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:24,404 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:24,405 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:24,405 - DEBUG - Finished Request
2025-07-01 20:14:25,406 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:25,413 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:25,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:25,414 - DEBUG - Finished Request
2025-07-01 20:14:26,415 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:26,423 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:26,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:26,424 - DEBUG - Finished Request
2025-07-01 20:14:27,425 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:27,434 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:27,434 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:27,435 - DEBUG - Finished Request
2025-07-01 20:14:28,436 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:28,445 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:28,445 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:28,446 - DEBUG - Finished Request
2025-07-01 20:14:29,447 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:29,455 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:29,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:29,456 - DEBUG - Finished Request
2025-07-01 20:14:30,457 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:30,465 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:30,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:30,465 - DEBUG - Finished Request
2025-07-01 20:14:31,466 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:31,474 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:31,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:31,475 - DEBUG - Finished Request
2025-07-01 20:14:32,476 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:32,483 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:32,484 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:32,484 - DEBUG - Finished Request
2025-07-01 20:14:33,486 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:33,494 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:33,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:33,494 - DEBUG - Finished Request
2025-07-01 20:14:34,495 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:34,504 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:34,504 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:34,505 - DEBUG - Finished Request
2025-07-01 20:14:35,506 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:35,513 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:35,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:35,514 - DEBUG - Finished Request
2025-07-01 20:14:36,514 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:36,523 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:36,523 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:36,524 - DEBUG - Finished Request
2025-07-01 20:14:37,525 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:37,533 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:37,534 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:37,534 - DEBUG - Finished Request
2025-07-01 20:14:38,535 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:38,543 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:38,543 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:38,543 - DEBUG - Finished Request
2025-07-01 20:14:39,544 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:39,552 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:39,552 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:39,553 - DEBUG - Finished Request
2025-07-01 20:14:40,554 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:40,563 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:40,563 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:40,564 - DEBUG - Finished Request
2025-07-01 20:14:41,565 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:41,576 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:41,576 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:41,577 - DEBUG - Finished Request
2025-07-01 20:14:42,577 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:42,585 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:42,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:42,586 - DEBUG - Finished Request
2025-07-01 20:14:43,588 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:43,595 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 200 0
2025-07-01 20:14:43,595 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:43,596 - DEBUG - Finished Request
2025-07-01 20:14:44,596 - DEBUG - GET http://localhost:59557/session/998097e586af8a61878391b5f54af016/url {}
2025-07-01 20:14:44,597 - DEBUG - http://localhost:59557 "GET /session/998097e586af8a61878391b5f54af016/url HTTP/1.1" 404 0
2025-07-01 20:14:44,597 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:44,598 - DEBUG - Finished Request
2025-07-01 20:14:44,599 - DEBUG - DELETE http://localhost:59557/session/998097e586af8a61878391b5f54af016 {}
2025-07-01 20:14:44,730 - DEBUG - http://localhost:59557 "DELETE /session/998097e586af8a61878391b5f54af016 HTTP/1.1" 200 0
2025-07-01 20:14:44,730 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 20:14:44,731 - DEBUG - Finished Request
