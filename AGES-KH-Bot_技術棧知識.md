# AGES-KH-Bot 技術棧知識

## 📚 **目錄**

1. [Web 自動化技術](#web-自動化技術)
2. [前端技術理解](#前端技術理解)
3. [政府網站特性](#政府網站特性)
4. [監控與調試技術](#監控與調試技術)
5. [自動化策略](#自動化策略)
6. [高級技術](#高級技術)
7. [實用工具](#實用工具)
8. [協作建議](#協作建議)

---

## 🎯 **Web 自動化技術**

### **Selenium WebDriver 基礎**

#### 基本概念
```python
# 瀏覽器控制
driver = webdriver.Chrome()
driver.get("https://example.com")

# 元素定位
element = driver.find_element(By.XPATH, "//button")
elements = driver.find_elements(By.CLASS_NAME, "btn")

# iframe 切換
driver.switch_to.frame(0)
driver.switch_to.default_content()
```

#### 元素定位策略優先級
```python
# 推薦順序 (穩定性從高到低)
1. By.ID           # 最穩定，唯一性高
2. By.NAME         # 次穩定，表單元素常用
3. By.CSS_SELECTOR # 靈活，性能好
4. By.XPATH        # 最強大但較慢
5. By.CLASS_NAME   # 容易重複，需謹慎使用
```

#### 常用操作
```python
# 點擊操作
element.click()

# 輸入文字
element.send_keys("文字內容")
element.clear()  # 清空輸入框

# 獲取屬性
text = element.text
value = element.get_attribute("value")
is_displayed = element.is_displayed()
```

---

## 🌐 **前端技術理解**

### **JavaScript 框架識別**

#### jQuery 特徵
```javascript
// 檢測方法
typeof jQuery !== 'undefined'  // jQuery 是否存在
jQuery.fn.jquery              // 版本號

// 常見用法
$('#element')                  // ID 選擇器
$('.class')                   // Class 選擇器
$(document).ready()           // DOM 載入完成
```

#### Bootstrap 特徵
```html
<!-- 按鈕樣式 -->
<button class="btn btn-primary">主要按鈕</button>
<button class="btn btn-success">成功按鈕</button>
<button class="btn btn-danger">危險按鈕</button>

<!-- 彈窗結構 -->
<div class="modal fade">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body">內容</div>
    </div>
  </div>
</div>

<!-- 警告訊息 -->
<div class="alert alert-success">成功訊息</div>
<div class="alert alert-danger">錯誤訊息</div>
```

### **iframe 架構理解**

#### iframe 層級結構
```
單層 iframe：
主頁面 → iframe

多層 iframe：
主頁面 → iframe 0 → iframe 0 → 目標元素
```

#### iframe 切換策略
```python
# 進入 iframe 的方法
driver.switch_to.frame(0)              # 使用索引
driver.switch_to.frame("frameName")    # 使用名稱
driver.switch_to.frame(iframe_element)  # 使用元素

# 導航方法
driver.switch_to.default_content()     # 回到主頁面
driver.switch_to.parent_frame()        # 回到上一層

# 多層 iframe 處理
driver.switch_to.frame(0)  # 第一層
driver.switch_to.frame(0)  # 第二層
# 現在可以操作第二層中的元素
```

---

## 🔧 **政府網站特性**

### **ASP.NET WebForms 識別**

#### 特徵元素
```html
<!-- ViewState (狀態管理) -->
<input type="hidden" name="__VIEWSTATE" value="..." />

<!-- PostBack 機制 -->
<input type="hidden" name="__DOPOSTBACK" />

<!-- 事件驗證 -->
<input type="hidden" name="__EVENTVALIDATION" value="..." />

<!-- 控制項命名模式 -->
<input name="ctl00$ContentPlaceHolder1$TextBox1" />
```

#### PostBack 機制
```javascript
// ASP.NET 自動生成的 JavaScript
__doPostBack('ctl00$Button1', '');

// 表單提交會重新載入頁面
// 狀態通過 ViewState 維護
```

### **常見安全機制**

#### 驗證碼類型
```
1. 圖片驗證碼 - 需要 OCR 識別
2. 數字驗證碼 - 4-6 位數字
3. 滑動驗證 - 拖拽滑塊
4. 點擊驗證 - 點擊指定圖片
```

#### 會話管理
```
Session 超時：通常 5-30 分鐘
Cookie 管理：JSESSIONID, PHPSESSID, ASP.NET_SessionId
CSRF Token：防止跨站請求偽造
```

---

## 📊 **監控與調試技術**

### **瀏覽器開發者工具**

#### F12 面板功能
```
Elements：查看和編輯 HTML/CSS
Console：執行 JavaScript，查看錯誤
Network：監控網路請求和響應
Sources：查看和調試 JavaScript 原始碼
Application：查看 Cookie、Storage 等
```

#### Console 實用命令
```javascript
// 元素選擇
$0                    // 當前在 Elements 面板選中的元素
$$('button')          // 查找所有按鈕 (等同於 querySelectorAll)
$('button')           // 查找第一個按鈕 (等同於 querySelector)

// 頁面信息
document.title        // 頁面標題
location.href         // 當前 URL
document.readyState   // 頁面載入狀態

// iframe 操作
frames[0]             // 第一個 iframe
frames[0].document    // iframe 的 document
```

### **網路請求分析**

#### 請求類型識別
```
Document：HTML 頁面
XHR：傳統 AJAX 請求
Fetch：現代 API 請求
JS：JavaScript 檔案
CSS：樣式檔案
Img：圖片檔案
```

#### 重要請求頭
```
User-Agent：瀏覽器識別字串
Referer：來源頁面 URL
Cookie：會話和狀態信息
X-Requested-With：AJAX 請求標識
Content-Type：請求內容類型
```

---

## 🎯 **自動化策略**

### **等待機制**

#### 顯式等待 (推薦)
```python
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 基本等待
wait = WebDriverWait(driver, 10)  # 最多等待 10 秒

# 等待元素出現
element = wait.until(
    EC.presence_of_element_located((By.ID, "submit"))
)

# 等待元素可點擊
element = wait.until(
    EC.element_to_be_clickable((By.ID, "submit"))
)

# 等待文字出現
wait.until(
    EC.text_to_be_present_in_element((By.ID, "status"), "完成")
)
```

#### 常用等待條件
```python
# 元素狀態
EC.presence_of_element_located()      # 元素存在於 DOM
EC.visibility_of_element_located()    # 元素可見
EC.invisibility_of_element_located()  # 元素不可見
EC.element_to_be_clickable()          # 元素可點擊

# 文字內容
EC.text_to_be_present_in_element()    # 元素包含指定文字
EC.text_to_be_present_in_element_value()  # 輸入框值包含指定文字

# 頁面狀態
EC.title_is()                         # 頁面標題等於
EC.title_contains()                   # 頁面標題包含
EC.url_contains()                     # URL 包含指定字串
```

### **錯誤處理模式**

#### 常見異常處理
```python
from selenium.common.exceptions import *

try:
    element = driver.find_element(By.ID, "submit")
    element.click()
    
except NoSuchElementException:
    print("元素不存在，請檢查定位策略")
    
except TimeoutException:
    print("等待超時，元素可能載入緩慢")
    
except ElementNotInteractableException:
    print("元素不可互動，可能被遮擋或隱藏")
    
except StaleElementReferenceException:
    print("元素引用過期，需要重新定位")
    
except WebDriverException as e:
    print(f"WebDriver 一般錯誤: {e}")
    
except Exception as e:
    print(f"未預期的錯誤: {e}")
```

#### 重試機制
```python
import time
from functools import wraps

def retry(max_attempts=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise e
                    print(f"第 {attempt + 1} 次嘗試失敗: {e}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@retry(max_attempts=3, delay=2)
def click_submit_button(driver):
    button = driver.find_element(By.XPATH, "//button[text()='送出']")
    button.click()
```

---

## 🚀 **高級技術**

### **JavaScript 注入**

#### 執行自定義腳本
```python
# 獲取頁面信息
title = driver.execute_script("return document.title")
url = driver.execute_script("return window.location.href")

# 操作元素
driver.execute_script("arguments[0].click();", element)
driver.execute_script("arguments[0].value = arguments[1];", input_element, "文字")

# 注入監控腳本
monitoring_script = """
window.AGES_MONITOR = {
    events: [],
    startTime: Date.now(),
    
    log: function(type, data) {
        this.events.push({
            type: type,
            data: data,
            timestamp: Date.now() - this.startTime
        });
    },
    
    getEvents: function() {
        return this.events;
    }
};

// 監控點擊事件
document.addEventListener('click', function(e) {
    window.AGES_MONITOR.log('click', {
        tagName: e.target.tagName,
        text: e.target.textContent,
        className: e.target.className
    });
});
"""

driver.execute_script(monitoring_script)
```

### **反檢測技術**

#### 模擬人類行為
```python
from selenium.webdriver.common.action_chains import ActionChains
import random
import time

# 滑鼠軌跡模擬
def human_like_click(driver, element):
    actions = ActionChains(driver)
    
    # 移動到元素附近
    actions.move_to_element_with_offset(element, 
                                       random.randint(-5, 5), 
                                       random.randint(-5, 5))
    
    # 隨機暫停
    actions.pause(random.uniform(0.1, 0.5))
    
    # 點擊
    actions.click()
    
    # 執行動作
    actions.perform()

# 隨機延遲
def random_delay(min_seconds=1, max_seconds=3):
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

# 模擬打字
def human_like_typing(element, text, typing_speed=0.1):
    for char in text:
        element.send_keys(char)
        time.sleep(random.uniform(0.05, typing_speed))
```

---

## 📋 **實用工具**

### **調試工具**

#### 截圖功能
```python
# 整頁截圖
driver.save_screenshot("debug_full_page.png")

# 元素截圖
element = driver.find_element(By.ID, "target")
element.screenshot("debug_element.png")

# 帶時間戳的截圖
from datetime import datetime
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
driver.save_screenshot(f"debug_{timestamp}.png")
```

#### 頁面信息保存
```python
# 保存頁面源碼
with open("page_source.html", "w", encoding="utf-8") as f:
    f.write(driver.page_source)

# 保存頁面信息
page_info = {
    "title": driver.title,
    "url": driver.current_url,
    "window_handles": driver.window_handles,
    "cookies": driver.get_cookies()
}

import json
with open("page_info.json", "w", encoding="utf-8") as f:
    json.dump(page_info, f, ensure_ascii=False, indent=2)
```

### **性能監控**

#### 執行時間測量
```python
import time
from contextlib import contextmanager

@contextmanager
def timer(description="操作"):
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        print(f"{description} 執行時間: {end_time - start_time:.2f} 秒")

# 使用方式
with timer("點擊送出按鈕"):
    submit_button = driver.find_element(By.XPATH, "//button[text()='送出']")
    submit_button.click()
```

#### 記憶體使用監控
```python
import psutil
import os

def get_memory_usage():
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return {
        "rss": memory_info.rss / 1024 / 1024,  # MB
        "vms": memory_info.vms / 1024 / 1024   # MB
    }

print(f"記憶體使用: {get_memory_usage()}")
```

---

## 🎯 **協作建議**

### **溝通術語統一**

#### 技術術語
```
元素定位 = 在網頁上找到特定的按鈕、輸入框等
iframe 切換 = 進入網頁中的子頁面或彈窗
等待機制 = 等待頁面或元素載入完成
異常處理 = 處理程式執行時的錯誤情況
```

#### 狀態描述
```
元素可見 = 在頁面上看得到
元素存在 = 在 HTML 中存在但可能不可見
元素可點擊 = 可以進行點擊操作
頁面載入完成 = DOM 結構已建立完成
```

### **問題回報格式**

#### 標準格式
```
問題描述：無法點擊送出按鈕
執行環境：Chrome 瀏覽器 v120，Windows 11
頁面結構：雙層 iframe (iframe 0 > iframe 0)
錯誤訊息：NoSuchElementException: Unable to locate element
已嘗試方案：
1. 確認已切換到正確 iframe
2. 使用多種定位策略 (XPath, CSS Selector)
3. 添加等待時間

期望結果：成功點擊送出按鈕並提交表單
實際結果：程式無法找到送出按鈕元素
```

### **代碼分享規範**

#### 代碼註解
```python
# 功能說明：切換到編輯彈窗並點擊送出按鈕
# 適用場景：AGES-KH-Bot 訂單提交流程
# 注意事項：需要先手動輸入驗證碼

def submit_order(driver, verification_code):
    """
    提交訂單
    
    Args:
        driver: WebDriver 實例
        verification_code: 驗證碼字串
        
    Returns:
        bool: 提交是否成功
        
    Raises:
        TimeoutException: 等待超時
        NoSuchElementException: 找不到元素
    """
    try:
        # 切換到雙層 iframe
        driver.switch_to.frame(0)  # 第一層：訂單清單
        driver.switch_to.frame(0)  # 第二層：編輯彈窗
        
        # 輸入驗證碼
        verification_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@placeholder='請輸入驗證碼']"))
        )
        verification_input.clear()
        verification_input.send_keys(verification_code)
        
        # 點擊送出按鈕
        submit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[text()='送出']"))
        )
        submit_button.click()
        
        return True
        
    except Exception as e:
        print(f"提交訂單失敗: {e}")
        return False
        
    finally:
        # 確保回到主頁面
        driver.switch_to.default_content()
```

---

**文檔版本**: v1.0  
**最後更新**: 2025-06-30  
**適用項目**: AGES-KH-Bot  
**維護者**: AI Assistant & User
