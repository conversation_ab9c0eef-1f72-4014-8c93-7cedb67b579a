2025-07-01 18:09:36,849 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_180936.log
2025-07-01 18:09:41,569 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 18:09:41,569 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 18:09:41,639 - DEBUG - chromedriver not found in PATH
2025-07-01 18:09:41,639 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:09:41,639 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 18:09:41,639 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 18:09:41,640 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 18:09:41,640 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 18:09:41,640 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:09:41,645 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 20632 using 0 to output -3
2025-07-01 18:09:42,161 - DEBUG - POST http://localhost:57111/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 18:09:42,162 - DEBUG - Starting new HTTP connection (1): localhost:57111
2025-07-01 18:09:42,711 - DEBUG - http://localhost:57111 "POST /session HTTP/1.1" 200 0
2025-07-01 18:09:42,711 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir20632_1393888631"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:57114"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"264017f6e40127d20cb5d6c24b4da057"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:42,712 - DEBUG - Finished Request
2025-07-01 18:09:42,712 - DEBUG - POST http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 18:09:44,135 - DEBUG - http://localhost:57111 "POST /session/264017f6e40127d20cb5d6c24b4da057/url HTTP/1.1" 200 0
2025-07-01 18:09:44,135 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:44,135 - DEBUG - Finished Request
2025-07-01 18:09:44,135 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 18:09:44,136 - DEBUG - POST http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 18:09:44,143 - DEBUG - http://localhost:57111 "POST /session/264017f6e40127d20cb5d6c24b4da057/execute/sync HTTP/1.1" 200 0
2025-07-01 18:09:44,144 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:44,144 - DEBUG - Finished Request
2025-07-01 18:09:44,144 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 18:09:44,145 - DEBUG - GET http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/url {}
2025-07-01 18:09:44,184 - DEBUG - http://localhost:57111 "GET /session/264017f6e40127d20cb5d6c24b4da057/url HTTP/1.1" 200 0
2025-07-01 18:09:44,185 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:44,185 - DEBUG - Finished Request
2025-07-01 18:09:45,186 - DEBUG - GET http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/url {}
2025-07-01 18:09:45,192 - DEBUG - http://localhost:57111 "GET /session/264017f6e40127d20cb5d6c24b4da057/url HTTP/1.1" 200 0
2025-07-01 18:09:45,192 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:45,192 - DEBUG - Finished Request
2025-07-01 18:09:46,193 - DEBUG - GET http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/url {}
2025-07-01 18:09:46,198 - DEBUG - http://localhost:57111 "GET /session/264017f6e40127d20cb5d6c24b4da057/url HTTP/1.1" 200 0
2025-07-01 18:09:46,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:46,199 - DEBUG - Finished Request
2025-07-01 18:09:47,200 - DEBUG - GET http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/url {}
2025-07-01 18:09:47,206 - DEBUG - http://localhost:57111 "GET /session/264017f6e40127d20cb5d6c24b4da057/url HTTP/1.1" 200 0
2025-07-01 18:09:47,206 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:47,206 - DEBUG - Finished Request
2025-07-01 18:09:48,208 - DEBUG - GET http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057/url {}
2025-07-01 18:09:48,215 - DEBUG - http://localhost:57111 "GET /session/264017f6e40127d20cb5d6c24b4da057/url HTTP/1.1" 200 0
2025-07-01 18:09:48,215 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:48,215 - DEBUG - Finished Request
2025-07-01 18:09:49,034 - DEBUG - DELETE http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057 {}
2025-07-01 18:09:49,077 - DEBUG - http://localhost:57111 "DELETE /session/264017f6e40127d20cb5d6c24b4da057 HTTP/1.1" 200 0
2025-07-01 18:09:49,077 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:09:49,078 - DEBUG - Finished Request
2025-07-01 18:09:49,230 - DEBUG - DELETE http://localhost:57111/session/264017f6e40127d20cb5d6c24b4da057 {}
2025-07-01 18:09:49,230 - DEBUG - Starting new HTTP connection (1): localhost:57111
