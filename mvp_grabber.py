# mvp_grabber.py v1.5.1-dev09
# AGES-KH 搶單主程式 - 恢復v1.4.33已驗證工作流程版本
# Last Modified: 2025-07-01
# Maintainer: Will Wang
# v1.5.1-dev09：恢復v1.4.33用戶確認機制，修復工作流程順序，確保"修改進廠確認單"彈窗正常出現

import tkinter as tk
from tkinter import simpledialog, messagebox, ttk
import csv
from datetime import datetime, date, timedelta
import os
import sys
import psutil
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import threading
import time
import logging
from rtt_config_gui import RTTConfigGUI
from rtt_config_manager import RTTConfigManager
from rtt_predictor import get_avg_rtt
import pandas as pd
import re

# 導入新的模組
from submission_result_detector import SubmissionResultDetector
from dom_inspector import DOMInspector

__VERSION__ = "1.5.1-dev12"  # 修復 start_driver_monitor 阻塞問題

driver = None  # 全域 driver
chrome_pid = None  # 追蹤由程式啟動的 Chrome PID
result_detector = None  # 全域結果檢測器
dom_inspector = None  # 全域 DOM 檢查器
current_execution_mode = 'normal'  # 執行模式：'normal' 或 'test'
gui_operation_completed = False  # 標記用戶是否已完成 GUI#09 操作

ORDERS_PATH = os.path.join('orders', 'orders.csv')

# 🔍 設置詳細日誌記錄
def setup_detailed_logging():
    """設置詳細的日誌記錄系統"""
    # 創建 logs 目錄
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 設置日誌格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'

    # 創建文件處理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/grabber_detailed_{timestamp}.log'

    # 配置日誌
    logging.basicConfig(
        level=logging.DEBUG,
        format=log_format,
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.FileHandler('ages_kh_bot.log', encoding='utf-8'),  # 保留原有日誌
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"🔍 詳細日誌記錄已啟動，日誌文件: {log_filename}")
    return logger, log_filename

# 初始化詳細日誌
logger, current_log_file = setup_detailed_logging()

# 全域變數
last_dialog_detection = None  # 儲存最後一次彈窗檢測結果

def log_page_content(driver, context=""):
    """詳細記錄頁面內容"""
    try:
        logger.info(f"🔍 [{context}] 開始記錄頁面內容...")

        # 基本頁面信息
        current_url = driver.current_url
        page_title = driver.title
        logger.info(f"🔍 [{context}] 當前 URL: {current_url}")
        logger.info(f"🔍 [{context}] 頁面標題: {page_title}")

        # 方法1: driver.page_source
        page_source = driver.page_source
        logger.info(f"🔍 [{context}] page_source 長度: {len(page_source)}")
        logger.info(f"🔍 [{context}] page_source 包含 E48B: {'E48B' in page_source}")
        logger.info(f"🔍 [{context}] page_source 包含目標訂單: {'E48B201611405190953' in page_source}")

        # 方法2: JavaScript innerText
        js_inner_text = driver.execute_script("return document.body.innerText || document.body.textContent || '';")
        logger.info(f"🔍 [{context}] innerText 長度: {len(js_inner_text)}")
        logger.info(f"🔍 [{context}] innerText 包含 E48B: {'E48B' in js_inner_text}")
        logger.info(f"🔍 [{context}] innerText 包含目標訂單: {'E48B201611405190953' in js_inner_text}")

        # 方法3: 表格檢測
        tables = driver.find_elements(By.TAG_NAME, "table")
        rows = driver.find_elements(By.TAG_NAME, "tr")
        logger.info(f"🔍 [{context}] 檢測到 {len(tables)} 個表格，{len(rows)} 個表格行")

        # 方法4: 檢查所有可能包含訂單號的元素
        possible_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'E48B')]")
        logger.info(f"🔍 [{context}] 包含 'E48B' 的元素數量: {len(possible_elements)}")

        for i, elem in enumerate(possible_elements[:5]):  # 只記錄前5個
            try:
                elem_text = elem.text.strip()
                elem_tag = elem.tag_name
                logger.info(f"🔍 [{context}] E48B 元素 {i+1}: <{elem_tag}> {elem_text[:100]}...")
            except:
                logger.warning(f"🔍 [{context}] 無法讀取 E48B 元素 {i+1}")

        # 方法5: 記錄頁面內容樣本
        logger.info(f"🔍 [{context}] innerText 前300字符:")
        logger.info(f"🔍 [{context}] {js_inner_text[:300]}...")

        if len(js_inner_text) > 300:
            logger.info(f"🔍 [{context}] innerText 後300字符:")
            logger.info(f"🔍 [{context}] ...{js_inner_text[-300:]}")

        # 方法6: 檢查是否有編輯按鈕
        edit_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']")
        logger.info(f"🔍 [{context}] 檢測到 {len(edit_buttons)} 個編輯按鈕")

        # 🎯 方法7: 檢查是否有 iframe，並檢測 iframe 內容
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        logger.info(f"🔍 [{context}] 檢測到 {len(iframes)} 個 iframe")

        iframe_info = {}
        if iframes:
            for i, iframe in enumerate(iframes):
                try:
                    iframe_id = iframe.get_attribute('id') or f'iframe_{i}'
                    iframe_src = iframe.get_attribute('src') or 'no_src'
                    logger.info(f"🔍 [{context}] iframe {i+1}: id='{iframe_id}', src='{iframe_src}'")

                    # 切換到 iframe
                    driver.switch_to.frame(iframe)

                    # 檢測 iframe 內容
                    iframe_inner_text = driver.execute_script("return document.body.innerText || document.body.textContent || '';")
                    iframe_tables = driver.find_elements(By.TAG_NAME, "table")
                    iframe_rows = driver.find_elements(By.TAG_NAME, "tr")
                    iframe_edit_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']")
                    iframe_e48b_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'E48B')]")

                    has_target_in_iframe = 'E48B201611405190953' in iframe_inner_text
                    has_e48b_in_iframe = 'E48B' in iframe_inner_text

                    logger.info(f"🔍 [{context}] iframe {i+1} 內容:")
                    logger.info(f"🔍 [{context}]   - 文字長度: {len(iframe_inner_text)}")
                    logger.info(f"🔍 [{context}]   - 包含目標訂單: {has_target_in_iframe}")
                    logger.info(f"🔍 [{context}]   - 包含 E48B: {has_e48b_in_iframe}")
                    logger.info(f"🔍 [{context}]   - 表格數量: {len(iframe_tables)}")
                    logger.info(f"🔍 [{context}]   - 表格行數: {len(iframe_rows)}")
                    logger.info(f"🔍 [{context}]   - 編輯按鈕數量: {len(iframe_edit_buttons)}")
                    logger.info(f"🔍 [{context}]   - E48B 元素數量: {len(iframe_e48b_elements)}")

                    if len(iframe_inner_text) > 300:
                        logger.info(f"🔍 [{context}]   - 內容前300字符: {iframe_inner_text[:300]}...")
                        logger.info(f"🔍 [{context}]   - 內容後300字符: ...{iframe_inner_text[-300:]}")
                    else:
                        logger.info(f"🔍 [{context}]   - 完整內容: {iframe_inner_text}")

                    iframe_info[iframe_id] = {
                        'src': iframe_src,
                        'inner_text_length': len(iframe_inner_text),
                        'has_target_order': has_target_in_iframe,
                        'has_e48b': has_e48b_in_iframe,
                        'table_count': len(iframe_tables),
                        'row_count': len(iframe_rows),
                        'edit_button_count': len(iframe_edit_buttons),
                        'e48b_element_count': len(iframe_e48b_elements)
                    }

                    # 🎯 重要修復：檢測完 iframe 內容後必須切換回主頁面
                    driver.switch_to.default_content()  # 恢復切換回主頁面
                    logger.info(f"🔍 [{context}] iframe {i+1} 檢測完成，已切換回主頁面")

                except Exception as e:
                    logger.error(f"🔍 [{context}] 檢測 iframe {i+1} 時發生錯誤: {e}")
                    # 🎯 發生錯誤時也要嘗試切換回主頁面
                    try:
                        driver.switch_to.default_content()
                        logger.info(f"🔍 [{context}] 錯誤處理：已切換回主頁面")
                    except Exception as switch_error:
                        logger.error(f"🔍 [{context}] 切換回主頁面失敗: {switch_error}")

        return {
            'url': current_url,
            'title': page_title,
            'page_source_length': len(page_source),
            'inner_text_length': len(js_inner_text),
            'has_target_order': 'E48B201611405190953' in js_inner_text,
            'has_e48b': 'E48B' in js_inner_text,
            'table_count': len(tables),
            'row_count': len(rows),
            'edit_button_count': len(edit_buttons),
            'e48b_element_count': len(possible_elements),
            'iframe_count': len(iframes),
            'iframe_info': iframe_info
        }

    except Exception as e:
        logger.error(f"🔍 [{context}] 記錄頁面內容時發生錯誤: {e}")
        return None

# 支援多種日期格式
DATE_FORMATS = [
    "%Y/%m/%d", "%Y-%m-%d", "%Y/%-m/%-d", "%Y/%m/%-d", "%Y/%-m/%d", "%Y/%d/%m"
]

# ===== 安全離開主程式 =====
def safe_exit():
    global driver, chrome_pid, result_detector, dom_inspector
    print("[INFO] 終止主程式...")
    try:
        if driver:
            try:
                driver.quit()
            except:
                pass
            finally:
                driver = None
    except Exception as e:
        print(f"[ERROR] 關閉 driver 發生錯誤: {str(e)}")

    try:
        for widget in tk._default_root.children.values():
            widget.destroy()
        tk._default_root.quit()
    except:
        pass

    # 清理全域變數
    chrome_pid = None
    result_detector = None
    dom_inspector = None
    os._exit(0)

# ===== 瀏覽器支援檢查 =====
def get_supported_browsers():
    """回傳目前支援的瀏覽器列表"""
    return ['chrome']  # 未來可以加入 'firefox', 'edge'

def has_supported_browser(tasks):
    """檢查是否有支援的瀏覽器任務"""
    supported = get_supported_browsers()
    return any(task.get('browser', '').strip().lower() in supported for task in tasks)

def start_browser(task):
    """根據任務啟動對應的瀏覽器"""
    global driver, chrome_pid, result_detector, dom_inspector
    print(f"[DEBUG] 準備啟動瀏覽器，任務內容: {task}")

    if not task or not isinstance(task, dict):
        print("[ERROR] 無效的任務格式")
        return False

    try:
        browser = task.get('browser', '').strip().lower()
        if not browser:
            print("[ERROR] 任務中未指定瀏覽器")
            return False

        if browser == 'chrome':
            # 直接啟動新的 Chrome 實例，不嘗試連接現有瀏覽器
            print("[INFO] 啟動新的 Chrome 瀏覽器實例...")

            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_argument('--start-maximized')  # 最大化視窗
            chrome_options.add_argument('--disable-gpu')  # 禁用 GPU 加速
            chrome_options.add_argument('--no-sandbox')  # 禁用沙盒模式
            chrome_options.add_argument('--disable-dev-shm-usage')  # 禁用 /dev/shm 使用

            try:
                driver = webdriver.Chrome(options=chrome_options)
                print(f"[DEBUG] Chrome WebDriver 創建成功")

                # 🚨 修復：安全獲取 Chrome PID，避免掛起
                try:
                    chrome_pid = driver.service.process.pid
                    print(f"[DEBUG] Chrome PID: {chrome_pid}")
                except Exception as e:
                    print(f"[WARN] 無法獲取 Chrome PID: {e}")
                    chrome_pid = None

                # 初始化檢測器
                result_detector = SubmissionResultDetector(driver, logger)
                dom_inspector = DOMInspector()
                print("[INFO] 結果檢測器和 DOM 檢查器已初始化")

                # 導航到平台首頁
                print("[INFO] 導航到平台首頁")
                driver.get("https://wmc.kcg.gov.tw/")

                # 🚀 立即設置瀏覽器事件監控
                print("[INFO] 🚀 設置瀏覽器事件監控...")
                setup_browser_event_monitoring()

                print("[DEBUG] 準備啟動 driver 監控...")
                start_driver_monitor()
                print("[DEBUG] driver 監控已啟動")
                print("[DEBUG] 瀏覽器啟動完成，返回 True")
                return True

            except Exception as e:
                print(f"[ERROR] 啟動 Chrome 失敗: {str(e)}")
                return False

        print(f"[ERROR] 不支援的瀏覽器: {browser}")
        return False

    except Exception as e:
        print(f"[ERROR] 啟動瀏覽器失敗: {str(e)}")
        if driver:
            try:
                driver.quit()
            except:
                pass
            driver = None
        chrome_pid = None
        result_detector = None
        dom_inspector = None
        return False

# ===== GUI：詢問使用者輸入觸發時間(支援毫秒) =====
def ask_trigger_time_gui(default="09:30:00.001") -> str:
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    answer = simpledialog.askstring("觸發時間設定 (支援毫秒)", 
                                     "請輸入本次搶單觸發時間 (格式 HH:MM:SS.sss)：",
                                     initialvalue=default)
    if not answer:
        return default
    return answer.strip()

def normalize_date(date_str: str) -> str:
    """
    將日期字串正規化為 YYYY-MM-DD 格式
    
    Args:
        date_str (str): 原始日期字串，支援 YYYY/M/D、YYYY/MM/DD、YYYY-MM-DD 等格式
        
    Returns:
        str: 正規化後的日期字串 (YYYY-MM-DD)，若為萬用日期則返回 '*-*-*'
    """
    s = date_str.strip()
    if s == '*-*-*':
        return '*-*-*'
    
    # 替換可能的日期分隔符號為統一格式
    s = s.replace("-", "/")
    
    # 嘗試解析日期 (支援 YYYY/M/D 和 YYYY/MM/DD)
    parts = s.split("/")
    if len(parts) == 3:
        try:
            year = int(parts[0])
            month = int(parts[1])
            day = int(parts[2])
            return f"{year:04d}-{month:02d}-{day:02d}"
        except (ValueError, IndexError):
            pass
            
    return s  # 無法解析則返回原始字串

def fix_row_keys(row: dict) -> dict:
    # 自動修正欄位名稱 user_profil -> user_profile
    if 'user_profil' in row and 'user_profile' not in row:
        row['user_profile'] = row['user_profil']
    return row

def load_and_filter_orders(path=ORDERS_PATH):
    """
    載入並過濾 orders.csv 中的任務
    
    Args:
        path (str): orders.csv 的檔案路徑
        
    Returns:
        tuple: (filtered_tasks, ignored_tasks, all_valid_tasks)
            - filtered_tasks: 過濾後要執行的任務(每個瀏覽器只保留第一筆)
            - ignored_tasks: 因瀏覽器重複而被忽略的任務
            - all_valid_tasks: 所有今日有效任務(包含萬用任務)
    """
    today_str = date.today().strftime("%Y-%m-%d")
    print(f"[DEBUG] 今天日期: {today_str}")
    print(f"[DEBUG] CSV 路徑: {path}")
    
    valid_tasks = []
    ignored_tasks = []
    filtered_tasks = []
    seen_browsers = set()
    
    if not os.path.exists(path):
        print(f"[ERROR] 找不到訂單檔案: {path}")
        return filtered_tasks, ignored_tasks, valid_tasks
        
    try:
        with open(path, newline='', encoding='utf-8') as f:
            # 先讀取第一行確認分隔符
            first_line = f.readline().strip()
            delimiter = '\t' if '\t' in first_line else ','
            f.seek(0)  # 重置檔案指標
            
            reader = csv.DictReader(f, delimiter=delimiter)
            print(f"[DEBUG] 使用分隔符: {delimiter}")
            
            for row in reader:
                row = fix_row_keys(row)
                raw_date = row.get('date', '')
                norm_date = normalize_date(raw_date)
                print(f"[DEBUG] 原始日期: {raw_date!r}, 正規化後: {norm_date!r}")
                
                if norm_date == today_str or norm_date == '*-*-*':
                    print(f"[DEBUG] 找到有效任務: {row}")
                    valid_tasks.append(row)
                    
                    browser = row.get('browser', '').strip().lower()
                    if browser and browser not in seen_browsers:
                        filtered_tasks.append(row)
                        seen_browsers.add(browser)
                    else:
                        ignored_tasks.append(row)
                        
    except Exception as e:
        print(f"[ERROR] 讀取訂單檔案失敗: {str(e)}")
        
    print(f"[DEBUG] 有效任務數量: {len(valid_tasks)}")
    print(f"[DEBUG] 過濾後任務數量: {len(filtered_tasks)}")
    return filtered_tasks, ignored_tasks, valid_tasks

# ===== GUI#02 - 準備提示視窗 =====
def show_preparation_gui(version, valid_summary, task_summary, ignored_tasks, filtered_tasks, trigger_time="09:30:00.001"):
    """GUI#02 - 準備提示視窗 - 複製自 gui_prototype_viewer.py 已驗證代碼"""
    print("[INFO] 📋 顯示 GUI#02 - 準備提示視窗")

    # 按鈕點擊處理
    def on_continue():
        print("[INFO] 用戶點擊 [啟動瀏覽器]")
        window.attributes('-topmost', False)
        window.destroy()
        # 1. 啟動瀏覽器
        print(f"[DEBUG] filtered_tasks: {filtered_tasks}")
        for task in filtered_tasks:
            print(f"[DEBUG] 嘗試啟動: {task.get('browser')}")
            if start_browser(task):
                break  # 只啟動第一個支援的瀏覽器任務
        # 2. 顯示 GUI#05 操作指南
        # 2. 顯示 GUI#05 操作指南 (遵守 GUI 規範)
        action = show_gui05_operation_guide(filtered_tasks, trigger_time)
        if action == 'ready':
            # 3. 開始搶單流程 (會自動點擊編輯按鈕並跳出 GUI#09)
            # 🚨 修復：移除雙重確認機制，GUI#05已經是用戶確認
            print("[INFO] ✅ 用戶已通過GUI#05確認準備完成，直接開始搶單流程")
            logger.info("✅ 用戶已通過GUI#05確認準備完成，直接開始搶單流程")
            execute_order_grabbing_direct(filtered_tasks)  # 使用直接版本，跳過重複確認
        else:
            print("[INFO] 用戶取消操作，程式結束")
            safe_exit()

    def on_cancel():
        print("[INFO] 用戶點擊 [取消]")
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()
        return False

    # 創建視窗(優化版，確保按鈕完整顯示)
    window = tk.Tk()
    window.title(f"GUI#02 - 準備提示")  # 移除版本號，節省標題欄空間
    window.geometry("400x180")  # 調整高度，確保按鈕顯示
    window.resizable(False, False)
    window.attributes('-topmost', True)

    # 移除表頭，直接顯示任務確認信息
    tk.Label(window, text="即將執行以下搶單任務：",
            font=("Arial", 10, "bold")).pack(pady=(15, 8))

    # 任務顯示區域(限制高度，最多3筆)
    task_frame = tk.Frame(window, height=60)  # 固定高度
    task_frame.pack(fill="x", padx=20, pady=(0, 8))
    task_frame.pack_propagate(False)  # 防止子元件撐大框架

    for task in filtered_tasks[:3]:  # 最多顯示3筆
        task_text = f"訂單: {task.get('order_id', 'N/A')} | 瀏覽器: {task.get('browser', 'N/A')}"
        tk.Label(task_frame, text=task_text, font=("Arial", 9),
                fg="green").pack(pady=1)

    # 簡化的操作提醒
    tk.Label(window, text="確認無誤後，點擊「啟動瀏覽器」開始搶單",
            font=("Arial", 8), fg="gray").pack(pady=(0, 8))

    # 優化的按鈕區域(平均大小，居中靠近)
    button_frame = tk.Frame(window)
    button_frame.pack(pady=(0, 15))

    tk.Button(button_frame, text="啟動瀏覽器", command=on_continue,
             font=("Arial", 10, "bold"), bg="#4CAF50", fg="white",
             width=12, pady=5).pack(side="left", padx=5)
    tk.Button(button_frame, text="取消", command=on_cancel,
             font=("Arial", 10), bg="#f44336", fg="white",
             width=12, pady=5).pack(side="left", padx=5)

    # 添加版本標示
    version_label = tk.Label(window, text=f"v{version} - 整合 GUI#02",
                             font=("Arial", 8), fg="gray")
    version_label.pack(pady=(5, 0))

    window.protocol("WM_DELETE_WINDOW", on_cancel)
    window.mainloop()

# ===== 等待使用者操作完成並開始搶單 (v1.5.0-dev05 正確流程) =====
def wait_for_user_operation_and_start_grabbing(filtered_tasks):
    """等待使用者操作完成並開始搶單 - 使用 v1.5.0-dev05 的正確流程"""
    def on_ready():
        window.attributes('-topmost', False)
        window.destroy()
        print("[INFO] 使用者已按下準備完成，準備進入搶單流程...")

        # 開始搶單流程 (v1.5.0-dev05 的正確流程)
        execute_order_grabbing(filtered_tasks)
        return True

    def on_cancel():
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()
        return False

    window = tk.Tk()
    window.title(f"AGES-KH 登入提示 v{__VERSION__} - PID: {os.getpid()}")
    window.geometry("400x200")
    window.attributes('-topmost', True)

    tk.Label(window, text="請手動登入平台並操作至清單畫面\n完成後請按下「準備完成」",
             padx=20, pady=20).pack()

    btn_frame = tk.Frame(window)
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="準備完成", command=on_ready, width=10).pack(side="left", padx=5)
    tk.Button(btn_frame, text="取消", command=on_cancel, width=10).pack(side="left", padx=5)
    window.protocol("WM_DELETE_WINDOW", on_cancel)
    window.mainloop()

# ===== GUI#05 - 操作指南 =====
def show_gui05_operation_guide(tasks, trigger_time):
    """GUI#05 - 操作指南 - 複製自 gui_prototype_viewer.py 已驗證代碼"""
    print("[INFO] 📖 顯示 GUI#05 - 操作指南")

    result = {'action': None}

    # 按鈕點擊處理
    def on_ready():
        print("[INFO] 用戶點擊 [✅ 準備完成]")
        result['action'] = 'ready'
        window.destroy()

    def on_cancel():
        print("[INFO] 用戶點擊 [❌ 取消操作]")
        result['action'] = 'cancel'
        window.destroy()

    # 創建視窗（比照 GUI#09 版型優化）
    window = tk.Tk()
    window.title(f"GUI#05 - AGES-KH 操作指南 v{__VERSION__}")
    window.geometry("600x500")
    window.resizable(False, False)
    window.attributes('-topmost', True)

    # 主框架（比照 GUI#09）
    main_frame = tk.Frame(window, padx=20, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 標題（比照 GUI#09）
    title_label = tk.Label(main_frame, text="🚀 搶單準備階段",
                          font=("Arial", 14, "bold"), fg="blue")
    title_label.pack(pady=(0, 20))

    # 內容區域框架（比照 GUI#09）
    content_frame = tk.LabelFrame(main_frame, text="📋 操作指南", padx=15, pady=15)
    content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

    # 指南內容（使用 Text 組件支援滾動）
    text_widget = tk.Text(content_frame, wrap=tk.WORD, font=("Arial", 10),
                        height=15, width=50, bg="#f8f9fa")
    scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)

    instructions_text = f"""1. 瀏覽器已啟動，請手動完成以下操作：
   • 登入您的帳號
   • 輸入登入驗證碼
   • 導航到搶單頁面（進廠確認單列表）
   • 確保能看到您要搶的訂單

2. 完成上述操作後，點擊「準備完成」

3. 程式將自動：
   • 掃描頁面元素
   • 等待觸發時間
   • 執行搶單動作

⚠️ 重要提醒：
• 請確保已在正確的訂單管理頁面
• 系統將自動尋找並點擊編輯按鈕
• 驗證碼需要手動輸入
• 系統會自動計算最佳提交時間

⏰ 觸發時間資訊：
🎯 觸發時間: {trigger_time}
📅 當前時間: {datetime.now().strftime('%H:%M:%S')}

📋 任務摘要：
📊 任務數量: {len(tasks)} 筆"""

    # 添加任務詳情
    for i, task in enumerate(tasks[:3]):  # 最多顯示3筆
        order_id = task.get('order_id', 'N/A')
        browser = task.get('browser', 'chrome')
        instructions_text += f"\n  {i+1}. 訂單: {order_id} | 瀏覽器: {browser}"

    text_widget.insert(tk.END, instructions_text)
    text_widget.config(state=tk.DISABLED)  # 設為只讀

    text_widget.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # 按鈕區域（比照 GUI#09 底部固定）
    button_frame = tk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))

    # 按鈕（完全比照 GUI#09 規格）
    ready_btn = tk.Button(button_frame, text="✅ 準備完成", command=on_ready,
                        font=("Arial", 11, "bold"), bg="#4CAF50", fg="white",
                        padx=20, pady=10)
    ready_btn.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill=tk.X)

    cancel_btn = tk.Button(button_frame, text="❌ 取消", command=on_cancel,
                         font=("Arial", 11, "bold"), bg="#f44336", fg="white",
                         padx=20, pady=10)
    cancel_btn.pack(side=tk.RIGHT, padx=(10, 0), expand=True, fill=tk.X)

    # 添加版本標示
    version_label = tk.Label(main_frame, text=f"v{__VERSION__} - 整合 GUI#05",
                             font=("Arial", 8), fg="gray")
    version_label.pack(pady=(10, 0))

    window.protocol("WM_DELETE_WINDOW", on_cancel)

    # 等待用戶操作
    window.mainloop()

    return result['action']

# ===== GUI#09 - 驗證碼輸入提醒 =====
def show_gui09_verification_reminder():
    """GUI#09 - 驗證碼輸入提醒 - 複製自 gui_prototype_viewer.py 已驗證代碼"""
    print("[INFO] 🔐 顯示 GUI#09 - 驗證碼輸入提醒")

    global current_execution_mode
    result = {'mode': None, 'confirmed': False}

    # 模擬檢測結果數據
    mock_detection_result = {
        'submit_buttons': ['submit1', 'submit2'],
        'cancel_buttons': ['cancel1'],
        'other_buttons': ['other1', 'other2']
    }

    # 創建提醒視窗（複製自主程式的實際代碼）
    reminder_window = tk.Tk()
    reminder_window.title("GUI#09 - 驗證碼輸入提醒")
    reminder_window.geometry("600x500")
    reminder_window.resizable(False, False)
    reminder_window.attributes('-topmost', True)
    reminder_window.focus_force()

    # 主框架
    main_frame = tk.Frame(reminder_window, padx=20, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 標題
    title_label = tk.Label(main_frame, text="🔐 請在修改進廠確認單中輸入驗證碼",
                          font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))

    # 操作說明
    instructions = [
        "📋 操作說明：",
        "1. 在彈出的修改進廠確認單中找到驗證碼輸入框",
        "2. 手動輸入驗證碼",
        "3. 確認所有資料正確",
        "4. 選擇下方按鈕完成準備",
        "",
        "⏰ 請在觸發時間前 5 分鐘內完成驗證碼輸入"
    ]

    for instruction in instructions:
        label = tk.Label(main_frame, text=instruction, font=("Arial", 10), anchor="w")
        label.pack(fill=tk.X, pady=2)

    # 狀態欄框架
    status_frame = tk.LabelFrame(main_frame, text="📊 系統檢測狀態", font=("Arial", 10, "bold"))
    status_frame.pack(fill=tk.X, pady=(20, 10))

    # 生成狀態信息（使用模擬數據）
    submit_count = len(mock_detection_result.get('submit_buttons', []))
    cancel_count = len(mock_detection_result.get('cancel_buttons', []))
    other_count = len(mock_detection_result.get('other_buttons', []))

    # 檢測狀態
    if submit_count > 0 and cancel_count > 0:
        location_status = "✅ 修改進廠確認單"
        detection_status = f"✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ✅ 找到取消按鈕"
    elif submit_count > 0:
        location_status = "⚠️ 修改進廠確認單 (部分載入)"
        detection_status = f"✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ❌ 未找到取消按鈕"
    else:
        location_status = "❌ 尚未進入修改進廠確認單"
        detection_status = "⏳ 等待編輯彈窗開啟..."

    # 元素統計（模擬數據，標示已知問題）
    total_buttons = submit_count + cancel_count + other_count
    element_stats = f"按鈕 {total_buttons} 個 | 送出 {submit_count} 個 | 取消 {cancel_count} 個 (原型數據)"

    # 狀態標籤
    status_labels = [
        f"📍 定位狀態: {location_status}",
        f"🔍 檢測結果: {detection_status}",
        f"📊 元素統計: {element_stats}"
    ]

    for status_text in status_labels:
        status_label = tk.Label(status_frame, text=status_text, font=("Arial", 9), anchor="w")
        status_label.pack(fill=tk.X, pady=2, padx=10)

    # 按鈕點擊處理
    def on_normal_submit():
        result['mode'] = 'normal'
        result['confirmed'] = True
        print("[INFO] 用戶選擇 [已輸入驗證碼-正式送單]")
        reminder_window.destroy()

    def on_test_submit():
        result['mode'] = 'test'
        result['confirmed'] = True
        print("[INFO] 用戶選擇 [已輸入驗證碼-模擬送單]")
        reminder_window.destroy()

    # 按鈕框架
    button_frame = tk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))

    # 按鈕（複製自主程式的實際設計）
    normal_button = tk.Button(button_frame, text="已輸入驗證碼-正式送單",
                             command=on_normal_submit, font=("Arial", 11, "bold"),
                             bg="#4CAF50", fg="white", padx=20, pady=10)
    normal_button.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill=tk.X)

    test_button = tk.Button(button_frame, text="已輸入驗證碼-模擬送單",
                           command=on_test_submit, font=("Arial", 11, "bold"),
                           bg="#FF9800", fg="white", padx=20, pady=10)
    test_button.pack(side=tk.RIGHT, padx=(10, 0), expand=True, fill=tk.X)

    # 添加版本標示
    version_label = tk.Label(main_frame, text=f"v{__VERSION__} - 整合 GUI#09",
                             font=("Arial", 8), fg="gray")
    version_label.pack(pady=(10, 0))

    # 視窗關閉處理
    def on_close():
        print("[INFO] 用戶關閉 GUI#09")
        result['confirmed'] = False
        reminder_window.destroy()

    reminder_window.protocol("WM_DELETE_WINDOW", on_close)

    # 等待用戶操作
    reminder_window.mainloop()

    if result['confirmed']:
        current_execution_mode = result['mode']
        print(f"[INFO] ✅ 執行模式設定為: {current_execution_mode}")

    return result



# ===== 背景監測 Chrome 是否被關閉 =====
def start_driver_monitor():
    def monitor():
        global chrome_pid
        print("[DEBUG] 監控線程已啟動")
        while True:
            try:
                if chrome_pid and not psutil.pid_exists(chrome_pid):
                    print("[WARN] 偵測到 Python 啟動的 Chrome 已關閉")
                    safe_exit()
                    break

                # 🚨 修復：安全檢查 driver.current_url，避免阻塞
                try:
                    _ = driver.current_url  # 只是檢查連接，不使用返回值
                except Exception as url_error:
                    print(f"[DEBUG] URL檢查失敗: {url_error}")
                    # 不要立即退出，給WebDriver一些時間恢復

                time.sleep(1)
            except (WebDriverException, AttributeError):
                print("[WARN] WebDriver 連線異常")
                safe_exit()
                break
            except Exception as e:
                print(f"[ERROR] 監控錯誤: {str(e)}")
                safe_exit()
                break

    threading.Thread(target=monitor, daemon=True).start()

class GrabberGUI:
    def __init__(self):
        print("[DEBUG] 初始化 GrabberGUI...")
        self.window = tk.Tk()
        self.window.title(f"AGES-KH 搶單主程式 v{__VERSION__}")
        self.window.geometry("500x600")
        self.window.resizable(False, False)

        # 新增：儲存觸發時間
        self.trigger_time = "09:30:00.001"  # 預設值

        # 建立 GUI 元件
        print("[DEBUG] 創建 GUI 元件...")
        self._create_widgets()

        # 載入今日任務
        print("[DEBUG] 載入今日任務...")
        self._load_today_tasks()
        print("[DEBUG] GrabberGUI 初始化完成")

    def _create_widgets(self):
        """建立 GUI 元件"""
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill="both", expand=True)

        # 標題
        title_label = ttk.Label(
            main_frame,
            text=f"AGES-KH 搶單主程式 v{__VERSION__}",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10, fill="x")

        # 觸發時間區域
        time_frame = ttk.Frame(main_frame)
        time_frame.pack(fill="x", pady=5)
        ttk.Label(time_frame, text="當前搶單時間:").pack(side="left")
        self.time_label = ttk.Label(time_frame, text=self.trigger_time, font=("Arial", 10, "bold"))
        self.time_label.pack(side="left", padx=5)
        ttk.Button(
            time_frame,
            text="修改時間",
            command=self._ask_trigger_time
        ).pack(side="left", padx=5)

        # 任務顯示區域
        self.task_frame = ttk.Frame(main_frame)
        self.task_frame.pack(fill="both", expand=True, pady=5)

        # 按鈕區域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=10, side="bottom")
        ttk.Button(
            button_frame,
            text="RTT 設定",
            command=self._show_rtt_config
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame,
            text="開始執行",
            command=self._start_execution
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame,
            text="結束程式",
            command=self.window.destroy
        ).pack(side="right", padx=5)

    def _display_tasks(self, filtered_tasks, ignored_tasks, valid_tasks):
        """顯示任務列表"""
        for widget in self.task_frame.winfo_children():
            widget.destroy()

        # 今日任務區域（固定高度 200）
        filtered_frame = ttk.LabelFrame(self.task_frame, text="今日任務（待執行）")
        filtered_frame.pack(side="top", fill="x", padx=5, pady=2)
        filtered_frame.config(height=200)
        filtered_frame.pack_propagate(False)
        filtered_canvas = tk.Canvas(filtered_frame, height=200)
        filtered_scrollbar = ttk.Scrollbar(filtered_frame, orient="vertical", command=filtered_canvas.yview)
        filtered_content = ttk.Frame(filtered_canvas)
        filtered_content.bind(
            "<Configure>",
            lambda e: filtered_canvas.configure(scrollregion=filtered_canvas.bbox("all"))
        )
        filtered_canvas.create_window((0, 0), window=filtered_content, anchor="nw")
        filtered_canvas.configure(yscrollcommand=filtered_scrollbar.set)
        filtered_canvas.pack(side="left", fill="both", expand=True)
        filtered_scrollbar.pack(side="right", fill="y")
        ttk.Label(
            filtered_content,
            text=f"總數：{len(filtered_tasks)} 筆",
            font=("Arial", 10, "bold")
        ).pack(anchor="w")
        if filtered_tasks:
            for task in filtered_tasks:
                task_text = (
                    f"任務日期： {task.get('date', 'N/A')}\n"
                    f"進廠單日： {task.get('order_date', 'N/A')}\n"
                    f"進廠單號： {task.get('order_id', 'N/A')}\n"
                    f"　瀏覽器： {task.get('browser', 'N/A')}\n"
                    f"　使用者： {task.get('user_profile', 'N/A')}\n"
                    f"預測模型： {task.get('model', 'N/A')}\n"
                    f"觸發時間： {task.get('trigger_time', 'N/A')}\n"
                    f"任務備註： {task.get('note', 'N/A')}\n"
                )
                ttk.Label(
                    filtered_content,
                    text=task_text,
                    font=("Arial", 10)
                ).pack(pady=5, anchor="w")
        else:
            ttk.Label(
                filtered_content,
                text="無今日任務",
                font=("Arial", 10)
            ).pack(pady=5)

        # 有效任務區域（固定高度 200）
        valid_frame = ttk.LabelFrame(self.task_frame, text="有效任務（全部有效，含待執行與不執行）")
        valid_frame.pack(side="top", fill="x", padx=5, pady=2)
        valid_frame.config(height=200)
        valid_frame.pack_propagate(False)
        valid_canvas = tk.Canvas(valid_frame, height=200)
        valid_scrollbar = ttk.Scrollbar(valid_frame, orient="vertical", command=valid_canvas.yview)
        valid_content = ttk.Frame(valid_canvas)
        valid_content.bind(
            "<Configure>",
            lambda e: valid_canvas.configure(scrollregion=valid_canvas.bbox("all"))
        )
        valid_canvas.create_window((0, 0), window=valid_content, anchor="nw")
        valid_canvas.configure(yscrollcommand=valid_scrollbar.set)
        valid_canvas.pack(side="left", fill="both", expand=True)
        valid_scrollbar.pack(side="right", fill="y")
        ttk.Label(
            valid_content,
            text=f"總數：{len(valid_tasks)} 筆",
            font=("Arial", 10, "bold")
        ).pack(anchor="w")
        if valid_tasks:
            for task in valid_tasks:
                task_text = (
                    f"任務日期： {task.get('date', 'N/A')}\n"
                    f"進廠單日： {task.get('order_date', 'N/A')}\n"
                    f"進廠單號： {task.get('order_id', 'N/A')}\n"
                    f"　瀏覽器： {task.get('browser', 'N/A')}\n"
                    f"　使用者： {task.get('user_profile', 'N/A')}\n"
                    f"預測模型： {task.get('model', 'N/A')}\n"
                    f"觸發時間： {task.get('trigger_time', 'N/A')}\n"
                    f"任務備註： {task.get('note', 'N/A')}\n"
                )
                ttk.Label(
                    valid_content,
                    text=task_text,
                    font=("Arial", 10)
                ).pack(pady=5, anchor="w")
        else:
            ttk.Label(
                valid_content,
                text="無有效任務",
                font=("Arial", 10)
            ).pack(pady=5)

    def _load_today_tasks(self):
        """載入今日任務"""
        try:
            filtered_tasks, ignored_tasks, valid_tasks = load_and_filter_orders()
            self._display_tasks(filtered_tasks, ignored_tasks, valid_tasks)
        except Exception as e:
            messagebox.showerror("錯誤", f"載入任務失敗：{str(e)}")

    def _show_rtt_config(self):
        """顯示 RTT 設定視窗"""
        RTTConfigGUI().show()

    def _ask_trigger_time(self):
        """詢問使用者輸入觸發時間"""
        new_time = ask_trigger_time_gui(self.trigger_time)
        if new_time:
            self.trigger_time = new_time
            self.time_label.config(text=self.trigger_time)
            
    def _start_execution(self):
        """開始執行搶單 - 簡化流程，避免重複確認"""
        # 確認觸發時間格式
        if not re.match(r"^\d{2}:\d{2}:\d{2}\.\d{3}$", self.trigger_time):
            messagebox.showerror("錯誤", "觸發時間格式不正確，請使用 HH:MM:SS.sss 格式")
            return

        # 載入並過濾訂單
        filtered_tasks, ignored_tasks, valid_tasks = load_and_filter_orders()

        if not filtered_tasks:
            messagebox.showwarning("警告", "無有效任務可執行")
            return

        # 準備任務摘要
        valid_summary = "\n".join(str(t) for t in valid_tasks)
        task_summary = "\n".join(str(t) for t in filtered_tasks)

        # 關閉主 GUI 並顯示 GUI#02 準備提示
        self.window.withdraw()  # 隱藏主視窗

        # 調用新的 GUI#02 準備提示視窗，傳遞觸發時間
        show_preparation_gui(__VERSION__, valid_summary, task_summary, ignored_tasks, filtered_tasks, self.trigger_time)



    def _show_simplified_user_guide(self, filtered_tasks):
        """顯示簡化的用戶操作指南"""
        print("[DEBUG] 進入 _show_simplified_user_guide 函數")
        def on_ready():
            window.attributes('-topmost', False)
            window.destroy()
            print("[INFO] 用戶確認準備完成，開始搶單流程...")
            logger.info("🎯 用戶點擊準備完成按鈕，開始詳細檢測...")

            # 🔍 詳細記錄當前頁面狀態
            if driver:
                page_info = log_page_content(driver, "用戶點擊準備完成")
                if page_info:
                    print(f"[INFO] 📋 頁面檢測摘要:")
                    print(f"  - URL: {page_info['url']}")
                    print(f"  - 標題: {page_info['title']}")
                    print(f"  - 包含目標訂單: {page_info['has_target_order']}")
                    print(f"  - 包含 E48B: {page_info['has_e48b']}")
                    print(f"  - 表格數量: {page_info['table_count']}")
                    print(f"  - 表格行數: {page_info['row_count']}")
                    print(f"  - 編輯按鈕數量: {page_info['edit_button_count']}")
                    print(f"  - E48B 元素數量: {page_info['e48b_element_count']}")
                    print(f"[INFO] 📄 詳細日誌已記錄到: {current_log_file}")

            # 檢查用戶是否在正確的頁面
            if not self._verify_correct_page():
                return

            # 開始搶單流程
            execute_order_grabbing(filtered_tasks)

        def on_cancel():
            window.attributes('-topmost', False)
            window.destroy()
            safe_exit()

        window = tk.Tk()
        window.title(f"AGES-KH 操作指南 v{__VERSION__}")
        window.geometry("600x500")  # 增大視窗
        window.resizable(False, False)  # 固定大小
        window.attributes('-topmost', True)

        # 創建主框架和滾動條
        main_frame = tk.Frame(window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 創建滾動框架
        canvas = tk.Canvas(main_frame)
        scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        title_label = tk.Label(
            scrollable_frame,
            text="🚀 搶單準備階段",
            font=("Arial", 14, "bold"),
            fg="blue"
        )
        title_label.pack(pady=(0, 10))

        instructions = [
            "1. 瀏覽器已啟動，請手動完成以下操作：",
            "   • 登入您的帳號",
            "   • 輸入登入驗證碼",
            "   • 導航到搶單頁面（進廠確認單列表）",
            "   • 確保能看到您要搶的訂單",
            "",
            "2. 完成上述操作後，點擊「準備完成」",
            "",
            "3. 程式將自動：",
            "   • 掃描頁面元素",
            "   • 等待觸發時間",
            "   • 執行搶單動作"
        ]

        for instruction in instructions:
            label = tk.Label(
                scrollable_frame,
                text=instruction,
                font=("Arial", 10),
                anchor="w",
                justify="left"
            )
            label.pack(fill="x", pady=1)

        # 時間顯示區域
        time_frame = tk.Frame(scrollable_frame)
        time_frame.pack(pady=(20, 10), fill="x")

        current_time_label = tk.Label(
            time_frame,
            text="⏰ 當前時間: 載入中...",
            font=("Arial", 10, "bold"),
            fg="blue"
        )
        current_time_label.pack()

        trigger_time_label = tk.Label(
            time_frame,
            text="🎯 觸發時間: 載入中...",
            font=("Arial", 10, "bold"),
            fg="red"
        )
        trigger_time_label.pack()

        countdown_label = tk.Label(
            time_frame,
            text="⏳ 倒數計時: 載入中...",
            font=("Arial", 12, "bold"),
            fg="green"
        )
        countdown_label.pack()

        # 時間更新函數
        def get_ntp_time():
            """獲取 NTP 精準時間"""
            from datetime import datetime

            # 嘗試導入 ntplib
            try:
                import ntplib
                print("[INFO] ntplib 模組載入成功")
            except ImportError:
                print("[WARN] ❌ ntplib 未安裝，請執行: pip install ntplib")
                return datetime.now(), "本地系統時間 (缺少 ntplib)"

            # 使用台灣 NTP 服務器
            ntp_servers = [
                'tock.stdtime.gov.tw',  # 台灣標準時間
                'watch.stdtime.gov.tw', # 台灣標準時間備用
                'time.stdtime.gov.tw',  # 台灣標準時間第三選擇
                'time.nist.gov',        # 美國 NIST
                'pool.ntp.org',         # 全球 NTP 池
                '1.tw.pool.ntp.org',    # 台灣 NTP 池
                '0.pool.ntp.org'        # 全球 NTP 池備用
            ]

            print(f"[INFO] 🌐 開始嘗試連接 NTP 服務器...")

            for i, server in enumerate(ntp_servers):
                try:
                    print(f"[INFO] 嘗試連接 NTP 服務器 {i+1}/{len(ntp_servers)}: {server}")

                    client = ntplib.NTPClient()
                    response = client.request(server, version=3, timeout=5)  # 增加超時時間
                    ntp_time = datetime.fromtimestamp(response.tx_time)

                    print(f"[INFO] ✅ NTP 連接成功: {server}")
                    print(f"[INFO] 🕐 NTP 時間: {ntp_time.strftime('%Y-%m-%d %H:%M:%S')}")

                    return ntp_time, server

                except Exception as e:
                    print(f"[WARN] ❌ NTP 服務器 {server} 連接失敗: {e}")
                    continue

            # 如果所有 NTP 服務器都失敗，使用本地時間
            print("[WARN] ⚠️ 所有 NTP 服務器連接失敗，使用本地時間")
            local_time = datetime.now()
            print(f"[INFO] 🕐 本地時間: {local_time.strftime('%Y-%m-%d %H:%M:%S')}")

            return local_time, "本地系統時間"

        def update_time_display():
            try:
                from datetime import datetime, timedelta

                # 獲取 NTP 精準時間
                try:
                    current_time, time_source = get_ntp_time()
                    current_time_label.config(text=f"⏰ 當前時間: {current_time.strftime('%H:%M:%S')} 📡{time_source}")
                    print(f"[INFO] 時間來源: {time_source}")
                except Exception as e:
                    print(f"[ERROR] NTP 時間獲取失敗: {e}")
                    current_time = datetime.now()
                    time_source = "本地系統時間"
                    current_time_label.config(text=f"⏰ 當前時間: {current_time.strftime('%H:%M:%S')} 📡{time_source}")

                # 獲取觸發時間（優先級：用戶輸入 > CSV > 預設）
                try:
                    print(f"[DEBUG] filtered_tasks 數量: {len(filtered_tasks) if filtered_tasks else 0}")

                    # 檢查是否有用戶在 GUI 中輸入的時間
                    # 從主 GUI 實例中獲取用戶設定的觸發時間
                    user_input_time = None
                    try:
                        # 嘗試從全局變量或主 GUI 實例獲取
                        if hasattr(self, 'trigger_time') and self.trigger_time != "09:30:00.001":
                            user_input_time = self.trigger_time
                            print(f"[DEBUG] 從主 GUI 獲取用戶時間: '{user_input_time}'")
                    except:
                        user_input_time = None

                    trigger_time_str = ""
                    trigger_time_source = ""

                    if user_input_time:
                        # 優先使用用戶輸入的時間
                        trigger_time_str = user_input_time
                        trigger_time_source = "用戶輸入"
                        print(f"[DEBUG] 使用用戶輸入時間: '{trigger_time_str}'")
                    elif filtered_tasks and len(filtered_tasks) > 0:
                        # 其次使用 CSV 中的時間
                        task = filtered_tasks[0]
                        print(f"[DEBUG] 第一個任務: {task}")
                        trigger_time_str = task.get('trigger_time', '')
                        if trigger_time_str:
                            trigger_time_source = "orders.csv"
                            print(f"[DEBUG] 使用 CSV 時間: '{trigger_time_str}'")
                        else:
                            # 最後使用預設時間
                            trigger_time_str = "09:30:00.001"
                            trigger_time_source = "系統預設"
                            print(f"[DEBUG] 使用預設時間: '{trigger_time_str}'")
                    else:
                        # 沒有任務時使用預設時間
                        trigger_time_str = "09:30:00.001"
                        trigger_time_source = "系統預設"
                        print(f"[DEBUG] 無任務，使用預設時間: '{trigger_time_str}'")

                    if trigger_time_str:
                        # 計算倒數計時和日期標示
                        try:
                            # 解析觸發時間
                            if '.' in trigger_time_str:
                                # 包含毫秒的格式 HH:MM:SS.fff
                                trigger_time_obj = datetime.strptime(trigger_time_str, '%H:%M:%S.%f').time()
                            else:
                                # 標準格式 HH:MM:SS
                                trigger_time_obj = datetime.strptime(trigger_time_str, '%H:%M:%S').time()

                            # 組合今天的日期和觸發時間
                            today = current_time.date()
                            trigger_datetime = datetime.combine(today, trigger_time_obj)

                            print(f"[DEBUG] 當前時間: {current_time}")
                            print(f"[DEBUG] 今天觸發時間: {trigger_datetime}")

                            # 判斷是今日還是明日
                            is_tomorrow = False
                            if trigger_datetime <= current_time:
                                trigger_datetime += timedelta(days=1)
                                is_tomorrow = True
                                print(f"[DEBUG] 觸發時間已過，設為明天: {trigger_datetime}")

                            # 生成日期標示
                            if is_tomorrow:
                                date_indicator = "明日"
                                date_emoji = "🌅"
                            else:
                                date_indicator = "今日"
                                date_emoji = "☀️"

                            # 更新觸發時間顯示（包含日期標示）
                            trigger_time_display = f"🎯 觸發時間: {date_emoji}{date_indicator} {trigger_time_str} 📡{trigger_time_source}"
                            trigger_time_label.config(text=trigger_time_display)

                            # 計算時間差
                            time_diff = trigger_datetime - current_time
                            print(f"[DEBUG] 時間差: {time_diff}")
                            print(f"[DEBUG] 總秒數: {time_diff.total_seconds()}")

                            # 處理負數情況
                            if time_diff.total_seconds() < 0:
                                countdown_label.config(text="⏳ 倒數計時: 已過期")
                            else:
                                total_seconds = int(time_diff.total_seconds())
                                hours, remainder = divmod(total_seconds, 3600)
                                minutes, seconds = divmod(remainder, 60)

                                print(f"[DEBUG] 倒數計時: {hours}小時 {minutes}分 {seconds}秒")

                                # 如果超過 24 小時，顯示天數
                                if hours >= 24:
                                    days = hours // 24
                                    remaining_hours = hours % 24
                                    countdown_text = f"⏳ 倒數計時: {days}天 {remaining_hours:02d}:{minutes:02d}:{seconds:02d}"
                                else:
                                    countdown_text = f"⏳ 倒數計時: {hours:02d}:{minutes:02d}:{seconds:02d}"

                                countdown_label.config(text=countdown_text)

                        except Exception as e:
                            countdown_label.config(text=f"⏳ 倒數計時: 解析錯誤")
                            print(f"[ERROR] 倒數計時計算錯誤: {e}")
                            print(f"[ERROR] 觸發時間字串: '{trigger_time_str}'")
                    else:
                        trigger_time_label.config(text="🎯 觸發時間: 未設定")
                        countdown_label.config(text="⏳ 倒數計時: 未設定")

                except Exception as e:
                    trigger_time_label.config(text="🎯 觸發時間: 解析失敗")
                    countdown_label.config(text="⏳ 倒數計時: 解析失敗")
                    print(f"[ERROR] 觸發時間處理失敗: {e}")

                # 每秒更新一次
                try:
                    window.after(1000, update_time_display)
                except Exception as e:
                    print(f"[ERROR] 設定下次更新失敗: {e}")

            except Exception as e:
                print(f"[ERROR] 時間更新主函數失敗: {e}")
                import traceback
                print(f"[ERROR] 詳細錯誤: {traceback.format_exc()}")

                # 設定錯誤訊息
                try:
                    current_time_label.config(text="⏰ 當前時間: 載入失敗")
                    trigger_time_label.config(text="🎯 觸發時間: 載入失敗")
                    countdown_label.config(text="⏳ 倒數計時: 載入失敗")
                except:
                    pass

        # 啟動時間更新
        update_time_display()

        # 按鈕區域
        btn_frame = tk.Frame(scrollable_frame)
        btn_frame.pack(pady=(20, 0))

        # 第一排按鈕
        btn_frame1 = tk.Frame(scrollable_frame)
        btn_frame1.pack(pady=(10, 5))

        ready_btn = tk.Button(
            btn_frame1,
            text="✅ 準備完成",
            command=on_ready,
            width=15,
            bg="lightgreen",
            font=("Arial", 10, "bold")
        )
        ready_btn.pack(side="left", padx=5)

        # 第二排按鈕
        btn_frame2 = tk.Frame(scrollable_frame)
        btn_frame2.pack(pady=5)

        test_btn = tk.Button(
            btn_frame2,
            text="🧪 測試模式",
            command=lambda: self._handle_mode_selection('test', window, filtered_tasks),
            width=15,
            bg="lightyellow",
            font=("Arial", 10, "bold")
        )
        test_btn.pack(side="left", padx=5)

        cancel_btn = tk.Button(
            btn_frame2,
            text="❌ 取消",
            command=on_cancel,
            width=15,
            bg="lightcoral"
        )
        cancel_btn.pack(side="left", padx=5)

        window.protocol("WM_DELETE_WINDOW", on_cancel)

        print("[DEBUG] GUI 視窗已創建，準備顯示...")
        try:
            window.mainloop()
        except Exception as e:
            print(f"[WARN] GUI 顯示失敗: {e}")
            print("[INFO] 使用控制台交互模式...")

            # 控制台交互模式
            print("\n" + "="*60)
            print("🚀 AGES-KH 搶單系統 - 控制台模式")
            print("="*60)
            print("\n📋 操作步驟：")
            print("1. 🌐 瀏覽器已自動啟動並導航到平台首頁")
            print("2. 🔐 請手動登入您的帳號")
            print("3. 📄 導航到「進廠確認單管理」頁面")
            print("4. ✅ 確認可以看到訂單列表")
            print("5. 🎯 輸入 'ready' 開始搶單")
            print("\n⚠️ 注意事項：")
            print("• 請確保已在正確的訂單管理頁面")
            print("• 系統將自動尋找並點擊編輯按鈕")
            print("• 驗證碼需要手動輸入")
            print("• 系統會自動計算最佳提交時間")

            while True:
                user_input = input("\n請輸入 'ready' 開始搶單，或 'quit' 退出: ").strip().lower()
                if user_input == 'ready':
                    on_ready()
                    break
                elif user_input == 'quit':
                    on_cancel()
                    break
                else:
                    print("請輸入 'ready' 或 'quit'")

    def _handle_mode_selection(self, mode, window, filtered_tasks):
        """處理不同模式的選擇"""
        window.attributes('-topmost', False)
        window.destroy()

        if mode == 'verification_ready':
            print("[INFO] 用戶確認驗證碼輸入完成，開始搶單流程...")
            logger.info("🎯 用戶確認驗證碼已輸入，開始搶單...")
        elif mode == 'test':
            print("[INFO] 用戶選擇測試模式...")
            logger.info("🧪 測試模式啟動...")

        # 設置全局模式變量
        global current_mode
        current_mode = mode

        # 開始搶單流程
        execute_order_grabbing(filtered_tasks)

    def _verify_correct_page(self):
        """驗證用戶是否在正確的搶單頁面"""
        global driver

        try:
            # 等待頁面穩定
            time.sleep(2)

            # 🔄 重新獲取當前頁面信息（重要！）
            current_url = driver.current_url
            page_title = driver.title
            print(f"[INFO] 檢查當前頁面: {current_url}")
            print(f"[INFO] 頁面標題: {page_title}")

            # 🔍 更智能的頁面檢測
            try:
                # 多次嘗試檢測頁面內容
                for attempt in range(3):
                    print(f"[INFO] 第 {attempt + 1} 次檢測頁面內容...")

                    # 重新獲取頁面元素
                    tables = driver.find_elements(By.XPATH, "//table")
                    rows = driver.find_elements(By.XPATH, "//tr")
                    page_source = driver.page_source

                    print(f"[INFO] 頁面包含 {len(tables)} 個表格，{len(rows)} 個表格行")

                    # 檢查是否有訂單相關的內容
                    has_order_content = any(keyword in page_source for keyword in [
                        "進廠確認單", "訂單", "編輯", "E48B", "預約", "確認單", "搶單"
                    ])

                    print(f"[INFO] 頁面包含訂單相關內容: {has_order_content}")

                    # 🎯 關鍵檢查：是否包含目標訂單號
                    target_order_in_page = "E48B201611405190953" in page_source
                    print(f"[INFO] 頁面包含目標訂單號: {target_order_in_page}")

                    # 如果找到目標訂單，直接確認為正確頁面
                    if target_order_in_page:
                        print("[INFO] ✅ 檢測到目標訂單，確認為正確頁面")
                        return True

                    # 如果有表格行且有訂單內容，也認為是正確頁面
                    if len(rows) > 1 and has_order_content:
                        print("[INFO] ✅ 檢測到正確的搶單頁面（有表格和訂單內容）")
                        return True

                    if attempt < 2:
                        print("[INFO] 等待 2 秒後重新檢測...")
                        time.sleep(2)

            except Exception as e:
                print(f"[WARN] 檢查表格失敗: {e}")

            # 如果檢測不到訂單內容，詢問用戶
            print(f"[WARN] 無法確認當前頁面是否為搶單頁面")

            # 顯示確認對話框
            import tkinter.messagebox as msgbox
            response = msgbox.askyesno(
                "頁面確認",
                f"當前頁面資訊：\n"
                f"URL: {current_url}\n"
                f"標題: {page_title}\n"
                f"表格數量: {len(tables) if 'tables' in locals() else '未知'}\n"
                f"表格行數: {len(rows) if 'rows' in locals() else '未知'}\n\n"
                f"請確認您是否已經：\n"
                f"1. 成功登入系統\n"
                f"2. 導航到進廠確認單列表頁面\n"
                f"3. 能看到訂單 E48B201611405190953\n\n"
                f"如果是，請點擊「是」繼續搶單\n"
                f"如果不是，請點擊「否」並手動導航到正確頁面"
            )

            if not response:
                print("[INFO] 用戶選擇不繼續，程序終止")
                safe_exit()
                return False
            else:
                print("[INFO] 用戶確認當前頁面正確，繼續執行搶單")
                return True

        except Exception as e:
            print(f"[ERROR] 頁面驗證失敗: {e}")
            return False

    def run(self):
        """執行主程式"""
        self.window.mainloop()

# ===== 頁面狀態檢查函數 =====
def check_order_list_page():
    """檢查是否在正確的訂單列表頁面"""
    global driver

    try:
        print("[INFO] 🔍 檢查頁面內容...")
        logger.info("🔍 開始檢查訂單列表頁面")

        # 檢查頁面標題
        page_title = driver.title
        print(f"[INFO] 頁面標題: {page_title}")

        # 檢查 URL
        current_url = driver.current_url
        print(f"[INFO] 當前 URL: {current_url}")

        # 使用 JavaScript 檢查頁面內容
        js_check = """
        // 檢查頁面是否包含訂單相關內容
        var bodyText = document.body.innerText || document.body.textContent || '';
        var hasOrderContent = bodyText.includes('進廠確認單') ||
                             bodyText.includes('訂單') ||
                             bodyText.includes('編輯') ||
                             bodyText.includes('E48B');

        // 檢查是否有表格
        var tables = document.querySelectorAll('table');
        var hasTable = tables.length > 0;

        // 檢查是否有編輯按鈕
        var editButtons = document.querySelectorAll('a, button, input[type="button"]');
        var editButtonCount = 0;
        editButtons.forEach(function(btn) {
            var text = (btn.textContent || btn.value || '').toLowerCase();
            if (text.includes('編輯') || text.includes('edit')) {
                editButtonCount++;
            }
        });

        return {
            hasOrderContent: hasOrderContent,
            hasTable: hasTable,
            editButtonCount: editButtonCount,
            tableCount: tables.length,
            bodyTextLength: bodyText.length,
            sampleText: bodyText.substring(0, 300)
        };
        """

        result = driver.execute_script(js_check)
        print(f"[INFO] 頁面檢查結果:")
        print(f"  - 包含訂單內容: {result['hasOrderContent']}")
        print(f"  - 包含表格: {result['hasTable']}")
        print(f"  - 編輯按鈕數量: {result['editButtonCount']}")
        print(f"  - 表格數量: {result['tableCount']}")
        print(f"  - 頁面文字長度: {result['bodyTextLength']}")
        print(f"  - 頁面內容樣本: {result['sampleText'][:100]}...")

        logger.info(f"頁面檢查結果: {result}")

        # 判斷是否在正確頁面
        is_correct_page = (result['hasOrderContent'] and
                          result['hasTable'] and
                          result['bodyTextLength'] > 100)

        if is_correct_page:
            print("[INFO] ✅ 確認在正確的訂單列表頁面")
            logger.info("✅ 確認在正確的訂單列表頁面")
        else:
            print("[WARN] ⚠️ 可能不在正確的訂單列表頁面")
            logger.warning("⚠️ 可能不在正確的訂單列表頁面")

        return is_correct_page

    except Exception as e:
        print(f"[ERROR] 頁面檢查失敗: {e}")
        logger.error(f"頁面檢查失敗: {e}")
        return False

def show_manual_navigation_guide():
    """顯示手動導航指引"""
    import tkinter as tk
    from tkinter import messagebox

    print("[INFO] 🚨 顯示手動導航指引...")
    logger.info("🚨 顯示手動導航指引")

    # 創建指引視窗
    guide_window = tk.Tk()
    guide_window.title("⚠️ 需要手動導航")
    guide_window.geometry("500x400")
    guide_window.attributes('-topmost', True)

    # 指引內容
    guide_text = """
🚨 檢測到您仍在登入頁面！

請手動完成以下步驟：

1. 🔐 登入您的帳號
   • 輸入用戶名和密碼
   • 輸入登入驗證碼

2. 📄 導航到進廠確認單列表頁面
   • 點擊相關菜單項目
   • 確保進入訂單管理頁面

3. ✅ 確認頁面狀態
   • 能看到訂單列表
   • 能看到編輯按鈕
   • 確認目標訂單存在

4. 🔄 完成後重新執行搶單流程

⚠️ 注意：程式將等待您完成上述操作
    """

    text_widget = tk.Text(guide_window, wrap=tk.WORD, padx=10, pady=10)
    text_widget.pack(fill=tk.BOTH, expand=True)
    text_widget.insert(tk.END, guide_text)
    text_widget.config(state=tk.DISABLED)

    def close_guide():
        guide_window.destroy()

    tk.Button(guide_window, text="我已完成導航", command=close_guide,
              bg="green", fg="white", font=("Arial", 12, "bold")).pack(pady=10)

    guide_window.mainloop()

# ===== iframe結構檢測函數 =====
def detect_iframe_structure():
    """檢測iframe結構並顯示詳細狀態"""
    global driver

    try:
        print("[INFO] 🔍 開始檢測iframe結構...")
        logger.info("🔍 開始檢測iframe結構")

        # JavaScript檢測iframe結構
        iframe_detection_script = """
        function detectIframeStructure() {
            var result = {
                mainPageIframes: 0,
                level1Iframes: 0,
                level2Iframes: 0,
                hasEditDialog: false,
                dialogTitle: '',
                currentLevel: 'main',
                iframeDetails: []
            };

            // 檢測主頁面iframe
            var mainIframes = document.querySelectorAll('iframe');
            result.mainPageIframes = mainIframes.length;

            console.log('主頁面iframe數量:', result.mainPageIframes);

            // 檢測每個iframe的內容
            for (var i = 0; i < mainIframes.length; i++) {
                try {
                    var iframe = mainIframes[i];
                    var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                    if (iframeDoc) {
                        var iframeText = iframeDoc.body.innerText || iframeDoc.body.textContent || '';
                        var hasEditContent = iframeText.includes('修改進廠確認單') ||
                                           iframeText.includes('編輯') ||
                                           iframeText.includes('驗證碼');

                        result.iframeDetails.push({
                            index: i,
                            hasEditContent: hasEditContent,
                            textLength: iframeText.length,
                            sampleText: iframeText.substring(0, 100)
                        });

                        if (hasEditContent) {
                            result.hasEditDialog = true;
                            result.currentLevel = 'level1';

                            // 檢查是否有標題
                            var titleElements = iframeDoc.querySelectorAll('h1, h2, h3, .title, [class*="title"]');
                            for (var j = 0; j < titleElements.length; j++) {
                                var titleText = titleElements[j].textContent || '';
                                if (titleText.includes('修改進廠確認單')) {
                                    result.dialogTitle = titleText;
                                    break;
                                }
                            }

                            // 檢測第二層iframe
                            var level2Iframes = iframeDoc.querySelectorAll('iframe');
                            result.level2Iframes = level2Iframes.length;

                            if (level2Iframes.length > 0) {
                                result.currentLevel = 'level2';
                            }
                        }
                    }
                } catch (e) {
                    console.log('無法訪問iframe內容:', e);
                    result.iframeDetails.push({
                        index: i,
                        hasEditContent: false,
                        error: e.message
                    });
                }
            }

            return result;
        }

        return detectIframeStructure();
        """

        result = driver.execute_script(iframe_detection_script)

        # 詳細狀態顯示
        print(f"[STATUS] 📊 iframe檢測結果:")
        print(f"[STATUS]   - 主頁面iframe數量: {result['mainPageIframes']}")
        print(f"[STATUS]   - 第二層iframe數量: {result['level2Iframes']}")
        print(f"[STATUS]   - 當前層級: {result['currentLevel']}")
        print(f"[STATUS]   - 發現編輯對話框: {result['hasEditDialog']}")
        print(f"[STATUS]   - 對話框標題: {result.get('dialogTitle', 'N/A')}")

        logger.info(f"iframe檢測結果: {result}")

        # 詳細iframe內容檢查
        for detail in result.get('iframeDetails', []):
            print(f"[STATUS]   - iframe[{detail['index']}]: 編輯內容={detail.get('hasEditContent', False)}, "
                  f"文字長度={detail.get('textLength', 0)}")
            if detail.get('sampleText'):
                print(f"[STATUS]     樣本內容: {detail['sampleText'][:50]}...")

        return result

    except Exception as e:
        print(f"[ERROR] iframe結構檢測失敗: {e}")
        logger.error(f"iframe結構檢測失敗: {e}")
        return None

# ===== 搶單執行核心邏輯 =====
# ===== 已驗證功能：用戶手動操作等待機制 (v1.4.33) =====
def wait_for_user_ready_confirmation():
    """等待用戶確認準備完成 - v1.4.33 已驗證功能，不得隨意修改！"""
    try:
        import tkinter as tk
        import tkinter.messagebox as msgbox

        print("[INFO] 🚨 等待用戶確認準備完成...")
        logger.info("🚨 等待用戶確認準備完成")

        # 創建確認對話框
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)

        response = msgbox.askyesno(
            "⚠️ 確認準備完成",
            "請確認您已經：\n\n"
            "✅ 成功登入平台\n"
            "✅ 導航到進廠確認單列表頁面\n"
            "✅ 能看到目標訂單\n"
            "✅ 準備好讓程式自動搶單\n\n"
            "點擊「是」開始自動搶單流程\n"
            "點擊「否」取消本次操作",
            parent=root
        )

        root.destroy()

        if response:
            print("[INFO] ✅ 用戶確認準備完成，開始自動搶單流程")
            logger.info("✅ 用戶確認準備完成，開始自動搶單流程")
            return True
        else:
            print("[INFO] ❌ 用戶取消操作")
            logger.info("❌ 用戶取消操作")
            return False

    except Exception as e:
        print(f"[ERROR] 用戶確認失敗: {e}")
        logger.error(f"用戶確認失敗: {e}")
        return False

def execute_order_grabbing_direct(tasks):
    """執行搶單流程 - 直接版本，跳過用戶確認（GUI#05已確認）"""
    global driver, result_detector, dom_inspector

    print("[DEBUG] 進入 execute_order_grabbing_direct 函數")
    print(f"[DEBUG] 任務數量: {len(tasks) if tasks else 0}")
    logger.info(f"🚀 開始執行搶單流程（直接版本），任務數量: {len(tasks) if tasks else 0}")

    if not driver or not result_detector:
        print("[ERROR] 瀏覽器或檢測器未初始化")
        print(f"[DEBUG] driver: {driver is not None}")
        print(f"[DEBUG] result_detector: {result_detector is not None}")
        logger.error(f"瀏覽器或檢測器未初始化 - driver: {driver is not None}, result_detector: {result_detector is not None}")
        return

    try:
        print("[INFO] 🚀 開始執行搶單流程（跳過用戶確認）...")

        # 🔍 檢查當前頁面狀態
        print("[INFO] 🔍 檢查當前頁面狀態...")
        current_url = driver.current_url
        page_title = driver.title
        print(f"[INFO] 當前 URL: {current_url}")
        print(f"[INFO] 頁面標題: {page_title}")
        logger.info(f"當前頁面狀態 - URL: {current_url}, 標題: {page_title}")

        # TODO: 這裡應該繼續實現搶單邏輯，暫時先返回
        print("[INFO] 🚧 execute_order_grabbing_direct 功能開發中...")

    except Exception as e:
        print(f"[ERROR] 搶單流程執行失敗: {e}")
        logger.error(f"搶單流程執行失敗: {e}")

def execute_order_grabbing(tasks):
    """執行搶單流程 - 恢復v1.4.33已驗證工作流程（包含用戶確認）"""
    global driver, result_detector, dom_inspector

    print("[DEBUG] 進入 execute_order_grabbing 函數")
    print(f"[DEBUG] 任務數量: {len(tasks) if tasks else 0}")
    logger.info(f"🚀 開始執行搶單流程，任務數量: {len(tasks) if tasks else 0}")

    if not driver or not result_detector:
        print("[ERROR] 瀏覽器或檢測器未初始化")
        print(f"[DEBUG] driver: {driver is not None}")
        print(f"[DEBUG] result_detector: {result_detector is not None}")
        logger.error(f"瀏覽器或檢測器未初始化 - driver: {driver is not None}, result_detector: {result_detector is not None}")
        return

    try:
        print("[INFO] 🚀 開始執行搶單流程...")

        # 🚨 關鍵修復：恢復v1.4.33的用戶確認機制
        print("[INFO] 🚨 等待用戶確認準備完成...")
        if not wait_for_user_ready_confirmation():
            print("[ERROR] 用戶取消操作，搶單流程終止")
            logger.error("用戶取消操作，搶單流程終止")
            return

        # 🔍 檢查當前頁面狀態
        print("[INFO] 🔍 檢查當前頁面狀態...")
        current_url = driver.current_url
        page_title = driver.title
        print(f"[INFO] 當前 URL: {current_url}")
        print(f"[INFO] 頁面標題: {page_title}")
        logger.info(f"當前頁面狀態 - URL: {current_url}, 標題: {page_title}")

        # 🎯 檢查是否在正確的訂單列表頁面
        print("[INFO] 🎯 檢查是否在訂單列表頁面...")
        page_content_check = check_order_list_page()
        if not page_content_check:
            print("[WARN] ⚠️ 可能不在正確的訂單列表頁面，但繼續執行")
            logger.warning("可能不在正確的訂單列表頁面，但繼續執行")

        # ===== 已驗證功能：執行每個任務 (v1.4.33) =====
        for task in tasks:
            order_id = task.get('order_id', 'N/A')
            trigger_time = task.get('trigger_time', '09:30:00.001')

            print(f"[INFO] 📋 處理訂單: {order_id}")
            print(f"[INFO] ⏰ 觸發時間: {trigger_time}")
            logger.info(f"📋 開始處理訂單: {order_id}, 觸發時間: {trigger_time}")

            # 1. 掃描頁面 DOM 元素
            print("[INFO] 📋 掃描頁面 DOM 元素...")
            scan_result = scan_current_page_dom()
            if not scan_result:
                print("[WARN] DOM 掃描未找到元素，但繼續嘗試搶單...")
                logger.warning(f"DOM 掃描未找到元素，訂單: {order_id}")

            # 2. 執行搶單動作（包含等待觸發時間）
            print(f"[INFO] 🎯 開始執行搶單流程: {order_id}")
            result = execute_single_order_grab(task)

            # 3. 檢查是否應該終止
            if result_detector.should_terminate_process(result):
                print("[INFO] 🔚 搶單生命週期結束，程序終止")
                break
            else:
                print("[INFO] 🔄 繼續下一個任務...")

    except Exception as e:
        print(f"[ERROR] 搶單執行過程發生異常: {e}")
        logger.error(f"搶單執行異常: {e}")
    finally:
        print("[INFO] 搶單流程結束")

def scan_current_page_dom():
    """掃描當前頁面的 DOM 元素"""
    global driver, dom_inspector

    try:
        print("[INFO] 開始掃描當前頁面...")

        # 檢查 driver 是否可用
        if not driver:
            print("[ERROR] 瀏覽器 driver 未初始化")
            return False

        try:
            current_url = driver.current_url
            print(f"[INFO] 正在掃描頁面: {current_url}")
        except Exception as e:
            print(f"[ERROR] 無法獲取當前頁面 URL: {e}")
            return False

        # 使用 DOM 檢查器的 find_order_elements 方法，傳入 driver
        elements = dom_inspector.find_order_elements(driver)

        if elements:
            print(f"[INFO] ✅ 掃描完成，找到 {len(elements)} 個元素")

            # 保存掃描結果
            dom_inspector.save_elements_config(elements)
            print("[INFO] DOM 配置已更新")

            return True
        else:
            print("[WARN] ⚠️ 未找到任何相關元素")
            return False

    except Exception as e:
        print(f"[ERROR] DOM 掃描失敗: {e}")
        logger.error(f"DOM 掃描失敗: {e}")
        return False

def wait_for_trigger_time(trigger_time_str, task=None):
    """等待觸發時間 - 整合 RTT 預測功能"""
    try:
        # 解析觸發時間
        trigger_time = datetime.strptime(trigger_time_str, "%H:%M:%S.%f").time()
        print(f"[INFO] ⏰ 目標觸發時間: {trigger_time_str}")

        # RTT 預測和調整
        rtt_adjustment = 0
        if task:
            try:
                # 獲取 RTT 預測模型
                model_name = task.get('model', 'A')  # 默認使用模型 A
                print(f"[INFO] 🔮 使用 RTT 預測模型: {model_name}")

                # 計算平均 RTT - 修復調用方式
                avg_rtt, rtts, timestamps = get_avg_rtt(model_name)
                if avg_rtt > 0:
                    rtt_adjustment = avg_rtt / 1000.0  # 轉換為秒
                    print(f"[INFO] 📊 平均 RTT: {avg_rtt:.2f}ms ({rtt_adjustment:.3f}s)")
                    print(f"[INFO] 📈 RTT 樣本數: {len(rtts)}")
                    print(f"[INFO] ⚡ 將提前 {rtt_adjustment:.3f} 秒執行以補償網路延遲")

                    # 記錄 RTT 日誌
                    logger.info(f"RTT 預測完成: model={model_name}, avg_rtt={avg_rtt:.2f}ms, samples={len(rtts)}, adjustment={rtt_adjustment:.3f}s")
                else:
                    print("[WARN] ⚠️ 無法獲取 RTT 數據，使用原始觸發時間")
                    logger.warning("RTT 預測返回 0，使用原始觸發時間")
            except Exception as e:
                print(f"[WARN] RTT 預測失敗，使用原始觸發時間: {e}")
                logger.error(f"RTT 預測失敗: {e}")
                # 如果 RTT 預測失敗，繼續使用原始時間

        # 調整後的實際觸發時間
        adjusted_trigger_time = trigger_time
        if rtt_adjustment > 0:
            # 將時間轉換為 datetime 進行計算
            trigger_datetime = datetime.combine(date.today(), trigger_time)
            adjusted_datetime = trigger_datetime - timedelta(seconds=rtt_adjustment)
            adjusted_trigger_time = adjusted_datetime.time()
            print(f"[INFO] 🎯 調整後觸發時間: {adjusted_trigger_time.strftime('%H:%M:%S.%f')[:-3]}")

        # 等待邏輯
        while True:
            current_time = datetime.now().time()

            if current_time >= adjusted_trigger_time:
                print(f"[INFO] 🚀 觸發時間到達: {current_time}")
                if rtt_adjustment > 0:
                    print(f"[INFO] ⚡ RTT 補償: 提前了 {rtt_adjustment:.3f} 秒執行")
                break

            # 計算剩餘時間
            current_datetime = datetime.combine(date.today(), current_time)
            trigger_datetime = datetime.combine(date.today(), adjusted_trigger_time)

            if trigger_datetime < current_datetime:
                # 觸發時間是明天
                trigger_datetime += timedelta(days=1)

            remaining = (trigger_datetime - current_datetime).total_seconds()

            if remaining > 60:
                print(f"[INFO] ⏳ 距離觸發時間還有 {remaining:.1f} 秒...")
                time.sleep(10)  # 每10秒檢查一次
            elif remaining > 1:
                time.sleep(0.1)  # 最後1秒每100毫秒檢查一次
            else:
                time.sleep(0.01)  # 最後1秒每10毫秒檢查一次

    except Exception as e:
        print(f"[ERROR] 等待觸發時間失敗: {e}")
        logger.error(f"等待觸發時間失敗: {e}")

def execute_single_order_grab(task):
    """執行單個訂單的搶單動作 - 修正流程"""
    global driver, result_detector

    try:
        order_id = task.get('order_id', 'N/A')
        print(f"[INFO] 🎯 開始搶單: {order_id}")

        # 1. 尋找並點擊編輯按鈕
        print(f"[INFO] 步驟 1: 尋找並點擊訂單 {order_id} 的編輯按鈕")
        if not find_and_click_edit_button(order_id):
            print(f"[ERROR] 找不到訂單 {order_id} 的編輯按鈕")
            return {"lifecycle_ended": True, "is_success": False, "message": "找不到編輯按鈕"}

        # 2. 等待編輯頁面載入
        print(f"[INFO] 步驟 2: 等待編輯頁面載入...")
        time.sleep(3)

        # 檢查是否成功進入編輯頁面
        try:
            current_url = driver.current_url
            page_title = driver.title
            print(f"[INFO] 當前頁面: {current_url}")
            print(f"[INFO] 頁面標題: {page_title}")
        except Exception as e:
            print(f"[WARN] 無法獲取頁面信息: {e}")

        # 3. 立即跳出 GUI#09 驗證碼輸入提醒
        print(f"[INFO] 步驟 3: 跳出 GUI#09 驗證碼輸入提醒...")
        gui09_result = show_gui09_verification_reminder()
        if not gui09_result['confirmed']:
            print("[ERROR] 用戶取消 GUI#09 操作")
            return {"lifecycle_ended": True, "is_success": False, "message": "用戶取消操作"}

        # 4. GUI#09 已完成，用戶已選擇執行模式，繼續後續流程
        print(f"[INFO] 步驟 4: GUI#09 已完成，執行模式: {current_execution_mode}")
        print(f"[INFO] 用戶已在 GUI#09 中完成驗證碼輸入確認")

        # 5. 根據 RTT 計算精確的送出時機，然後點擊送出按鈕
        print(f"[INFO] 步驟 5: 根據 RTT 精確計算送出時機...")
        if not execute_precise_submit(task):
            print("[ERROR] 精確送出失敗")
            return {"lifecycle_ended": True, "is_success": False, "message": "精確送出失敗"}

        # 6. 檢測送出結果
        print("[INFO] 步驟 6: 檢測送出結果...")
        result = result_detector.detect_submission_result(timeout=10)

        # 7. 記錄結果
        print("[INFO] 步驟 7: 記錄搶單結果...")
        log_order_result(task, result)

        return result

    except Exception as e:
        print(f"[ERROR] 搶單執行異常: {e}")
        logger.error(f"搶單執行異常: {e}")
        return {"lifecycle_ended": True, "is_success": False, "message": f"執行異常: {e}"}

def find_and_click_edit_button(order_id):
    """自動尋找並點擊指定訂單的編輯按鈕 - 採用 v1.4.33 快速定位策略 + 增強LOG"""
    global driver, last_dialog_detection

    try:
        print(f"[INFO] 🔍 自動尋找訂單 {order_id} 的編輯按鈕...")
        logger.info(f"🔍 開始尋找訂單 {order_id} 的編輯按鈕")

        # 🎯 終端狀態顯示
        print(f"[STATUS] 🎯 定位狀態：搜索訂單 {order_id}")
        print(f"[STATUS] 📍 當前位置：主頁面")
        print(f"[STATUS] 🔄 搜索進度：開始掃描...")

        # 🚀 使用 v1.4.33 的快速 JavaScript TreeWalker 策略
        print(f"[INFO] ⚡ 使用快速 JavaScript 搜尋策略...")

        try:
            # 🎯 JavaScript TreeWalker 快速搜尋並點擊 (v1.4.33 策略)
            click_script = f"""
            var walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            var node;
            while (node = walker.nextNode()) {{
                if (node.textContent.includes('{order_id}')) {{
                    var row = node.parentElement.closest('tr');
                    if (row) {{
                        var editElements = row.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
                        for (var i = 0; i < editElements.length; i++) {{
                            var el = editElements[i];
                            var text = (el.textContent || el.value || '').toLowerCase();
                            if (text.includes('編輯') || text.includes('edit')) {{
                                el.click();
                                return true;
                            }}
                        }}
                    }}
                }}
            }}
            return false;
            """

            clicked = driver.execute_script(click_script)
            if clicked:
                print(f"[INFO] ✅ 成功使用 JavaScript 快速點擊編輯按鈕!")
                logger.info(f"🎯 成功點擊編輯按鈕: JavaScript TreeWalker 快速策略")

                # 🔍 立即檢測點擊後的 DOM 變化 - v1.4.33 策略
                print(f"[INFO] 🔍 檢測點擊編輯按鈕後的 DOM 變化...")
                logger.info(f"🔍 檢測點擊編輯按鈕後的 DOM 變化...")

                # 🚀 使用增強的檢測功能
                dialog_detection_result = monitor_dialog_opening()

                if dialog_detection_result:
                    print(f"[INFO] ✅ 編輯彈窗檢測成功")
                    logger.info(f"✅ 編輯彈窗檢測成功")

                    # 保存檢測結果到全域變數供後續使用
                    global last_dialog_detection
                    last_dialog_detection = dialog_detection_result
                else:
                    print(f"[WARN] ⚠️ 編輯彈窗檢測未完全成功，但繼續執行")
                    logger.warning(f"⚠️ 編輯彈窗檢測未完全成功，但繼續執行")

                # 等待頁面跳轉
                time.sleep(2)
                return True
            else:
                print(f"[WARN] JavaScript 快速點擊失敗，嘗試備用方法...")

        except Exception as e:
            print(f"[WARN] JavaScript 快速搜尋失敗: {e}")

        # 🔄 備用方法：使用 WebDriverWait 等待 AJAX 動態載入完成
        print(f"[INFO] 🔄 使用備用方法：WebDriverWait 等待內容載入...")

        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # 簡化等待策略：只等待目標訂單號出現
            print(f"[INFO] 等待目標訂單號 {order_id} 出現...")
            try:
                WebDriverWait(driver, 10).until(  # 縮短等待時間
                    EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{order_id}')]"))
                )
                print(f"[INFO] ✅ 成功檢測到目標訂單 {order_id}！")
            except Exception as e:
                print(f"[WARN] 等待目標訂單超時: {e}")
                # 繼續嘗試，不中斷流程

            # 🎯 使用 JavaScript 再次檢查動態內容
            print(f"[INFO] 使用 JavaScript 檢查最終載入狀態...")
            js_final_check = f"""
            // 檢查頁面最終狀態
            var bodyText = document.body.innerText || document.body.textContent || '';
            var hasTargetOrder = bodyText.includes('{order_id}');
            var rows = document.querySelectorAll('tr');
            var rowCount = rows.length;

            // 檢查是否有編輯按鈕
            var editButtons = document.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
            var editButtonCount = 0;
            editButtons.forEach(function(btn) {{
                var text = (btn.textContent || btn.value || '').toLowerCase();
                if (text.includes('編輯') || text.includes('edit')) {{
                    editButtonCount++;
                }}
            }});

            return {{
                hasTargetOrder: hasTargetOrder,
                rowCount: rowCount,
                editButtonCount: editButtonCount,
                bodyTextLength: bodyText.length,
                sampleText: bodyText.substring(0, 200)
            }};
            """

            final_result = driver.execute_script(js_final_check)
            print(f"[INFO] 最終檢測結果:")
            print(f"  - 包含目標訂單: {final_result['hasTargetOrder']}")
            print(f"  - 表格行數: {final_result['rowCount']}")
            print(f"  - 編輯按鈕數量: {final_result['editButtonCount']}")
            print(f"  - 頁面文字長度: {final_result['bodyTextLength']}")
            print(f"  - 頁面內容樣本: {final_result['sampleText']}...")

            # 🔍 詳細診斷：使用新的日誌記錄功能
            print(f"\n[DEBUG] 🔍 詳細診斷 - 記錄頁面內容到日誌文件...")
            page_info = log_page_content(driver, "WebDriverWait完成後")

            if page_info:
                print(f"[DEBUG] 頁面信息已記錄到日誌文件: {current_log_file}")
                print(f"[DEBUG] 快速摘要:")
                print(f"  - URL: {page_info['url']}")
                print(f"  - 標題: {page_info['title']}")
                print(f"  - 包含目標訂單: {page_info['has_target_order']}")
                print(f"  - 包含 E48B: {page_info['has_e48b']}")
                print(f"  - 表格數量: {page_info['table_count']}")
                print(f"  - 表格行數: {page_info['row_count']}")
                print(f"  - 編輯按鈕數量: {page_info['edit_button_count']}")
                print(f"  - E48B 元素數量: {page_info['e48b_element_count']}")
            else:
                print(f"[ERROR] 無法記錄頁面內容")

        except Exception as e:
            print(f"[ERROR] WebDriverWait 等待失敗: {e}")

        print(f"[INFO] 動態內容等待完成，開始搜尋訂單...")

        # 🎯 確保我們在 iframe 內進行搜尋
        print(f"[INFO] 🎯 確保在 iframe 內搜尋...")
        try:
            # 檢查是否有 iframe - 使用雙層切換邏輯
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            if iframes:
                print(f"[INFO] 檢測到 {len(iframes)} 個 iframe，執行雙層切換")

                # 第一層：切換到訂單清單 iframe
                driver.switch_to.frame(0)
                print(f"[INFO] ✅ 已切換到第一層 iframe (訂單清單)")

                # 檢查第一層中是否有第二層 iframe
                second_iframes = driver.find_elements(By.TAG_NAME, "iframe")
                if second_iframes:
                    # 第二層：切換到編輯彈窗 iframe
                    driver.switch_to.frame(0)
                    print(f"[INFO] ✅ 已切換到第二層 iframe (編輯彈窗)")
                else:
                    print(f"[INFO] 第一層 iframe 中未檢測到第二層 iframe")
            else:
                print(f"[INFO] 未檢測到 iframe，在主頁面搜尋")
        except Exception as e:
            print(f"[WARN] 切換到 iframe 時發生錯誤: {e}")
            # 嘗試回到主頁面
            try:
                driver.switch_to.default_content()
            except:
                pass

        # 獲取當前頁面信息
        current_url = driver.current_url
        page_title = driver.title
        print(f"[INFO] 當前頁面: {current_url}")
        print(f"[INFO] 頁面標題: {page_title}")

        # 策略 1: 直接尋找包含訂單號的表格行
        print(f"[INFO] 🔍 搜尋包含訂單號 {order_id} 的表格行...")

        # 更全面的選擇器
        row_selectors = [
            f"//tr[contains(., '{order_id}')]",
            f"//tbody//tr[contains(., '{order_id}')]",
            f"//table//tr[contains(., '{order_id}')]",
            f"//tr[td[contains(text(), '{order_id}')]]",
            f"//tr[td[contains(., '{order_id}')]]",
            f"//tr[descendant::*[contains(text(), '{order_id}')]]",
            f"//*[contains(text(), '{order_id}')]/ancestor::tr",
            f"//div[contains(., '{order_id}')]/ancestor::tr"
        ]

        target_row = None
        for i, row_selector in enumerate(row_selectors, 1):
            try:
                print(f"[DEBUG] 嘗試選擇器 {i}: {row_selector}")
                rows = driver.find_elements(By.XPATH, row_selector)
                print(f"[DEBUG] 找到 {len(rows)} 個匹配的行")

                if rows:
                    # 檢查每一行是否真的包含完整的訂單號
                    for row in rows:
                        row_text = row.text
                        print(f"[DEBUG] 檢查行內容: {row_text[:100]}...")
                        if order_id in row_text:
                            target_row = row
                            print(f"[INFO] ✅ 找到包含訂單 {order_id} 的表格行")
                            print(f"[INFO] 行內容預覽: {row_text[:200]}...")
                            break

                    if target_row:
                        break

            except Exception as e:
                print(f"[DEBUG] 選擇器 {row_selector} 失敗: {e}")
                continue

        if not target_row:
            print(f"[ERROR] ❌ 找不到包含訂單 {order_id} 的表格行")

            # 嘗試獲取頁面上所有的表格行來調試
            try:
                all_rows = driver.find_elements(By.XPATH, "//tr")
                print(f"[DEBUG] 頁面總共有 {len(all_rows)} 個表格行")

                # 顯示所有行的內容來調試（增加顯示數量）
                print(f"[DEBUG] 顯示所有表格行內容以供調試：")
                for i, row in enumerate(all_rows[:20]):  # 顯示前20行
                    try:
                        row_text = row.text.strip()
                        if row_text:
                            print(f"[DEBUG] 行 {i+1}: {row_text}")
                        else:
                            print(f"[DEBUG] 行 {i+1}: (空行)")
                    except Exception as e:
                        print(f"[DEBUG] 行 {i+1}: 無法讀取 - {e}")

                # 額外檢查：搜尋頁面中是否包含訂單號（不限於表格）
                print(f"[DEBUG] 檢查整個頁面是否包含訂單號 {order_id}...")
                page_source = driver.page_source
                if order_id in page_source:
                    print(f"[INFO] ✅ 頁面原始碼中包含訂單號 {order_id}")

                    # 🔍 實現類似 Ctrl+F 的搜尋機制
                    print(f"[INFO] 🔍 使用 Ctrl+F 類似的搜尋機制...")

                    # 策略 1: 使用 JavaScript 搜尋文字並高亮
                    try:
                        # 使用 JavaScript 在頁面中搜尋文字
                        js_search_script = f"""
                        // 搜尋包含訂單號的所有元素
                        var walker = document.createTreeWalker(
                            document.body,
                            NodeFilter.SHOW_TEXT,
                            null,
                            false
                        );

                        var foundElements = [];
                        var node;
                        while (node = walker.nextNode()) {{
                            if (node.textContent.includes('{order_id}')) {{
                                foundElements.push(node.parentElement);
                            }}
                        }}

                        return foundElements.map(el => {{
                            return {{
                                tagName: el.tagName,
                                text: el.textContent.substring(0, 200),
                                className: el.className,
                                id: el.id
                            }};
                        }});
                        """

                        found_elements = driver.execute_script(js_search_script)
                        print(f"[INFO] JavaScript 搜尋找到 {len(found_elements)} 個包含訂單號的元素")

                        for i, elem_info in enumerate(found_elements[:5]):
                            print(f"[DEBUG] JS元素 {i+1}: {elem_info['tagName']} - {elem_info['text'][:100]}...")

                        # 如果找到元素，嘗試定位到編輯按鈕
                        if found_elements:
                            print(f"[INFO] 🎯 嘗試在找到的元素附近尋找編輯按鈕...")

                            # 使用 JavaScript 尋找編輯按鈕
                            js_find_edit_script = f"""
                            var walker = document.createTreeWalker(
                                document.body,
                                NodeFilter.SHOW_TEXT,
                                null,
                                false
                            );

                            var orderElements = [];
                            var node;
                            while (node = walker.nextNode()) {{
                                if (node.textContent.includes('{order_id}')) {{
                                    orderElements.push(node.parentElement);
                                }}
                            }}

                            // 在每個包含訂單號的元素附近尋找編輯按鈕
                            var editButtons = [];
                            orderElements.forEach(function(orderEl) {{
                                // 向上尋找表格行
                                var row = orderEl.closest('tr');
                                if (row) {{
                                    // 在行中尋找編輯相關的元素
                                    var editElements = row.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
                                    editElements.forEach(function(el) {{
                                        var text = (el.textContent || el.value || '').toLowerCase();
                                        if (text.includes('編輯') || text.includes('edit')) {{
                                            editButtons.push({{
                                                element: el,
                                                text: el.textContent || el.value,
                                                tagName: el.tagName,
                                                className: el.className,
                                                id: el.id
                                            }});
                                        }}
                                    }});
                                }}
                            }});

                            return editButtons.map(btn => {{
                                return {{
                                    text: btn.text,
                                    tagName: btn.tagName,
                                    className: btn.className,
                                    id: btn.id
                                }};
                            }});
                            """

                            edit_buttons = driver.execute_script(js_find_edit_script)
                            print(f"[INFO] 找到 {len(edit_buttons)} 個可能的編輯按鈕")

                            for i, btn_info in enumerate(edit_buttons):
                                print(f"[DEBUG] 編輯按鈕 {i+1}: {btn_info['tagName']} - '{btn_info['text']}'")

                            # 如果找到編輯按鈕，嘗試點擊第一個
                            if edit_buttons:
                                try:
                                    click_script = f"""
                                    var walker = document.createTreeWalker(
                                        document.body,
                                        NodeFilter.SHOW_TEXT,
                                        null,
                                        false
                                    );

                                    var node;
                                    while (node = walker.nextNode()) {{
                                        if (node.textContent.includes('{order_id}')) {{
                                            var row = node.parentElement.closest('tr');
                                            if (row) {{
                                                var editElements = row.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
                                                for (var i = 0; i < editElements.length; i++) {{
                                                    var el = editElements[i];
                                                    var text = (el.textContent || el.value || '').toLowerCase();
                                                    if (text.includes('編輯') || text.includes('edit')) {{
                                                        el.click();
                                                        return true;
                                                    }}
                                                }}
                                            }}
                                        }}
                                    }}
                                    return false;
                                    """

                                    clicked = driver.execute_script(click_script)
                                    if clicked:
                                        print(f"[INFO] ✅ 成功使用 JavaScript 點擊編輯按鈕!")
                                        time.sleep(2)
                                        return True
                                    else:
                                        print(f"[WARN] JavaScript 點擊失敗")

                                except Exception as e:
                                    print(f"[WARN] JavaScript 點擊編輯按鈕失敗: {e}")

                    except Exception as e:
                        print(f"[WARN] JavaScript 搜尋失敗: {e}")

                    # 嘗試用更廣泛的選擇器
                    broader_selectors = [
                        f"//*[contains(text(), '{order_id}')]",
                        f"//div[contains(., '{order_id}')]",
                        f"//span[contains(., '{order_id}')]",
                        f"//td[contains(., '{order_id}')]"
                    ]

                    for selector in broader_selectors:
                        try:
                            elements = driver.find_elements(By.XPATH, selector)
                            if elements:
                                print(f"[INFO] 使用選擇器 {selector} 找到 {len(elements)} 個元素")
                                for elem in elements[:3]:  # 顯示前3個
                                    print(f"[DEBUG] 元素內容: {elem.text[:100]}...")
                        except Exception as e:
                            print(f"[DEBUG] 選擇器 {selector} 失敗: {e}")
                else:
                    print(f"[ERROR] ❌ 頁面原始碼中不包含訂單號 {order_id}")
                    print(f"[INFO] 這可能表示：")
                    print(f"  1. 頁面內容尚未完全載入")
                    print(f"  2. 需要手動刷新頁面")
                    print(f"  3. 訂單號格式不匹配")
                    print(f"  4. 當前不在正確的頁面")

                    # 顯示當前頁面的一些內容來調試
                    print(f"[DEBUG] 當前頁面內容預覽（前500字符）:")
                    print(f"{page_source[:500]}...")

            except Exception as e:
                print(f"[DEBUG] 獲取所有行失敗: {e}")

            return False

        # 在找到的行中尋找編輯按鈕
        print(f"[INFO] � 在找到的行中搜尋編輯按鈕...")

        edit_selectors_in_row = [
            ".//a[contains(text(), '編輯')]",
            ".//button[contains(text(), '編輯')]",
            ".//input[@value='編輯']",
            ".//a[contains(@class, 'edit')]",
            ".//button[contains(@class, 'edit')]",
            ".//input[contains(@class, 'edit')]",
            ".//a",  # 最後嘗試任何連結
            ".//button",  # 最後嘗試任何按鈕
            ".//input[@type='button']",  # 嘗試按鈕類型的 input
            ".//input[@type='submit']"   # 嘗試提交類型的 input
        ]

        for i, edit_selector in enumerate(edit_selectors_in_row, 1):
            try:
                print(f"[DEBUG] 嘗試編輯選擇器 {i}: {edit_selector}")
                edit_elements = target_row.find_elements(By.XPATH, edit_selector)
                print(f"[DEBUG] 找到 {len(edit_elements)} 個候選編輯元素")

                for j, edit_element in enumerate(edit_elements):
                    try:
                        # 獲取元素信息
                        element_text = edit_element.text.strip()
                        element_value = edit_element.get_attribute('value') or ''
                        element_class = edit_element.get_attribute('class') or ''
                        element_tag = edit_element.tag_name

                        print(f"[DEBUG] 元素 {j+1}: tag={element_tag}, text='{element_text}', value='{element_value}', class='{element_class}'")

                        # 檢查元素是否可見且可點擊
                        if not (edit_element.is_displayed() and edit_element.is_enabled()):
                            print(f"[DEBUG] 元素 {j+1} 不可見或不可點擊")
                            continue

                        # 如果是通用選擇器，檢查文字內容
                        if edit_selector in [".//a", ".//button", ".//input[@type='button']", ".//input[@type='submit']"]:
                            combined_text = f"{element_text} {element_value}".strip()
                            if "編輯" not in combined_text:
                                print(f"[DEBUG] 元素 {j+1} 不包含'編輯'文字: '{combined_text}'")
                                continue

                        # 點擊編輯按鈕
                        print(f"[INFO] 🎯 嘗試點擊編輯元素: {element_tag} - '{element_text or element_value}'")
                        edit_element.click()
                        print(f"[INFO] ✅ 成功點擊編輯按鈕!")
                        print(f"[INFO] 按鈕詳情: {element_tag}[text='{element_text}', value='{element_value}']")
                        logger.info(f"🎯 成功點擊編輯按鈕: {element_tag}[text='{element_text}', value='{element_value}']")

                        # 🔍 立即檢測點擊後的 DOM 變化 - 增強版本
                        print(f"[INFO] 🔍 檢測點擊編輯按鈕後的 DOM 變化...")
                        logger.info(f"🔍 檢測點擊編輯按鈕後的 DOM 變化...")

                        # 🚀 使用增強的檢測功能
                        dialog_detection_result = monitor_dialog_opening()

                        if dialog_detection_result:
                            print(f"[INFO] ✅ 編輯彈窗檢測成功")
                            logger.info(f"✅ 編輯彈窗檢測成功")

                            # 保存檢測結果到全域變數供後續使用
                            last_dialog_detection = dialog_detection_result
                        else:
                            print(f"[WARN] ⚠️ 編輯彈窗檢測未完全成功，但繼續執行")
                            logger.warning(f"⚠️ 編輯彈窗檢測未完全成功，但繼續執行")

                        # 等待頁面跳轉
                        time.sleep(2)
                        return True

                    except Exception as e:
                        print(f"[DEBUG] 點擊元素 {j+1} 失敗: {e}")
                        continue

            except Exception as e:
                print(f"[DEBUG] 編輯選擇器 {edit_selector} 失敗: {e}")
                continue

        print(f"[ERROR] ❌ 在訂單 {order_id} 的行中找不到可點擊的編輯按鈕")

        # 顯示該行的所有可點擊元素來調試
        try:
            all_clickable = target_row.find_elements(By.XPATH, ".//a | .//button | .//input[@type='button'] | .//input[@type='submit']")
            print(f"[DEBUG] 該行共有 {len(all_clickable)} 個可點擊元素:")
            for i, elem in enumerate(all_clickable[:5]):  # 只顯示前5個
                try:
                    print(f"[DEBUG] 元素 {i+1}: {elem.tag_name} - text='{elem.text}' value='{elem.get_attribute('value') or ''}' class='{elem.get_attribute('class') or ''}'")
                except:
                    continue
        except Exception as e:
            print(f"[DEBUG] 獲取可點擊元素失敗: {e}")

        return False

    except Exception as e:
        print(f"[ERROR] 尋找編輯按鈕失敗: {e}")
        logger.error(f"尋找編輯按鈕失敗: {e}")
        return False

def wait_for_user_ready_confirmation():
    """等待用戶確認準備完成(在驗證碼輸入後)"""
    try:
        import tkinter as tk
        import tkinter.messagebox as msgbox

        # 創建確認對話框
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)

        response = msgbox.askyesno(
            "確認準備完成",
            "請確認您已經：\n\n"
            "✅ 輸入了驗證碼\n"
            "✅ 檢查了所有信息正確\n"
            "✅ 準備好讓程式自動點擊送出\n\n"
            "點擊「是」程式將在觸發時間自動送出\n"
            "點擊「否」取消本次搶單",
            parent=root
        )

        root.destroy()

        if response:
            print("[INFO] ✅ 用戶確認準備完成，程式將在觸發時間自動送出")
            return True
        else:
            print("[INFO] ❌ 用戶取消操作")
            return False

    except Exception as e:
        print(f"[ERROR] 用戶確認失敗: {e}")
        return False

def execute_precise_submit(task):
    """根據 RTT 精確計算並執行送出"""
    global driver

    try:
        # 獲取觸發時間
        trigger_time_str = task.get('trigger_time', '09:30:00.001')
        print(f"[INFO] 目標觸發時間: {trigger_time_str}")

        # 計算 RTT 補償（如果之前沒有計算過）
        rtt_adjustment = 0
        try:
            model_name = task.get('model', 'A')
            avg_rtt, rtts, timestamps = get_avg_rtt(model_name)
            if avg_rtt > 0:
                rtt_adjustment = avg_rtt / 1000.0  # 轉換為秒
                print(f"[INFO] RTT 補償: {avg_rtt:.2f}ms ({rtt_adjustment:.3f}s)")
        except Exception as e:
            print(f"[WARN] RTT 計算失敗: {e}")

        # 解析觸發時間
        from datetime import datetime, time as dt_time, timedelta
        trigger_time = datetime.strptime(trigger_time_str, "%H:%M:%S.%f").time()

        # 調整觸發時間（減去 RTT）
        if rtt_adjustment > 0:
            trigger_datetime = datetime.combine(datetime.now().date(), trigger_time)
            adjusted_datetime = trigger_datetime - timedelta(seconds=rtt_adjustment)
            adjusted_trigger_time = adjusted_datetime.time()
            print(f"[INFO] 調整後觸發時間: {adjusted_trigger_time.strftime('%H:%M:%S.%f')[:-3]}")
        else:
            adjusted_trigger_time = trigger_time

        # 等待精確的觸發時間
        print(f"[INFO] ⏰ 等待精確觸發時間...")
        while True:
            current_time = datetime.now().time()
            if current_time >= adjusted_trigger_time:
                print(f"[INFO] 🚀 觸發時間到達，立即送出!")
                break
            time.sleep(0.001)  # 1毫秒精度

        # 立即點擊送出按鈕
        return click_submit_button()

    except Exception as e:
        print(f"[ERROR] 精確送出失敗: {e}")
        return False

def show_verification_reminder_gui(detection_result=None):
    """GUI#09 - 驗證碼輸入提醒"""
    global driver, current_execution_mode, gui_operation_completed

    try:
        print("[INFO] 🔐 顯示驗證碼輸入提醒 GUI...")

        # 檢測當前定位狀態（如果沒有傳入檢測結果）
        if detection_result is None:
            detection_result = enhanced_dialog_button_detection()

        # 創建提醒視窗
        import tkinter as tk
        from tkinter import ttk

        # 創建根窗口（如果不存在）
        try:
            root = tk._default_root
            if root is None:
                root = tk.Tk()
                root.withdraw()  # 隱藏主窗口
        except:
            root = tk.Tk()
            root.withdraw()  # 隱藏主窗口

        reminder_window = tk.Toplevel(root)
        reminder_window.title("GUI#09 - 驗證碼輸入提醒")
        reminder_window.geometry("600x500")
        reminder_window.resizable(False, False)

        # 設置視窗置頂
        reminder_window.attributes('-topmost', True)
        reminder_window.focus_force()

        # 主框架
        main_frame = ttk.Frame(reminder_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 標題
        title_label = tk.Label(main_frame, text="🔐 請在修改進廠確認單中輸入驗證碼",
                              font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 操作說明
        instructions = [
            "📋 操作說明：",
            "1. 在彈出的修改進廠確認單中找到驗證碼輸入框",
            "2. 手動輸入驗證碼",
            "3. 確認所有資料正確",
            "4. 選擇下方按鈕完成準備",
            "",
            "⏰ 請在觸發時間前 5 分鐘內完成驗證碼輸入"
        ]

        for instruction in instructions:
            label = tk.Label(main_frame, text=instruction, font=("Arial", 10), anchor="w")
            label.pack(fill=tk.X, pady=2)

        # 狀態欄框架
        status_frame = tk.LabelFrame(main_frame, text="📊 系統檢測狀態", font=("Arial", 10, "bold"))
        status_frame.pack(fill=tk.X, pady=(20, 10))

        # 生成狀態信息
        if detection_result:
            submit_count = len(detection_result.get('submit_buttons', []))
            cancel_count = len(detection_result.get('cancel_buttons', []))
            other_count = len(detection_result.get('other_buttons', []))

            # 檢測狀態
            if submit_count > 0 and cancel_count > 0:
                location_status = "✅ 修改進廠確認單"
                detection_status = f"✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ✅ 找到取消按鈕"
            elif submit_count > 0:
                location_status = "⚠️ 修改進廠確認單 (部分載入)"
                detection_status = f"✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ❌ 未找到取消按鈕"
            else:
                location_status = "❌ 尚未進入修改進廠確認單"
                detection_status = "⏳ 等待編輯彈窗開啟..."

            # 元素統計 - 修正版本（解決用戶反饋的統計問題）
            # 注意：這裡的統計可能不準確，已列入 issue list 待優化
            total_buttons = submit_count + cancel_count + other_count
            element_stats = f"按鈕 {total_buttons} 個 | 送出 {submit_count} 個 | 取消 {cancel_count} 個"
        else:
            location_status = "❌ 檢測失敗"
            detection_status = "❌ 無法檢測頁面狀態"
            element_stats = "檢測失敗"

        # 狀態標籤
        status_labels = [
            f"📍 定位狀態: {location_status}",
            f"🔍 檢測結果: {detection_status}",
            f"📊 元素統計: {element_stats}"
        ]

        for status_text in status_labels:
            status_label = tk.Label(status_frame, text=status_text, font=("Arial", 9), anchor="w")
            status_label.pack(fill=tk.X, pady=2, padx=10)

        # 結果變量
        result = {'mode': None, 'confirmed': False}

        # 按鈕點擊處理
        def on_normal_submit():
            global current_execution_mode, gui_operation_completed
            result['mode'] = 'normal'
            result['confirmed'] = True
            current_execution_mode = 'normal'  # 設定全局變量
            gui_operation_completed = True     # 標記操作完成
            print("[INFO] 🎯 用戶選擇正式送單模式")
            reminder_window.destroy()

        def on_test_submit():
            global current_execution_mode, gui_operation_completed
            result['mode'] = 'test'
            result['confirmed'] = True
            current_execution_mode = 'test'  # 設定全局變量
            gui_operation_completed = True   # 標記操作完成
            print("[INFO] 🧪 用戶選擇測試模式")
            reminder_window.destroy()

        # 按鈕框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 按鈕
        normal_button = tk.Button(button_frame, text="已輸入驗證碼-正式送單",
                                 command=on_normal_submit, font=("Arial", 11, "bold"),
                                 bg="#4CAF50", fg="white", padx=20, pady=10)
        normal_button.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill=tk.X)

        test_button = tk.Button(button_frame, text="已輸入驗證碼-模擬送單",
                               command=on_test_submit, font=("Arial", 11, "bold"),
                               bg="#FF9800", fg="white", padx=20, pady=10)
        test_button.pack(side=tk.RIGHT, padx=(10, 0), expand=True, fill=tk.X)

        # 等待用戶選擇
        reminder_window.wait_window()

        if result['confirmed']:
            print(f"[INFO] ✅ 用戶選擇執行模式: {result['mode']}")
            return result
        else:
            print("[INFO] ❌ 用戶取消操作")
            return None

    except Exception as e:
        print(f"[ERROR] 驗證碼提醒 GUI 失敗: {e}")
        return None

def handle_captcha_input():
    """處理驗證碼輸入 - 改進版本"""
    global driver

    try:
        print("[INFO] 開始處理驗證碼輸入...")

        # 等待編輯頁面完全載入
        time.sleep(2)

        # 檢查是否有驗證碼圖片
        captcha_image_found = False
        captcha_image_selectors = [
            "img[src*='captcha']",
            "img[src*='verify']",
            "img[alt*='驗證']",
            "img[alt*='驗證碼']"
        ]

        for img_selector in captcha_image_selectors:
            try:
                captcha_images = driver.find_elements(By.CSS_SELECTOR, img_selector)
                if captcha_images:
                    captcha_image_found = True
                    print(f"[INFO] ✅ 找到驗證碼圖片: {img_selector}")
                    break
            except Exception:
                continue

        if not captcha_image_found:
            print("[WARN] 未找到驗證碼圖片，可能不需要驗證碼")

        # 檢查是否需要先點擊「確認取得驗證碼」按鈕
        refresh_button_selectors = [
            "button:contains('確認取得驗證碼')",
            "button:contains('重新產生')",
            "a:contains('重新產生')"
        ]

        for refresh_selector in refresh_button_selectors:
            try:
                if ":contains(" in refresh_selector:
                    text_content = refresh_selector.split("'")[1]
                    xpath = f"//button[contains(text(), '{text_content}')]"
                    refresh_buttons = driver.find_elements(By.XPATH, xpath)
                else:
                    refresh_buttons = driver.find_elements(By.CSS_SELECTOR, refresh_selector)

                if refresh_buttons:
                    refresh_button = refresh_buttons[0]
                    if refresh_button.is_displayed() and refresh_button.is_enabled():
                        print(f"[INFO] 點擊驗證碼刷新按鈕: {refresh_button.text}")
                        refresh_button.click()
                        time.sleep(1)  # 等待驗證碼重新載入
                        break
            except Exception:
                continue

        # 彈出對話框讓用戶輸入驗證碼
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)  # 確保對話框在最上層

        captcha_code = simpledialog.askstring(
            "驗證碼輸入",
            "請查看瀏覽器中的驗證碼圖片並輸入驗證碼:\n\n注意：驗證碼通常為4-5位數字或字母",
            parent=root
        )

        root.destroy()

        if captcha_code and captcha_code.strip():
            captcha_code = captcha_code.strip()
            print(f"[INFO] 用戶輸入驗證碼: {captcha_code}")

            # 尋找驗證碼輸入框 - 改進的選擇器
            captcha_selectors = [
                "input[name*='captcha']",
                "input[name*='verify']",
                "input[id*='captcha']",
                "input[id*='verify']",
                "input[placeholder*='驗證']",
                "input[placeholder*='驗證碼']",
                "input[type='text'][maxlength='4']",
                "input[type='text'][maxlength='5']",
                "input[type='text']"  # 最後嘗試所有文字輸入框
            ]

            for selector in captcha_selectors:
                try:
                    captcha_inputs = driver.find_elements(By.CSS_SELECTOR, selector)
                    for captcha_input in captcha_inputs:
                        if captcha_input.is_displayed() and captcha_input.is_enabled():
                            # 🎯 改進驗證碼輸入方式
                            try:
                                # 方法1: 等待元素可互動
                                WebDriverWait(driver, 3).until(EC.element_to_be_clickable(captcha_input))

                                # 方法2: 使用 JavaScript 清空並輸入
                                driver.execute_script("arguments[0].focus();", captcha_input)
                                driver.execute_script("arguments[0].value = '';", captcha_input)
                                time.sleep(0.2)  # 短暫等待
                                driver.execute_script("arguments[0].value = arguments[1];", captcha_input, captcha_code)

                                # 方法3: 觸發相關事件
                                driver.execute_script("""
                                    arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                                    arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                                """, captcha_input)

                                print(f"[INFO] ✅ 成功輸入驗證碼到: {selector}")
                                print(f"[INFO] 輸入的驗證碼: {captcha_code}")
                                logger.info(f"🎯 成功輸入驗證碼: {captcha_code}")

                                # 🎯 獲取驗證碼輸入後的事件日誌
                                print("[INFO] 📊 獲取驗證碼輸入後的事件日誌...")
                                event_log = get_browser_event_log()
                                if event_log:
                                    # 尋找驗證碼輸入相關的事件
                                    captcha_events = [e for e in event_log if
                                                    e.get('type') in ['input', 'keydown'] and
                                                    captcha_code in str(e.get('target', {}).get('value', ''))]
                                    if captcha_events:
                                        print(f"[INFO] 🎯 檢測到 {len(captcha_events)} 個驗證碼輸入事件")
                                        logger.info(f"🎯 檢測到 {len(captcha_events)} 個驗證碼輸入事件")

                                return captcha_code

                            except Exception as e:
                                print(f"[WARN] JavaScript 輸入失敗，嘗試傳統方式: {e}")
                                logger.warning(f"🎯 JavaScript 輸入失敗: {e}")
                                # 備用方法: 傳統輸入
                                try:
                                    captcha_input.clear()
                                    captcha_input.send_keys(captcha_code)
                                    print(f"[INFO] ✅ 傳統方式輸入驗證碼成功: {selector}")
                                    return captcha_code
                                except Exception as e2:
                                    print(f"[ERROR] 驗證碼輸入完全失敗: {e2}")
                                    logger.error(f"🎯 驗證碼輸入完全失敗: {e2}")
                                    continue
                except Exception as e:
                    print(f"[DEBUG] 嘗試驗證碼輸入框 {selector} 失敗: {e}")
                    continue

            print("[ERROR] 找不到可用的驗證碼輸入框")
            return None
        else:
            print("[WARN] 用戶取消驗證碼輸入或輸入為空")
            return None

    except Exception as e:
        print(f"[ERROR] 驗證碼處理失敗: {e}")
        logger.error(f"驗證碼處理失敗: {e}")
        return None

def setup_browser_event_monitoring():
    """🎯 設置瀏覽器事件監控 - 立即行動版本"""
    global driver

    try:
        print("[INFO] 🎯 設置瀏覽器事件監控...")
        logger.info("🎯 設置瀏覽器事件監控...")

        # JavaScript 事件監控腳本
        monitoring_script = """
        // 創建事件監控系統
        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];

        // 監控所有點擊事件 - 增強版本
        document.addEventListener('click', function(e) {
            var eventInfo = {
                type: 'click',
                timestamp: new Date().toISOString(),
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    textContent: (e.target.textContent || '').substring(0, 100),
                    value: e.target.value || '',
                    type: e.target.type || '',
                    name: e.target.name || '',
                    title: e.target.title || '',
                    alt: e.target.alt || '',
                    href: e.target.href || '',
                    onclick: (e.target.onclick || '').toString().substring(0, 200)
                },
                coordinates: {
                    clientX: e.clientX,
                    clientY: e.clientY,
                    pageX: e.pageX,
                    pageY: e.pageY,
                    screenX: e.screenX,
                    screenY: e.screenY
                },
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                },
                element_rect: (function() {
                    try {
                        var rect = e.target.getBoundingClientRect();
                        return {
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height,
                            right: rect.right,
                            bottom: rect.bottom
                        };
                    } catch(err) {
                        return null;
                    }
                })()
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Enhanced Click Event:', eventInfo);
        }, true);

        // 監控所有鍵盤輸入 - 增強版本
        document.addEventListener('keydown', function(e) {
            var eventInfo = {
                type: 'keydown',
                timestamp: new Date().toISOString(),
                key: e.key,
                code: e.code,
                keyCode: e.keyCode,
                which: e.which,
                altKey: e.altKey,
                ctrlKey: e.ctrlKey,
                shiftKey: e.shiftKey,
                metaKey: e.metaKey,
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    type: e.target.type || '',
                    name: e.target.name || '',
                    placeholder: e.target.placeholder || '',
                    value: (e.target.value || '').substring(0, 50),
                    maxLength: e.target.maxLength || '',
                    required: e.target.required || false
                },
                element_rect: (function() {
                    try {
                        var rect = e.target.getBoundingClientRect();
                        return {
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height
                        };
                    } catch(err) {
                        return null;
                    }
                })()
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Enhanced Key Event:', eventInfo);
        }, true);

        // 監控輸入框值變化
        document.addEventListener('input', function(e) {
            var eventInfo = {
                type: 'input',
                timestamp: new Date().toISOString(),
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    type: e.target.type || '',
                    value: (e.target.value || '').substring(0, 50)
                }
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Input Event:', eventInfo);
        }, true);

        // 監控表單提交
        document.addEventListener('submit', function(e) {
            var eventInfo = {
                type: 'submit',
                timestamp: new Date().toISOString(),
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    action: e.target.action || ''
                }
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Submit Event:', eventInfo);
        }, true);

        // 監控 DOM 變化
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    var eventInfo = {
                        type: 'dom_change',
                        timestamp: new Date().toISOString(),
                        mutation_type: mutation.type,
                        added_nodes: mutation.addedNodes.length,
                        target: {
                            tagName: mutation.target.tagName,
                            id: mutation.target.id || '',
                            className: mutation.target.className || ''
                        }
                    };
                    window.AGES_EVENT_LOG.push(eventInfo);
                    console.log('AGES DOM Change:', eventInfo);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true
        });

        console.log('AGES Event Monitoring System Initialized');
        return true;
        """

        # 執行監控腳本
        driver.execute_script(monitoring_script)
        print("[INFO] ✅ 瀏覽器事件監控已啟動")
        logger.info("✅ 瀏覽器事件監控已啟動")
        return True

    except Exception as e:
        print(f"[ERROR] 設置瀏覽器事件監控失敗: {e}")
        logger.error(f"設置瀏覽器事件監控失敗: {e}")
        return False

def get_browser_event_log():
    """📊 獲取瀏覽器事件日誌"""
    global driver

    try:
        # 獲取事件日誌
        event_log = driver.execute_script("return window.AGES_EVENT_LOG || [];")

        if event_log:
            print(f"[INFO] 📊 獲取到 {len(event_log)} 個瀏覽器事件")
            logger.info(f"📊 獲取到 {len(event_log)} 個瀏覽器事件")

            # 記錄最近的事件
            recent_events = event_log[-10:]  # 最近10個事件
            for i, event in enumerate(recent_events):
                print(f"[INFO] 📋 事件 {len(event_log)-len(recent_events)+i+1}: {event['type']} - {event.get('target', {}).get('tagName', 'N/A')}")
                logger.info(f"📋 事件詳情: {event}")

        return event_log

    except Exception as e:
        print(f"[ERROR] 獲取瀏覽器事件日誌失敗: {e}")
        logger.error(f"獲取瀏覽器事件日誌失敗: {e}")
        return []

def wait_for_dialog_content_loaded():
    """🎯 等待編輯彈窗內容完全載入"""
    global driver

    try:
        print("[INFO] 🎯 等待編輯彈窗內容載入...")
        logger.info("🎯 等待編輯彈窗內容載入...")

        max_wait_time = 15  # 最多等待15秒
        wait_interval = 1   # 每1秒檢查一次

        for attempt in range(int(max_wait_time / wait_interval)):
            try:
                # 檢測彈窗內容
                current_text = driver.execute_script("return document.body.innerText || '';")

                # 檢查是否包含編輯相關內容
                content_indicators = [
                    '驗證碼',
                    '送出',
                    '確認',
                    '提交',
                    '儲存',
                    'submit',
                    'confirm',
                    'save',
                    '表單',
                    '輸入',
                    'input',
                    'form'
                ]

                found_indicators = [indicator for indicator in content_indicators if indicator in current_text]

                print(f"[INFO] 🔍 載入檢查 {attempt+1}: 內容長度={len(current_text)}, 找到指標={found_indicators}")
                logger.info(f"🔍 載入檢查 {attempt+1}: 內容長度={len(current_text)}, 找到指標={found_indicators}")

                # 如果找到編輯相關內容，認為載入完成
                if found_indicators:
                    print(f"[INFO] ✅ 編輯彈窗內容載入完成，找到指標: {found_indicators}")
                    logger.info(f"✅ 編輯彈窗內容載入完成，找到指標: {found_indicators}")
                    return True

                # 檢查內容是否有變化（表示還在載入中）
                if attempt > 0:
                    # 如果內容長度穩定且包含編輯標題，可能載入完成
                    if '編輯進廠確認單' in current_text and len(current_text) > 100:
                        print(f"[INFO] ✅ 編輯彈窗基本內容已載入")
                        logger.info(f"✅ 編輯彈窗基本內容已載入")
                        return True

                time.sleep(wait_interval)

            except Exception as e:
                print(f"[DEBUG] 載入檢查 {attempt+1} 失敗: {e}")
                time.sleep(wait_interval)

        print(f"[WARN] ⚠️ 編輯彈窗內容載入超時")
        logger.warning(f"⚠️ 編輯彈窗內容載入超時")
        return False

    except Exception as e:
        print(f"[ERROR] 等待編輯彈窗內容載入失敗: {e}")
        logger.error(f"等待編輯彈窗內容載入失敗: {e}")
        return False

def monitor_dialog_opening():
    """🔍 監控編輯彈窗打開過程 - 增強版本，包含iframe層級檢測"""
    global driver

    try:
        print("[INFO] 🔍 開始監控編輯彈窗打開過程...")
        logger.info("🔍 開始監控編輯彈窗打開過程...")

        # 🎯 終端狀態顯示
        print(f"[STATUS] 🔍 定位狀態：檢測彈窗打開")
        print(f"[STATUS] 📍 當前位置：主頁面")
        print(f"[STATUS] 🔄 檢測進度：開始iframe掃描...")

        # 📸 截圖保存當前狀態
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            screenshot_path = f"screenshots/dialog_opening_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            driver.save_screenshot(screenshot_path)
            print(f"[INFO] 📸 彈窗打開截圖: {screenshot_path}")
            logger.info(f"📸 彈窗打開截圖: {screenshot_path}")
        except Exception as e:
            print(f"[WARN] 截圖失敗: {e}")

        # 🔍 iframe層級檢測和狀態顯示
        print(f"[STATUS] 🔍 定位狀態：檢測iframe結構")
        iframe_detection_result = detect_iframe_structure()

        if iframe_detection_result:
            print(f"[STATUS] ✅ 定位狀態：發現iframe結構")
            print(f"[STATUS] 📍 當前位置：iframe層級 {iframe_detection_result.get('level', 'unknown')}")
            logger.info(f"✅ 發現iframe結構: {iframe_detection_result}")
        else:
            print(f"[STATUS] ❌ 定位狀態：未發現iframe結構")
            logger.warning("❌ 未發現iframe結構")

        # 🔍 等待彈窗完全載入 - 增加等待時間
        max_wait_time = 10  # 最多等待10秒
        print(f"[STATUS] ⏳ 定位狀態：等待彈窗載入 (最多{max_wait_time}秒)")
        wait_interval = 0.5  # 每500毫秒檢查一次

        for attempt in range(int(max_wait_time / wait_interval)):
            try:
                # 檢測彈窗是否出現
                dialog_appeared = False

                # 方法1: 檢測 jQuery UI Dialog
                ui_dialogs = driver.find_elements(By.CSS_SELECTOR, ".ui-dialog")
                if ui_dialogs:
                    dialog_appeared = True
                    print(f"[INFO] ✅ 檢測到 {len(ui_dialogs)} 個 jQuery UI Dialog")
                    logger.info(f"✅ 檢測到 {len(ui_dialogs)} 個 jQuery UI Dialog")

                # 方法2: 檢測 role='dialog'
                role_dialogs = driver.find_elements(By.CSS_SELECTOR, "[role='dialog']")
                if role_dialogs:
                    dialog_appeared = True
                    print(f"[INFO] ✅ 檢測到 {len(role_dialogs)} 個 role='dialog' 元素")
                    logger.info(f"✅ 檢測到 {len(role_dialogs)} 個 role='dialog' 元素")

                # 方法3: 檢測彈窗標題
                title_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '編輯進廠確認單')]")
                if title_elements:
                    dialog_appeared = True
                    print(f"[INFO] ✅ 檢測到編輯彈窗標題")
                    logger.info(f"✅ 檢測到編輯彈窗標題")

                if dialog_appeared:
                    print(f"[INFO] 🎯 彈窗已出現，等待內容完全載入...")

                    # 🎯 等待編輯彈窗內容完全載入
                    content_loaded = wait_for_dialog_content_loaded()
                    if content_loaded:
                        print(f"[INFO] ✅ 編輯彈窗內容已完全載入")
                        logger.info(f"✅ 編輯彈窗內容已完全載入")
                    else:
                        print(f"[WARN] ⚠️ 編輯彈窗內容載入可能不完整")
                        logger.warning(f"⚠️ 編輯彈窗內容載入可能不完整")

                    break

                time.sleep(wait_interval)

            except Exception as e:
                print(f"[DEBUG] 彈窗檢測嘗試 {attempt+1} 失敗: {e}")

        # 🔍 詳細檢測彈窗內容
        print("[INFO] 🔍 詳細檢測彈窗內容...")

        # 記錄點擊後的完整頁面狀態
        try:
            current_source = driver.page_source
            current_text = driver.execute_script("return document.body.innerText || document.body.textContent || '';")

            print(f"[INFO] 📊 彈窗打開後頁面狀態:")
            print(f"  - page_source 長度: {len(current_source)}")
            print(f"  - innerText 長度: {len(current_text)}")

            logger.info(f"📊 彈窗打開後頁面狀態: page_source={len(current_source)}, innerText={len(current_text)}")

            # 檢測關鍵內容
            has_captcha_text = any(keyword in current_text for keyword in ['驗證碼', '驗證', 'captcha', 'verify'])
            has_submit_text = '送出' in current_text
            has_cancel_text = '取消' in current_text
            has_close_text = 'Close' in current_text

            print(f"[INFO] 🔍 關鍵內容檢測:")
            print(f"  - 包含驗證碼相關文字: {has_captcha_text}")
            print(f"  - 包含'送出'文字: {has_submit_text}")
            print(f"  - 包含'取消'文字: {has_cancel_text}")
            print(f"  - 包含'Close'文字: {has_close_text}")

            logger.info(f"🔍 關鍵內容檢測: 驗證碼={has_captcha_text}, 送出={has_submit_text}, 取消={has_cancel_text}, Close={has_close_text}")

            # 檢測表單元素
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            text_inputs = [inp for inp in all_inputs if inp.get_attribute("type") in ["text", None]]
            all_buttons = driver.find_elements(By.TAG_NAME, "button")

            print(f"[INFO] 📋 表單元素統計:")
            print(f"  - 總 input 元素: {len(all_inputs)}")
            print(f"  - 文字輸入框: {len(text_inputs)}")
            print(f"  - 總 button 元素: {len(all_buttons)}")

            logger.info(f"📋 表單元素統計: input={len(all_inputs)}, text_input={len(text_inputs)}, button={len(all_buttons)}")

            # 🚀 立即執行增強的按鈕檢測
            print("[INFO] 🚀 執行增強的按鈕檢測...")
            button_detection = enhanced_dialog_button_detection()

            # 🎯 自動觸發 GUI#09 驗證碼提醒（如果檢測到編輯彈窗）
            if dialog_appeared:
                print("[INFO] 🎯 檢測到編輯彈窗，自動觸發驗證碼提醒 GUI...")
                logger.info("🎯 檢測到編輯彈窗，自動觸發驗證碼提醒 GUI...")

                try:
                    import threading
                    # 在新線程中顯示 GUI，避免阻塞檢測流程
                    def show_gui_thread():
                        try:
                            # 傳遞按鈕檢測結果，如果沒有就傳 None
                            show_verification_reminder_gui(button_detection)
                        except Exception as e:
                            print(f"[ERROR] GUI 線程執行失敗: {e}")
                            logger.error(f"GUI 線程執行失敗: {e}")

                    gui_thread = threading.Thread(target=show_gui_thread, daemon=True)
                    gui_thread.start()
                    print("[INFO] ✅ 驗證碼提醒 GUI 已在後台線程啟動")
                    logger.info("✅ 驗證碼提醒 GUI 已在後台線程啟動")

                except Exception as e:
                    print(f"[ERROR] 啟動 GUI 線程失敗: {e}")
                    logger.error(f"啟動 GUI 線程失敗: {e}")
            else:
                print("[INFO] ⚠️ 未檢測到編輯彈窗，不觸發 GUI#09")
                logger.info("⚠️ 未檢測到編輯彈窗，不觸發 GUI#09")

            return {
                'dialog_appeared': dialog_appeared,
                'has_captcha_text': has_captcha_text,
                'has_submit_text': has_submit_text,
                'has_cancel_text': has_cancel_text,
                'has_close_text': has_close_text,
                'input_count': len(all_inputs),
                'text_input_count': len(text_inputs),
                'button_count': len(all_buttons),
                'button_detection': button_detection,
                'screenshot_path': screenshot_path if 'screenshot_path' in locals() else None,
                'page_source_length': len(current_source),
                'inner_text_length': len(current_text)
            }

        except Exception as e:
            print(f"[ERROR] 彈窗內容檢測失敗: {e}")
            logger.error(f"彈窗內容檢測失敗: {e}")
            return None

    except Exception as e:
        print(f"[ERROR] 監控彈窗打開失敗: {e}")
        logger.error(f"監控彈窗打開失敗: {e}")
        return None

def enhanced_dialog_button_detection():
    """🚀 增強的編輯彈窗按鈕檢測 - 立即行動版本 (支援 iframe)"""
    global driver

    try:
        print("[INFO] 🔍 開始增強的編輯彈窗按鈕檢測...")
        logger.info("🔍 開始增強的編輯彈窗按鈕檢測...")

        # 📸 第一步：截圖保存當前狀態
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            screenshot_path = f"screenshots/dialog_detection_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            driver.save_screenshot(screenshot_path)
            print(f"[INFO] 📸 截圖已保存: {screenshot_path}")
            logger.info(f"📸 截圖已保存: {screenshot_path}")
        except Exception as e:
            print(f"[WARN] 截圖失敗: {e}")
            logger.warning(f"截圖失敗: {e}")

        # 🎯 第二步：檢測並切換到 iframe (關鍵修復)
        print("[INFO] 🎯 檢測 iframe 架構...")
        logger.info("🎯 檢測 iframe 架構...")

        # 檢測所有 iframe
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print(f"[INFO] 📊 檢測到 {len(iframes)} 個 iframe")
        logger.info(f"📊 檢測到 {len(iframes)} 個 iframe")

        # 嘗試切換到每個 iframe 並檢測內容
        iframe_detection_results = []

        for i, iframe in enumerate(iframes):
            try:
                print(f"[INFO] 🔍 檢測 iframe {i+1}...")

                # 切換到 iframe
                driver.switch_to.frame(iframe)

                # 檢測 iframe 內的內容
                iframe_content = driver.execute_script("return document.body.innerText || document.body.textContent || '';")
                iframe_buttons = driver.find_elements(By.TAG_NAME, "button")
                iframe_inputs = driver.find_elements(By.TAG_NAME, "input")

                # 檢測是否包含編輯相關內容
                has_edit_content = any(keyword in iframe_content for keyword in ['編輯進廠確認單', '送出', '取消', '驗證碼'])

                iframe_info = {
                    'index': i,
                    'content_length': len(iframe_content),
                    'button_count': len(iframe_buttons),
                    'input_count': len(iframe_inputs),
                    'has_edit_content': has_edit_content,
                    'content_preview': iframe_content[:200] if iframe_content else ''
                }

                iframe_detection_results.append(iframe_info)

                print(f"[INFO] 📋 iframe {i+1} 資訊:")
                print(f"  - 內容長度: {iframe_info['content_length']}")
                print(f"  - 按鈕數量: {iframe_info['button_count']}")
                print(f"  - 輸入框數量: {iframe_info['input_count']}")
                print(f"  - 包含編輯內容: {iframe_info['has_edit_content']}")
                if iframe_info['content_preview']:
                    print(f"  - 內容預覽: {iframe_info['content_preview'][:100]}...")

                logger.info(f"📋 iframe {i+1} 資訊: {iframe_info}")

                # 如果找到編輯相關內容，在此 iframe 中執行檢測
                if has_edit_content:
                    print(f"[INFO] 🎯 在 iframe {i+1} 中找到編輯內容，執行詳細檢測...")
                    logger.info(f"🎯 在 iframe {i+1} 中找到編輯內容，執行詳細檢測...")

                    # 在此 iframe 中執行按鈕檢測
                    detection_result = detect_buttons_in_current_context()
                    detection_result['active_iframe'] = i
                    detection_result['iframe_info'] = iframe_info

                    # 切換回主頁面
                    driver.switch_to.default_content()
                    return detection_result

                # 切換回主頁面
                driver.switch_to.default_content()

            except Exception as e:
                print(f"[DEBUG] 檢測 iframe {i+1} 失敗: {e}")
                logger.warning(f"檢測 iframe {i+1} 失敗: {e}")
                # 確保切換回主頁面
                try:
                    driver.switch_to.default_content()
                except:
                    pass

        # 如果沒有在 iframe 中找到編輯內容，在主頁面檢測
        print("[INFO] 🔍 在主頁面執行檢測...")
        logger.info("🔍 在主頁面執行檢測...")
        detection_result = detect_buttons_in_current_context()
        detection_result['active_iframe'] = None
        detection_result['iframe_detection_results'] = iframe_detection_results

        return detection_result

    except Exception as e:
        print(f"[ERROR] 增強按鈕檢測失敗: {e}")
        logger.error(f"增強按鈕檢測失敗: {e}")
        # 確保切換回主頁面
        try:
            driver.switch_to.default_content()
        except:
            pass
        return None

def detect_buttons_in_current_context():
    """在當前上下文中檢測按鈕 (可能是主頁面或 iframe)"""
    global driver

    try:
        # 🔍 第三步：全面掃描彈窗的各個區域
        print("[INFO] 🔍 掃描 jQuery UI Dialog 結構...")

        # A. 檢測 jQuery UI Dialog 容器
        dialog_containers = []
        dialog_selectors = [
            ".ui-dialog",
            "[role='dialog']",
            ".ui-dialog-content",
            ".modal",
            ".dialog",
            ".popup"
        ]

        for selector in dialog_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    dialog_containers.extend(elements)
                    print(f"[INFO] ✅ 找到 {len(elements)} 個 {selector} 容器")
                    logger.info(f"✅ 找到 {len(elements)} 個 {selector} 容器")
            except Exception as e:
                print(f"[DEBUG] 檢測 {selector} 失敗: {e}")

        # B. 檢測彈窗標題
        title_found = False
        title_selectors = [
            ".ui-dialog-title",
            ".ui-dialog-titlebar",
            ".modal-title",
            ".dialog-title"
        ]

        for selector in title_selectors:
            try:
                titles = driver.find_elements(By.CSS_SELECTOR, selector)
                for title in titles:
                    title_text = title.text.strip()
                    if "編輯進廠確認單" in title_text:
                        print(f"[INFO] ✅ 找到彈窗標題: {title_text}")
                        logger.info(f"✅ 找到彈窗標題: {title_text}")
                        title_found = True
                        break
            except Exception as e:
                print(f"[DEBUG] 檢測標題 {selector} 失敗: {e}")

        # C. 專門檢測 jQuery UI Dialog 按鈕區域
        print("[INFO] 🎯 專門檢測 jQuery UI Dialog 按鈕區域...")
        button_containers = []
        button_container_selectors = [
            ".ui-dialog-buttonpane",
            ".ui-dialog-buttonset",
            ".ui-dialog-buttons",
            ".modal-footer",
            ".dialog-footer",
            ".dialog-buttons"
        ]

        for selector in button_container_selectors:
            try:
                containers = driver.find_elements(By.CSS_SELECTOR, selector)
                if containers:
                    button_containers.extend(containers)
                    print(f"[INFO] ✅ 找到 {len(containers)} 個 {selector} 按鈕容器")
                    logger.info(f"✅ 找到 {len(containers)} 個 {selector} 按鈕容器")

                    # 檢測容器內的按鈕
                    for container in containers:
                        try:
                            container_text = container.text.strip()
                            print(f"[INFO] 📋 按鈕容器內容: {container_text}")
                            logger.info(f"📋 按鈕容器內容: {container_text}")
                        except Exception as e:
                            print(f"[DEBUG] 讀取容器內容失敗: {e}")
            except Exception as e:
                print(f"[DEBUG] 檢測按鈕容器 {selector} 失敗: {e}")

        # D. 全面檢測所有可點擊元素 (擴大搜尋範圍)
        print("[INFO] 🔍 全面檢測所有可點擊元素...")
        all_buttons = []
        button_selectors = [
            "button",
            "input[type='submit']",
            "input[type='button']",
            "input[type='reset']",
            "a[role='button']",
            ".ui-button",
            ".btn",
            "a[href='#']",
            "a[onclick]",
            "div[onclick]",
            "span[onclick]",
            "[role='button']"
        ]

        for selector in button_selectors:
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                all_buttons.extend(buttons)
                print(f"[INFO] 📊 找到 {len(buttons)} 個 {selector} 元素")
                logger.info(f"📊 找到 {len(buttons)} 個 {selector} 元素")
            except Exception as e:
                print(f"[DEBUG] 檢測元素 {selector} 失敗: {e}")

        # 額外檢測：尋找包含特定文字的所有元素
        print("[INFO] 🎯 搜尋包含送出相關文字的所有元素...")
        submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok', '確定']

        for keyword in submit_keywords:
            try:
                # 使用 XPath 搜尋包含特定文字的所有元素
                xpath_selectors = [
                    f"//*[contains(text(), '{keyword}')]",
                    f"//*[@value='{keyword}']",
                    f"//*[@title='{keyword}']",
                    f"//*[@alt='{keyword}']"
                ]

                for xpath in xpath_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, xpath)
                        if elements:
                            all_buttons.extend(elements)
                            print(f"[INFO] 🎯 找到 {len(elements)} 個包含 '{keyword}' 的元素 (XPath: {xpath})")
                            logger.info(f"🎯 找到 {len(elements)} 個包含 '{keyword}' 的元素")
                    except Exception as e:
                        print(f"[DEBUG] XPath 搜尋失敗 {xpath}: {e}")

            except Exception as e:
                print(f"[DEBUG] 搜尋關鍵字 '{keyword}' 失敗: {e}")

        # 去除重複元素
        unique_buttons = []
        seen_elements = set()
        for button in all_buttons:
            try:
                element_id = id(button)
                if element_id not in seen_elements:
                    unique_buttons.append(button)
                    seen_elements.add(element_id)
            except:
                unique_buttons.append(button)

        all_buttons = unique_buttons
        print(f"[INFO] 📊 去重後總計 {len(all_buttons)} 個可點擊元素")

        # E. 詳細分析每個按鈕
        print(f"[INFO] 📋 詳細分析 {len(all_buttons)} 個按鈕...")
        submit_buttons = []
        cancel_buttons = []
        other_buttons = []

        for i, button in enumerate(all_buttons):
            try:
                # 獲取按鈕信息
                button_text = button.text.strip()
                button_value = button.get_attribute('value') or ''
                button_class = button.get_attribute('class') or ''
                button_id = button.get_attribute('id') or ''
                button_tag = button.tag_name
                button_type = button.get_attribute('type') or ''

                # 檢查可見性
                is_displayed = button.is_displayed()
                is_enabled = button.is_enabled()

                # 獲取更多屬性信息
                button_title = button.get_attribute('title') or ''
                button_alt = button.get_attribute('alt') or ''
                button_onclick = button.get_attribute('onclick') or ''

                # 組合所有可能包含按鈕功能信息的文字
                all_text = f"{button_text} {button_value} {button_title} {button_alt} {button_onclick}".strip().lower()

                print(f"[INFO] 🔍 元素 {i+1}: {button_tag}[type='{button_type}'] text='{button_text}' value='{button_value}' class='{button_class}' id='{button_id}' title='{button_title}' visible={is_displayed} enabled={is_enabled}")
                logger.info(f"🔍 元素 {i+1}: {button_tag}[type='{button_type}'] text='{button_text}' value='{button_value}' class='{button_class}' id='{button_id}' title='{button_title}' visible={is_displayed} enabled={is_enabled}")

                # 更精確的按鈕分類
                submit_keywords = ['送出', 'submit', '提交', '確認', '儲存', 'save', 'confirm', 'ok', '確定']
                cancel_keywords = ['取消', 'cancel', 'close', '關閉', '刪除', 'delete']

                is_submit = any(keyword in all_text for keyword in submit_keywords)
                is_cancel = any(keyword in all_text for keyword in cancel_keywords)

                # 特殊處理：如果同時包含取消和其他關鍵字，優先判斷為取消
                if '取消' in all_text and any(other in all_text for other in ['刪除']):
                    is_cancel = True
                    is_submit = False

                button_info = {
                    'element': button,
                    'text': button_text,
                    'value': button_value,
                    'class': button_class,
                    'id': button_id,
                    'tag': button_tag,
                    'type': button_type,
                    'title': button_title,
                    'alt': button_alt,
                    'onclick': button_onclick[:100] if button_onclick else '',  # 限制長度
                    'visible': is_displayed,
                    'enabled': is_enabled,
                    'all_text': all_text
                }

                if is_submit and not is_cancel:
                    submit_buttons.append(button_info)
                    print(f"[INFO] ✅ 識別為送出按鈕: '{button_text or button_value}' (匹配: {[k for k in submit_keywords if k in all_text]})")
                    logger.info(f"✅ 識別為送出按鈕: '{button_text or button_value}'")
                elif is_cancel:
                    cancel_buttons.append(button_info)
                    print(f"[INFO] ⚠️ 識別為取消按鈕: '{button_text or button_value}' (匹配: {[k for k in cancel_keywords if k in all_text]})")
                    logger.info(f"⚠️ 識別為取消按鈕: '{button_text or button_value}'")
                else:
                    other_buttons.append(button_info)
                    print(f"[INFO] ❓ 其他元素: '{button_text or button_value}'")
                    logger.info(f"❓ 其他元素: '{button_text or button_value}'")

            except Exception as e:
                print(f"[DEBUG] 分析按鈕 {i+1} 失敗: {e}")
                logger.warning(f"分析按鈕 {i+1} 失敗: {e}")

        # F. 總結檢測結果
        print(f"\n[INFO] 📊 按鈕檢測總結:")
        print(f"  - 送出按鈕: {len(submit_buttons)} 個")
        print(f"  - 取消按鈕: {len(cancel_buttons)} 個")
        print(f"  - 其他按鈕: {len(other_buttons)} 個")
        print(f"  - 總計: {len(all_buttons)} 個")

        logger.info(f"📊 按鈕檢測總結: 送出={len(submit_buttons)}, 取消={len(cancel_buttons)}, 其他={len(other_buttons)}, 總計={len(all_buttons)}")

        # G. 返回檢測結果
        return {
            'submit_buttons': submit_buttons,
            'cancel_buttons': cancel_buttons,
            'other_buttons': other_buttons,
            'dialog_containers': dialog_containers,
            'button_containers': button_containers,
            'title_found': title_found
        }

    except Exception as e:
        print(f"[ERROR] 增強按鈕檢測失敗: {e}")
        logger.error(f"增強按鈕檢測失敗: {e}")
        return None

def click_submit_button():
    """點擊送出按鈕 - 增強版本（支持模式選擇）"""
    global driver, current_execution_mode

    try:
        # 檢查執行模式
        if current_execution_mode == 'test':
            print("[INFO] 🧪 測試模式：將點擊取消按鈕而非送出按鈕")
            logger.info("🧪 測試模式：將點擊取消按鈕而非送出按鈕")
            return click_cancel_button()

        print("[INFO] 🎯 正式模式：開始尋找並點擊送出按鈕...")
        logger.info("🎯 正式模式：開始尋找並點擊送出按鈕...")

        # 🚀 使用增強的按鈕檢測
        detection_result = enhanced_dialog_button_detection()

        if not detection_result:
            print("[ERROR] 按鈕檢測失敗")
            return False

        submit_buttons = detection_result.get('submit_buttons', [])

        if not submit_buttons:
            print("[ERROR] ❌ 沒有找到送出按鈕")
            logger.error("❌ 沒有找到送出按鈕")

            # 顯示所有可用按鈕供調試
            all_buttons = detection_result.get('submit_buttons', []) + detection_result.get('cancel_buttons', []) + detection_result.get('other_buttons', [])
            print(f"[DEBUG] 可用的按鈕列表:")
            for i, btn_info in enumerate(all_buttons[:10]):  # 只顯示前10個
                print(f"  {i+1}. {btn_info['tag']} - '{btn_info['text'] or btn_info['value']}' (visible={btn_info['visible']}, enabled={btn_info['enabled']})")

            return False

        # 🎯 嘗試點擊第一個可用的送出按鈕
        for i, btn_info in enumerate(submit_buttons):
            try:
                button = btn_info['element']

                if not (btn_info['visible'] and btn_info['enabled']):
                    print(f"[WARN] 送出按鈕 {i+1} 不可點擊 (visible={btn_info['visible']}, enabled={btn_info['enabled']})")
                    continue

                print(f"[INFO] 🎯 嘗試點擊送出按鈕 {i+1}: '{btn_info['text'] or btn_info['value']}'")
                logger.info(f"🎯 嘗試點擊送出按鈕 {i+1}: '{btn_info['text'] or btn_info['value']}'")

                # 多種點擊方式
                try:
                    # 🎯 點擊前獲取事件日誌基準
                    pre_click_events = get_browser_event_log()
                    pre_click_count = len(pre_click_events)

                    # 方法1: 標準點擊
                    button.click()
                    print(f"[INFO] ✅ 成功點擊送出按鈕 (標準方式)")
                    logger.info(f"✅ 成功點擊送出按鈕 (標準方式)")

                    # 🎯 點擊後檢查事件日誌
                    time.sleep(0.5)  # 等待事件記錄
                    post_click_events = get_browser_event_log()
                    new_events = post_click_events[pre_click_count:]

                    if new_events:
                        print(f"[INFO] 📊 送出按鈕點擊產生了 {len(new_events)} 個新事件")
                        logger.info(f"📊 送出按鈕點擊產生了 {len(new_events)} 個新事件")

                        # 檢查是否有提交相關事件
                        submit_events = [e for e in new_events if e.get('type') in ['click', 'submit']]
                        if submit_events:
                            print(f"[INFO] 🎯 檢測到 {len(submit_events)} 個提交相關事件")
                            logger.info(f"🎯 檢測到 {len(submit_events)} 個提交相關事件")

                    return True
                except Exception as e1:
                    print(f"[WARN] 標準點擊失敗: {e1}")

                    try:
                        # 方法2: JavaScript 點擊
                        driver.execute_script("arguments[0].click();", button)
                        print(f"[INFO] ✅ 成功點擊送出按鈕 (JavaScript 方式)")
                        logger.info(f"✅ 成功點擊送出按鈕 (JavaScript 方式)")
                        return True
                    except Exception as e2:
                        print(f"[WARN] JavaScript 點擊失敗: {e2}")

                        try:
                            # 方法3: ActionChains 點擊
                            from selenium.webdriver.common.action_chains import ActionChains
                            ActionChains(driver).move_to_element(button).click().perform()
                            print(f"[INFO] ✅ 成功點擊送出按鈕 (ActionChains 方式)")
                            logger.info(f"✅ 成功點擊送出按鈕 (ActionChains 方式)")
                            return True
                        except Exception as e3:
                            print(f"[ERROR] 所有點擊方式都失敗: 標準={e1}, JS={e2}, ActionChains={e3}")
                            logger.error(f"所有點擊方式都失敗: 標準={e1}, JS={e2}, ActionChains={e3}")
                            continue

            except Exception as e:
                print(f"[ERROR] 點擊送出按鈕 {i+1} 失敗: {e}")
                logger.error(f"點擊送出按鈕 {i+1} 失敗: {e}")
                continue

        print("[ERROR] ❌ 所有送出按鈕都無法點擊")
        logger.error("❌ 所有送出按鈕都無法點擊")
        return False

    except Exception as e:
        print(f"[ERROR] 點擊送出按鈕失敗: {e}")
        logger.error(f"點擊送出按鈕失敗: {e}")
        return False

def click_cancel_button():
    """點擊取消按鈕 - 用於測試模式"""
    global driver

    try:
        print("[INFO] 🎯 開始尋找並點擊取消按鈕...")
        logger.info("🎯 開始尋找並點擊取消按鈕...")

        # 🚀 使用增強的按鈕檢測
        detection_result = enhanced_dialog_button_detection()

        if not detection_result:
            print("[ERROR] 按鈕檢測失敗")
            return False

        cancel_buttons = detection_result.get('cancel_buttons', [])

        if not cancel_buttons:
            print("[ERROR] ❌ 未找到取消按鈕")
            logger.error("❌ 未找到取消按鈕")
            return False

        print(f"[INFO] 🎯 找到 {len(cancel_buttons)} 個取消按鈕，開始嘗試點擊...")

        # 嘗試點擊每個取消按鈕
        for i, button_info in enumerate(cancel_buttons):
            try:
                button = button_info['element']
                button_text = button_info.get('text', 'N/A')
                print(f"[INFO] 🎯 嘗試點擊取消按鈕 {i+1}: {button_text}")

                # 檢查按鈕是否可點擊
                if not button.is_enabled():
                    print(f"[WARN] 取消按鈕 {i+1} 不可點擊，跳過")
                    continue

                # 嘗試多種點擊方式
                click_success = False

                # 方式 1: 標準點擊
                try:
                    button.click()
                    print(f"[INFO] ✅ 取消按鈕 {i+1} 點擊成功 (標準方式)")
                    logger.info(f"✅ 取消按鈕 {i+1} 點擊成功 (標準方式)")
                    click_success = True
                except Exception as e1:
                    print(f"[WARN] 標準點擊失敗: {e1}")

                    # 方式 2: JavaScript 點擊
                    try:
                        driver.execute_script("arguments[0].click();", button)
                        print(f"[INFO] ✅ 取消按鈕 {i+1} 點擊成功 (JS方式)")
                        logger.info(f"✅ 取消按鈕 {i+1} 點擊成功 (JS方式)")
                        click_success = True
                    except Exception as e2:
                        print(f"[WARN] JS點擊失敗: {e2}")

                        # 方式 3: ActionChains 點擊
                        try:
                            from selenium.webdriver.common.action_chains import ActionChains
                            actions = ActionChains(driver)
                            actions.move_to_element(button).click().perform()
                            print(f"[INFO] ✅ 取消按鈕 {i+1} 點擊成功 (ActionChains方式)")
                            logger.info(f"✅ 取消按鈕 {i+1} 點擊成功 (ActionChains方式)")
                            click_success = True
                        except Exception as e3:
                            print(f"[ERROR] 所有點擊方式都失敗: 標準={e1}, JS={e2}, ActionChains={e3}")
                            logger.error(f"所有點擊方式都失敗: 標準={e1}, JS={e2}, ActionChains={e3}")
                            continue

                if click_success:
                    print("[INFO] 🎉 取消按鈕點擊成功，模擬測試完成")
                    logger.info("🎉 取消按鈕點擊成功，模擬測試完成")
                    return True

            except Exception as e:
                print(f"[ERROR] 點擊取消按鈕 {i+1} 失敗: {e}")
                logger.error(f"點擊取消按鈕 {i+1} 失敗: {e}")
                continue

        print("[ERROR] ❌ 所有取消按鈕都無法點擊")
        logger.error("❌ 所有取消按鈕都無法點擊")
        return False

    except Exception as e:
        print(f"[ERROR] 點擊取消按鈕失敗: {e}")
        logger.error(f"點擊取消按鈕失敗: {e}")
        return False

def log_order_result(task, result):
    """記錄訂單結果"""
    try:
        order_id = task.get('order_id', 'N/A')

        if result.get('is_success') is True:
            print(f"[INFO] 🎉 訂單 {order_id} 可能搶單成功！")
        elif result.get('is_success') is False:
            print(f"[INFO] ❌ 訂單 {order_id} 搶單失敗: {result.get('message', 'N/A')}")
        else:
            print(f"[INFO] ❓ 訂單 {order_id} 結果不明確: {result.get('message', 'N/A')}")

        # 記錄到 CSV
        results_path = "results/results.csv"
        os.makedirs("results", exist_ok=True)

        # 準備結果數據
        result_data = {
            'timestamp': result.get('timestamp', datetime.now().strftime('%Y%m%d_%H%M%S')),
            'order_id': order_id,
            'order_date': task.get('order_date', 'N/A'),
            'browser': task.get('browser', 'N/A'),
            'trigger_time': task.get('trigger_time', 'N/A'),
            'is_success': result.get('is_success'),
            'result_type': result.get('result_type', 'N/A'),
            'message': result.get('message', 'N/A'),
            'screenshot_path': result.get('screenshot_path', 'N/A')
        }

        # 寫入 CSV
        file_exists = os.path.exists(results_path)
        with open(results_path, 'a', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=result_data.keys())
            if not file_exists:
                writer.writeheader()
            writer.writerow(result_data)

        print(f"[INFO] 結果已記錄到: {results_path}")

    except Exception as e:
        print(f"[ERROR] 記錄結果失敗: {e}")
        logger.error(f"記錄結果失敗: {e}")

if __name__ == "__main__":
    try:
        app = GrabberGUI()
        app.run()
    except Exception as e:
        print(f"[ERROR] 主程式執行失敗: {str(e)}")
        safe_exit()