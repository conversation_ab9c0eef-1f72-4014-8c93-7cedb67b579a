2025-06-27 19:52:26,754 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250627_195226.log
2025-06-27 19:52:52,795 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-27 19:52:52,795 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-27 19:52:52,863 - DEBUG - chromedriver not found in PATH
2025-06-27 19:52:52,863 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:52:52,863 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-27 19:52:52,864 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-27 19:52:52,864 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-27 19:52:52,864 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-27 19:52:52,864 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:52:52,867 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 10216 using 0 to output -3
2025-06-27 19:52:53,390 - DEBUG - POST http://localhost:56643/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-27 19:52:53,391 - DEBUG - Starting new HTTP connection (1): localhost:56643
2025-06-27 19:52:53,922 - DEBUG - http://localhost:56643 "POST /session HTTP/1.1" 200 0
2025-06-27 19:52:53,922 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir10216_287534513"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:56646"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"f12a4792854d46b91fc74e9c6ebca3ee"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:53,923 - DEBUG - Finished Request
2025-06-27 19:52:53,923 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-27 19:52:55,004 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:52:55,005 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:55,005 - DEBUG - Finished Request
2025-06-27 19:52:55,006 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:52:55,068 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:52:55,068 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:55,069 - DEBUG - Finished Request
2025-06-27 19:52:56,070 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:52:56,079 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:52:56,079 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:56,079 - DEBUG - Finished Request
2025-06-27 19:52:57,080 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:52:57,085 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:52:57,085 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:57,085 - DEBUG - Finished Request
2025-06-27 19:52:58,086 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:52:58,091 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:52:58,092 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:58,092 - DEBUG - Finished Request
2025-06-27 19:52:59,093 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:52:59,100 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:52:59,100 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:52:59,101 - DEBUG - Finished Request
2025-06-27 19:53:00,102 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:00,108 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:00,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:00,109 - DEBUG - Finished Request
2025-06-27 19:53:01,110 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:01,118 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:01,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:01,118 - DEBUG - Finished Request
2025-06-27 19:53:02,120 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:02,128 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:02,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:02,129 - DEBUG - Finished Request
2025-06-27 19:53:03,130 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:03,138 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:03,139 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:03,139 - DEBUG - Finished Request
2025-06-27 19:53:04,140 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:04,148 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:04,149 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:04,149 - DEBUG - Finished Request
2025-06-27 19:53:05,150 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:05,158 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:05,158 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:05,159 - DEBUG - Finished Request
2025-06-27 19:53:06,161 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:06,169 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:06,170 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:06,170 - DEBUG - Finished Request
2025-06-27 19:53:07,171 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:07,180 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:07,180 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:07,180 - DEBUG - Finished Request
2025-06-27 19:53:08,182 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:08,189 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:08,189 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:08,189 - DEBUG - Finished Request
2025-06-27 19:53:09,191 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:09,199 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:09,200 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:09,200 - DEBUG - Finished Request
2025-06-27 19:53:10,201 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:10,208 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:10,209 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:10,209 - DEBUG - Finished Request
2025-06-27 19:53:11,211 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:11,219 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:11,220 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:11,220 - DEBUG - Finished Request
2025-06-27 19:53:12,221 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:12,229 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:12,229 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:12,229 - DEBUG - Finished Request
2025-06-27 19:53:13,231 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:13,239 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:13,239 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:13,240 - DEBUG - Finished Request
2025-06-27 19:53:14,241 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:14,249 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:14,249 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:14,250 - DEBUG - Finished Request
2025-06-27 19:53:15,251 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:15,259 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:15,259 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:15,260 - DEBUG - Finished Request
2025-06-27 19:53:16,261 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:16,269 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:16,269 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:16,270 - DEBUG - Finished Request
2025-06-27 19:53:17,271 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:17,278 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:17,278 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:17,279 - DEBUG - Finished Request
2025-06-27 19:53:18,280 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:18,286 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:18,286 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:18,286 - DEBUG - Finished Request
2025-06-27 19:53:19,287 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:19,294 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:19,295 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:19,295 - DEBUG - Finished Request
2025-06-27 19:53:20,297 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:20,305 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:20,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:20,305 - DEBUG - Finished Request
2025-06-27 19:53:21,307 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:21,313 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:21,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:21,314 - DEBUG - Finished Request
2025-06-27 19:53:22,315 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:22,322 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:22,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:22,322 - DEBUG - Finished Request
2025-06-27 19:53:23,324 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:23,331 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:23,331 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:23,331 - DEBUG - Finished Request
2025-06-27 19:53:24,333 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:24,338 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:24,339 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:24,339 - DEBUG - Finished Request
2025-06-27 19:53:25,340 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:25,346 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:25,346 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:25,346 - DEBUG - Finished Request
2025-06-27 19:53:26,347 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:26,354 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:26,354 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:26,355 - DEBUG - Finished Request
2025-06-27 19:53:27,356 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:27,362 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:27,363 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:27,363 - DEBUG - Finished Request
2025-06-27 19:53:28,365 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:28,372 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:28,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:28,372 - DEBUG - Finished Request
2025-06-27 19:53:29,373 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:29,380 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:29,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:29,380 - DEBUG - Finished Request
2025-06-27 19:53:30,381 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:30,388 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:30,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:30,388 - DEBUG - Finished Request
2025-06-27 19:53:31,389 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:31,395 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:31,395 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:31,395 - DEBUG - Finished Request
2025-06-27 19:53:32,397 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:32,405 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:32,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:32,406 - DEBUG - Finished Request
2025-06-27 19:53:33,407 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:33,414 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:33,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:33,414 - DEBUG - Finished Request
2025-06-27 19:53:34,415 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:34,422 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:34,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:34,422 - DEBUG - Finished Request
2025-06-27 19:53:35,423 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:35,430 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:35,430 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:35,430 - DEBUG - Finished Request
2025-06-27 19:53:36,431 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:36,436 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:36,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:36,438 - DEBUG - Finished Request
2025-06-27 19:53:37,438 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:37,454 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:37,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:37,455 - DEBUG - Finished Request
2025-06-27 19:53:38,456 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:38,463 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:38,463 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:38,463 - DEBUG - Finished Request
2025-06-27 19:53:39,464 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:39,472 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:39,472 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:39,472 - DEBUG - Finished Request
2025-06-27 19:53:40,473 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:40,479 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:40,479 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:40,479 - DEBUG - Finished Request
2025-06-27 19:53:41,480 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:41,488 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:41,488 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:41,488 - DEBUG - Finished Request
2025-06-27 19:53:42,490 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:42,495 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:42,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:42,496 - DEBUG - Finished Request
2025-06-27 19:53:43,497 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:43,504 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:43,504 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:43,504 - DEBUG - Finished Request
2025-06-27 19:53:44,506 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:44,511 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:44,512 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:44,512 - DEBUG - Finished Request
2025-06-27 19:53:45,514 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:45,520 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:45,520 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:45,520 - DEBUG - Finished Request
2025-06-27 19:53:46,521 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:46,526 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:46,526 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:46,526 - DEBUG - Finished Request
2025-06-27 19:53:47,528 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:47,535 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:47,535 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:47,535 - DEBUG - Finished Request
2025-06-27 19:53:48,536 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:48,542 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:48,543 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:48,543 - DEBUG - Finished Request
2025-06-27 19:53:49,545 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:49,551 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:49,551 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:49,551 - DEBUG - Finished Request
2025-06-27 19:53:50,066 - INFO - 🎯 用戶點擊準備完成按鈕，開始詳細檢測...
2025-06-27 19:53:50,066 - INFO - 🔍 [用戶點擊準備完成] 開始記錄頁面內容...
2025-06-27 19:53:50,067 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:50,086 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:50,087 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,087 - DEBUG - Finished Request
2025-06-27 19:53:50,087 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/title {}
2025-06-27 19:53:50,095 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/title HTTP/1.1" 200 0
2025-06-27 19:53:50,095 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,095 - DEBUG - Finished Request
2025-06-27 19:53:50,096 - INFO - 🔍 [用戶點擊準備完成] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:53:50,096 - INFO - 🔍 [用戶點擊準備完成] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:53:50,096 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/source {}
2025-06-27 19:53:50,100 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/source HTTP/1.1" 200 0
2025-06-27 19:53:50,100 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'd7bO3126BRUPNcZ8aKG6V11sBUE17sACLXAFPiv1Rmzkony3_6sQ6prRXNknNL8Y_JJFVcypfcVVoHmVRLciIxuVZuq_BOxiXfHC0881bPs1:KXWTb9t8LIL_BEE9jV1z365TSVG-lYOOgoYddzJoumNSNVO9BGeUfjASTCykn7Is-VIvmdqWgBXmmwHRpQZA6IujjerdTA9bnJ4_2lisCO01' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:53:38\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;9564bebe9e554a88&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,101 - DEBUG - Finished Request
2025-06-27 19:53:50,101 - INFO - 🔍 [用戶點擊準備完成] page_source 長度: 8479
2025-06-27 19:53:50,102 - INFO - 🔍 [用戶點擊準備完成] page_source 包含 E48B: False
2025-06-27 19:53:50,102 - INFO - 🔍 [用戶點擊準備完成] page_source 包含目標訂單: False
2025-06-27 19:53:50,102 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:53:50,109 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:50,109 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:53:38\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,109 - DEBUG - Finished Request
2025-06-27 19:53:50,109 - INFO - 🔍 [用戶點擊準備完成] innerText 長度: 97
2025-06-27 19:53:50,109 - INFO - 🔍 [用戶點擊準備完成] innerText 包含 E48B: False
2025-06-27 19:53:50,110 - INFO - 🔍 [用戶點擊準備完成] innerText 包含目標訂單: False
2025-06-27 19:53:50,110 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:53:50,121 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,122 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,122 - DEBUG - Finished Request
2025-06-27 19:53:50,122 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:53:50,133 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,134 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,134 - DEBUG - Finished Request
2025-06-27 19:53:50,134 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個表格，0 個表格行
2025-06-27 19:53:50,135 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:53:50,145 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,146 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,146 - DEBUG - Finished Request
2025-06-27 19:53:50,147 - INFO - 🔍 [用戶點擊準備完成] 包含 'E48B' 的元素數量: 0
2025-06-27 19:53:50,147 - INFO - 🔍 [用戶點擊準備完成] innerText 前300字符:
2025-06-27 19:53:50,147 - INFO - 🔍 [用戶點擊準備完成]  環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:53:38

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-06-27 19:53:50,147 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:53:50,157 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,157 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,157 - DEBUG - Finished Request
2025-06-27 19:53:50,157 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個編輯按鈕
2025-06-27 19:53:50,157 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:53:50,168 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,169 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.96B8E6479BC65F940AB8CC30331774D0.d.80F5C33470B2CAE3BAC989052E74FC3F.e.36"}]} | headers=HTTPHeaderDict({'Content-Length': '128', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,169 - DEBUG - Finished Request
2025-06-27 19:53:50,169 - INFO - 🔍 [用戶點擊準備完成] 檢測到 1 個 iframe
2025-06-27 19:53:50,170 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.96B8E6479BC65F940AB8CC30331774D0.d.80F5C33470B2CAE3BAC989052E74FC3F.e.36'}, 'id']}
2025-06-27 19:53:50,178 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:50,178 - DEBUG - Remote response: status=200 | data={"value":"frameid"} | headers=HTTPHeaderDict({'Content-Length': '19', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,180 - DEBUG - Finished Request
2025-06-27 19:53:50,180 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.96B8E6479BC65F940AB8CC30331774D0.d.80F5C33470B2CAE3BAC989052E74FC3F.e.36'}, 'src']}
2025-06-27 19:53:50,187 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:50,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00"} | headers=HTTPHeaderDict({'Content-Length': '58', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,187 - DEBUG - Finished Request
2025-06-27 19:53:50,188 - INFO - 🔍 [用戶點擊準備完成] iframe 1: id='frameid', src='https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00'
2025-06-27 19:53:50,188 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/frame {'id': {'element-6066-11e4-a52e-4f735466cecf': 'f.96B8E6479BC65F940AB8CC30331774D0.d.80F5C33470B2CAE3BAC989052E74FC3F.e.36'}}
2025-06-27 19:53:50,209 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/frame HTTP/1.1" 200 0
2025-06-27 19:53:50,209 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,210 - DEBUG - Finished Request
2025-06-27 19:53:50,210 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:53:50,216 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:50,217 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t815.691\t76.89\t0\t453.919\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406230886\t(NEW)H4 2808 星期五(30%)\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406200782\tH4 2808義大遊樂\tKEP-2808\t專車清運\t1.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181141\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181137\t仁\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181136\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181135\t岡\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406181133\t(NEW)H9 5580星期五 只有一車(25%)\tKEJ-5580\t一般清運\t6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110721\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110719\t南\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110718\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110717\t南\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110715\t岡\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110714\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261022\t岡\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261021\t南\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210775\t岡\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210774\t仁\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210773\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190957\t(NEW)119星期五(50%)\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190956\t南\tKED-9671\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190955\tH2 119 義大\t119-BR\t專車清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190954\t南\t117-BR\t一般清運\t3.5\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190953\t仁\t119-BR\t一般清運\t3.6\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190949\t南\t119-BR\t一般清運\t3.6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190948\t仁\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190947\t岡\t120-BR\t一般清運\t3.2\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190945\t仁\t121-BR\t一般清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190944\t岡\t121-BR\t一般清運\t3.5\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190943\t仁\t129-BR\t一般清運\t4.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190940\t岡\t129-BR\t一般清運\t4.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190938\t(NEW)H72560星期五(有美生) (20%)\tKEP-2560\t一般清運\t4.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190936\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190934\t岡\t937-N6\t一般清運\t4.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190932\t南\t937-N6\t一般清運\t4.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190931\t南\tKEH-9278\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190929\t仁\tKEH-9278\t一般清運\t8.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190928\t岡\tKER-2807\t一般清運\t7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190698\t仁\tKER-2807\t一般清運\t7\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n顯示第 1 至 38 項結果，共 38 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '7514', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,217 - DEBUG - Finished Request
2025-06-27 19:53:50,217 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:53:50,225 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,225 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.129"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,225 - DEBUG - Finished Request
2025-06-27 19:53:50,226 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:53:50,233 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,233 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.131"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.170"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,234 - DEBUG - Finished Request
2025-06-27 19:53:50,234 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:53:50,243 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,244 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.78"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.172"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.239"}]} | headers=HTTPHeaderDict({'Content-Length': '8270', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,245 - DEBUG - Finished Request
2025-06-27 19:53:50,245 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:53:50,252 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:50,253 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.241"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.242"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.243"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.277"}]} | headers=HTTPHeaderDict({'Content-Length': '4495', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,253 - DEBUG - Finished Request
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成] iframe 1 內容:
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - 文字長度: 3955
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - 包含目標訂單: True
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - 包含 E48B: True
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - 表格數量: 2
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - 表格行數: 41
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - 編輯按鈕數量: 70
2025-06-27 19:53:50,254 - INFO - 🔍 [用戶點擊準備完成]   - E48B 元素數量: 38
2025-06-27 19:53:50,255 - INFO - 🔍 [用戶點擊準備完成]   - 內容前300字符: 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

6月
7月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	815.691	76.89	0	453.919
每日開放查詢時日1...
2025-06-27 19:53:50,255 - INFO - 🔍 [用戶點擊準備完成]   - 內容後300字符: ...		高南廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190929	仁	KEH-9278	一般清運	8.5	2025-07-04		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190928	岡	KER-2807	一般清運	7	2025-07-04		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190698	仁	KER-2807	一般清運	7	2025-07-04		仁武廠	0	一般	取消(刪除)
顯示第 1 至 38 項結果，共 38 項
上一頁
1
下一頁
2025-06-27 19:53:50,552 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:50,557 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:50,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:50,557 - DEBUG - Finished Request
2025-06-27 19:53:51,559 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:51,566 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:51,566 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:51,567 - DEBUG - Finished Request
2025-06-27 19:53:52,257 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,263 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,264 - DEBUG - Finished Request
2025-06-27 19:53:52,264 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/title {}
2025-06-27 19:53:52,269 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/title HTTP/1.1" 200 0
2025-06-27 19:53:52,269 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,270 - DEBUG - Finished Request
2025-06-27 19:53:52,271 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:53:52,279 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,280 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.129"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,280 - DEBUG - Finished Request
2025-06-27 19:53:52,281 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:53:52,289 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,289 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.131"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.170"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,290 - DEBUG - Finished Request
2025-06-27 19:53:52,290 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/source {}
2025-06-27 19:53:52,295 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/source HTTP/1.1" 200 0
2025-06-27 19:53:52,296 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 16.14px;\">\u003Chead>\u003Clink href=\"/Content/MainCss?v=zPHWubngoBLlh81X9bw_8aWFtUJfWgM1VR7izfwxpoQ1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DataTableCss?v=Z2hod0l-s4nUkCqEwOiqsErJ5wq6PmToPF0R4Rizr9g1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n\u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DataTableJs?v=PrL5mJgVEL8_qJD7wVZM5xxp6rEVZ9g1TBfDlvdUlWg1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'jALW1rx83SXKMFO-MZjDfE2166ss1b-tmKmaK7Iw8RpXY23zj5JvEXr14cQ4zYWQVDCqdFV2kmPlXdU1B4KYKawM6heR0RZRvhpUnvOOago1:UsteFvpkPHOBEgjWNV7mBoJJQ_CD1i51a-jbtxHcoDpyoJ7CNWv20mqmzaiMKV_SnMOqkigPlyheVcb9AHZRxT-0nARbZnRcj2_MI7UugDI1' };\n    \n    let lengthMenuList = [[10, 50, 100, 300], [10, 50, 100, 300]];\n\u003C/script>\n\n\n    \u003Cscript>\n        var UrlDeleteCleEntryForm = '/Frontend/CLE/DeleteCleEntryForm';\n        var Token = 'aEymg9dqVbAs6pq7WxBfKyIVL4IfVWFpMFcORxzZzHlsgMPFkVWljzNyZq-EOks_8DUF-KDDozyitvd7ZC--WECzQ270P8iObuSlcImILyk1:L5S0IohNGcrZOde92_rpbMeBQZp0c-1vMWd1aoTAKCbMFvv3blS4EO0WhlgrEgtGp8SMzp0aRBizxePgF0Zc-5io1hymJc_fYYPOqvHvGcY1';\n        var UrlExport1 = '/Frontend/CLE/DownloadCLE104Data';\n        var UrlExport = '/Frontend/CLE/PrintEntryForm';\n        var UrlEFDetailExport = '/Frontend/CLE/DownloadCLEDetailData';\n        var UrlLog = '/Frontend/CLE/LogFP';\n   \n        let actualWeightDays = 10;\n        console.log(UrlLog);\n        let addRouteOption = {\n            Title: '新增路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030A01',\n            BeforeClose: function () {\n\n            }\n        };\n\n        let editRouteOption = {\n            Title: '編輯路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030E00',\n            BeforeClose: function () {\n\n            }\n        };\n\n      $(function () {\n\n        //  var FPoptions = {\n        //      fonts: { extendedJsFonts: true },\n        //      excludes: {\n        //          cpuClass: true,\n        //          fonts: true,\n        //          fontsFlash: true,\n        //          doNotTrack: true,\n        //          webgl: true,\n        //          webglVendorAndRenderer: true,\n              \n                \n        //      }\n        //  }\n        //    var FP_token;\n\n        //    setTimeout(function () {\n        //        Fingerprint2.get(FPoptions, function (components) {\n        //            var values = components.map(function (component) { return component.value })\n        //            var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //            FP_token = murmur\n        //            LogData2(FP_token);\n        //        })\n        //    }, 500)\n\n\n        //    Fingerprint2.get(FPoptions, function (components) {\n        //        var values = components.map(function (component) { return component.value })\n        //        var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //        FP_token = murmur\n        //        LogData(FP_token);\n        //    });\n\n\n        });\n          var FP_token;\n\n          function getOrCreateLocalID() {\n              const key = 'local_fingerprint_id';\n              let id = localStorage.getItem(key);\n              if (!id) {\n                  id = 'id-' + Math.random().toString(36).substr(2, 9);\n                  localStorage.setItem(key, id);\n              }\n              return id;\n          }\n\n          window.addEventListener('load', function () {\n              setTimeout(function () {\n                  Fingerprint2.get(function (components) {\n                      // 過濾與排序\n                      const baseComponents = components\n                          .filter(c => !['userAgent', 'language', 'webdriver'].includes(c.key))\n                          .sort((a, b) => a.key.localeCompare(b.key));\n\n                      // 額外客製化參數\n                      const customComponents = [\n                          { key: 'timezoneOffset', value: new Date().getTimezoneOffset() },\n                          { key: 'localID', value: getOrCreateLocalID() },\n                          { key: 'cpuCores', value: navigator.hardwareConcurrency || 'unknown' },\n                          { key: 'pixelRatio', value: window.devicePixelRatio },\n                          { key: 'connectionType', value: (navigator.connection && navigator.connection.effectiveType) || 'unknown' }\n                      ];\n\n                      const allComponents = [...baseComponents, ...customComponents];\n\n\n                      const values = allComponents\n                          .sort((a, b) => a.key.localeCompare(b.key))\n                          .map(component => component.value);\n\n                      const rawString = values.join('###');\n\n\n                      const hash = Fingerprint2.x64hash128(rawString, 31);\n\n\n                      LogData(hash);\n\n                      console.log('Extended fingerprint hash:', hash);\n                      console.log('All components:', allComponents);\n                  });\n              }, 500);\n\n          });\n\n\n\n        function LogData(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n        function LogData2(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog2,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n\n\n    \u003C/script>\n    \u003Cscript src=\"/Scripts/fingerprint2.js\" type=\"text/javascript\">\u003C/script>\n    \u003Cscript src=\"/Scripts/View/Frontend/CLE/CLE1040Q00.js?2025062719534062\">\u003C/script>\n\n\n\n\n\n\n\u003C/head>\u003Cbody>\u003Cdiv class=\"container-fluid\">\n    \n\n\n\n\n\u003Cdiv class=\"mainTitle\">\n    \u003Ch2>進廠確認單管理\u003C/h2>\n\u003C/div>\n\u003Cdiv class=\"row\">\n    \u003Cdiv class=\"col-md-7 text-left\">\n        \n\n\n\u003Cform action=\"/Frontend/CLE/QueryEntryForm\" autocomplete=\"off\" id=\"QueryForm\" method=\"post\" novalidate=\"\">\u003Cinput name=\"__RequestVerificationToken\" type=\"hidden\" value=\"FZ82RI_3K05TdSDZndptMgp7QCJgYm9KMI1HiAuYeFrCfHhb90mkzQHj3b--pWOylIbB8KO05Yd-tkEokvTKytwI7DDEqD5sbyAeSxeogYc1\">    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryIncineratorId\">進廠別\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control\" id=\"EntryIncineratorId\" name=\"EntryIncineratorId\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"0\">調度中心\u003C/option>\n\u003Coption value=\"1\">高南廠\u003C/option>\n\u003Coption value=\"2\">岡山廠\u003C/option>\n\u003Coption value=\"3\">仁武廠\u003C/option>\n\u003Coption value=\"5\">路竹掩埋場\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"EntryNo\">進廠確認單號\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cinput class=\"form-control\" id=\"EntryNo\" name=\"EntryNo\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"FormStatus\">狀態\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control p-2\" id=\"FormStatus\" name=\"FormStatus\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"00\">暫存\u003C/option>\n\u003Coption value=\"01\">待審查\u003C/option>\n\u003Coption value=\"02\">未載運\u003C/option>\n\u003Coption value=\"03\">已載運-待清除確認\u003C/option>\n\u003Coption value=\"41\">已載運-檢核未通過\u003C/option>\n\u003Coption value=\"91\">取消\u003C/option>\n\u003Coption value=\"97\">審查退回\u003C/option>\n\u003Coption value=\"98\">退運\u003C/option>\n\u003Coption value=\"99\">已完成\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"CheckWeighFail\">檢核結果\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cselect class=\"form-control\" id=\"CheckWeighFail\" name=\"CheckWeighFail\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"1\">通過\u003C/option>\n\u003Coption value=\"2\">未通過\u003C/option>\n\u003Coption value=\"3\">未檢核\u003C/option>\n\u003C/select>\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"ReserveEntryDate_Start\">預計進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_Start\" name=\"ReserveEntryDate_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_End\" name=\"ReserveEntryDate_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryDateTime_Start\">實際進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_Start\" name=\"EntryDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_End\" name=\"EntryDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"\">報表日期起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_Start\" name=\"RptDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_End\" name=\"RptDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"text-center\">\n        \n\n        \u003Cbutton type=\"button\" id=\"btnQuery\" onclick=\"tableDraw(queryTable, 'QueryForm');tableDraw(queryQueryCleEntryWeight, '',_queryQueryCleEntryWeightConfig );\" class=\"btn btn-primary\">查詢\u003C/button>\n        \u003Cspan style=\"color:red\">請按查詢以顯示清單\u003C/span>\n        \n\n        \u003Cbr>\n                                \u003Cbutton type=\"button\" class=\"btn btn-warning\" onclick=\"addA1Option.Data = {entryFormType:'A1'};EditShowDialog(addA1Option)\">新增A1本市事廢\u003C/button>\n                        \u003Cbr>\n\n        \n                        \u003Cbutton type=\"button\" class=\"btn btn-dark\" onclick=\"addA3bOption.Data = {entryFormType:'A3b'};EditShowDialog(addA3bOption)\">新增A3b2050專案\u003C/button>\n                \n                    \u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"DownloadCLEDetailData();\">下載明細報表\u003C/button>\n            \u003Cbr>\n\n    \u003C/div>\n\u003C/form>\n    \u003C/div>\n    \u003Cdiv class=\"col-md-5 text-left\">\n\u003Cform action=\"/Frontend/CLE/QueryCleEntryWeight\" autocomplete=\"off\" id=\"QueryDocForm\" method=\"post\" novalidate=\"\" onsubmit=\"return false;\">            \u003Cdiv>\n                \u003Cselect id=\"QueryDate\" name=\"QueryDate\" onchange=\"getCleEntryWeight()\">\u003Coption value=\"2025/06/27\">6月\u003C/option>\n\u003Coption value=\"2025/07/27\">7月\u003C/option>\n\u003C/select>\u003Cspan>進廠量統計\u003C/span>\n                \n            \u003C/div>\n\u003C/form>        \u003Cdiv id=\"queryQueryCleEntryWeight_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryQueryCleEntryWeight\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">月核定量(A)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">日控量\u003Cbr>(七天後)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠量(B)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量(C)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">上月超量(D)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">剩餘進廠量\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>A1\u003C/td>\u003Ctd>1346.5\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>815.691\u003C/td>\u003Ctd>76.89\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>453.919\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryQueryCleEntryWeight_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryQueryCleEntryWeightConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": false,\n  \"lengthChange\": false,\n  \"ordering\": false,\n  \"paging\": false,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"get\",\n    \"url\": \"/Frontend/CLE/QueryCleEntryWeight\",\n    \"data\": ajaxParam\n  },\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 0,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"ConsentType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"月核定量(A)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ControlOpenWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"日控量\u003Cbr/>(七天後)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthEntryWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"實際進廠量(B)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量(C)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthOverWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"上月超量(D)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthRemainingWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"剩餘進廠量\",\n      \"visible\": true\n    }\n  ]\n};\nlet queryQueryCleEntryWeight = $('#queryQueryCleEntryWeight').DataTable(_queryQueryCleEntryWeightConfig);\n\u003C/script>\n        \u003Cdiv>\n            \u003Cul>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">每日開放查詢時日10:30~次日09:00\u003C/span>\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">欄位說明：\u003C/span>(單位：噸)\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">月核定量(A)：\u003C/span>因進廠管控措施機制，故月核可量為浮動數值\u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月實際進廠量(B)：\u003C/span>進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月預計進廠量(C)：\u003C/span>進廠確認單狀態為「未載運」、「已逾期」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">上月超量(D)：\u003C/span>上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月剩餘進廠量：\u003C/span>A-B-C-D\n                \u003C/li>\n                \n            \u003C/ul>\n        \u003C/div>\n    \u003C/div>\n\n\u003C/div>\n\n\n\n\n\u003Cscript>\n\n    function operateFormatter(data, type, row, meta) {\n        let result = \"\";\n        let todayDate = new Date(); //Today Date\n\n        if (row.IsLocked === true) {\n\n        }\n\n\n\n        //預計進廠日前一天可修改、刪除\n        //if (moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString())) {\n        if (\n            (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1\n            && row.FormStatus != '03'\n            && (row.FormStatus === '00' || row.FormStatus === '02' || row.FormStatus == '97')\n        ) {\n            //console.log((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")));\n            if (row.EntryFormType == \"A4a\") {\n                //02=審查完成\n                if (row.FormStatus !== '02') {\n                    result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"Edit41EOption.Data = {entryId:${data}};Edit41EOption.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(Edit41EOption)\">編輯\u003C/button>`;\n                }\n            }\n            else {\n                if (row.FormStatus === '00') {\n                    if (row.ReserveType != \"特殊\") {\n                        if (row.ReserveType != \"A3a\") {\n                            result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"EditE20Option.Data = {entryId:${data}};EditE20Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE20Option)\">編輯\u003C/button>`;\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n\n                    }\n                }\n                else {\n                    if (row.ReserveType != \"特殊\" && row.IsLocked === false) {\n                        if (row.ReserveType == \"優先\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE30Option.Data = {entryId:${data}};EditE30Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE30Option)\">編輯\u003C/button>`;\n                        }\n                        else if (row.EntryFormType == \"A3a\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                    }\n\n                }\n\n            }\n\n\n        }\n        else {\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02')) {\n\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('確定取消單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormCancel', { entryId: ${data}}, 'post', _Headers);}\">未進廠取消\u003C/button>`;\n            }\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\") \u003C= actualWeightDays)) {\n                if ((row.FormStatus == '03' || row.FormStatus == '41')) { ////20250220 關閉隔日可填寫實際重量\n                    // if ((row.FormStatus == '03' || row.FormStatus == '41') || (row.FormStatus == '02' && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02'))) {\n                    if (row.EntryFormType == \"A4a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"Edit41E10Option.Data = {entryId:${data}};Edit41E10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(Edit41E10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else if (row.EntryFormType == \"A3a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                }\n            }\n            \n            if (row.EntryFormType == \"A4a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1041R00')\">查看\u003C/button>`;\n            }\n            else if (row.EntryFormType == \"A3a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R10')\">查看\u003C/button>`;\n            }\n            else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R00')\">查看\u003C/button>`;\n            }\n\n\n\n        }\n\n        //if (row.CargoRoute) {\n        //    result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"editRouteOption.Data = {RouteId:${row.RouteId}};window.parent.EditShowDialog(editRouteOption)\">編輯路線\u003C/button>`;\n        //}\n        //else {\n        result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"addRouteOption.Data = {entryId:${row.EntryId}, ClearType:'${row.ClearType}',EntryFormType:'${row.EntryFormType}'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>`;\n        //}\n\n        if ((moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString()) || moment(row.ReserveEntryDate).isSame(todayDate.toLocaleDateString()))\n            && row.FormStatus === '02') {\n            //  && row.IsLocked === false) {\n            result += `\u003Cbutton type=\"button\" class='btn btn-secondary' onclick=\"ShowEntryForm(${data})\">列印\u003C/button>`;\n\n        }\n        if (row.EntryFormType == \"A1\")\n        {\n         }\n\n             return result;\n         }\n\n    function cancelFormatter(data, type, row, meta) {\n        let todayDate = new Date(); //Today Date\n        let result = \"\";\n        //刪單須在預約進廠日前一天中午前刪除\n        let hours = moment(row.ReserveEntryDate).diff(todayDate, 'hours');\n\n        if ((hours >= 12 && data === false) || row.FormStatus === '00') {\n            if (row.ReserveType == \"一般\" && row.IsLocked === true) {\n                result = \"\";\n            } else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n            }\n         }\n        else if (row.ReserveType == \"特殊\" && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1 && row.FormStatus === '02') {\n\n            result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n\n        }\n\n        return result;\n    }\n\n    function entryFormTypeRender(data, type, row, meta) {\n        let result = \"\";\n        result = `\u003Cspan>${data}${row.EntryFormTypeText}\u003C/span>`\n        return result;\n    }\n\n    function CarNoReserveType(data, type, row, meta) {\n        let result = \"\";\n        if (row.ReserveType == '特殊') {\n            result = `\u003Cspan>${data}(${row.ReserveType})\u003C/span>`\n        }\n        else {\n            result = `\u003Cspan>${data}\u003C/span>`\n        }\n        return result;\n    }\n\n\n    function checkStatus(row, data, dataIndex) {\n        if (data[\"IsLocked\"] == true) {\n          /*  $(row).css(\"background-color\", \"red\");*/\n        }\n    }\n\n\u003C/script>\n\n\u003Cdiv class=\"pt-2\">\n\n    \u003Cdiv id=\"queryTable_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003Cdiv class=\"dataTables_length\" id=\"queryTable_length\">\u003Clabel>顯示 \u003Cselect name=\"queryTable_length\" aria-controls=\"queryTable\" class=\"custom-select custom-select-sm form-control form-control-sm\">\u003Coption value=\"10\">10\u003C/option>\u003Coption value=\"50\">50\u003C/option>\u003Coption value=\"100\">100\u003C/option>\u003Coption value=\"300\">300\u003C/option>\u003C/select> 項結果\u003C/label>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryTable\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\" aria-describedby=\"queryTable_info\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\" style=\"width: 15%;\">操作\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">狀態\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠單號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運路線\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">車號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運種類\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠日\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠時間\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">業者過磅淨重(噸)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預約類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">取消\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3146082\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3146082};EditE00Option.Title='編輯進廠確認單(E48B201611406230886)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3146082, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3146082)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406230886\u003C/td>\u003Ctd>(NEW)H4 2808 星期五(30%)\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406230886？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3146082}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3144221\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3144221};EditE00Option.Title='編輯進廠確認單(E48B201611406200782)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3144221, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3144221)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406200782\u003C/td>\u003Ctd>H4 2808義大遊樂\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>1.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406200782？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3144221}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142204};EditE20Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142204\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142204};EditE00Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142204, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181141\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181141？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142204}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142201};EditE20Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142201\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142201};EditE00Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142201, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181137\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181137？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142201}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142200};EditE20Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142200\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142200};EditE00Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142200, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181136\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181136？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142200}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142199};EditE20Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142199\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142199};EditE00Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142199, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181135\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181135？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142199}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3142197\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3142197};EditE00Option.Title='編輯進廠確認單(E48B201611406181133)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142197, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3142197)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406181133\u003C/td>\u003Ctd>(NEW)H9 5580星期五 只有一車(25%)\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181133？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142197}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134987};EditE20Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134987\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134987};EditE00Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134987, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110721\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110721？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134987}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134985};EditE20Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134985\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134985};EditE00Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134985, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110719\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110719？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134985}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134984};EditE20Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134984\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134984};EditE00Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134984, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110718\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110718？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134984}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134983};EditE20Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134983\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134983};EditE00Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134983, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110717\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110717？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134983}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134981};EditE20Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134981\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134981};EditE00Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134981, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110715\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110715？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134981}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134980};EditE20Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134980\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134980};EditE00Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134980, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110714\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110714？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134980}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121411};EditE20Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121411\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121411};EditE00Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121411, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261022\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261022？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121411}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121410};EditE20Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121410\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121410};EditE00Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121410, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261021\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261021？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121410}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117615};EditE20Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117615};EditE00Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210775\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210775？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117614};EditE20Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117614\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117614};EditE00Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117614, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210774\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210774？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117614}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117613};EditE20Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117613\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117613};EditE00Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117613, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210773\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210773？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117613}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115644\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115644};EditE00Option.Title='編輯進廠確認單(E48B201611405190957)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115644, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115644)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190957\u003C/td>\u003Ctd>(NEW)119星期五(50%)\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190957？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115644}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115643};EditE20Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115643\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115643};EditE00Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115643, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190956\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9671\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190956？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115643}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115642\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115642};EditE00Option.Title='編輯進廠確認單(E48B201611405190955)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115642, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115642)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190955\u003C/td>\u003Ctd>H2 119 義大\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190955？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115642}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115641};EditE20Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115641\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115641};EditE00Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115641, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190954\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>117-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190954？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115641}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115640};EditE20Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115640\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115640};EditE00Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115640, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190953\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190953？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115640}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115636};EditE20Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115636\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115636};EditE00Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115636, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190949\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190949？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115636}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115635};EditE20Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115635\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115635};EditE00Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115635, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190948\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190948？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115635}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115634};EditE20Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115634\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115634};EditE00Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115634, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190947\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190947？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115634}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115632};EditE20Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115632\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115632};EditE00Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115632, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190945\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190945？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115632}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115631};EditE20Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115631\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115631};EditE00Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115631, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190944\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190944？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115631}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115630};EditE20Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115630\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115630};EditE00Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115630, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190943\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190943？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115630}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115627};EditE20Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115627\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115627};EditE00Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115627, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190940\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190940？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115627}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115625\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115625};EditE00Option.Title='編輯進廠確認單(E48B201611405190938)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115625, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115625)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190938\u003C/td>\u003Ctd>(NEW)H72560星期五(有美生) (20%)\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190938？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115625}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115623};EditE20Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115623\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115623};EditE00Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115623, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190936\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190936？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115623}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115621};EditE20Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115621\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115621};EditE00Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115621, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190934\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190934？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115621}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115619};EditE20Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115619\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115619};EditE00Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115619, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190932\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190932？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115619}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115618};EditE20Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115618\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115618};EditE00Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115618, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190931\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190931？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115618}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115616};EditE20Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115616\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115616};EditE00Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115616, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190929\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190929？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115616}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115615};EditE20Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115615};EditE00Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190928\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190928？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115399};EditE20Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115399\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115399};EditE00Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115399, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190698\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190698？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115399}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryTable_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003Cdiv class=\"dataTables_info\" id=\"queryTable_info\" role=\"status\" aria-live=\"polite\">\u003Cbig>顯示第 1 至 38 項結果，共\u003Cfont color=\"red\"> 38 \u003C/font>項\u003C/big>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003Cdiv class=\"dataTables_paginate paging_simple_numbers\" id=\"queryTable_paginate\">\u003Cul class=\"pagination\">\u003Cli class=\"paginate_button page-item previous disabled\" id=\"queryTable_previous\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"0\" tabindex=\"0\" class=\"page-link\">上一頁\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item active\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"1\" tabindex=\"0\" class=\"page-link\">1\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item next disabled\" id=\"queryTable_next\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"2\" tabindex=\"0\" class=\"page-link\">下一頁\u003C/a>\u003C/li>\u003C/ul>\u003C/div>\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryTableConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": true,\n  \"lengthChange\": true,\n  \"ordering\": false,\n  \"paging\": true,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"post\",\n    \"url\": \"/Frontend/CLE/QueryEntryForm\",\n    \"data\": ajaxParam\n  },\n  \"createdRow\": checkStatus,\n  \"deferLoading\": \"0\",\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"lengthMenu\": lengthMenuList,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 50,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"EntryId\",\n      \"orderable\": true,\n      \"render\": operateFormatter,\n      \"searchable\": true,\n      \"title\": \"操作\",\n      \"visible\": true,\n      \"width\": \"15%\"\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"render\": entryFormTypeRender,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatusText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠單號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CargoRoute\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CarNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"車號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ClearTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveEntryDate\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"預計進廠日\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryDateTime\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"實際進廠時間\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"Incinerator_ShortName\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"NetWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"業者過磅淨重(噸)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預約類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"IsDelete\",\n      \"orderable\": true,\n      \"render\": cancelFormatter,\n      \"searchable\": true,\n      \"title\": \"取消\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatus\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"IsLocked\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"鎖定\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"RouteId\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"ClearType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    }\n  ]\n};\nlet queryTable = $('#queryTable').DataTable(_queryTableConfig);\n\u003C/script>\n\u003C/div>\n\n\n\n\n\n\n\u003C/div>\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;9564beccbc9a4a88&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '92489', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,302 - DEBUG - Finished Request
2025-06-27 19:53:52,303 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,309 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,309 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,309 - DEBUG - Finished Request
2025-06-27 19:53:52,309 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,315 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,315 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,316 - DEBUG - Finished Request
2025-06-27 19:53:52,317 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,324 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,324 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,325 - DEBUG - Finished Request
2025-06-27 19:53:52,325 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:53:52,342 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,343 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.279"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.78"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.172"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.239"}]} | headers=HTTPHeaderDict({'Content-Length': '8506', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,343 - DEBUG - Finished Request
2025-06-27 19:53:52,344 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278/name {}
2025-06-27 19:53:52,352 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278/name HTTP/1.1" 200 0
2025-06-27 19:53:52,352 - DEBUG - Remote response: status=200 | data={"value":"script"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,352 - DEBUG - Finished Request
2025-06-27 19:53:52,353 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278/text {}
2025-06-27 19:53:52,365 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278/text HTTP/1.1" 200 0
2025-06-27 19:53:52,366 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,366 - DEBUG - Finished Request
2025-06-27 19:53:52,366 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278'}, 'id']}
2025-06-27 19:53:52,372 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,372 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,373 - DEBUG - Finished Request
2025-06-27 19:53:52,373 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278'}, 'class']}
2025-06-27 19:53:52,379 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,379 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,380 - DEBUG - Finished Request
2025-06-27 19:53:52,381 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.278'}, 'name']}
2025-06-27 19:53:52,387 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,387 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,388 - DEBUG - Finished Request
2025-06-27 19:53:52,388 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,392 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,392 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,393 - DEBUG - Finished Request
2025-06-27 19:53:52,393 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[name*="captcha"]'}
2025-06-27 19:53:52,402 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,402 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,402 - DEBUG - Finished Request
2025-06-27 19:53:52,403 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[id*="captcha"]'}
2025-06-27 19:53:52,411 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,411 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,411 - DEBUG - Finished Request
2025-06-27 19:53:52,412 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證"]'}
2025-06-27 19:53:52,419 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,420 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,420 - DEBUG - Finished Request
2025-06-27 19:53:52,420 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證碼"]'}
2025-06-27 19:53:52,428 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,429 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,429 - DEBUG - Finished Request
2025-06-27 19:53:52,429 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[name*="code"]'}
2025-06-27 19:53:52,437 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,437 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,437 - DEBUG - Finished Request
2025-06-27 19:53:52,438 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[name*="verify"]'}
2025-06-27 19:53:52,445 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,445 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,445 - DEBUG - Finished Request
2025-06-27 19:53:52,446 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="4"]'}
2025-06-27 19:53:52,454 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,454 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,455 - DEBUG - Finished Request
2025-06-27 19:53:52,455 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="5"]'}
2025-06-27 19:53:52,461 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,462 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,462 - DEBUG - Finished Request
2025-06-27 19:53:52,462 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,467 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,467 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,467 - DEBUG - Finished Request
2025-06-27 19:53:52,467 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'img[src*="captcha"]'}
2025-06-27 19:53:52,475 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,475 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,475 - DEBUG - Finished Request
2025-06-27 19:53:52,476 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'img[src*="verify"]'}
2025-06-27 19:53:52,484 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,484 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,484 - DEBUG - Finished Request
2025-06-27 19:53:52,485 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'img[alt*="驗證"]'}
2025-06-27 19:53:52,493 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,494 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,494 - DEBUG - Finished Request
2025-06-27 19:53:52,494 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'img[alt*="驗證碼"]'}
2025-06-27 19:53:52,505 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,506 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,506 - DEBUG - Finished Request
2025-06-27 19:53:52,506 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,510 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,510 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,510 - DEBUG - Finished Request
2025-06-27 19:53:52,511 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), '確認取得驗證碼')]"}
2025-06-27 19:53:52,520 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,520 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,520 - DEBUG - Finished Request
2025-06-27 19:53:52,521 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:53:52,530 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,530 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,531 - DEBUG - Finished Request
2025-06-27 19:53:52,531 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:53:52,537 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,537 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,537 - DEBUG - Finished Request
2025-06-27 19:53:52,538 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': '.captcha-refresh'}
2025-06-27 19:53:52,545 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,545 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,546 - DEBUG - Finished Request
2025-06-27 19:53:52,546 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,551 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,551 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,551 - DEBUG - Finished Request
2025-06-27 19:53:52,552 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), '送出')]"}
2025-06-27 19:53:52,560 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,561 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,561 - DEBUG - Finished Request
2025-06-27 19:53:52,561 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[value="送出"]'}
2025-06-27 19:53:52,567 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,567 - DEBUG - Starting new HTTP connection (2): localhost:56643
2025-06-27 19:53:52,569 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,570 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,570 - DEBUG - Finished Request
2025-06-27 19:53:52,571 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'input[type="submit"]'}
2025-06-27 19:53:52,573 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,573 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,574 - DEBUG - Finished Request
2025-06-27 19:53:52,580 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,581 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:53:52,581 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,581 - DEBUG - Finished Request
2025-06-27 19:53:52,582 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'button[type="submit"]'}
2025-06-27 19:53:52,588 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,589 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,589 - DEBUG - Finished Request
2025-06-27 19:53:52,589 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': '.btn-submit'}
2025-06-27 19:53:52,597 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,597 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,598 - DEBUG - Finished Request
2025-06-27 19:53:52,598 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,602 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,602 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,602 - DEBUG - Finished Request
2025-06-27 19:53:52,602 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), '取消')]"}
2025-06-27 19:53:52,613 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,613 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.279"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.124"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.282"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.283"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.284"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.285"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.286"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.287"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.288"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.289"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.290"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.291"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.292"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.293"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.294"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.295"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.296"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.297"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.298"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.299"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.300"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.301"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.302"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.303"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.304"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.305"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.306"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.307"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.308"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.309"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.310"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.311"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.312"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.313"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.314"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.315"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.316"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.317"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.318"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.319"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.320"}]} | headers=HTTPHeaderDict({'Content-Length': '4967', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,614 - DEBUG - Finished Request
2025-06-27 19:53:52,614 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280/name {}
2025-06-27 19:53:52,619 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280/name HTTP/1.1" 200 0
2025-06-27 19:53:52,620 - DEBUG - Remote response: status=200 | data={"value":"option"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,620 - DEBUG - Finished Request
2025-06-27 19:53:52,621 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280/text {}
2025-06-27 19:53:52,629 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280/text HTTP/1.1" 200 0
2025-06-27 19:53:52,629 - DEBUG - Remote response: status=200 | data={"value":"取消"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,630 - DEBUG - Finished Request
2025-06-27 19:53:52,630 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280/text {}
2025-06-27 19:53:52,636 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280/text HTTP/1.1" 200 0
2025-06-27 19:53:52,636 - DEBUG - Remote response: status=200 | data={"value":"取消"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,637 - DEBUG - Finished Request
2025-06-27 19:53:52,637 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280'}, 'id']}
2025-06-27 19:53:52,643 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,644 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,644 - DEBUG - Finished Request
2025-06-27 19:53:52,644 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280'}, 'class']}
2025-06-27 19:53:52,651 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,651 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,652 - DEBUG - Finished Request
2025-06-27 19:53:52,652 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.280'}, 'name']}
2025-06-27 19:53:52,657 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,657 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,658 - DEBUG - Finished Request
2025-06-27 19:53:52,658 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:52,663 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:52,663 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,663 - DEBUG - Finished Request
2025-06-27 19:53:52,664 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': 'table'}
2025-06-27 19:53:52,671 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:52,671 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.129"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,671 - DEBUG - Finished Request
2025-06-27 19:53:52,672 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128/name {}
2025-06-27 19:53:52,677 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128/name HTTP/1.1" 200 0
2025-06-27 19:53:52,677 - DEBUG - Remote response: status=200 | data={"value":"table"} | headers=HTTPHeaderDict({'Content-Length': '17', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,677 - DEBUG - Finished Request
2025-06-27 19:53:52,677 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128/text {}
2025-06-27 19:53:52,686 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128/text HTTP/1.1" 200 0
2025-06-27 19:53:52,687 - DEBUG - Remote response: status=200 | data={"value":"進廠類別 月核定量(A) 日控量\n(七天後) 實際進廠量(B) 預計進廠量(C) 上月超量(D) 剩餘進廠量\nA1 1346.5 0 815.691 76.89 0 453.919"} | headers=HTTPHeaderDict({'Content-Length': '170', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,687 - DEBUG - Finished Request
2025-06-27 19:53:52,688 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128/text {}
2025-06-27 19:53:52,697 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128/text HTTP/1.1" 200 0
2025-06-27 19:53:52,697 - DEBUG - Remote response: status=200 | data={"value":"進廠類別 月核定量(A) 日控量\n(七天後) 實際進廠量(B) 預計進廠量(C) 上月超量(D) 剩餘進廠量\nA1 1346.5 0 815.691 76.89 0 453.919"} | headers=HTTPHeaderDict({'Content-Length': '170', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,697 - DEBUG - Finished Request
2025-06-27 19:53:52,697 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128'}, 'id']}
2025-06-27 19:53:52,704 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,704 - DEBUG - Remote response: status=200 | data={"value":"queryQueryCleEntryWeight"} | headers=HTTPHeaderDict({'Content-Length': '36', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,704 - DEBUG - Finished Request
2025-06-27 19:53:52,705 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128'}, 'class']}
2025-06-27 19:53:52,711 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,711 - DEBUG - Remote response: status=200 | data={"value":"table table-striped table-bordered dataTable no-footer"} | headers=HTTPHeaderDict({'Content-Length': '66', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,712 - DEBUG - Finished Request
2025-06-27 19:53:52,712 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128'}, 'name']}
2025-06-27 19:53:52,718 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:52,718 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,719 - DEBUG - Finished Request
2025-06-27 19:53:52,725 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:53:52,735 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/element HTTP/1.1" 200 0
2025-06-27 19:53:52,736 - DEBUG - Remote response: status=200 | data={"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.262"}} | headers=HTTPHeaderDict({'Content-Length': '127', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:52,736 - DEBUG - Finished Request
2025-06-27 19:53:53,575 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:53,583 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:53,584 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:53,584 - DEBUG - Finished Request
2025-06-27 19:53:54,585 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:54,593 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:54,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,593 - DEBUG - Finished Request
2025-06-27 19:53:54,737 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '\n            // 檢查頁面最終狀態\n            var bodyText = document.body.innerText || document.body.textCon...', 'args': []}
2025-06-27 19:53:54,745 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:54,745 - DEBUG - Remote response: status=200 | data={"value":{"bodyTextLength":3955,"editButtonCount":70,"hasTargetOrder":true,"rowCount":41,"sampleText":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n"}} | headers=HTTPHeaderDict({'Content-Length': '632', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,745 - DEBUG - Finished Request
2025-06-27 19:53:54,746 - INFO - 🔍 [WebDriverWait完成後] 開始記錄頁面內容...
2025-06-27 19:53:54,746 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:54,751 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:54,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,752 - DEBUG - Finished Request
2025-06-27 19:53:54,752 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/title {}
2025-06-27 19:53:54,756 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/title HTTP/1.1" 200 0
2025-06-27 19:53:54,756 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,756 - DEBUG - Finished Request
2025-06-27 19:53:54,756 - INFO - 🔍 [WebDriverWait完成後] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:53:54,756 - INFO - 🔍 [WebDriverWait完成後] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:53:54,757 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/source {}
2025-06-27 19:53:54,761 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/source HTTP/1.1" 200 0
2025-06-27 19:53:54,762 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 16.14px;\">\u003Chead>\u003Clink href=\"/Content/MainCss?v=zPHWubngoBLlh81X9bw_8aWFtUJfWgM1VR7izfwxpoQ1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DataTableCss?v=Z2hod0l-s4nUkCqEwOiqsErJ5wq6PmToPF0R4Rizr9g1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n\u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DataTableJs?v=PrL5mJgVEL8_qJD7wVZM5xxp6rEVZ9g1TBfDlvdUlWg1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'jALW1rx83SXKMFO-MZjDfE2166ss1b-tmKmaK7Iw8RpXY23zj5JvEXr14cQ4zYWQVDCqdFV2kmPlXdU1B4KYKawM6heR0RZRvhpUnvOOago1:UsteFvpkPHOBEgjWNV7mBoJJQ_CD1i51a-jbtxHcoDpyoJ7CNWv20mqmzaiMKV_SnMOqkigPlyheVcb9AHZRxT-0nARbZnRcj2_MI7UugDI1' };\n    \n    let lengthMenuList = [[10, 50, 100, 300], [10, 50, 100, 300]];\n\u003C/script>\n\n\n    \u003Cscript>\n        var UrlDeleteCleEntryForm = '/Frontend/CLE/DeleteCleEntryForm';\n        var Token = 'aEymg9dqVbAs6pq7WxBfKyIVL4IfVWFpMFcORxzZzHlsgMPFkVWljzNyZq-EOks_8DUF-KDDozyitvd7ZC--WECzQ270P8iObuSlcImILyk1:L5S0IohNGcrZOde92_rpbMeBQZp0c-1vMWd1aoTAKCbMFvv3blS4EO0WhlgrEgtGp8SMzp0aRBizxePgF0Zc-5io1hymJc_fYYPOqvHvGcY1';\n        var UrlExport1 = '/Frontend/CLE/DownloadCLE104Data';\n        var UrlExport = '/Frontend/CLE/PrintEntryForm';\n        var UrlEFDetailExport = '/Frontend/CLE/DownloadCLEDetailData';\n        var UrlLog = '/Frontend/CLE/LogFP';\n   \n        let actualWeightDays = 10;\n        console.log(UrlLog);\n        let addRouteOption = {\n            Title: '新增路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030A01',\n            BeforeClose: function () {\n\n            }\n        };\n\n        let editRouteOption = {\n            Title: '編輯路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030E00',\n            BeforeClose: function () {\n\n            }\n        };\n\n      $(function () {\n\n        //  var FPoptions = {\n        //      fonts: { extendedJsFonts: true },\n        //      excludes: {\n        //          cpuClass: true,\n        //          fonts: true,\n        //          fontsFlash: true,\n        //          doNotTrack: true,\n        //          webgl: true,\n        //          webglVendorAndRenderer: true,\n              \n                \n        //      }\n        //  }\n        //    var FP_token;\n\n        //    setTimeout(function () {\n        //        Fingerprint2.get(FPoptions, function (components) {\n        //            var values = components.map(function (component) { return component.value })\n        //            var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //            FP_token = murmur\n        //            LogData2(FP_token);\n        //        })\n        //    }, 500)\n\n\n        //    Fingerprint2.get(FPoptions, function (components) {\n        //        var values = components.map(function (component) { return component.value })\n        //        var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //        FP_token = murmur\n        //        LogData(FP_token);\n        //    });\n\n\n        });\n          var FP_token;\n\n          function getOrCreateLocalID() {\n              const key = 'local_fingerprint_id';\n              let id = localStorage.getItem(key);\n              if (!id) {\n                  id = 'id-' + Math.random().toString(36).substr(2, 9);\n                  localStorage.setItem(key, id);\n              }\n              return id;\n          }\n\n          window.addEventListener('load', function () {\n              setTimeout(function () {\n                  Fingerprint2.get(function (components) {\n                      // 過濾與排序\n                      const baseComponents = components\n                          .filter(c => !['userAgent', 'language', 'webdriver'].includes(c.key))\n                          .sort((a, b) => a.key.localeCompare(b.key));\n\n                      // 額外客製化參數\n                      const customComponents = [\n                          { key: 'timezoneOffset', value: new Date().getTimezoneOffset() },\n                          { key: 'localID', value: getOrCreateLocalID() },\n                          { key: 'cpuCores', value: navigator.hardwareConcurrency || 'unknown' },\n                          { key: 'pixelRatio', value: window.devicePixelRatio },\n                          { key: 'connectionType', value: (navigator.connection && navigator.connection.effectiveType) || 'unknown' }\n                      ];\n\n                      const allComponents = [...baseComponents, ...customComponents];\n\n\n                      const values = allComponents\n                          .sort((a, b) => a.key.localeCompare(b.key))\n                          .map(component => component.value);\n\n                      const rawString = values.join('###');\n\n\n                      const hash = Fingerprint2.x64hash128(rawString, 31);\n\n\n                      LogData(hash);\n\n                      console.log('Extended fingerprint hash:', hash);\n                      console.log('All components:', allComponents);\n                  });\n              }, 500);\n\n          });\n\n\n\n        function LogData(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n        function LogData2(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog2,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n\n\n    \u003C/script>\n    \u003Cscript src=\"/Scripts/fingerprint2.js\" type=\"text/javascript\">\u003C/script>\n    \u003Cscript src=\"/Scripts/View/Frontend/CLE/CLE1040Q00.js?2025062719534062\">\u003C/script>\n\n\n\n\n\n\n\u003C/head>\u003Cbody>\u003Cdiv class=\"container-fluid\">\n    \n\n\n\n\n\u003Cdiv class=\"mainTitle\">\n    \u003Ch2>進廠確認單管理\u003C/h2>\n\u003C/div>\n\u003Cdiv class=\"row\">\n    \u003Cdiv class=\"col-md-7 text-left\">\n        \n\n\n\u003Cform action=\"/Frontend/CLE/QueryEntryForm\" autocomplete=\"off\" id=\"QueryForm\" method=\"post\" novalidate=\"\">\u003Cinput name=\"__RequestVerificationToken\" type=\"hidden\" value=\"FZ82RI_3K05TdSDZndptMgp7QCJgYm9KMI1HiAuYeFrCfHhb90mkzQHj3b--pWOylIbB8KO05Yd-tkEokvTKytwI7DDEqD5sbyAeSxeogYc1\">    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryIncineratorId\">進廠別\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control\" id=\"EntryIncineratorId\" name=\"EntryIncineratorId\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"0\">調度中心\u003C/option>\n\u003Coption value=\"1\">高南廠\u003C/option>\n\u003Coption value=\"2\">岡山廠\u003C/option>\n\u003Coption value=\"3\">仁武廠\u003C/option>\n\u003Coption value=\"5\">路竹掩埋場\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"EntryNo\">進廠確認單號\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cinput class=\"form-control\" id=\"EntryNo\" name=\"EntryNo\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"FormStatus\">狀態\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control p-2\" id=\"FormStatus\" name=\"FormStatus\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"00\">暫存\u003C/option>\n\u003Coption value=\"01\">待審查\u003C/option>\n\u003Coption value=\"02\">未載運\u003C/option>\n\u003Coption value=\"03\">已載運-待清除確認\u003C/option>\n\u003Coption value=\"41\">已載運-檢核未通過\u003C/option>\n\u003Coption value=\"91\">取消\u003C/option>\n\u003Coption value=\"97\">審查退回\u003C/option>\n\u003Coption value=\"98\">退運\u003C/option>\n\u003Coption value=\"99\">已完成\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"CheckWeighFail\">檢核結果\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cselect class=\"form-control\" id=\"CheckWeighFail\" name=\"CheckWeighFail\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"1\">通過\u003C/option>\n\u003Coption value=\"2\">未通過\u003C/option>\n\u003Coption value=\"3\">未檢核\u003C/option>\n\u003C/select>\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"ReserveEntryDate_Start\">預計進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_Start\" name=\"ReserveEntryDate_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_End\" name=\"ReserveEntryDate_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryDateTime_Start\">實際進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_Start\" name=\"EntryDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_End\" name=\"EntryDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"\">報表日期起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_Start\" name=\"RptDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_End\" name=\"RptDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"text-center\">\n        \n\n        \u003Cbutton type=\"button\" id=\"btnQuery\" onclick=\"tableDraw(queryTable, 'QueryForm');tableDraw(queryQueryCleEntryWeight, '',_queryQueryCleEntryWeightConfig );\" class=\"btn btn-primary\">查詢\u003C/button>\n        \u003Cspan style=\"color:red\">請按查詢以顯示清單\u003C/span>\n        \n\n        \u003Cbr>\n                                \u003Cbutton type=\"button\" class=\"btn btn-warning\" onclick=\"addA1Option.Data = {entryFormType:'A1'};EditShowDialog(addA1Option)\">新增A1本市事廢\u003C/button>\n                        \u003Cbr>\n\n        \n                        \u003Cbutton type=\"button\" class=\"btn btn-dark\" onclick=\"addA3bOption.Data = {entryFormType:'A3b'};EditShowDialog(addA3bOption)\">新增A3b2050專案\u003C/button>\n                \n                    \u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"DownloadCLEDetailData();\">下載明細報表\u003C/button>\n            \u003Cbr>\n\n    \u003C/div>\n\u003C/form>\n    \u003C/div>\n    \u003Cdiv class=\"col-md-5 text-left\">\n\u003Cform action=\"/Frontend/CLE/QueryCleEntryWeight\" autocomplete=\"off\" id=\"QueryDocForm\" method=\"post\" novalidate=\"\" onsubmit=\"return false;\">            \u003Cdiv>\n                \u003Cselect id=\"QueryDate\" name=\"QueryDate\" onchange=\"getCleEntryWeight()\">\u003Coption value=\"2025/06/27\">6月\u003C/option>\n\u003Coption value=\"2025/07/27\">7月\u003C/option>\n\u003C/select>\u003Cspan>進廠量統計\u003C/span>\n                \n            \u003C/div>\n\u003C/form>        \u003Cdiv id=\"queryQueryCleEntryWeight_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryQueryCleEntryWeight\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">月核定量(A)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">日控量\u003Cbr>(七天後)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠量(B)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量(C)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">上月超量(D)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">剩餘進廠量\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>A1\u003C/td>\u003Ctd>1346.5\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>815.691\u003C/td>\u003Ctd>76.89\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>453.919\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryQueryCleEntryWeight_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryQueryCleEntryWeightConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": false,\n  \"lengthChange\": false,\n  \"ordering\": false,\n  \"paging\": false,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"get\",\n    \"url\": \"/Frontend/CLE/QueryCleEntryWeight\",\n    \"data\": ajaxParam\n  },\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 0,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"ConsentType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"月核定量(A)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ControlOpenWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"日控量\u003Cbr/>(七天後)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthEntryWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"實際進廠量(B)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量(C)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthOverWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"上月超量(D)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthRemainingWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"剩餘進廠量\",\n      \"visible\": true\n    }\n  ]\n};\nlet queryQueryCleEntryWeight = $('#queryQueryCleEntryWeight').DataTable(_queryQueryCleEntryWeightConfig);\n\u003C/script>\n        \u003Cdiv>\n            \u003Cul>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">每日開放查詢時日10:30~次日09:00\u003C/span>\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">欄位說明：\u003C/span>(單位：噸)\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">月核定量(A)：\u003C/span>因進廠管控措施機制，故月核可量為浮動數值\u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月實際進廠量(B)：\u003C/span>進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月預計進廠量(C)：\u003C/span>進廠確認單狀態為「未載運」、「已逾期」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">上月超量(D)：\u003C/span>上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月剩餘進廠量：\u003C/span>A-B-C-D\n                \u003C/li>\n                \n            \u003C/ul>\n        \u003C/div>\n    \u003C/div>\n\n\u003C/div>\n\n\n\n\n\u003Cscript>\n\n    function operateFormatter(data, type, row, meta) {\n        let result = \"\";\n        let todayDate = new Date(); //Today Date\n\n        if (row.IsLocked === true) {\n\n        }\n\n\n\n        //預計進廠日前一天可修改、刪除\n        //if (moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString())) {\n        if (\n            (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1\n            && row.FormStatus != '03'\n            && (row.FormStatus === '00' || row.FormStatus === '02' || row.FormStatus == '97')\n        ) {\n            //console.log((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")));\n            if (row.EntryFormType == \"A4a\") {\n                //02=審查完成\n                if (row.FormStatus !== '02') {\n                    result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"Edit41EOption.Data = {entryId:${data}};Edit41EOption.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(Edit41EOption)\">編輯\u003C/button>`;\n                }\n            }\n            else {\n                if (row.FormStatus === '00') {\n                    if (row.ReserveType != \"特殊\") {\n                        if (row.ReserveType != \"A3a\") {\n                            result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"EditE20Option.Data = {entryId:${data}};EditE20Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE20Option)\">編輯\u003C/button>`;\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n\n                    }\n                }\n                else {\n                    if (row.ReserveType != \"特殊\" && row.IsLocked === false) {\n                        if (row.ReserveType == \"優先\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE30Option.Data = {entryId:${data}};EditE30Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE30Option)\">編輯\u003C/button>`;\n                        }\n                        else if (row.EntryFormType == \"A3a\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                    }\n\n                }\n\n            }\n\n\n        }\n        else {\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02')) {\n\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('確定取消單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormCancel', { entryId: ${data}}, 'post', _Headers);}\">未進廠取消\u003C/button>`;\n            }\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\") \u003C= actualWeightDays)) {\n                if ((row.FormStatus == '03' || row.FormStatus == '41')) { ////20250220 關閉隔日可填寫實際重量\n                    // if ((row.FormStatus == '03' || row.FormStatus == '41') || (row.FormStatus == '02' && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02'))) {\n                    if (row.EntryFormType == \"A4a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"Edit41E10Option.Data = {entryId:${data}};Edit41E10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(Edit41E10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else if (row.EntryFormType == \"A3a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                }\n            }\n            \n            if (row.EntryFormType == \"A4a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1041R00')\">查看\u003C/button>`;\n            }\n            else if (row.EntryFormType == \"A3a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R10')\">查看\u003C/button>`;\n            }\n            else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R00')\">查看\u003C/button>`;\n            }\n\n\n\n        }\n\n        //if (row.CargoRoute) {\n        //    result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"editRouteOption.Data = {RouteId:${row.RouteId}};window.parent.EditShowDialog(editRouteOption)\">編輯路線\u003C/button>`;\n        //}\n        //else {\n        result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"addRouteOption.Data = {entryId:${row.EntryId}, ClearType:'${row.ClearType}',EntryFormType:'${row.EntryFormType}'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>`;\n        //}\n\n        if ((moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString()) || moment(row.ReserveEntryDate).isSame(todayDate.toLocaleDateString()))\n            && row.FormStatus === '02') {\n            //  && row.IsLocked === false) {\n            result += `\u003Cbutton type=\"button\" class='btn btn-secondary' onclick=\"ShowEntryForm(${data})\">列印\u003C/button>`;\n\n        }\n        if (row.EntryFormType == \"A1\")\n        {\n         }\n\n             return result;\n         }\n\n    function cancelFormatter(data, type, row, meta) {\n        let todayDate = new Date(); //Today Date\n        let result = \"\";\n        //刪單須在預約進廠日前一天中午前刪除\n        let hours = moment(row.ReserveEntryDate).diff(todayDate, 'hours');\n\n        if ((hours >= 12 && data === false) || row.FormStatus === '00') {\n            if (row.ReserveType == \"一般\" && row.IsLocked === true) {\n                result = \"\";\n            } else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n            }\n         }\n        else if (row.ReserveType == \"特殊\" && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1 && row.FormStatus === '02') {\n\n            result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n\n        }\n\n        return result;\n    }\n\n    function entryFormTypeRender(data, type, row, meta) {\n        let result = \"\";\n        result = `\u003Cspan>${data}${row.EntryFormTypeText}\u003C/span>`\n        return result;\n    }\n\n    function CarNoReserveType(data, type, row, meta) {\n        let result = \"\";\n        if (row.ReserveType == '特殊') {\n            result = `\u003Cspan>${data}(${row.ReserveType})\u003C/span>`\n        }\n        else {\n            result = `\u003Cspan>${data}\u003C/span>`\n        }\n        return result;\n    }\n\n\n    function checkStatus(row, data, dataIndex) {\n        if (data[\"IsLocked\"] == true) {\n          /*  $(row).css(\"background-color\", \"red\");*/\n        }\n    }\n\n\u003C/script>\n\n\u003Cdiv class=\"pt-2\">\n\n    \u003Cdiv id=\"queryTable_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003Cdiv class=\"dataTables_length\" id=\"queryTable_length\">\u003Clabel>顯示 \u003Cselect name=\"queryTable_length\" aria-controls=\"queryTable\" class=\"custom-select custom-select-sm form-control form-control-sm\">\u003Coption value=\"10\">10\u003C/option>\u003Coption value=\"50\">50\u003C/option>\u003Coption value=\"100\">100\u003C/option>\u003Coption value=\"300\">300\u003C/option>\u003C/select> 項結果\u003C/label>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryTable\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\" aria-describedby=\"queryTable_info\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\" style=\"width: 15%;\">操作\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">狀態\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠單號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運路線\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">車號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運種類\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠日\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠時間\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">業者過磅淨重(噸)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預約類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">取消\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3146082\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3146082};EditE00Option.Title='編輯進廠確認單(E48B201611406230886)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3146082, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3146082)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406230886\u003C/td>\u003Ctd>(NEW)H4 2808 星期五(30%)\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406230886？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3146082}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3144221\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3144221};EditE00Option.Title='編輯進廠確認單(E48B201611406200782)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3144221, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3144221)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406200782\u003C/td>\u003Ctd>H4 2808義大遊樂\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>1.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406200782？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3144221}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142204};EditE20Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142204\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142204};EditE00Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142204, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181141\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181141？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142204}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142201};EditE20Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142201\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142201};EditE00Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142201, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181137\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181137？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142201}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142200};EditE20Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142200\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142200};EditE00Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142200, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181136\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181136？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142200}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142199};EditE20Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142199\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142199};EditE00Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142199, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181135\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181135？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142199}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3142197\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3142197};EditE00Option.Title='編輯進廠確認單(E48B201611406181133)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142197, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3142197)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406181133\u003C/td>\u003Ctd>(NEW)H9 5580星期五 只有一車(25%)\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181133？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142197}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134987};EditE20Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134987\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134987};EditE00Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134987, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110721\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110721？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134987}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134985};EditE20Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134985\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134985};EditE00Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134985, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110719\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110719？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134985}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134984};EditE20Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134984\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134984};EditE00Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134984, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110718\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110718？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134984}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134983};EditE20Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134983\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134983};EditE00Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134983, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110717\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110717？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134983}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134981};EditE20Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134981\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134981};EditE00Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134981, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110715\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110715？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134981}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134980};EditE20Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134980\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134980};EditE00Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134980, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110714\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110714？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134980}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121411};EditE20Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121411\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121411};EditE00Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121411, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261022\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261022？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121411}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121410};EditE20Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121410\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121410};EditE00Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121410, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261021\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261021？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121410}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117615};EditE20Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117615};EditE00Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210775\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210775？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117614};EditE20Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117614\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117614};EditE00Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117614, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210774\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210774？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117614}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117613};EditE20Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117613\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117613};EditE00Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117613, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210773\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210773？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117613}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115644\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115644};EditE00Option.Title='編輯進廠確認單(E48B201611405190957)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115644, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115644)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190957\u003C/td>\u003Ctd>(NEW)119星期五(50%)\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190957？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115644}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115643};EditE20Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115643\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115643};EditE00Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115643, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190956\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9671\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190956？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115643}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115642\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115642};EditE00Option.Title='編輯進廠確認單(E48B201611405190955)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115642, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115642)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190955\u003C/td>\u003Ctd>H2 119 義大\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190955？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115642}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115641};EditE20Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115641\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115641};EditE00Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115641, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190954\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>117-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190954？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115641}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115640};EditE20Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115640\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115640};EditE00Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115640, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190953\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190953？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115640}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115636};EditE20Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115636\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115636};EditE00Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115636, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190949\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190949？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115636}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115635};EditE20Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115635\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115635};EditE00Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115635, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190948\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190948？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115635}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115634};EditE20Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115634\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115634};EditE00Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115634, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190947\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190947？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115634}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115632};EditE20Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115632\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115632};EditE00Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115632, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190945\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190945？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115632}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115631};EditE20Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115631\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115631};EditE00Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115631, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190944\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190944？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115631}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115630};EditE20Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115630\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115630};EditE00Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115630, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190943\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190943？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115630}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115627};EditE20Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115627\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115627};EditE00Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115627, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190940\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190940？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115627}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115625\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115625};EditE00Option.Title='編輯進廠確認單(E48B201611405190938)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115625, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115625)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190938\u003C/td>\u003Ctd>(NEW)H72560星期五(有美生) (20%)\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190938？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115625}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115623};EditE20Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115623\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115623};EditE00Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115623, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190936\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190936？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115623}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115621};EditE20Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115621\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115621};EditE00Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115621, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190934\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190934？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115621}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115619};EditE20Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115619\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115619};EditE00Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115619, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190932\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190932？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115619}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115618};EditE20Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115618\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115618};EditE00Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115618, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190931\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190931？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115618}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115616};EditE20Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115616\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115616};EditE00Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115616, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190929\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190929？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115616}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115615};EditE20Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115615};EditE00Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190928\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190928？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115399};EditE20Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115399\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115399};EditE00Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115399, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190698\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190698？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115399}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryTable_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003Cdiv class=\"dataTables_info\" id=\"queryTable_info\" role=\"status\" aria-live=\"polite\">\u003Cbig>顯示第 1 至 38 項結果，共\u003Cfont color=\"red\"> 38 \u003C/font>項\u003C/big>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003Cdiv class=\"dataTables_paginate paging_simple_numbers\" id=\"queryTable_paginate\">\u003Cul class=\"pagination\">\u003Cli class=\"paginate_button page-item previous disabled\" id=\"queryTable_previous\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"0\" tabindex=\"0\" class=\"page-link\">上一頁\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item active\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"1\" tabindex=\"0\" class=\"page-link\">1\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item next disabled\" id=\"queryTable_next\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"2\" tabindex=\"0\" class=\"page-link\">下一頁\u003C/a>\u003C/li>\u003C/ul>\u003C/div>\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryTableConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": true,\n  \"lengthChange\": true,\n  \"ordering\": false,\n  \"paging\": true,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"post\",\n    \"url\": \"/Frontend/CLE/QueryEntryForm\",\n    \"data\": ajaxParam\n  },\n  \"createdRow\": checkStatus,\n  \"deferLoading\": \"0\",\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"lengthMenu\": lengthMenuList,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 50,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"EntryId\",\n      \"orderable\": true,\n      \"render\": operateFormatter,\n      \"searchable\": true,\n      \"title\": \"操作\",\n      \"visible\": true,\n      \"width\": \"15%\"\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"render\": entryFormTypeRender,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatusText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠單號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CargoRoute\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CarNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"車號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ClearTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveEntryDate\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"預計進廠日\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryDateTime\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"實際進廠時間\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"Incinerator_ShortName\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"NetWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"業者過磅淨重(噸)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預約類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"IsDelete\",\n      \"orderable\": true,\n      \"render\": cancelFormatter,\n      \"searchable\": true,\n      \"title\": \"取消\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatus\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"IsLocked\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"鎖定\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"RouteId\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"ClearType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    }\n  ]\n};\nlet queryTable = $('#queryTable').DataTable(_queryTableConfig);\n\u003C/script>\n\u003C/div>\n\n\n\n\n\n\n\u003C/div>\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;9564beccbc9a4a88&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '92489', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,767 - DEBUG - Finished Request
2025-06-27 19:53:54,767 - INFO - 🔍 [WebDriverWait完成後] page_source 長度: 71771
2025-06-27 19:53:54,768 - INFO - 🔍 [WebDriverWait完成後] page_source 包含 E48B: True
2025-06-27 19:53:54,768 - INFO - 🔍 [WebDriverWait完成後] page_source 包含目標訂單: True
2025-06-27 19:53:54,768 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:53:54,775 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:54,775 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t815.691\t76.89\t0\t453.919\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406230886\t(NEW)H4 2808 星期五(30%)\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406200782\tH4 2808義大遊樂\tKEP-2808\t專車清運\t1.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181141\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181137\t仁\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181136\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181135\t岡\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406181133\t(NEW)H9 5580星期五 只有一車(25%)\tKEJ-5580\t一般清運\t6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110721\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110719\t南\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110718\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110717\t南\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110715\t岡\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110714\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261022\t岡\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261021\t南\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210775\t岡\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210774\t仁\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210773\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190957\t(NEW)119星期五(50%)\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190956\t南\tKED-9671\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190955\tH2 119 義大\t119-BR\t專車清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190954\t南\t117-BR\t一般清運\t3.5\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190953\t仁\t119-BR\t一般清運\t3.6\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190949\t南\t119-BR\t一般清運\t3.6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190948\t仁\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190947\t岡\t120-BR\t一般清運\t3.2\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190945\t仁\t121-BR\t一般清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190944\t岡\t121-BR\t一般清運\t3.5\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190943\t仁\t129-BR\t一般清運\t4.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190940\t岡\t129-BR\t一般清運\t4.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190938\t(NEW)H72560星期五(有美生) (20%)\tKEP-2560\t一般清運\t4.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190936\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190934\t岡\t937-N6\t一般清運\t4.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190932\t南\t937-N6\t一般清運\t4.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190931\t南\tKEH-9278\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190929\t仁\tKEH-9278\t一般清運\t8.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190928\t岡\tKER-2807\t一般清運\t7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190698\t仁\tKER-2807\t一般清運\t7\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n顯示第 1 至 38 項結果，共 38 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '7514', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,776 - DEBUG - Finished Request
2025-06-27 19:53:54,776 - INFO - 🔍 [WebDriverWait完成後] innerText 長度: 3955
2025-06-27 19:53:54,776 - INFO - 🔍 [WebDriverWait完成後] innerText 包含 E48B: True
2025-06-27 19:53:54,776 - INFO - 🔍 [WebDriverWait完成後] innerText 包含目標訂單: True
2025-06-27 19:53:54,776 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:53:54,782 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,783 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.128"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.129"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,783 - DEBUG - Finished Request
2025-06-27 19:53:54,783 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:53:54,791 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,791 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.131"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.170"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,792 - DEBUG - Finished Request
2025-06-27 19:53:54,792 - INFO - 🔍 [WebDriverWait完成後] 檢測到 2 個表格，41 個表格行
2025-06-27 19:53:54,792 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:53:54,799 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,799 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.241"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.242"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.243"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.277"}]} | headers=HTTPHeaderDict({'Content-Length': '4495', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,799 - DEBUG - Finished Request
2025-06-27 19:53:54,800 - INFO - 🔍 [WebDriverWait完成後] 包含 'E48B' 的元素數量: 38
2025-06-27 19:53:54,800 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.240/text {}
2025-06-27 19:53:54,806 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.240/text HTTP/1.1" 200 0
2025-06-27 19:53:54,806 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406230886"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,807 - DEBUG - Finished Request
2025-06-27 19:53:54,807 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.240/name {}
2025-06-27 19:53:54,812 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.240/name HTTP/1.1" 200 0
2025-06-27 19:53:54,812 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,812 - DEBUG - Finished Request
2025-06-27 19:53:54,812 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 1: <td> E48B201611406230886...
2025-06-27 19:53:54,813 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.241/text {}
2025-06-27 19:53:54,819 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.241/text HTTP/1.1" 200 0
2025-06-27 19:53:54,820 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406200782"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,820 - DEBUG - Finished Request
2025-06-27 19:53:54,820 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.241/name {}
2025-06-27 19:53:54,824 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.241/name HTTP/1.1" 200 0
2025-06-27 19:53:54,824 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,825 - DEBUG - Finished Request
2025-06-27 19:53:54,825 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 2: <td> E48B201611406200782...
2025-06-27 19:53:54,825 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.242/text {}
2025-06-27 19:53:54,831 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.242/text HTTP/1.1" 200 0
2025-06-27 19:53:54,832 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406181141"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,832 - DEBUG - Finished Request
2025-06-27 19:53:54,832 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.242/name {}
2025-06-27 19:53:54,837 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.242/name HTTP/1.1" 200 0
2025-06-27 19:53:54,837 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,837 - DEBUG - Finished Request
2025-06-27 19:53:54,837 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 3: <td> E48B201611406181141...
2025-06-27 19:53:54,837 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.243/text {}
2025-06-27 19:53:54,843 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.243/text HTTP/1.1" 200 0
2025-06-27 19:53:54,843 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406181137"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,843 - DEBUG - Finished Request
2025-06-27 19:53:54,843 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.243/name {}
2025-06-27 19:53:54,849 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.243/name HTTP/1.1" 200 0
2025-06-27 19:53:54,849 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,849 - DEBUG - Finished Request
2025-06-27 19:53:54,849 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 4: <td> E48B201611406181137...
2025-06-27 19:53:54,850 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.244/text {}
2025-06-27 19:53:54,856 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.244/text HTTP/1.1" 200 0
2025-06-27 19:53:54,856 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406181136"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,856 - DEBUG - Finished Request
2025-06-27 19:53:54,856 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.244/name {}
2025-06-27 19:53:54,860 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.244/name HTTP/1.1" 200 0
2025-06-27 19:53:54,860 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,860 - DEBUG - Finished Request
2025-06-27 19:53:54,860 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 5: <td> E48B201611406181136...
2025-06-27 19:53:54,860 - INFO - 🔍 [WebDriverWait完成後] innerText 前300字符:
2025-06-27 19:53:54,860 - INFO - 🔍 [WebDriverWait完成後] 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

6月
7月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	815.691	76.89	0	453.919
每日開放查詢時日1...
2025-06-27 19:53:54,861 - INFO - 🔍 [WebDriverWait完成後] innerText 後300字符:
2025-06-27 19:53:54,861 - INFO - 🔍 [WebDriverWait完成後] ...		高南廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190929	仁	KEH-9278	一般清運	8.5	2025-07-04		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190928	岡	KER-2807	一般清運	7	2025-07-04		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190698	仁	KER-2807	一般清運	7	2025-07-04		仁武廠	0	一般	取消(刪除)
顯示第 1 至 38 項結果，共 38 項
上一頁
1
下一頁
2025-06-27 19:53:54,861 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:53:54,871 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,871 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.78"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.172"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.239"}]} | headers=HTTPHeaderDict({'Content-Length': '8270', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,871 - DEBUG - Finished Request
2025-06-27 19:53:54,872 - INFO - 🔍 [WebDriverWait完成後] 檢測到 70 個編輯按鈕
2025-06-27 19:53:54,872 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:53:54,877 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,877 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,877 - DEBUG - Finished Request
2025-06-27 19:53:54,877 - INFO - 🔍 [WebDriverWait完成後] 檢測到 0 個 iframe
2025-06-27 19:53:54,878 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:53:54,884 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,885 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,885 - DEBUG - Finished Request
2025-06-27 19:53:54,885 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:54,894 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:54,894 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,895 - DEBUG - Finished Request
2025-06-27 19:53:54,895 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/title {}
2025-06-27 19:53:54,899 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/title HTTP/1.1" 200 0
2025-06-27 19:53:54,899 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,899 - DEBUG - Finished Request
2025-06-27 19:53:54,900 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//tr[contains(., 'E48B201611405190953')]"}
2025-06-27 19:53:54,908 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,908 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155"}]} | headers=HTTPHeaderDict({'Content-Length': '129', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,908 - DEBUG - Finished Request
2025-06-27 19:53:54,909 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155/text {}
2025-06-27 19:53:54,917 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155/text HTTP/1.1" 200 0
2025-06-27 19:53:54,917 - DEBUG - Remote response: status=200 | data={"value":"編輯新增路線 A1本市事廢 暫存 E48B201611405190953 仁 119-BR 一般清運 3.6 2025-07-04 仁武廠 0 一般 取消(刪除)"} | headers=HTTPHeaderDict({'Content-Length': '145', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,917 - DEBUG - Finished Request
2025-06-27 19:53:54,917 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155/elements {'using': 'xpath', 'value': ".//a[contains(text(), '編輯')]"}
2025-06-27 19:53:54,926 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,926 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,926 - DEBUG - Finished Request
2025-06-27 19:53:54,926 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155/elements {'using': 'xpath', 'value': ".//button[contains(text(), '編輯')]"}
2025-06-27 19:53:54,937 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.155/elements HTTP/1.1" 200 0
2025-06-27 19:53:54,937 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.210"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,937 - DEBUG - Finished Request
2025-06-27 19:53:54,937 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/text {}
2025-06-27 19:53:54,944 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/text HTTP/1.1" 200 0
2025-06-27 19:53:54,944 - DEBUG - Remote response: status=200 | data={"value":"編輯"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,944 - DEBUG - Finished Request
2025-06-27 19:53:54,944 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209'}, 'value']}
2025-06-27 19:53:54,951 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:54,951 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,952 - DEBUG - Finished Request
2025-06-27 19:53:54,952 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209'}, 'class']}
2025-06-27 19:53:54,956 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:54,957 - DEBUG - Remote response: status=200 | data={"value":"btn btn-success"} | headers=HTTPHeaderDict({'Content-Length': '27', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,957 - DEBUG - Finished Request
2025-06-27 19:53:54,957 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/name {}
2025-06-27 19:53:54,961 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/name HTTP/1.1" 200 0
2025-06-27 19:53:54,961 - DEBUG - Remote response: status=200 | data={"value":"button"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,962 - DEBUG - Finished Request
2025-06-27 19:53:54,962 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* isDisplayed */return (function(){return (function(){var g=this||self;\nfunction aa(a){var b=typeof...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209'}]}
2025-06-27 19:53:54,970 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:53:54,970 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,970 - DEBUG - Finished Request
2025-06-27 19:53:54,970 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/enabled {}
2025-06-27 19:53:54,977 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/enabled HTTP/1.1" 200 0
2025-06-27 19:53:54,977 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:54,977 - DEBUG - Finished Request
2025-06-27 19:53:54,977 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/click {}
2025-06-27 19:53:55,104 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.209/click HTTP/1.1" 200 0
2025-06-27 19:53:55,105 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:55,105 - DEBUG - Finished Request
2025-06-27 19:53:55,593 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:55,600 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:55,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:55,600 - DEBUG - Finished Request
2025-06-27 19:53:56,601 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:56,608 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:56,608 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:56,608 - DEBUG - Finished Request
2025-06-27 19:53:57,610 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:57,616 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:57,616 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:57,616 - DEBUG - Finished Request
2025-06-27 19:53:58,616 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:58,623 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:58,624 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:58,624 - DEBUG - Finished Request
2025-06-27 19:53:59,624 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:53:59,633 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:53:59,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:53:59,633 - DEBUG - Finished Request
2025-06-27 19:54:00,106 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:00,113 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:00,114 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:00,114 - DEBUG - Finished Request
2025-06-27 19:54:00,114 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/title {}
2025-06-27 19:54:00,119 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/title HTTP/1.1" 200 0
2025-06-27 19:54:00,120 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:00,120 - DEBUG - Finished Request
2025-06-27 19:54:00,634 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:00,639 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:00,639 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:00,639 - DEBUG - Finished Request
2025-06-27 19:54:01,640 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:01,646 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:01,647 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:01,647 - DEBUG - Finished Request
2025-06-27 19:54:02,122 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "img[src*='captcha']"}
2025-06-27 19:54:02,131 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,131 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,131 - DEBUG - Finished Request
2025-06-27 19:54:02,131 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "img[src*='verify']"}
2025-06-27 19:54:02,140 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,140 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,141 - DEBUG - Finished Request
2025-06-27 19:54:02,141 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "img[alt*='驗證']"}
2025-06-27 19:54:02,148 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,148 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,149 - DEBUG - Finished Request
2025-06-27 19:54:02,149 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "img[alt*='驗證碼']"}
2025-06-27 19:54:02,157 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,157 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,157 - DEBUG - Finished Request
2025-06-27 19:54:02,157 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//button[contains(text(), '確認取得驗證碼')]"}
2025-06-27 19:54:02,165 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,166 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,166 - DEBUG - Finished Request
2025-06-27 19:54:02,166 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//button[contains(text(), '重新產生')]"}
2025-06-27 19:54:02,174 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,174 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,174 - DEBUG - Finished Request
2025-06-27 19:54:02,174 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'xpath', 'value': "//button[contains(text(), '重新產生')]"}
2025-06-27 19:54:02,180 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:02,181 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,181 - DEBUG - Finished Request
2025-06-27 19:54:02,647 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:02,653 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:02,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:02,653 - DEBUG - Finished Request
2025-06-27 19:54:03,654 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:03,660 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:03,660 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:03,660 - DEBUG - Finished Request
2025-06-27 19:54:04,661 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:04,666 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:04,666 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:04,666 - DEBUG - Finished Request
2025-06-27 19:54:05,667 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:05,673 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:05,673 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:05,673 - DEBUG - Finished Request
2025-06-27 19:54:06,674 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:06,682 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:06,682 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:06,682 - DEBUG - Finished Request
2025-06-27 19:54:07,683 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:07,690 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:07,690 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,690 - DEBUG - Finished Request
2025-06-27 19:54:07,744 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[name*='captcha']"}
2025-06-27 19:54:07,763 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,763 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,764 - DEBUG - Finished Request
2025-06-27 19:54:07,764 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[name*='verify']"}
2025-06-27 19:54:07,774 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,774 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,774 - DEBUG - Finished Request
2025-06-27 19:54:07,774 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[id*='captcha']"}
2025-06-27 19:54:07,784 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,785 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,785 - DEBUG - Finished Request
2025-06-27 19:54:07,785 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[id*='verify']"}
2025-06-27 19:54:07,794 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,795 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,795 - DEBUG - Finished Request
2025-06-27 19:54:07,795 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[placeholder*='驗證']"}
2025-06-27 19:54:07,803 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,804 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,804 - DEBUG - Finished Request
2025-06-27 19:54:07,804 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[placeholder*='驗證碼']"}
2025-06-27 19:54:07,815 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,815 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,815 - DEBUG - Finished Request
2025-06-27 19:54:07,815 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[type='text'][maxlength='4']"}
2025-06-27 19:54:07,823 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,824 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,824 - DEBUG - Finished Request
2025-06-27 19:54:07,824 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[type='text'][maxlength='5']"}
2025-06-27 19:54:07,835 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,836 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,836 - DEBUG - Finished Request
2025-06-27 19:54:07,836 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/elements {'using': 'css selector', 'value': "input[type='text']"}
2025-06-27 19:54:07,848 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/elements HTTP/1.1" 200 0
2025-06-27 19:54:07,848 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.47"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.48"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.49"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.50"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.51"},{"element-6066-11e4-a52e-4f735466cecf":"f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.52"}]} | headers=HTTPHeaderDict({'Content-Length': '830', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,848 - DEBUG - Finished Request
2025-06-27 19:54:07,848 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* isDisplayed */return (function(){return (function(){var g=this||self;\nfunction aa(a){var b=typeof...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44'}]}
2025-06-27 19:54:07,855 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:54:07,855 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,855 - DEBUG - Finished Request
2025-06-27 19:54:07,856 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44/enabled {}
2025-06-27 19:54:07,864 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44/enabled HTTP/1.1" 200 0
2025-06-27 19:54:07,864 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,864 - DEBUG - Finished Request
2025-06-27 19:54:07,865 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': '/* isDisplayed */return (function(){return (function(){var g=this||self;\nfunction aa(a){var b=typeof...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44'}]}
2025-06-27 19:54:07,871 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:54:07,871 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,871 - DEBUG - Finished Request
2025-06-27 19:54:07,871 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44/enabled {}
2025-06-27 19:54:07,879 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/element/f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44/enabled HTTP/1.1" 200 0
2025-06-27 19:54:07,879 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,879 - DEBUG - Finished Request
2025-06-27 19:54:07,879 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': 'arguments[0].focus();', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44'}]}
2025-06-27 19:54:07,887 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:54:07,888 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,888 - DEBUG - Finished Request
2025-06-27 19:54:07,888 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': "arguments[0].value = '';", 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44'}]}
2025-06-27 19:54:07,895 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:54:07,895 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:07,896 - DEBUG - Finished Request
2025-06-27 19:54:08,097 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': 'arguments[0].value = arguments[1];', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44'}, '0228']}
2025-06-27 19:54:08,105 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:54:08,106 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:08,106 - DEBUG - Finished Request
2025-06-27 19:54:08,106 - DEBUG - POST http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync {'script': "\n                                    arguments[0].dispatchEvent(new Event('input', { bubbles: true }...", 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.B04D2B9D015279F6789708EA1FE8852E.d.4B4A7337E4CDF7F3655C308CEA9BF00B.e.44'}]}
2025-06-27 19:54:08,113 - DEBUG - http://localhost:56643 "POST /session/f12a4792854d46b91fc74e9c6ebca3ee/execute/sync HTTP/1.1" 200 0
2025-06-27 19:54:08,113 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:08,113 - DEBUG - Finished Request
2025-06-27 19:54:08,113 - INFO - 🎯 成功輸入驗證碼: 0228
2025-06-27 19:54:08,692 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:08,699 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:08,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:08,699 - DEBUG - Finished Request
2025-06-27 19:54:09,700 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:09,706 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:09,706 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:09,707 - DEBUG - Finished Request
2025-06-27 19:54:10,708 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:10,717 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:10,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:10,717 - DEBUG - Finished Request
2025-06-27 19:54:11,718 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:11,725 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:11,726 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:11,726 - DEBUG - Finished Request
2025-06-27 19:54:12,727 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:12,734 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:12,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:12,734 - DEBUG - Finished Request
2025-06-27 19:54:13,735 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:13,743 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:13,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:13,743 - DEBUG - Finished Request
2025-06-27 19:54:14,744 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:14,751 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:14,751 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:14,752 - DEBUG - Finished Request
2025-06-27 19:54:15,753 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:15,760 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:15,760 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:15,760 - DEBUG - Finished Request
2025-06-27 19:54:16,761 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:16,769 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:16,769 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:16,769 - DEBUG - Finished Request
2025-06-27 19:54:17,770 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:17,778 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:17,778 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:17,778 - DEBUG - Finished Request
2025-06-27 19:54:18,779 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:18,785 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:18,785 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:18,786 - DEBUG - Finished Request
2025-06-27 19:54:19,787 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:19,794 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:19,794 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:19,794 - DEBUG - Finished Request
2025-06-27 19:54:20,794 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:20,802 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:20,803 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:20,803 - DEBUG - Finished Request
2025-06-27 19:54:21,804 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:21,810 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:21,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:21,810 - DEBUG - Finished Request
2025-06-27 19:54:22,811 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:22,818 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:22,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:22,818 - DEBUG - Finished Request
2025-06-27 19:54:23,820 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:23,828 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:23,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:23,828 - DEBUG - Finished Request
2025-06-27 19:54:24,829 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:24,838 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:24,838 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:24,838 - DEBUG - Finished Request
2025-06-27 19:54:25,839 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:25,847 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:25,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:25,847 - DEBUG - Finished Request
2025-06-27 19:54:26,847 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:26,856 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:26,856 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:26,856 - DEBUG - Finished Request
2025-06-27 19:54:27,856 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:27,864 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:27,864 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:27,864 - DEBUG - Finished Request
2025-06-27 19:54:28,865 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:28,873 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:28,874 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:28,874 - DEBUG - Finished Request
2025-06-27 19:54:29,875 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:29,881 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:29,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:29,882 - DEBUG - Finished Request
2025-06-27 19:54:30,883 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:30,890 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:30,890 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:30,891 - DEBUG - Finished Request
2025-06-27 19:54:31,892 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:31,898 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:31,899 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:31,899 - DEBUG - Finished Request
2025-06-27 19:54:32,900 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:32,908 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:32,908 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:32,908 - DEBUG - Finished Request
2025-06-27 19:54:33,909 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:33,917 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:33,917 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:33,917 - DEBUG - Finished Request
2025-06-27 19:54:34,918 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:34,926 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:34,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:34,927 - DEBUG - Finished Request
2025-06-27 19:54:35,928 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:35,937 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:35,938 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:35,938 - DEBUG - Finished Request
2025-06-27 19:54:36,683 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:36,757 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:36,939 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:36,945 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:36,945 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:36,945 - DEBUG - Finished Request
2025-06-27 19:54:37,261 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:37,340 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:37,842 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:37,906 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:37,946 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:37,951 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:37,952 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:37,952 - DEBUG - Finished Request
2025-06-27 19:54:38,408 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:38,494 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:38,953 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:38,958 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 200 0
2025-06-27 19:54:38,958 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:38,958 - DEBUG - Finished Request
2025-06-27 19:54:38,997 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:39,059 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:39,561 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:39,713 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:39,959 - DEBUG - GET http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee/url {}
2025-06-27 19:54:39,962 - DEBUG - http://localhost:56643 "GET /session/f12a4792854d46b91fc74e9c6ebca3ee/url HTTP/1.1" 404 0
2025-06-27 19:54:39,962 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d059b5]\n\t(No symbol) [0x0x7ff7d7d2a9ca]\n\t(No symbol) [0x0x7ff7d7da05e5]\n\t(No symbol) [0x0x7ff7d7dc0b42]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:39,963 - DEBUG - Finished Request
2025-06-27 19:54:39,963 - DEBUG - DELETE http://localhost:56643/session/f12a4792854d46b91fc74e9c6ebca3ee {}
2025-06-27 19:54:39,993 - DEBUG - http://localhost:56643 "DELETE /session/f12a4792854d46b91fc74e9c6ebca3ee HTTP/1.1" 200 0
2025-06-27 19:54:39,993 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:54:39,994 - DEBUG - Finished Request
2025-06-27 19:54:40,215 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:40,274 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:40,776 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:40,836 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:41,338 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:41,414 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:41,916 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:41,966 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:42,470 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:42,539 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:43,042 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:43,098 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:43,601 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:43,662 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:44,164 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:44,213 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:44,716 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:44,773 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:45,275 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:45,361 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:45,865 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:45,930 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:46,432 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:46,502 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:47,005 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:47,062 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:47,565 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:47,637 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:48,140 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:48,212 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:48,715 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:48,778 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:49,279 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:49,342 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:49,845 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:49,906 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:50,411 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:50,467 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:50,970 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:51,050 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:51,554 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:51,618 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:52,122 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:52,170 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:52,674 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:52,748 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:53,251 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:53,308 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:53,811 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:53,875 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:54,380 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:54,436 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:54,941 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:55,018 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:55,522 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:55,602 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:56,105 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:56,186 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:56,690 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:56,750 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:57,254 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:57,328 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:57,832 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:57,908 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:58,410 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:58,469 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:58,972 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:59,037 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:54:59,540 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:54:59,591 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:00,094 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:00,167 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:00,670 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:00,740 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:01,243 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:01,307 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:01,810 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:01,866 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:02,368 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:02,417 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:02,920 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:02,978 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:03,480 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:03,542 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:04,044 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:04,110 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:04,613 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:04,677 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:05,181 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:05,250 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:05,753 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:05,825 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:06,330 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:06,401 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:06,905 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:06,970 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:07,474 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:07,541 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:08,044 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:08,126 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:08,629 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:08,695 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:09,198 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:09,240 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:09,742 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:09,800 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:10,302 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:10,352 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:10,855 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:10,926 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:11,429 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:11,503 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:12,005 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:12,049 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:12,551 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:12,611 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:13,114 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:13,172 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:13,675 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:13,746 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:14,250 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:14,299 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:14,803 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:14,878 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:15,381 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:15,438 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:15,942 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:15,998 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:16,501 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:16,561 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:17,063 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:17,117 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:17,620 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:17,686 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:18,188 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:18,254 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:18,758 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:18,825 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:19,328 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:19,376 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:19,879 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:19,946 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:20,449 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:20,503 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:21,006 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:21,062 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:21,565 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:21,642 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:22,146 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:22,205 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:22,708 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:22,748 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:23,252 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:23,311 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:23,813 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:23,869 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:24,371 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:24,430 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:24,933 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:25,006 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:25,510 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:25,595 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:26,097 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:26,162 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:26,663 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:26,737 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:27,239 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:27,311 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:27,814 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:27,876 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:28,378 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:28,432 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:28,934 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:28,990 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:29,492 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:29,535 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:30,037 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:30,079 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:30,581 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:30,646 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:31,148 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:31,208 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:31,711 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:31,772 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:32,275 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:32,355 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:32,857 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:32,909 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:33,412 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:33,479 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:33,982 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:34,037 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:34,540 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:34,599 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:35,101 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:35,156 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:35,659 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:35,725 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
2025-06-27 19:55:36,228 - DEBUG - Starting new HTTPS connection (1): wmc.kcg.gov.tw:443
2025-06-27 19:55:36,273 - DEBUG - https://wmc.kcg.gov.tw:443 "HEAD / HTTP/1.1" 302 0
