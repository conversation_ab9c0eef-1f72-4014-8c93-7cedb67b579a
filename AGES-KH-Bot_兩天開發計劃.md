# AGES-KH-Bot 兩天開發計劃

## 📅 **開發時程表**

**開發期間**: 2025-06-30 ~ 2025-07-01 (2天)  
**交付目標**: 第一版封裝交付  
**當前版本**: v1.4.0 → **目標版本**: v1.5.0  

---

## 🎯 **開發目標與範圍**

### **核心三本柱完成**:
1. ✅ **主程式** - mvp_grabber.py (iframe 雙層切換修復)
2. ✅ **RTT程式** - 精確時機控制 (已完成)
3. 🔄 **自然人模擬** - Cookie + UA + User Profile 統一管理

### **第一交付版需求**:
- 🔧 **單一封裝** - PyInstaller 打包成 .exe
- 🔐 **簡易擴散禁止** - 反編譯 + 代碼隱藏 + 啟動限制 + 密碼保護
- 📦 **跨電腦安裝解決方案** - 硬體指紋綁定

---

## 📂 **資料夾結構規劃**

### **開發環境**:
```
AGES-KH-Bot/                    # 當前開發資料夾
├── mvp_grabber.py              # 主程式
├── independent_tracking_tool.py # 測試工具
├── rtt_*.py                    # RTT 相關模組
├── human_simulation.py         # 新增：自然人模擬模組
└── 其他開發檔案...
```

### **交付環境**:
```
AGES-KH-Bot-Release/            # 新建交付資料夾
├── dist/                       # 封裝後的執行檔
│   └── AGES-KH-Bot.exe        # 最終交付檔案
├── build/                      # 建置暫存檔案
├── release_notes.md            # 版本說明
└── deployment_guide.md         # 部署指南
```

---

## 📅 **Day 1 開發計劃 (2025-06-30)**

### **上午 (09:00-12:00): 核心功能修復**

#### **Task 1.1: iframe 雙層切換修復** ⏰ 2小時
- **目標**: 修復 mvp_grabber.py 中的 iframe 切換邏輯
- **具體工作**:
  ```python
  # 修改現有的單層切換
  driver.switch_to.frame(iframes[0])  # ❌ 舊版本
  
  # 改為雙層切換
  def switch_to_edit_dialog():
      driver.switch_to.frame(0)  # 第一層：訂單清單
      driver.switch_to.frame(0)  # 第二層：編輯彈窗
  ```
- **驗證方式**: 使用 independent_tracking_tool.py 驗證能找到送出按鈕
- **完成標準**: 主程式能成功定位到編輯彈窗中的送出按鈕

#### **Task 1.2: 第三層檢測驗證** ⏰ 1小時
- **目標**: 使用 independent_tracking_tool.py 確認結果訊息位置
- **具體工作**:
  - 執行完整送出流程
  - 在結果出現時立即掃描
  - 確認 "送出失敗"、"已滿"、"成功" 訊息的確切位置
- **決策點**: 確定是否需要第三層 iframe 切換

### **下午 (13:00-18:00): 自然人模擬模組開發**

#### **Task 1.3: 創建 human_simulation.py 模組** ⏰ 3小時
- **目標**: 統一管理 Cookie + UA + User Profile
- **模組結構**:
  ```python
  class HumanSimulation:
      def __init__(self):
          self.ua_manager = UserAgentManager()
          self.profile_manager = ProfileManager()
          self.cookie_manager = CookieManager()
      
      def setup_natural_browser(self, driver):
          """設置自然人類瀏覽器環境"""
          pass
      
      def simulate_human_behavior(self, driver):
          """模擬人類瀏覽行為"""
          pass
  ```

#### **Task 1.4: Cookie 管理策略實現** ⏰ 2小時
- **目標**: 實現智能 Cookie 管理
- **功能需求**:
  ```python
  class CookieManager:
      def preserve_session_cookies(self):
          """保留會話 Cookie"""
          pass
      
      def clear_tracking_cookies(self):
          """清理追蹤 Cookie"""
          pass
      
      def simulate_normal_cookies(self):
          """模擬正常 Cookie 行為"""
          pass
  ```

### **晚上 (19:00-21:00): 整合測試**

#### **Task 1.5: 模組整合** ⏰ 2小時
- **目標**: 將 human_simulation.py 整合到 mvp_grabber.py
- **整合點**:
  - 瀏覽器啟動時設置自然人環境
  - 操作過程中模擬人類行為
- **測試**: 完整流程測試，確保功能正常

---

## 📅 **Day 2 開發計劃 (2025-07-01)**

### **上午 (09:00-12:00): 封裝準備**

#### **Task 2.1: 版本整理與清理** ⏰ 1小時
- **目標**: 準備乾淨的交付版本
- **具體工作**:
  - 更新版本號到 v1.5.0
  - 清理調試代碼和註釋
  - 整理依賴項目清單
  - 創建 AGES-KH-Bot-Release/ 資料夾

#### **Task 2.2: 授權保護系統開發** ⏰ 2小時
- **目標**: 實現簡易擴散禁止機制
- **功能需求**:
  ```python
  class LicenseManager:
      def __init__(self):
          self.hardware_fingerprint = self.get_hardware_id()
          self.usage_counter = 0
          self.max_usage = 100  # 啟動次數限制
      
      def verify_license(self, password):
          """驗證授權和密碼"""
          pass
      
      def check_hardware_binding(self):
          """檢查硬體綁定"""
          pass
      
      def update_usage_counter(self):
          """更新使用次數"""
          pass
  ```

### **下午 (13:00-18:00): 封裝與測試**

#### **Task 2.3: PyInstaller 封裝** ⏰ 2小時
- **目標**: 創建單一 .exe 檔案
- **封裝配置**:
  ```bash
  # PyInstaller 命令
  pyinstaller --onefile \
              --noconsole \
              --add-data "chromedriver.exe;." \
              --add-data "orders;orders" \
              --add-data "logs;logs" \
              --name "AGES-KH-Bot" \
              --icon "icon.ico" \
              mvp_grabber.py
  ```

#### **Task 2.4: 代碼保護與混淆** ⏰ 1.5小時
- **目標**: 實現反編譯保護
- **保護措施**:
  - 使用 PyArmor 進行代碼混淆
  - 添加反調試檢測
  - 字串加密處理
  ```bash
  # PyArmor 混淆
  pyarmor obfuscate --recursive mvp_grabber.py
  ```

#### **Task 2.5: 跨電腦安裝解決方案** ⏰ 1.5小時
- **目標**: 解決不同電腦安裝問題
- **解決方案**:
  ```python
  class HardwareBinding:
      def generate_machine_id(self):
          """生成機器唯一識別碼"""
          # MAC Address + CPU ID + 主機板序號
          pass
      
      def create_license_file(self, machine_id, password):
          """為特定機器創建授權檔案"""
          pass
      
      def validate_machine_license(self):
          """驗證機器授權"""
          pass
  ```

### **晚上 (19:00-21:00): 最終測試與交付**

#### **Task 2.6: 完整測試** ⏰ 1小時
- **測試項目**:
  - ✅ 封裝後的 .exe 能正常啟動
  - ✅ 授權驗證功能正常
  - ✅ 硬體綁定機制有效
  - ✅ 使用次數限制正常
  - ✅ 核心搶單功能完整

#### **Task 2.7: 交付文檔準備** ⏰ 1小時
- **文檔清單**:
  - `release_notes.md` - 版本說明
  - `deployment_guide.md` - 部署指南
  - `user_manual.md` - 使用手冊
  - `license_guide.md` - 授權說明

---

## 🔧 **技術實現細節**

### **硬體指紋方案**:
```python
import uuid
import psutil
import hashlib

def get_hardware_fingerprint():
    """獲取硬體指紋"""
    # MAC Address
    mac = ':'.join(['{:02x}'.format((uuid.getnode() >> i) & 0xff) 
                   for i in range(0,8*6,8)][::-1])
    
    # CPU 信息
    cpu_info = psutil.cpu_count()
    
    # 組合生成唯一 ID
    combined = f"{mac}_{cpu_info}"
    return hashlib.sha256(combined.encode()).hexdigest()[:16]
```

### **授權檔案格式**:
```json
{
    "machine_id": "abc123def456",
    "password_hash": "sha256_hash",
    "max_usage": 100,
    "current_usage": 0,
    "created_date": "2025-06-30",
    "expire_date": "2025-12-31"
}
```

### **啟動驗證流程**:
```python
def startup_verification():
    """程式啟動時的驗證流程"""
    # 1. 檢查授權檔案
    # 2. 驗證硬體指紋
    # 3. 檢查使用次數
    # 4. 要求輸入密碼
    # 5. 更新使用計數
    pass
```

---

## 📊 **開發里程碑**

### **Day 1 完成標準**:
- [x] iframe 雙層切換修復完成
- [x] 第三層檢測方案確定
- [x] human_simulation.py 模組完成
- [x] Cookie 管理功能實現
- [x] 整合測試通過

### **Day 2 完成標準**:
- [x] 授權保護系統完成
- [x] PyInstaller 封裝成功
- [x] 代碼混淆保護完成
- [x] 跨電腦安裝方案實現
- [x] 完整測試通過
- [x] 交付文檔準備完成

---

## 🚨 **風險控制**

### **時間風險**:
- **風險**: 2天時間緊迫
- **應對**: 優先核心功能，次要功能可延後

### **技術風險**:
- **風險**: 封裝可能遇到相容性問題
- **應對**: 準備多種封裝方案 (PyInstaller + cx_Freeze)

### **測試風險**:
- **風險**: 測試時間不足
- **應對**: 重點測試核心流程，建立快速測試腳本

---

## 📋 **交付清單**

### **最終交付物**:
1. **AGES-KH-Bot.exe** - 主程式執行檔
2. **license.json** - 授權檔案模板
3. **deployment_guide.md** - 部署指南
4. **user_manual.md** - 使用手冊
5. **release_notes.md** - 版本說明

### **客戶部署需求**:
- Windows 10/11 系統
- 無需安裝 Python 環境
- 需要提供機器硬體信息用於授權綁定
- 需要設定啟動密碼

---

**開發負責人**: AI Assistant & User  
**計劃版本**: v1.0  
**最後更新**: 2025-06-30
