# Deployment Security Module for AGES-KH
# 處理客戶端部署的安全認證和遠程回報機制
# Version: 1.0
# Author: <PERSON>

import hashlib
import hmac
import json
import os
import platform
import socket
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import requests
import base64

class DeploymentSecurity:
    def __init__(self, config_file: str = "deployment_config.json"):
        self.config_file = config_file
        self.device_id = self._generate_device_id()
        self.config = self._load_config()
        self.authenticated = False
        
    def _generate_device_id(self) -> str:
        """生成唯一的設備 ID"""
        # 基於硬體資訊生成穩定的設備 ID
        mac = uuid.getnode()
        hostname = socket.gethostname()
        platform_info = platform.platform()
        
        device_string = f"{mac}-{hostname}-{platform_info}"
        device_hash = hashlib.sha256(device_string.encode()).hexdigest()
        return device_hash[:16].upper()
        
    def _load_config(self) -> Dict[str, Any]:
        """載入部署配置"""
        default_config = {
            "server_url": "https://your-server.com/api",
            "device_name": f"AGES-Device-{self.device_id}",
            "auth_token": "",
            "reporting_enabled": True,
            "reporting_interval": 3600,  # 1 小時
            "max_daily_reports": 24
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合併預設配置
                    default_config.update(config)
            except Exception as e:
                print(f"[WARNING] 載入配置失敗: {e}")
                
        return default_config
        
    def _save_config(self):
        """儲存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"[ERROR] 儲存配置失敗: {e}")
            
    def authenticate_device(self, activation_code: str) -> bool:
        """設備認證"""
        try:
            # 準備認證資料
            auth_data = {
                "device_id": self.device_id,
                "activation_code": activation_code,
                "device_info": {
                    "hostname": socket.gethostname(),
                    "platform": platform.platform(),
                    "python_version": platform.python_version(),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # 發送認證請求
            response = requests.post(
                f"{self.config['server_url']}/authenticate",
                json=auth_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.config["auth_token"] = result.get("token")
                    self.config["device_name"] = result.get("device_name", self.config["device_name"])
                    self._save_config()
                    self.authenticated = True
                    print(f"[INFO] 設備認證成功: {self.config['device_name']}")
                    return True
                    
            print(f"[ERROR] 認證失敗: {response.text}")
            return False
            
        except Exception as e:
            print(f"[ERROR] 認證過程發生錯誤: {e}")
            return False
            
    def verify_license(self) -> bool:
        """驗證授權"""
        if not self.config.get("auth_token"):
            print("[ERROR] 未找到認證令牌，請先進行設備認證")
            return False
            
        try:
            headers = {
                "Authorization": f"Bearer {self.config['auth_token']}",
                "Device-ID": self.device_id
            }
            
            response = requests.get(
                f"{self.config['server_url']}/verify",
                headers=headers,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("valid"):
                    self.authenticated = True
                    print("[INFO] 授權驗證成功")
                    return True
                    
            print(f"[ERROR] 授權驗證失敗: {response.text}")
            return False
            
        except Exception as e:
            print(f"[ERROR] 授權驗證過程發生錯誤: {e}")
            return False
            
    def report_execution_data(self, execution_data: Dict[str, Any]) -> bool:
        """回報執行資料"""
        if not self.authenticated or not self.config.get("reporting_enabled"):
            return False
            
        try:
            # 準備回報資料
            report_data = {
                "device_id": self.device_id,
                "device_name": self.config["device_name"],
                "timestamp": datetime.now().isoformat(),
                "execution_data": execution_data,
                "system_info": {
                    "cpu_count": os.cpu_count(),
                    "platform": platform.platform(),
                    "python_version": platform.python_version()
                }
            }
            
            # 加密敏感資料
            encrypted_data = self._encrypt_sensitive_data(report_data)
            
            headers = {
                "Authorization": f"Bearer {self.config['auth_token']}",
                "Device-ID": self.device_id,
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"{self.config['server_url']}/report",
                json=encrypted_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print("[INFO] 執行資料回報成功")
                return True
            else:
                print(f"[WARNING] 資料回報失敗: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[WARNING] 資料回報過程發生錯誤: {e}")
            return False
            
    def _encrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """加密敏感資料"""
        # 簡單的 base64 編碼 (實際部署時應使用更強的加密)
        sensitive_fields = ["execution_data"]
        
        encrypted_data = data.copy()
        for field in sensitive_fields:
            if field in encrypted_data:
                json_str = json.dumps(encrypted_data[field], ensure_ascii=False)
                encoded = base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
                encrypted_data[field] = encoded
                
        return encrypted_data
        
    def create_activation_prompt(self) -> str:
        """建立啟動提示"""
        return f"""
╔══════════════════════════════════════════════════════════════╗
║                    AGES-KH 系統啟動認證                      ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  設備 ID: {self.device_id}                           ║
║  設備名稱: {socket.gethostname()}                            ║
║                                                              ║
║  請輸入授權啟動碼以繼續使用系統                              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""

    def check_daily_usage_limit(self) -> bool:
        """檢查每日使用限制"""
        # 這裡可以實作每日使用次數限制
        # 例如：檢查今日已執行次數，防止過度使用
        return True
        
    def get_system_fingerprint(self) -> Dict[str, str]:
        """取得系統指紋"""
        return {
            "device_id": self.device_id,
            "hostname": socket.gethostname(),
            "platform": platform.platform(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "mac_address": ':'.join(['{:02x}'.format((uuid.getnode() >> ele) & 0xff) 
                                   for ele in range(0,8*6,8)][::-1])
        }

def require_authentication(func):
    """裝飾器：要求認證後才能執行"""
    def wrapper(self, *args, **kwargs):
        if not hasattr(self, 'security') or not self.security.authenticated:
            print("[ERROR] 系統未認證，無法執行此功能")
            return None
        return func(self, *args, **kwargs)
    return wrapper

# 使用範例
if __name__ == "__main__":
    security = DeploymentSecurity()
    
    # 顯示啟動提示
    print(security.create_activation_prompt())
    
    # 模擬認證流程
    activation_code = input("請輸入啟動碼: ").strip()
    
    if activation_code:
        if security.authenticate_device(activation_code):
            print("[INFO] 認證成功，系統已啟動")
            
            # 模擬執行資料回報
            sample_data = {
                "execution_time": datetime.now().isoformat(),
                "orders_processed": 3,
                "success_count": 2,
                "rtt_average": 125.5
            }
            
            security.report_execution_data(sample_data)
        else:
            print("[ERROR] 認證失敗，系統無法啟動")
    else:
        print("[ERROR] 未輸入啟動碼")
