# AGES-KH-Bot Issue List

## 📋 **問題追蹤清單**

**最後更新**: 2025-06-30  
**項目狀態**: 開發中  

---

## 🔴 **高優先級 Issues**

### **Issue #001: Cookie 管理策略**
- **狀態**: 🔴 未解決
- **優先級**: 高
- **發現日期**: 2025-06-30
- **描述**: 
  - 目前只處理了 UA 和 user profile，但沒有處理 cookie 管理
  - 完全禁止 cookie 與人類習慣不符，可能被檢測為機器人
  - 需要實現合理的 cookie 策略來模擬真實用戶行為

- **技術細節**:
  ```python
  # 當前問題：可能完全禁用 cookie
  options.add_argument("--disable-cookies")  # 不自然
  
  # 需要實現：智能 cookie 管理
  # 1. 保留必要的會話 cookie
  # 2. 清理追蹤 cookie
  # 3. 模擬正常的 cookie 行為
  ```

- **影響範圍**: 反檢測機制
- **解決方案建議**:
  - [ ] 研究政府網站的 cookie 使用模式
  - [ ] 實現選擇性 cookie 管理
  - [ ] 模擬真實用戶的 cookie 行為
  - [ ] 測試不同 cookie 策略的檢測風險

---

### **Issue #002: 生產環境交付封裝**
- **狀態**: 🔴 未解決
- **優先級**: 高
- **發現日期**: 2025-06-30
- **描述**:
  - 需要將整個系統封裝成單一可執行檔案
  - 包含所有依賴項目，客戶端無需安裝 Python 環境
  - 實現授權控制機制，限制部署台數和使用次數
  - 符合合約條款的交付要求

- **技術細節**:
  ```python
  # 封裝需求
  1. 單一 .exe 檔案 (PyInstaller/cx_Freeze)
  2. 包含所有依賴 (Selenium, ChromeDriver, Python Runtime)
  3. 授權驗證系統
  4. 使用次數/台數限制
  5. 防止逆向工程

  # 授權機制
  - 硬體指紋綁定 (MAC Address, CPU ID, 主機板序號)
  - 使用次數計數器
  - 到期時間控制
  - 遠端授權驗證 (可選)
  ```

- **影響範圍**: 產品交付、客戶部署
- **解決方案建議**:
  - [ ] 選擇封裝工具 (PyInstaller vs cx_Freeze)
  - [ ] 設計授權驗證架構
  - [ ] 實現硬體指紋識別
  - [ ] 開發使用次數限制機制
  - [ ] 測試不同環境的相容性
  - [ ] 加入防逆向工程保護
  - [ ] 建立客戶端部署文檔

---

### **Issue #003: 第三層 iframe 結果檢測**
- **狀態**: 🔴 未解決
- **優先級**: 高
- **發現日期**: 2025-06-30
- **描述**:
  - 送出按鈕點擊後的結果訊息可能在第三層 iframe 中
  - 目前只能檢測到第二層 iframe (編輯彈窗)
  - 無法檢測 "送出失敗"、"已滿"、"成功" 等關鍵訊息

- **技術細節**:
  ```
  當前檢測層級：
  iframe 0 > iframe 0 (編輯彈窗) ✅ 已解決

  需要檢測：
  iframe 0 > iframe 0 > iframe 0 (結果訊息) ❌ 未實現
  ```

- **影響範圍**: 搶單成功/失敗判斷
- **解決方案建議**:
  - [ ] 修改掃描邏輯支援第三層 iframe
  - [ ] 實現送出後的動態檢測
  - [ ] 添加結果訊息的關鍵字匹配
  - [ ] 測試不同結果狀態的檢測

---

### **Issue #003: 動態監控時機控制**
- **狀態**: 🟡 部分解決
- **優先級**: 中高
- **發現日期**: 2025-06-30
- **描述**: 
  - 三重監控系統在編輯彈窗打開前執行，錯過關鍵內容
  - 自動監控無法跟上動態內容的變化
  - 需要更智能的時機控制機制

- **技術細節**:
  ```
  問題流程：
  1. 啟動三重監控 (編輯彈窗還沒打開)
  2. 用戶點擊編輯按鈕
  3. 編輯彈窗打開 (監控已經錯過)
  
  理想流程：
  1. 檢測到編輯按鈕點擊
  2. 等待編輯彈窗載入
  3. 自動啟動針對彈窗的監控
  ```

- **影響範圍**: 監控系統效果
- **解決方案建議**:
  - [x] 手動時機控制 (已實現)
  - [ ] 智能檢測彈窗出現
  - [ ] 自動切換監控目標
  - [ ] 事件驅動的監控啟動

---

## 🟡 **中優先級 Issues**

### **Issue #004: 反檢測機制完善**
- **狀態**: 🟡 部分解決
- **優先級**: 中
- **發現日期**: 2025-06-30
- **描述**: 
  - 目前有基本的 UA 和 profile 設定
  - 缺少更全面的反檢測策略
  - 需要模擬更真實的人類行為模式

- **技術細節**:
  ```python
  # 已實現
  - User-Agent 設定
  - 瀏覽器 profile 管理
  
  # 待實現
  - Cookie 管理策略
  - 滑鼠軌跡模擬
  - 隨機延遲機制
  - 視窗大小和位置
  - 字體和語言設定
  ```

- **影響範圍**: 整體系統安全性
- **解決方案建議**:
  - [ ] 實現滑鼠軌跡模擬
  - [ ] 添加隨機行為模式
  - [ ] 完善瀏覽器指紋偽裝
  - [ ] 測試檢測規避效果

---

### **Issue #005: 錯誤處理和重試機制**
- **狀態**: 🟡 部分解決
- **優先級**: 中
- **發現日期**: 2025-06-30
- **描述**: 
  - 基本的異常捕獲已實現
  - 缺少智能重試機制
  - 需要針對不同錯誤類型的處理策略

- **技術細節**:
  ```python
  # 需要處理的錯誤類型
  - 網路連線錯誤 → 重試
  - 元素定位失敗 → 重新掃描
  - 驗證碼錯誤 → 重新輸入
  - 會話超時 → 重新登入
  - 系統維護 → 等待重試
  ```

- **影響範圍**: 系統穩定性
- **解決方案建議**:
  - [ ] 實現分類錯誤處理
  - [ ] 添加智能重試邏輯
  - [ ] 設計降級處理機制
  - [ ] 完善日誌記錄

---

### **Issue #006: 性能優化**
- **狀態**: 🟢 已優化
- **優先級**: 中
- **發現日期**: 2025-06-30
- **描述**: 
  - 監控頻率已針對高性能 CPU 優化
  - 記憶體使用需要持續監控
  - 長時間運行的穩定性待驗證

- **技術細節**:
  ```python
  # 當前設定 (針對 AMD AI9 HX370)
  EVENT_COLLECTION_INTERVAL = 50   # 毫秒
  WEBDRIVER_MONITOR_INTERVAL = 500 # 毫秒
  PAGE_CHANGE_CHECK_INTERVAL = 1000 # 毫秒
  ```

- **影響範圍**: 系統性能
- **解決方案建議**:
  - [x] 高頻率監控設定 (已完成)
  - [ ] 記憶體洩漏檢測
  - [ ] 長時間運行測試
  - [ ] 資源使用監控

---

## 🟢 **低優先級 Issues**

### **Issue #007: 日誌系統改進**
- **狀態**: 🟢 基本完成
- **優先級**: 低
- **發現日期**: 2025-06-30
- **描述**: 
  - 基本日誌功能已實現
  - 可考慮添加更詳細的分類和過濾
  - 日誌檔案管理和輪轉

- **解決方案建議**:
  - [ ] 日誌等級分類
  - [ ] 日誌檔案輪轉
  - [ ] 結構化日誌格式
  - [ ] 日誌查詢和分析工具

---

### **Issue #008: 使用者介面優化**
- **狀態**: 🟢 基本完成
- **優先級**: 低
- **發現日期**: 2025-06-30
- **描述**: 
  - 基本 GUI 功能完整
  - 可考慮添加更多便利功能
  - 使用體驗優化

- **解決方案建議**:
  - [ ] 快捷鍵支援
  - [ ] 設定檔案管理
  - [ ] 操作歷史記錄
  - [ ] 主題和外觀設定

---

## 📊 **Issue 統計**

### **按狀態分類**:
- 🔴 **未解決**: 3 個
- 🟡 **部分解決**: 3 個
- 🟢 **已解決/低優先級**: 3 個

### **按優先級分類**:
- **高優先級**: 4 個
- **中優先級**: 3 個
- **低優先級**: 2 個

---

## 🎯 **下一步行動計劃**

### **立即處理 (本週)**:
1. **Issue #001**: 設計 Cookie 管理策略
2. **Issue #002**: 生產環境交付封裝設計
3. **Issue #003**: 實現第三層 iframe 檢測

### **短期處理 (下週)**:
4. **Issue #004**: 改進動態監控時機
5. **Issue #005**: 完善反檢測機制

### **中期處理 (下個月)**:
6. **Issue #006**: 強化錯誤處理
7. **Issue #007**: 性能監控和優化
8. **Issue #002**: 完成授權系統開發和測試

---

## 📝 **Issue 模板**

### **新 Issue 格式**:
```markdown
### **Issue #XXX: [問題標題]**
- **狀態**: 🔴/🟡/🟢 [狀態]
- **優先級**: 高/中/低
- **發現日期**: YYYY-MM-DD
- **描述**: [問題詳細描述]
- **技術細節**: [技術實現細節]
- **影響範圍**: [影響的功能模組]
- **解決方案建議**:
  - [ ] [具體解決步驟1]
  - [ ] [具體解決步驟2]
```

---

## 🔄 **更新記錄**

- **2025-06-30**: 初始版本，添加 8 個核心 Issues
- **待更新**: 根據開發進度持續更新狀態

---

**維護者**: AI Assistant & User  
**項目**: AGES-KH-Bot  
**版本**: v1.0
