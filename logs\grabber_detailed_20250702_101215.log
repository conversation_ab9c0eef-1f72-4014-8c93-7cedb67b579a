2025-07-02 10:12:15,528 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_101215.log
2025-07-02 10:12:34,213 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 10:12:34,214 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 10:12:34,879 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-02 10:12:34,880 - DEBUG - chromedriver not found in PATH
2025-07-02 10:12:34,880 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 10:12:34,880 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 10:12:34,880 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-02 10:12:34,880 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 10:12:34,880 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 10:12:34,881 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 10:12:34,881 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 10:12:34,886 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 10636 using 0 to output -3
2025-07-02 10:12:35,419 - DEBUG - POST http://localhost:50684/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 10:12:35,420 - DEBUG - Starting new HTTP connection (1): localhost:50684
2025-07-02 10:12:35,967 - DEBUG - http://localhost:50684 "POST /session HTTP/1.1" 200 0
2025-07-02 10:12:35,967 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir10636_1547323861"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:50689"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"b00a184b93da157de2e73d69efd08150"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:12:35,967 - DEBUG - Finished Request
2025-07-02 10:12:35,968 - DEBUG - POST http://localhost:50684/session/b00a184b93da157de2e73d69efd08150/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 10:12:36,864 - DEBUG - http://localhost:50684 "POST /session/b00a184b93da157de2e73d69efd08150/url HTTP/1.1" 200 0
2025-07-02 10:12:36,864 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:12:36,865 - DEBUG - Finished Request
2025-07-02 10:12:36,865 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 10:12:36,866 - DEBUG - POST http://localhost:50684/session/b00a184b93da157de2e73d69efd08150/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 10:12:36,875 - DEBUG - http://localhost:50684 "POST /session/b00a184b93da157de2e73d69efd08150/execute/sync HTTP/1.1" 200 0
2025-07-02 10:12:36,875 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:12:36,875 - DEBUG - Finished Request
2025-07-02 10:12:36,876 - INFO - ✅ 瀏覽器事件監控已啟動
