#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試截圖功能
Simple Screenshot Test
"""

import os
import sys
import time
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_screenshot_only():
    """只測試截圖功能"""
    print("📸 測試截圖功能")
    print("=" * 40)
    
    try:
        # 導入主程式模組
        from mvp_grabber import start_browser
        
        print("✅ 成功導入模組")
        
        # 測試任務配置
        test_task = {
            'browser': 'chrome',
            'order_id': 'TEST',
            'trigger_time': '09:30:00.001',
            'model': 'A'
        }
        
        print(f"📋 測試任務: {test_task}")
        
        # 啟動瀏覽器
        print("\n🌐 啟動瀏覽器...")
        if start_browser(test_task):
            print("✅ 瀏覽器啟動成功")
            
            # 直接測試截圖功能
            print("\n📸 測試截圖功能...")
            
            from mvp_grabber import driver
            
            if driver:
                # 創建截圖目錄
                os.makedirs("screenshots", exist_ok=True)
                
                # 生成截圖
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                screenshot_path = f"screenshots/test_screenshot_{timestamp}.png"
                
                try:
                    driver.save_screenshot(screenshot_path)
                    print(f"✅ 截圖成功: {screenshot_path}")
                    
                    # 檢查文件是否存在
                    if os.path.exists(screenshot_path):
                        file_size = os.path.getsize(screenshot_path)
                        print(f"📊 截圖文件大小: {file_size} bytes")
                        
                        if file_size > 0:
                            print("✅ 截圖功能正常工作")
                        else:
                            print("❌ 截圖文件為空")
                    else:
                        print("❌ 截圖文件未生成")
                        
                except Exception as e:
                    print(f"❌ 截圖失敗: {e}")
                
                # 測試事件監控
                print("\n📊 測試事件監控...")
                try:
                    from mvp_grabber import get_browser_event_log
                    event_log = get_browser_event_log()
                    
                    if event_log:
                        print(f"✅ 事件監控正常，獲取到 {len(event_log)} 個事件")
                    else:
                        print("⚠️ 事件監控未獲取到事件（可能正常，因為沒有用戶操作）")
                        
                except Exception as e:
                    print(f"❌ 事件監控測試失敗: {e}")
                
                print("\n🎯 基礎功能測試完成！")
                print("瀏覽器將保持開啟 10 秒後自動關閉...")
                
                # 等待 10 秒
                for i in range(10, 0, -1):
                    print(f"⏰ {i} 秒後關閉...")
                    time.sleep(1)
                
                # 關閉瀏覽器
                try:
                    driver.quit()
                    print("✅ 瀏覽器已關閉")
                except:
                    pass
                    
            else:
                print("❌ 瀏覽器驅動未初始化")
                return False
                
        else:
            print("❌ 瀏覽器啟動失敗")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        return False
    
    return True

def main():
    """主函數"""
    print("🧪 AGES-KH-Bot 截圖功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    try:
        success = test_screenshot_only()
        
        if success:
            print("\n✅ 截圖測試完成")
            
            # 檢查截圖目錄
            if os.path.exists("screenshots"):
                screenshots = [f for f in os.listdir("screenshots") if f.endswith('.png')]
                if screenshots:
                    print(f"📸 生成的截圖文件:")
                    for screenshot in screenshots:
                        file_path = os.path.join("screenshots", screenshot)
                        file_size = os.path.getsize(file_path)
                        print(f"  - {screenshot} ({file_size} bytes)")
                else:
                    print("❌ 沒有生成截圖文件")
            else:
                print("❌ 截圖目錄不存在")
        else:
            print("\n❌ 截圖測試失敗")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷測試")
    except Exception as e:
        print(f"\n❌ 測試過程發生未預期錯誤: {e}")
    
    print("\n測試結束")

if __name__ == "__main__":
    main()
