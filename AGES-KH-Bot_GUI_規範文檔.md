# AGES-KH-Bot GUI 規範文檔

## 📋 文檔信息
- **創建日期**: 2025-07-01
- **版本**: v1.5.0 正式規範
- **狀態**: 正式生效
- **基於版本**: v1.5.0-dev05 測試結果

## ⚠️ **重要規範**

### 🚫 **禁止任意增刪 GUI**
1. **嚴禁隨意新增 GUI** - 所有新 GUI 必須經過完整設計評估
2. **嚴禁隨意刪除 GUI** - 已確認的 GUI 不得任意廢棄
3. **變更需要審核** - 任何 GUI 變更都必須經過用戶確認
4. **版本控制管理** - 所有 GUI 變更都要記錄版本和原因

### 📊 **GUI 編號規範**
- **GUI#01 ~ GUI#11** - 已分配編號，不得重複使用
- **新增 GUI** - 必須使用 GUI#12 以後的編號
- **廢棄 GUI** - 編號保留，標註廢棄原因，不得重複使用

## 🎯 **當前 GUI 清單 (v1.5.0)**

### **✅ 已確認實作的 GUI**

#### **GUI#01 - 觸發時間設定**
- **類型**: `simpledialog.askstring`
- **功能**: 詢問用戶輸入觸發時間
- **按鈕數量**: 2個 (確定、取消)
- **跳轉邏輯**: 確定 → 返回主程式 | 取消 → 使用預設值
- **狀態**: ✅ 已確認，不得修改

#### **GUI#02 - 準備提示視窗**
- **類型**: 完整視窗 (400x180)
- **功能**: 顯示即將執行的任務，最後確認
- **按鈕數量**: 2個
  - `[啟動瀏覽器]` → 跳轉到 GUI#05
  - `[取消]` → 關閉程式
- **任務顯示**: 最多3筆任務 (平台限制)
- **狀態**: ✅ 已優化確認，不得修改

#### **GUI#04 - 主搶單程式 (GrabberGUI)**
- **類型**: 完整視窗 (主管理介面)
- **功能**: 觸發時間設定、RTT設定、任務管理、開始執行
- **按鈕數量**: 多個 (參考 v1.5.X 主程式)
- **跳轉邏輯**: 開始執行 → GUI#02
- **狀態**: ✅ 參考主程式，不再修改原型

#### **GUI#05 - 操作指南**
- **類型**: 完整視窗 (帶滾動條)
- **功能**: 顯示操作步驟和時間資訊
- **按鈕數量**: 2個
  - `[✅ 準備完成]` → 觸發搶單流程，跳轉到 GUI#09
  - `[❌ 取消操作]` → 關閉程式
- **狀態**: ✅ 已優化確認，不得修改

#### **GUI#09 - 驗證碼輸入提醒**
- **類型**: 完整視窗 (帶狀態欄)
- **功能**: 提醒用戶在修改進廠確認單中輸入驗證碼
- **按鈕數量**: 2個
  - `[已輸入驗證碼-正式送單]` → 跳轉到 GUI#10 (正式模式)
  - `[已輸入驗證碼-模擬送單]` → 跳轉到 GUI#10 (模擬模式)
- **狀態欄**: 顯示定位狀態、檢測結果、元素統計
- **狀態**: ✅ 已確認，不得修改

### **📅  計劃實作的 GUI**

#### **GUI#10 - 等待觸發時間**
- **計劃版本**: v1.6.0
- **類型**: 完整視窗 (實時更新)
- **功能**: 顯示倒數計時、系統準備狀態、執行進度
- **按鈕數量**: 2個
  - `[緊急取消執行]` → 中止搶單，跳轉到錯誤處理
  - `[最小化視窗]` → 縮小視窗，繼續執行
- **跳轉邏輯**: 觸發時間到達 → 自動跳轉到 GUI#11
- **狀態**: 📅 v1.6.0 計劃實作

#### **GUI#11 - 執行結果**
- **計劃版本**: v1.7.0
- **類型**: 完整視窗
- **功能**: 顯示搶單執行結果、時間分析、執行摘要
- **按鈕數量**: 2個
  - `[查看詳細日誌]` → 開啟日誌檔案
  - `[關閉程式]` → 結束程式
- **跳轉邏輯**: 程式生命週期結束
- **狀態**: 📅 v1.7.0 計劃實作

### **❌ 已廢棄的 GUI**

#### **GUI#03 - 登入提示**
- **廢棄原因**: 功能併入 GUI#05
- **廢棄版本**: v1.5.0
- **編號狀態**: 保留，不得重複使用

#### **GUI#06 - 頁面確認對話框**
- **廢棄原因**: 功能併入 GUI#05
- **廢棄版本**: v1.5.0
- **編號狀態**: 保留，不得重複使用

#### **GUI#07 - 準備完成確認**
- **廢棄原因**: 與 GUI#09 功能重複
- **廢棄版本**: v1.5.0
- **編號狀態**: 保留，不得重複使用

#### **GUI#08 - 驗證碼輸入**
- **廢棄原因**: 改為 GUI#09 驗證碼提醒模式
- **廢棄版本**: v1.5.0
- **編號狀態**: 保留，不得重複使用

## 🔄 **GUI 流程圖**

### **完整流程 (v1.5.0 當前)**
```
程式啟動
    ↓
GUI#04 (主管理介面)
    ↓ 設定完成，點擊「開始執行」
GUI#02 (最後確認)
    ↓ 點擊「啟動瀏覽器」
GUI#05 (操作指南)
    ↓ 點擊「準備完成」
程式自動點擊編輯按鈕
    ↓ 編輯彈窗打開
GUI#09 (驗證碼提醒)
    ↓ 選擇執行模式
[等待觸發時間] (v1.6.0 將實作 GUI#10)
    ↓ 觸發時間到達
[執行結果] (v1.7.0 將實作 GUI#11)
```

### **未來完整流程 (v1.7.0 目標)**
```
GUI#04 → GUI#02 → GUI#05 → GUI#09 → GUI#10 → GUI#11
```

## 📋 **變更管理流程**

### **新增 GUI 流程**
1. **需求分析** - 確認新 GUI 的必要性
2. **功能設計** - 詳細設計 GUI 功能和介面
3. **編號分配** - 使用 GUI#12 以後的編號
4. **原型確認** - 在 gui_prototype_viewer 中實作原型
5. **用戶確認** - 獲得用戶明確同意
6. **文檔更新** - 更新本規範文檔
7. **版本記錄** - 記錄變更版本和原因

### **修改 GUI 流程**
1. **變更申請** - 說明修改原因和影響
2. **影響評估** - 評估對其他 GUI 的影響
3. **原型修改** - 在原型中實作修改
4. **用戶確認** - 獲得用戶明確同意
5. **文檔更新** - 更新規範文檔
6. **版本記錄** - 記錄變更歷史

### **廢棄 GUI 流程**
1. **廢棄申請** - 說明廢棄原因
2. **替代方案** - 提供功能替代方案
3. **影響評估** - 評估對流程的影響
4. **用戶確認** - 獲得用戶明確同意
5. **編號保留** - 編號標註廢棄，不得重複使用
6. **文檔更新** - 更新規範文檔

## 🔒 **版本控制**

### **v1.5.0 確認狀態**
- ✅ GUI#01: 已確認
- ✅ GUI#02: 已優化確認
- ✅ GUI#04: 參考主程式
- ✅ GUI#05: 已優化確認
- ✅ GUI#09: 已確認
- ❌ GUI#03, #06, #07, #08: 已廢棄

### **未來版本規劃**
- 📅 v1.6.0: GUI#10 實作
- 📅 v1.7.0: GUI#11 實作
- 🔮 v1.8.0+: 新需求評估

## 📞 **聯絡資訊**
- **文檔維護**: AI Assistant
- **變更審核**: 用戶確認
- **技術實作**: 開發團隊

---
**本文檔為 AGES-KH-Bot GUI 開發的正式規範，所有 GUI 相關變更都必須遵循此規範。**
