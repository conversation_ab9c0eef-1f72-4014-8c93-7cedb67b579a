# mvp_grabber_v1.5.0_planned.py
# AGES-KH 搶單主程式 - 規劃版本
# 完全按照 GUI 規範文檔實作的版本
# Last Modified: 2025-07-01
# Maintainer: <PERSON>

import tkinter as tk
from tkinter import simpledialog, messagebox, ttk
import csv
from datetime import datetime, date, timedelta
import os
import sys
import threading
import time
import logging

# 版本信息
__VERSION__ = "1.5.0-planned"

# 全局變量
current_execution_mode = 'normal'  # 'normal' 或 'test'
driver = None
filtered_tasks = []

print(f"[INFO] 🚀 AGES-KH-Bot v{__VERSION__} - 規劃版本啟動")

# ===== GUI#01 - 觸發時間設定 =====
def show_gui01_trigger_time_setting(default="09:30:00.001"):
    """GUI#01 - 觸發時間設定"""
    print("[INFO] 📅 顯示 GUI#01 - 觸發時間設定")
    
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    
    answer = simpledialog.askstring(
        "觸發時間設定 (支援毫秒)", 
        "請輸入本次搶單觸發時間 (格式 HH:MM:SS.sss)：",
        initialvalue=default
    )
    
    root.destroy()
    
    if not answer:
        print(f"[INFO] 用戶取消輸入，使用預設值: {default}")
        return default
    
    result = answer.strip()
    print(f"[INFO] 用戶輸入觸發時間: {result}")
    return result

# ===== GUI#02 - 準備提示視窗 =====
def show_gui02_preparation_window(tasks):
    """GUI#02 - 準備提示視窗 - 複製自 gui_prototype_viewer.py 已驗證代碼"""
    print("[INFO] 📋 顯示 GUI#02 - 準備提示視窗")

    result = {'action': None}

    # 按鈕點擊處理
    def on_continue():
        print("[INFO] 用戶點擊 [啟動瀏覽器]")
        result['action'] = 'continue'
        window.destroy()

    def on_cancel():
        print("[INFO] 用戶點擊 [取消]")
        result['action'] = 'cancel'
        window.destroy()

    # 創建視窗（優化版，確保按鈕完整顯示）
    window = tk.Tk()
    window.title(f"GUI#02 - 準備提示")  # 移除版本號，節省標題欄空間
    window.geometry("400x180")  # 調整高度，確保按鈕顯示
    window.resizable(False, False)
    window.attributes('-topmost', True)

    # 移除表頭，直接顯示任務確認信息
    tk.Label(window, text="即將執行以下搶單任務：",
            font=("Arial", 10, "bold")).pack(pady=(15, 8))

    # 任務顯示區域（限制高度，最多3筆）
    task_frame = tk.Frame(window, height=60)  # 固定高度
    task_frame.pack(fill="x", padx=20, pady=(0, 8))
    task_frame.pack_propagate(False)  # 防止子元件撐大框架

    for task in tasks[:3]:  # 最多顯示3筆
        task_text = f"訂單: {task.get('order_number', 'N/A')} | 瀏覽器: {task.get('browser', 'N/A')}"
        tk.Label(task_frame, text=task_text, font=("Arial", 9),
                fg="green").pack(pady=1)

    # 簡化的操作提醒
    tk.Label(window, text="確認無誤後，點擊「啟動瀏覽器」開始搶單",
            font=("Arial", 8), fg="gray").pack(pady=(0, 8))

    # 優化的按鈕區域（平均大小，居中靠近）
    button_frame = tk.Frame(window)
    button_frame.pack(pady=(0, 15))

    tk.Button(button_frame, text="啟動瀏覽器", command=on_continue,
             font=("Arial", 10, "bold"), bg="#4CAF50", fg="white",
             width=12, pady=5).pack(side="left", padx=5)
    tk.Button(button_frame, text="取消", command=on_cancel,
             font=("Arial", 10), bg="#f44336", fg="white",
             width=12, pady=5).pack(side="left", padx=5)

    # 添加原型標示
    prototype_label = tk.Label(window, text="🎨 原型展示模式 - 簡化版 GUI#02",
                             font=("Arial", 8), fg="gray")
    prototype_label.pack(pady=(5, 0))

    window.protocol("WM_DELETE_WINDOW", on_cancel)

    # 等待用戶操作
    window.mainloop()

    return result['action']

# ===== GUI#05 - 操作指南 =====
def show_gui05_operation_guide(tasks, trigger_time):
    """GUI#05 - 操作指南 - 複製自 gui_prototype_viewer.py 已驗證代碼"""
    print("[INFO] 📖 顯示 GUI#05 - 操作指南")

    result = {'action': None}

    # 按鈕點擊處理
    def on_ready():
        print("[INFO] 用戶點擊 [✅ 準備完成]")
        result['action'] = 'ready'
        window.destroy()

    def on_cancel():
        print("[INFO] 用戶點擊 [❌ 取消操作]")
        result['action'] = 'cancel'
        window.destroy()

    # 創建視窗（比照 GUI#09 版型優化）
    window = tk.Tk()
    window.title(f"GUI#05 - AGES-KH 操作指南 v{__VERSION__}")
    window.geometry("600x500")
    window.resizable(False, False)
    window.attributes('-topmost', True)

    # 主框架（比照 GUI#09）
    main_frame = tk.Frame(window, padx=20, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 標題（比照 GUI#09）
    title_label = tk.Label(main_frame, text="🚀 搶單準備階段",
                          font=("Arial", 14, "bold"), fg="blue")
    title_label.pack(pady=(0, 20))

    # 內容區域框架（比照 GUI#09）
    content_frame = tk.LabelFrame(main_frame, text="📋 操作指南", padx=15, pady=15)
    content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

    # 指南內容（使用 Text 組件支援滾動）
    text_widget = tk.Text(content_frame, wrap=tk.WORD, font=("Arial", 10),
                        height=15, width=50, bg="#f8f9fa")
    scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)

    instructions_text = f"""1. 瀏覽器已啟動，請手動完成以下操作：
   • 登入您的帳號
   • 輸入登入驗證碼
   • 導航到搶單頁面（進廠確認單列表）
   • 確保能看到您要搶的訂單

2. 完成上述操作後，點擊「準備完成」

3. 程式將自動：
   • 掃描頁面元素
   • 等待觸發時間
   • 執行搶單動作

⚠️ 重要提醒：
• 請確保已在正確的訂單管理頁面
• 系統將自動尋找並點擊編輯按鈕
• 驗證碼需要手動輸入
• 系統會自動計算最佳提交時間

⏰ 觸發時間資訊：
🎯 觸發時間: {trigger_time}
📅 當前時間: {datetime.now().strftime('%H:%M:%S')}

📋 任務摘要：
📊 任務數量: {len(tasks)} 筆"""

    # 添加任務詳情
    for i, task in enumerate(tasks[:3]):  # 最多顯示3筆
        order_number = task.get('order_number', 'N/A')
        browser = task.get('browser', 'chrome')
        instructions_text += f"\n  {i+1}. 訂單: {order_number} | 瀏覽器: {browser}"

    text_widget.insert(tk.END, instructions_text)
    text_widget.config(state=tk.DISABLED)  # 設為只讀

    text_widget.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # 按鈕區域（比照 GUI#09 底部固定）
    button_frame = tk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))

    # 按鈕（完全比照 GUI#09 規格）
    ready_btn = tk.Button(button_frame, text="✅ 準備完成", command=on_ready,
                        font=("Arial", 11, "bold"), bg="#4CAF50", fg="white",
                        padx=20, pady=10)
    ready_btn.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill=tk.X)

    cancel_btn = tk.Button(button_frame, text="❌ 取消", command=on_cancel,
                         font=("Arial", 11, "bold"), bg="#f44336", fg="white",
                         padx=20, pady=10)
    cancel_btn.pack(side=tk.RIGHT, padx=(10, 0), expand=True, fill=tk.X)

    # 添加原型標示（比照 GUI#09）
    prototype_label = tk.Label(main_frame, text="🎨 原型展示模式 - 複製自 gui_prototype_viewer.py 已驗證代碼",
                             font=("Arial", 8), fg="gray")
    prototype_label.pack(pady=(10, 0))

    window.protocol("WM_DELETE_WINDOW", on_cancel)

    # 等待用戶操作
    window.mainloop()

    return result['action']

# ===== GUI#09 - 驗證碼輸入提醒 =====
def show_gui09_verification_reminder():
    """GUI#09 - 驗證碼輸入提醒 - 複製自 gui_prototype_viewer.py 已驗證代碼"""
    print("[INFO] 🔐 顯示 GUI#09 - 驗證碼輸入提醒")

    global current_execution_mode
    result = {'mode': None, 'confirmed': False}

    # 模擬檢測結果數據
    mock_detection_result = {
        'submit_buttons': ['submit1', 'submit2'],
        'cancel_buttons': ['cancel1'],
        'other_buttons': ['other1', 'other2']
    }

    # 創建提醒視窗（複製自主程式的實際代碼）
    reminder_window = tk.Tk()
    reminder_window.title("GUI#09 - 驗證碼輸入提醒")
    reminder_window.geometry("600x500")
    reminder_window.resizable(False, False)
    reminder_window.attributes('-topmost', True)
    reminder_window.focus_force()

    # 主框架
    main_frame = tk.Frame(reminder_window, padx=20, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 標題
    title_label = tk.Label(main_frame, text="🔐 請在修改進廠確認單中輸入驗證碼",
                          font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))

    # 操作說明
    instructions = [
        "📋 操作說明：",
        "1. 在彈出的修改進廠確認單中找到驗證碼輸入框",
        "2. 手動輸入驗證碼",
        "3. 確認所有資料正確",
        "4. 選擇下方按鈕完成準備",
        "",
        "⏰ 請在觸發時間前 5 分鐘內完成驗證碼輸入"
    ]

    for instruction in instructions:
        label = tk.Label(main_frame, text=instruction, font=("Arial", 10), anchor="w")
        label.pack(fill=tk.X, pady=2)

    # 狀態欄框架
    status_frame = tk.LabelFrame(main_frame, text="📊 系統檢測狀態", font=("Arial", 10, "bold"))
    status_frame.pack(fill=tk.X, pady=(20, 10))

    # 生成狀態信息（使用模擬數據）
    submit_count = len(mock_detection_result.get('submit_buttons', []))
    cancel_count = len(mock_detection_result.get('cancel_buttons', []))
    other_count = len(mock_detection_result.get('other_buttons', []))

    # 檢測狀態
    if submit_count > 0 and cancel_count > 0:
        location_status = "✅ 修改進廠確認單"
        detection_status = f"✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ✅ 找到取消按鈕"
    elif submit_count > 0:
        location_status = "⚠️ 修改進廠確認單 (部分載入)"
        detection_status = f"✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ❌ 未找到取消按鈕"
    else:
        location_status = "❌ 尚未進入修改進廠確認單"
        detection_status = "⏳ 等待編輯彈窗開啟..."

    # 元素統計（模擬數據，標示已知問題）
    total_buttons = submit_count + cancel_count + other_count
    element_stats = f"按鈕 {total_buttons} 個 | 送出 {submit_count} 個 | 取消 {cancel_count} 個 (原型數據)"

    # 狀態標籤
    status_labels = [
        f"📍 定位狀態: {location_status}",
        f"🔍 檢測結果: {detection_status}",
        f"📊 元素統計: {element_stats}"
    ]

    for status_text in status_labels:
        status_label = tk.Label(status_frame, text=status_text, font=("Arial", 9), anchor="w")
        status_label.pack(fill=tk.X, pady=2, padx=10)

    # 按鈕點擊處理
    def on_normal_submit():
        result['mode'] = 'normal'
        result['confirmed'] = True
        print("[INFO] 用戶選擇 [已輸入驗證碼-正式送單]")
        reminder_window.destroy()

    def on_test_submit():
        result['mode'] = 'test'
        result['confirmed'] = True
        print("[INFO] 用戶選擇 [已輸入驗證碼-模擬送單]")
        reminder_window.destroy()

    # 按鈕框架
    button_frame = tk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))

    # 按鈕（複製自主程式的實際設計）
    normal_button = tk.Button(button_frame, text="已輸入驗證碼-正式送單",
                             command=on_normal_submit, font=("Arial", 11, "bold"),
                             bg="#4CAF50", fg="white", padx=20, pady=10)
    normal_button.pack(side=tk.LEFT, padx=(0, 10), expand=True, fill=tk.X)

    test_button = tk.Button(button_frame, text="已輸入驗證碼-模擬送單",
                           command=on_test_submit, font=("Arial", 11, "bold"),
                           bg="#FF9800", fg="white", padx=20, pady=10)
    test_button.pack(side=tk.RIGHT, padx=(10, 0), expand=True, fill=tk.X)

    # 添加原型標示
    prototype_label = tk.Label(main_frame, text="🎨 原型展示模式 - 複製自 gui_prototype_viewer.py 已驗證代碼",
                             font=("Arial", 8), fg="gray")
    prototype_label.pack(pady=(10, 0))

    # 視窗關閉處理
    def on_close():
        print("[INFO] 用戶關閉 GUI#09")
        result['confirmed'] = False
        reminder_window.destroy()

    reminder_window.protocol("WM_DELETE_WINDOW", on_close)

    # 等待用戶操作
    reminder_window.mainloop()

    if result['confirmed']:
        current_execution_mode = result['mode']
        print(f"[INFO] ✅ 執行模式設定為: {current_execution_mode}")

    return result

# ===== 主程式流程 =====
def main_planned_flow():
    """主程式流程 - 按照規劃的 GUI 流程"""
    print(f"[INFO] 🚀 啟動 AGES-KH-Bot v{__VERSION__} 規劃版本")
    
    # 模擬任務數據
    mock_tasks = [
        {"order_number": "A123456", "browser": "chrome", "trigger_time": "09:30:00.001"},
        {"order_number": "B789012", "browser": "chrome", "trigger_time": "09:30:00.001"}
    ]
    
    try:
        # GUI#01 - 觸發時間設定
        trigger_time = show_gui01_trigger_time_setting()
        print(f"[INFO] 觸發時間設定完成: {trigger_time}")
        
        # GUI#02 - 準備提示視窗
        action = show_gui02_preparation_window(mock_tasks)
        if action != 'continue':
            print("[INFO] 用戶取消操作，程式結束")
            return
        
        print("[INFO] 模擬啟動瀏覽器...")
        time.sleep(1)  # 模擬瀏覽器啟動時間
        
        # GUI#05 - 操作指南
        action = show_gui05_operation_guide(mock_tasks, trigger_time)
        if action != 'ready':
            print("[INFO] 用戶取消操作，程式結束")
            return
        
        print("[INFO] 模擬自動點擊編輯按鈕...")
        time.sleep(1)  # 模擬點擊編輯按鈕
        
        # GUI#09 - 驗證碼輸入提醒
        result = show_gui09_verification_reminder()
        if not result['confirmed']:
            print("[INFO] 用戶取消操作，程式結束")
            return
        
        print(f"[INFO] ✅ GUI 流程完成！執行模式: {current_execution_mode}")
        print("[INFO] 🎯 準備進入等待觸發時間階段...")
        
        # 這裡將來會是 GUI#10 - 等待觸發時間
        messagebox.showinfo("流程完成", 
                           f"GUI 流程測試完成！\n\n"
                           f"觸發時間: {trigger_time}\n"
                           f"執行模式: {current_execution_mode}\n"
                           f"任務數量: {len(mock_tasks)} 筆\n\n"
                           f"下一步將實作 GUI#10 等待觸發時間")
        
    except Exception as e:
        print(f"[ERROR] 程式執行失敗: {e}")
        messagebox.showerror("錯誤", f"程式執行失敗: {e}")

if __name__ == "__main__":
    main_planned_flow()
