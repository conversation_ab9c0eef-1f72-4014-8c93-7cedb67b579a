2025-07-01 16:42:11,705 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_164211.log
2025-07-01 16:42:21,139 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:42:21,140 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:42:21,188 - DEBUG - chromedriver not found in PATH
2025-07-01 16:42:21,189 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:42:21,189 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:42:21,189 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:42:21,189 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:42:21,189 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:42:21,189 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:42:21,193 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 25800 using 0 to output -3
2025-07-01 16:42:21,727 - DEBUG - POST http://localhost:55410/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:42:21,727 - DEBUG - Starting new HTTP connection (1): localhost:55410
2025-07-01 16:42:22,274 - DEBUG - http://localhost:55410 "POST /session HTTP/1.1" 200 0
2025-07-01 16:42:22,274 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir25800_1594289361"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55413"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"eb549de62bfbeacfd886907283b480e5"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:22,275 - DEBUG - Finished Request
2025-07-01 16:42:22,276 - DEBUG - POST http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:42:23,693 - DEBUG - http://localhost:55410 "POST /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:23,694 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:23,694 - DEBUG - Finished Request
2025-07-01 16:42:23,694 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:42:23,694 - DEBUG - POST http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:42:23,702 - DEBUG - http://localhost:55410 "POST /session/eb549de62bfbeacfd886907283b480e5/execute/sync HTTP/1.1" 200 0
2025-07-01 16:42:23,702 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:23,702 - DEBUG - Finished Request
2025-07-01 16:42:23,702 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:42:23,703 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:23,734 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:23,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:23,735 - DEBUG - Finished Request
2025-07-01 16:42:24,735 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:24,741 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:24,741 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:24,741 - DEBUG - Finished Request
2025-07-01 16:42:25,743 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:25,748 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:25,749 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:25,749 - DEBUG - Finished Request
2025-07-01 16:42:26,750 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:26,758 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:26,758 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:26,758 - DEBUG - Finished Request
2025-07-01 16:42:27,759 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:27,766 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:27,766 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:27,766 - DEBUG - Finished Request
2025-07-01 16:42:28,767 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:28,772 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:28,773 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:28,773 - DEBUG - Finished Request
2025-07-01 16:42:29,773 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:29,779 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:29,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:29,780 - DEBUG - Finished Request
2025-07-01 16:42:30,781 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:30,800 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:30,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:30,801 - DEBUG - Finished Request
2025-07-01 16:42:31,802 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:31,811 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:31,811 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:31,811 - DEBUG - Finished Request
2025-07-01 16:42:32,812 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:32,820 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:32,820 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:32,820 - DEBUG - Finished Request
2025-07-01 16:42:33,821 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:33,836 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:33,837 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:33,837 - DEBUG - Finished Request
2025-07-01 16:42:34,838 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:34,847 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:34,848 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:34,848 - DEBUG - Finished Request
2025-07-01 16:42:35,849 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:35,857 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:35,858 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:35,858 - DEBUG - Finished Request
2025-07-01 16:42:36,859 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:36,867 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:36,867 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:36,868 - DEBUG - Finished Request
2025-07-01 16:42:37,868 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:37,877 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:37,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:37,877 - DEBUG - Finished Request
2025-07-01 16:42:38,878 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:38,884 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:38,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:38,885 - DEBUG - Finished Request
2025-07-01 16:42:39,886 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:39,893 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:39,893 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:39,893 - DEBUG - Finished Request
2025-07-01 16:42:40,894 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:40,901 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:40,901 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:40,901 - DEBUG - Finished Request
2025-07-01 16:42:41,901 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:41,909 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:41,909 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:41,910 - DEBUG - Finished Request
2025-07-01 16:42:42,911 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:42,919 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:42,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:42,919 - DEBUG - Finished Request
2025-07-01 16:42:43,920 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:43,927 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:43,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:43,928 - DEBUG - Finished Request
2025-07-01 16:42:44,929 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:44,937 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:44,937 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:44,937 - DEBUG - Finished Request
2025-07-01 16:42:45,938 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:45,945 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:45,945 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:45,945 - DEBUG - Finished Request
2025-07-01 16:42:46,947 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:46,956 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:46,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:46,956 - DEBUG - Finished Request
2025-07-01 16:42:47,958 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:47,966 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:47,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:47,966 - DEBUG - Finished Request
2025-07-01 16:42:48,967 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:48,974 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:48,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:48,974 - DEBUG - Finished Request
2025-07-01 16:42:49,975 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:49,983 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:49,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:49,983 - DEBUG - Finished Request
2025-07-01 16:42:50,985 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:50,991 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:50,991 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:50,991 - DEBUG - Finished Request
2025-07-01 16:42:51,993 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:52,001 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:52,001 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:52,001 - DEBUG - Finished Request
2025-07-01 16:42:53,002 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:53,009 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:53,009 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:53,010 - DEBUG - Finished Request
2025-07-01 16:42:54,011 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:54,018 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:54,018 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:54,018 - DEBUG - Finished Request
2025-07-01 16:42:55,019 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:55,028 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:55,028 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:55,028 - DEBUG - Finished Request
2025-07-01 16:42:56,029 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:56,039 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:56,039 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:56,039 - DEBUG - Finished Request
2025-07-01 16:42:57,040 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:57,050 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:57,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:57,051 - DEBUG - Finished Request
2025-07-01 16:42:58,052 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:58,061 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:58,061 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:58,061 - DEBUG - Finished Request
2025-07-01 16:42:59,062 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:42:59,070 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:42:59,071 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:59,071 - DEBUG - Finished Request
2025-07-01 16:43:00,072 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:00,079 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:00,079 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:00,079 - DEBUG - Finished Request
2025-07-01 16:43:01,081 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:01,090 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:01,090 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:01,090 - DEBUG - Finished Request
2025-07-01 16:43:02,091 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:02,098 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:02,098 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:02,098 - DEBUG - Finished Request
2025-07-01 16:43:03,100 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:03,108 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:03,108 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:03,108 - DEBUG - Finished Request
2025-07-01 16:43:04,110 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:04,120 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:04,120 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:04,121 - DEBUG - Finished Request
2025-07-01 16:43:05,121 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:05,128 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:05,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:05,129 - DEBUG - Finished Request
2025-07-01 16:43:06,130 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:06,138 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:06,138 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:06,138 - DEBUG - Finished Request
2025-07-01 16:43:07,139 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:07,147 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:07,147 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:07,148 - DEBUG - Finished Request
2025-07-01 16:43:08,149 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:08,154 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:08,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:08,154 - DEBUG - Finished Request
2025-07-01 16:43:09,155 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:09,162 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:09,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:09,162 - DEBUG - Finished Request
2025-07-01 16:43:10,164 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:10,171 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:10,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:10,172 - DEBUG - Finished Request
2025-07-01 16:43:11,173 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:11,179 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:11,179 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:11,179 - DEBUG - Finished Request
2025-07-01 16:43:12,180 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:12,187 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:12,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:12,188 - DEBUG - Finished Request
2025-07-01 16:43:13,189 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:13,196 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:13,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:13,196 - DEBUG - Finished Request
2025-07-01 16:43:14,197 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:14,429 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:14,429 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:14,429 - DEBUG - Finished Request
2025-07-01 16:43:15,430 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:15,437 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:15,438 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:15,438 - DEBUG - Finished Request
2025-07-01 16:43:16,438 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:16,450 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:16,450 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:16,450 - DEBUG - Finished Request
2025-07-01 16:43:17,452 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:17,458 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:17,458 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:17,459 - DEBUG - Finished Request
2025-07-01 16:43:18,459 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:18,465 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:18,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:18,465 - DEBUG - Finished Request
2025-07-01 16:43:19,466 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:19,473 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:19,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:19,474 - DEBUG - Finished Request
2025-07-01 16:43:20,475 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:20,481 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:20,481 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:20,481 - DEBUG - Finished Request
2025-07-01 16:43:21,482 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:21,488 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:21,488 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:21,489 - DEBUG - Finished Request
2025-07-01 16:43:22,490 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:22,497 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:22,497 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:22,497 - DEBUG - Finished Request
2025-07-01 16:43:23,498 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:23,507 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:23,507 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:23,507 - DEBUG - Finished Request
2025-07-01 16:43:24,508 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:24,513 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:24,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:24,514 - DEBUG - Finished Request
2025-07-01 16:43:25,514 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:25,519 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:25,520 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:25,520 - DEBUG - Finished Request
2025-07-01 16:43:26,521 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:26,529 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:26,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:26,530 - DEBUG - Finished Request
2025-07-01 16:43:27,531 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:27,537 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:27,537 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:27,537 - DEBUG - Finished Request
2025-07-01 16:43:28,538 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:28,545 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:28,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:28,546 - DEBUG - Finished Request
2025-07-01 16:43:29,547 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:29,554 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:29,555 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:29,555 - DEBUG - Finished Request
2025-07-01 16:43:30,556 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:30,565 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:30,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:30,566 - DEBUG - Finished Request
2025-07-01 16:43:31,567 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:31,573 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:31,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:31,574 - DEBUG - Finished Request
2025-07-01 16:43:32,574 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:32,580 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:32,581 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:32,581 - DEBUG - Finished Request
2025-07-01 16:43:33,582 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:33,588 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:33,588 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:33,588 - DEBUG - Finished Request
2025-07-01 16:43:34,590 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:34,596 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:34,596 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:34,596 - DEBUG - Finished Request
2025-07-01 16:43:35,598 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:35,608 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:35,608 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:35,608 - DEBUG - Finished Request
2025-07-01 16:43:36,609 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:36,616 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:36,616 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:36,616 - DEBUG - Finished Request
2025-07-01 16:43:37,618 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:37,625 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:37,625 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:37,625 - DEBUG - Finished Request
2025-07-01 16:43:38,626 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:38,632 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:38,632 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:38,632 - DEBUG - Finished Request
2025-07-01 16:43:39,633 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:39,640 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:39,640 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:39,640 - DEBUG - Finished Request
2025-07-01 16:43:40,641 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:40,647 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:40,647 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:40,648 - DEBUG - Finished Request
2025-07-01 16:43:41,648 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:41,655 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:41,655 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:41,655 - DEBUG - Finished Request
2025-07-01 16:43:42,656 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:42,662 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:42,662 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:42,662 - DEBUG - Finished Request
2025-07-01 16:43:43,663 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:43,670 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:43,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:43,671 - DEBUG - Finished Request
2025-07-01 16:43:44,671 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:44,803 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:44,803 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:44,803 - DEBUG - Finished Request
2025-07-01 16:43:45,804 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:45,811 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:45,811 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:45,811 - DEBUG - Finished Request
2025-07-01 16:43:46,812 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:46,818 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:46,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:46,818 - DEBUG - Finished Request
2025-07-01 16:43:47,819 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:47,825 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:47,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:47,825 - DEBUG - Finished Request
2025-07-01 16:43:48,826 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:48,833 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:48,833 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:48,833 - DEBUG - Finished Request
2025-07-01 16:43:49,834 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:49,840 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:49,840 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:49,841 - DEBUG - Finished Request
2025-07-01 16:43:50,841 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:50,846 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:50,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:50,847 - DEBUG - Finished Request
2025-07-01 16:43:51,847 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:51,853 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 200 0
2025-07-01 16:43:51,853 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:51,854 - DEBUG - Finished Request
2025-07-01 16:43:52,855 - DEBUG - GET http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5/url {}
2025-07-01 16:43:52,857 - DEBUG - http://localhost:55410 "GET /session/eb549de62bfbeacfd886907283b480e5/url HTTP/1.1" 404 0
2025-07-01 16:43:52,857 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:52,858 - DEBUG - Finished Request
2025-07-01 16:43:52,859 - DEBUG - DELETE http://localhost:55410/session/eb549de62bfbeacfd886907283b480e5 {}
2025-07-01 16:43:52,930 - DEBUG - http://localhost:55410 "DELETE /session/eb549de62bfbeacfd886907283b480e5 HTTP/1.1" 200 0
2025-07-01 16:43:52,931 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:43:52,931 - DEBUG - Finished Request
