"""
整合測試腳本 - 測試主程式與新功能的整合
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestIntegration(unittest.TestCase):
    """整合測試"""
    
    def setUp(self):
        """測試前準備"""
        # Mock tkinter 以避免 GUI 彈出
        self.tk_patcher = patch('tkinter.Tk')
        self.tk_mock = self.tk_patcher.start()
        
        # Mock simpledialog
        self.dialog_patcher = patch('tkinter.simpledialog.askstring')
        self.dialog_mock = self.dialog_patcher.start()
        self.dialog_mock.return_value = "1234"  # 模擬驗證碼輸入
    
    def tearDown(self):
        """測試後清理"""
        self.tk_patcher.stop()
        self.dialog_patcher.stop()
    
    def test_import_modules(self):
        """測試模組導入"""
        try:
            # 測試主程式導入
            import mvp_grabber
            
            # 測試新模組導入
            from submission_result_detector import SubmissionResultDetector
            from dom_inspector import DOMInspector
            
            print("✅ 所有模組導入成功")
            
        except ImportError as e:
            self.fail(f"模組導入失敗: {e}")
    
    @patch('mvp_grabber.webdriver.Chrome')
    @patch('mvp_grabber.psutil.pid_exists')
    def test_browser_initialization(self, mock_pid_exists, mock_chrome):
        """測試瀏覽器初始化"""
        # Mock WebDriver
        mock_driver = Mock()
        mock_driver.service.process.pid = 12345
        mock_chrome.return_value = mock_driver
        mock_pid_exists.return_value = True
        
        # 導入並測試
        import mvp_grabber
        
        task = {'browser': 'chrome'}
        result = mvp_grabber.start_browser(task)
        
        self.assertTrue(result)
        self.assertIsNotNone(mvp_grabber.driver)
        self.assertIsNotNone(mvp_grabber.result_detector)
        self.assertIsNotNone(mvp_grabber.dom_inspector)
        
        print("✅ 瀏覽器初始化測試通過")
    
    @patch('mvp_grabber.driver')
    @patch('mvp_grabber.result_detector')
    def test_captcha_handling(self, mock_detector, mock_driver):
        """測試驗證碼處理"""
        # Mock driver 元素
        mock_input = Mock()
        mock_driver.find_element.return_value = mock_input
        
        # 導入並測試
        import mvp_grabber
        
        result = mvp_grabber.handle_captcha_input()
        
        # 驗證結果
        self.assertEqual(result, "1234")  # 來自 mock
        mock_input.clear.assert_called_once()
        mock_input.send_keys.assert_called_once_with("1234")
        
        print("✅ 驗證碼處理測試通過")
    
    @patch('mvp_grabber.driver')
    def test_submit_button_click(self, mock_driver):
        """測試送出按鈕點擊"""
        # Mock 送出按鈕
        mock_button = Mock()
        mock_button.is_displayed.return_value = True
        mock_button.is_enabled.return_value = True
        mock_driver.find_element.return_value = mock_button
        
        # 導入並測試
        import mvp_grabber
        
        result = mvp_grabber.click_submit_button()
        
        self.assertTrue(result)
        mock_button.click.assert_called_once()
        
        print("✅ 送出按鈕點擊測試通過")
    
    def test_dom_config_exists(self):
        """測試 DOM 配置文件"""
        config_path = "dom_elements_config.json"
        
        if os.path.exists(config_path):
            import json
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 檢查必要的配置項
                self.assertIn('elements', config)
                self.assertIn('scan_info', config)
                
                print("✅ DOM 配置文件格式正確")
                
            except json.JSONDecodeError:
                self.fail("DOM 配置文件格式錯誤")
        else:
            print("⚠️ DOM 配置文件不存在，將在首次掃描時創建")
    
    @patch('mvp_grabber.os.path.exists')
    @patch('builtins.open')
    def test_result_logging(self, mock_open, mock_exists):
        """測試結果記錄"""
        mock_exists.return_value = False  # 文件不存在
        mock_file = Mock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        # 導入並測試
        import mvp_grabber
        
        task = {
            'order_id': 'TEST001',
            'order_date': '2025-06-27',
            'browser': 'chrome',
            'trigger_time': '09:30:00.001'
        }
        
        result = {
            'timestamp': '20250627_101500',
            'is_success': True,
            'result_type': 'success',
            'message': '測試成功',
            'screenshot_path': 'test.png'
        }
        
        # 不應該拋出異常
        try:
            mvp_grabber.log_order_result(task, result)
            print("✅ 結果記錄測試通過")
        except Exception as e:
            self.fail(f"結果記錄失敗: {e}")

def run_integration_tests():
    """運行整合測試"""
    print("🧪 開始運行整合測試...")
    
    # 創建測試套件
    test_suite = unittest.TestSuite()
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestIntegration))
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 輸出結果
    if result.wasSuccessful():
        print("✅ 所有整合測試通過！")
        return True
    else:
        print("❌ 部分整合測試失敗")
        print(f"失敗數量: {len(result.failures)}")
        print(f"錯誤數量: {len(result.errors)}")
        return False

def check_dependencies():
    """檢查依賴項"""
    print("🔍 檢查依賴項...")
    
    required_files = [
        "mvp_grabber.py",
        "submission_result_detector.py", 
        "dom_inspector.py",
        "orders/orders.csv"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    else:
        print("✅ 所有必要文件都存在")
        return True

def check_directories():
    """檢查並創建必要目錄"""
    print("📁 檢查目錄結構...")
    
    required_dirs = [
        "orders",
        "results", 
        "screenshots",
        "logs"
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"📁 創建目錄: {dir_path}")
        else:
            print(f"✅ 目錄存在: {dir_path}")

if __name__ == "__main__":
    print("🚀 AGES-KH-Bot 整合測試開始")
    print("="*50)
    
    # 1. 檢查依賴項
    if not check_dependencies():
        print("❌ 依賴項檢查失敗")
        sys.exit(1)
    
    # 2. 檢查目錄
    check_directories()
    
    # 3. 運行整合測試
    success = run_integration_tests()
    
    print("="*50)
    if success:
        print("🎉 整合測試完成！主程式已準備就緒")
        print("\n📋 下一步:")
        print("1. 確保 orders/orders.csv 包含今日任務")
        print("2. 運行 python mvp_grabber.py 開始搶單")
        print("3. 在實際環境中測試完整流程")
    else:
        print("❌ 整合測試失敗，請檢查錯誤信息")
        sys.exit(1)
