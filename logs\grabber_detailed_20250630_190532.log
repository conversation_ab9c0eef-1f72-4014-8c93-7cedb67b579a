2025-06-30 19:05:32,199 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_190532.log
2025-06-30 19:05:37,699 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 19:05:37,700 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 19:05:37,758 - DEBUG - chromedriver not found in PATH
2025-06-30 19:05:37,758 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:05:37,758 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 19:05:37,759 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 19:05:37,759 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 19:05:37,759 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 19:05:37,759 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:05:37,763 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 27304 using 0 to output -3
2025-06-30 19:05:38,273 - DEBUG - POST http://localhost:54363/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 19:05:38,273 - DEBUG - Starting new HTTP connection (1): localhost:54363
2025-06-30 19:05:38,812 - DEBUG - http://localhost:54363 "POST /session HTTP/1.1" 200 0
2025-06-30 19:05:38,812 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir27304_171368943"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54366"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"97d3a8ed27e76b3de168644b1ee461db"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:38,813 - DEBUG - Finished Request
2025-06-30 19:05:38,813 - DEBUG - POST http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 19:05:40,223 - DEBUG - http://localhost:54363 "POST /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:40,224 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:40,224 - DEBUG - Finished Request
2025-06-30 19:05:40,224 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 19:05:40,225 - DEBUG - POST http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 19:05:40,231 - DEBUG - http://localhost:54363 "POST /session/97d3a8ed27e76b3de168644b1ee461db/execute/sync HTTP/1.1" 200 0
2025-06-30 19:05:40,231 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:40,231 - DEBUG - Finished Request
2025-06-30 19:05:40,231 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 19:05:40,232 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:40,258 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:40,258 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:40,258 - DEBUG - Finished Request
2025-06-30 19:05:41,260 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:41,265 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:41,265 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:41,265 - DEBUG - Finished Request
2025-06-30 19:05:42,266 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:42,272 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:42,273 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:42,273 - DEBUG - Finished Request
2025-06-30 19:05:43,274 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:43,280 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:43,280 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:43,280 - DEBUG - Finished Request
2025-06-30 19:05:44,281 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:44,288 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:44,288 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:44,288 - DEBUG - Finished Request
2025-06-30 19:05:45,290 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:45,298 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:45,298 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:45,299 - DEBUG - Finished Request
2025-06-30 19:05:46,300 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:46,308 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:46,308 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:46,308 - DEBUG - Finished Request
2025-06-30 19:05:47,309 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:47,318 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:47,318 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:47,319 - DEBUG - Finished Request
2025-06-30 19:05:48,319 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:48,327 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:48,328 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:48,328 - DEBUG - Finished Request
2025-06-30 19:05:49,329 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:49,337 - DEBUG - http://localhost:54363 "GET /session/97d3a8ed27e76b3de168644b1ee461db/url HTTP/1.1" 200 0
2025-06-30 19:05:49,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:49,338 - DEBUG - Finished Request
2025-06-30 19:05:49,521 - DEBUG - DELETE http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db {}
2025-06-30 19:05:49,573 - DEBUG - http://localhost:54363 "DELETE /session/97d3a8ed27e76b3de168644b1ee461db HTTP/1.1" 200 0
2025-06-30 19:05:49,573 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:05:49,574 - DEBUG - Finished Request
2025-06-30 19:05:50,339 - DEBUG - GET http://localhost:54363/session/97d3a8ed27e76b3de168644b1ee461db/url {}
2025-06-30 19:05:50,340 - DEBUG - Starting new HTTP connection (1): localhost:54363
2025-06-30 19:05:51,230 - DEBUG - Incremented Retry for (url='/session/97d3a8ed27e76b3de168644b1ee461db/url'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-06-30 19:05:51,231 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /session/97d3a8ed27e76b3de168644b1ee461db/url
2025-06-30 19:05:51,231 - DEBUG - Starting new HTTP connection (2): localhost:54363
