# mvp_grabber.py v1.3.3
# AGES-KH 搶單主程式
# Last Modified: 2024-06-17
# Maintainer: <PERSON>
# 調整內容：v1.3.3 - 優化日期正規化邏輯，完善任務過濾機制，增加詳細 debug 訊息

import tkinter as tk
from tkinter import simpledialog, messagebox, ttk
import csv
from datetime import datetime, date, timedelta
import os
import sys
import psutil
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
import threading
import time
from rtt_config_gui import RTTConfigGUI
from rtt_config_manager import RTTConfigManager
from rtt_predictor import get_avg_rtt
import pandas as pd
import re

__VERSION__ = "1.3.3"

driver = None  # 全域 driver
chrome_pid = None  # 追蹤由程式啟動的 Chrome PID

ORDERS_PATH = os.path.join('orders', 'orders.csv')

# 支援多種日期格式
DATE_FORMATS = [
    "%Y/%m/%d", "%Y-%m-%d", "%Y/%-m/%-d", "%Y/%m/%-d", "%Y/%-m/%d", "%Y/%d/%m"
]

# ===== 安全離開主程式 =====
def safe_exit():
    global driver, chrome_pid
    print("[INFO] 終止主程式...")
    try:
        if driver:
            try:
                driver.quit()
            except:
                pass
            finally:
                driver = None
    except Exception as e:
        print(f"[ERROR] 關閉 driver 發生錯誤: {str(e)}")

    try:
        for widget in tk._default_root.children.values():
            widget.destroy()
        tk._default_root.quit()
    except:
        pass

    chrome_pid = None
    os._exit(0)

# ===== 瀏覽器支援檢查 =====
def get_supported_browsers():
    """回傳目前支援的瀏覽器列表"""
    return ['chrome']  # 未來可以加入 'firefox', 'edge'

def has_supported_browser(tasks):
    """檢查是否有支援的瀏覽器任務"""
    supported = get_supported_browsers()
    return any(task.get('browser', '').strip().lower() in supported for task in tasks)

def start_browser(task):
    """根據任務啟動對應的瀏覽器"""
    global driver, chrome_pid
    print(f"[DEBUG] 準備啟動瀏覽器，任務內容: {task}")
    
    if not task or not isinstance(task, dict):
        print("[ERROR] 無效的任務格式")
        return False
        
    try:
        browser = task.get('browser', '').strip().lower()
        if not browser:
            print("[ERROR] 任務中未指定瀏覽器")
            return False
            
        if browser == 'chrome':
            # 設定 Chrome 選項
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_argument('--start-maximized')  # 最大化視窗
            chrome_options.add_argument('--disable-gpu')  # 禁用 GPU 加速
            chrome_options.add_argument('--no-sandbox')  # 禁用沙盒模式
            chrome_options.add_argument('--disable-dev-shm-usage')  # 禁用 /dev/shm 使用
            
            # 啟動 Chrome
            try:
                driver = webdriver.Chrome(options=chrome_options)
                chrome_pid = driver.service.process.pid
                print(f"[DEBUG] Chrome PID: {chrome_pid}")
                
                # 開啟平台網站
                driver.get("https://wmc.kcg.gov.tw/")
                start_driver_monitor()
                return True
            except Exception as e:
                print(f"[ERROR] 啟動 Chrome 失敗: {str(e)}")
                return False
                
        print(f"[ERROR] 不支援的瀏覽器: {browser}")
        return False
        
    except Exception as e:
        print(f"[ERROR] 啟動瀏覽器失敗: {str(e)}")
        if driver:
            try:
                driver.quit()
            except:
                pass
            driver = None
        chrome_pid = None
        return False

# ===== GUI：詢問使用者輸入觸發時間（支援毫秒） =====
def ask_trigger_time_gui(default="09:30:00.001") -> str:
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    answer = simpledialog.askstring("觸發時間設定 (支援毫秒)", 
                                     "請輸入本次搶單觸發時間 (格式 HH:MM:SS.sss)：",
                                     initialvalue=default)
    if not answer:
        return default
    return answer.strip()

def normalize_date(date_str: str) -> str:
    """
    將日期字串正規化為 YYYY-MM-DD 格式
    
    Args:
        date_str (str): 原始日期字串，支援 YYYY/M/D、YYYY/MM/DD、YYYY-MM-DD 等格式
        
    Returns:
        str: 正規化後的日期字串 (YYYY-MM-DD)，若為萬用日期則返回 '*-*-*'
    """
    s = date_str.strip()
    if s == '*-*-*':
        return '*-*-*'
    
    # 替換可能的日期分隔符號為統一格式
    s = s.replace("-", "/")
    
    # 嘗試解析日期 (支援 YYYY/M/D 和 YYYY/MM/DD)
    parts = s.split("/")
    if len(parts) == 3:
        try:
            year = int(parts[0])
            month = int(parts[1])
            day = int(parts[2])
            return f"{year:04d}-{month:02d}-{day:02d}"
        except (ValueError, IndexError):
            pass
            
    return s  # 無法解析則返回原始字串

def fix_row_keys(row: dict) -> dict:
    # 自動修正欄位名稱 user_profil -> user_profile
    if 'user_profil' in row and 'user_profile' not in row:
        row['user_profile'] = row['user_profil']
    return row

def load_and_filter_orders(path=ORDERS_PATH):
    """
    載入並過濾 orders.csv 中的任務
    
    Args:
        path (str): orders.csv 的檔案路徑
        
    Returns:
        tuple: (filtered_tasks, ignored_tasks, all_valid_tasks)
            - filtered_tasks: 過濾後要執行的任務（每個瀏覽器只保留第一筆）
            - ignored_tasks: 因瀏覽器重複而被忽略的任務
            - all_valid_tasks: 所有今日有效任務（包含萬用任務）
    """
    today_str = date.today().strftime("%Y-%m-%d")
    print(f"[DEBUG] 今天日期: {today_str}")
    print(f"[DEBUG] CSV 路徑: {path}")
    
    valid_tasks = []
    ignored_tasks = []
    filtered_tasks = []
    seen_browsers = set()
    
    if not os.path.exists(path):
        print(f"[ERROR] 找不到訂單檔案: {path}")
        return filtered_tasks, ignored_tasks, valid_tasks
        
    try:
        with open(path, newline='', encoding='utf-8') as f:
            # 先讀取第一行確認分隔符
            first_line = f.readline().strip()
            delimiter = '\t' if '\t' in first_line else ','
            f.seek(0)  # 重置檔案指標
            
            reader = csv.DictReader(f, delimiter=delimiter)
            print(f"[DEBUG] 使用分隔符: {delimiter}")
            
            for row in reader:
                row = fix_row_keys(row)
                raw_date = row.get('date', '')
                norm_date = normalize_date(raw_date)
                print(f"[DEBUG] 原始日期: {raw_date!r}, 正規化後: {norm_date!r}")
                
                if norm_date == today_str or norm_date == '*-*-*':
                    print(f"[DEBUG] 找到有效任務: {row}")
                    valid_tasks.append(row)
                    
                    browser = row.get('browser', '').strip().lower()
                    if browser and browser not in seen_browsers:
                        filtered_tasks.append(row)
                        seen_browsers.add(browser)
                    else:
                        ignored_tasks.append(row)
                        
    except Exception as e:
        print(f"[ERROR] 讀取訂單檔案失敗: {str(e)}")
        
    print(f"[DEBUG] 有效任務數量: {len(valid_tasks)}")
    print(f"[DEBUG] 過濾後任務數量: {len(filtered_tasks)}")
    return filtered_tasks, ignored_tasks, valid_tasks

# ===== 顯示準備提示 GUI =====
def show_preparation_gui(version, valid_summary, task_summary, ignored_tasks, filtered_tasks):
    def on_continue():
        window.attributes('-topmost', False)
        window.destroy()
        # 1. 啟動瀏覽器
        print(f"[DEBUG] filtered_tasks: {filtered_tasks}")
        for task in filtered_tasks:
            print(f"[DEBUG] 嘗試啟動: {task.get('browser')}")
            if start_browser(task):
                break  # 只啟動第一個支援的瀏覽器任務
        # 2. 等待使用者操作
        wait_for_user_operation()
        print("[INFO] 使用者已按下繼續，準備進入搶單流程...")

    def on_cancel():
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()
        return False

    def show_rtt_config():
        """顯示 RTT 設定 GUI"""
        def run_rtt_gui():
            rtt_gui = RTTConfigGUI()
            rtt_gui.show()

        # 在新執行緒中啟動 RTT 設定視窗
        thread = threading.Thread(target=run_rtt_gui)
        thread.daemon = True
        thread.start()

    window = tk.Tk()
    window.title(f"AGES-KH 準備提示 v{version} - PID: {os.getpid()}")
    window.geometry("700x600")
    window.attributes('-topmost', True)

    # 版本與操作提醒
    tk.Label(window, text=f"AGES-KH 搶單主程式 v{version}", font=("Arial", 12, "bold"), fg="blue").pack(pady=(10, 0))
    tk.Label(window, text="【重要提醒】\n1. 請確認已準備好登入資訊\n2. 點選「啟動瀏覽器」後，請手動登入並操作至清單畫面", 
             fg="red", wraplength=660, justify="left").pack(pady=(0, 10))

    # 今日有效任務（可滾動）
    frame1 = tk.Frame(window)
    frame1.pack(fill="both", expand=True, padx=10, pady=5)
    tk.Label(frame1, text="今日有效任務（符合今日或萬用條件）：", anchor="w", fg="black").pack(fill="x")
    valid_text = tk.Text(frame1, height=6, wrap="none", font=("Consolas", 10))
    valid_text.pack(fill="both", expand=True)
    valid_text.insert("end", valid_summary)
    valid_text.config(state="disabled")

    # 今日將執行的任務（可滾動）
    frame2 = tk.Frame(window)
    frame2.pack(fill="both", expand=True, padx=10, pady=5)
    tk.Label(frame2, text="今日將執行的任務（每個瀏覽器僅執行一筆）：", anchor="w", fg="green").pack(fill="x")
    text = tk.Text(frame2, height=6, wrap="none", font=("Consolas", 10))
    text.pack(fill="both", expand=True)
    text.insert("end", task_summary)
    text.config(state="disabled")

    # 被忽略的 browser 任務警告
    if ignored_tasks:
        tk.Label(window, text="【警告】以下任務雖符合今日條件，但因同一瀏覽器僅執行一筆，已被忽略：", 
                fg="orange", wraplength=660, justify="left").pack(pady=(5, 0))
        ignore_text = tk.Text(window, height=5, wrap="none", font=("Consolas", 10))
        ignore_text.pack(fill="x", padx=10)
        for task in ignored_tasks:
            ignore_text.insert("end", str(task) + "  （同 browser 已有任務，已忽略）\n")
        ignore_text.config(state="disabled")

    # 按鈕框架
    button_frame = tk.Frame(window)
    button_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Button(button_frame, text="啟動瀏覽器", command=on_continue).pack(side="left", padx=5)
    tk.Button(button_frame, text="RTT 設定", command=show_rtt_config).pack(side="left", padx=5)
    tk.Button(button_frame, text="取消", command=on_cancel).pack(side="right", padx=5)

    window.protocol("WM_DELETE_WINDOW", on_cancel)
    window.mainloop()

# ===== 等待使用者操作完成 =====
def wait_for_user_operation():
    def on_ready():
        window.attributes('-topmost', False)
        window.destroy()
        return True

    def on_cancel():
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()
        return False

    window = tk.Tk()
    window.title(f"AGES-KH 登入提示 v{__VERSION__} - PID: {os.getpid()}")
    window.geometry("400x200")
    window.attributes('-topmost', True)

    tk.Label(window, text="請手動登入平台並操作至清單畫面\n完成後請按下「準備完成」", 
             padx=20, pady=20).pack()

    btn_frame = tk.Frame(window)
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="準備完成", command=on_ready, width=10).pack(side="left", padx=5)
    tk.Button(btn_frame, text="取消", command=on_cancel, width=10).pack(side="left", padx=5)
    window.protocol("WM_DELETE_WINDOW", on_cancel)
    window.mainloop()

# ===== 背景監測 Chrome 是否被關閉 =====
def start_driver_monitor():
    def monitor():
        global chrome_pid
        while True:
            try:
                if chrome_pid and not psutil.pid_exists(chrome_pid):
                    print("[WARN] 偵測到 Python 啟動的 Chrome 已關閉")
                    safe_exit()
                    break
                driver.current_url
                time.sleep(1)
            except (WebDriverException, AttributeError):
                print("[WARN] WebDriver 連線異常")
                safe_exit()
                break
            except Exception as e:
                print(f"[ERROR] 監控錯誤: {str(e)}")
                safe_exit()
                break

    threading.Thread(target=monitor, daemon=True).start()

class GrabberGUI:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title(f"AGES-KH 搶單主程式 v{__VERSION__}")
        self.window.geometry("800x600")
        self.window.resizable(False, False)

        # 建立 GUI 元件
        self._create_widgets()

        # 載入今日任務
        self._load_today_tasks()

    def _create_widgets(self):
        """建立 GUI 元件"""
        # 建立主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill="both", expand=True)

        # 建立標題
        title_label = ttk.Label(
            main_frame,
            text=f"AGES-KH 搶單主程式 v{__VERSION__}",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10)

        # 建立任務顯示區域
        self.task_frame = ttk.LabelFrame(main_frame, text="今日任務", padding=10)
        self.task_frame.pack(fill="both", expand=True, pady=5)

        # 建立按鈕框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=10)

        # 建立按鈕
        ttk.Button(
            button_frame,
            text="RTT 設定",
            command=self._show_rtt_config
        ).pack(side="left", padx=5)

        ttk.Button(
            button_frame,
            text="開始執行",
            command=self._start_execution
        ).pack(side="left", padx=5)

        ttk.Button(
            button_frame,
            text="結束程式",
            command=self.window.destroy
        ).pack(side="right", padx=5)

    def _load_today_tasks(self):
        """載入今日任務"""
        try:
            # 讀取並過濾訂單
            filtered_tasks, ignored_tasks, valid_tasks = load_and_filter_orders()
            
            if not valid_tasks:
                messagebox.showinfo("提示", "今日無有效任務")
                return

            # 顯示任務
            self._display_tasks(filtered_tasks)

        except Exception as e:
            messagebox.showerror("錯誤", f"載入任務失敗: {str(e)}")

    def _display_tasks(self, tasks):
        """顯示任務"""
        # 清除現有內容
        for widget in self.task_frame.winfo_children():
            widget.destroy()

        if not tasks:
            ttk.Label(
                self.task_frame,
                text="今日無有效任務",
                font=("Arial", 12)
            ).pack(pady=20)
            return

        # 建立任務顯示
        for task in tasks:
            task_text = (
                f"任務日期: {task.get('date', 'N/A')}\n"
                f"進廠單日: {task.get('order_date', 'N/A')}\n"
                f"進廠單號: {task.get('order_id', 'N/A')}\n"
                f"瀏 覽 器: {task.get('browser', 'N/A')}\n"
                f"使 用 者: {task.get('user_profile', 'N/A')}\n"
                f"預測模型: {task.get('model', 'N/A')}\n"
                f"觸發時間: {task.get('trigger_time', 'N/A')}\n"
                f"備　　註: {task.get('note', 'N/A')}\n"
            )
            ttk.Label(
                self.task_frame,
                text=task_text,
                font=("Arial", 10)
            ).pack(pady=5, anchor="w")

    def _show_rtt_config(self):
        """顯示 RTT 設定視窗"""
        RTTConfigGUI().show()

    def _start_execution(self):
        """開始執行搶單"""
        # 載入並過濾訂單
        filtered_tasks, ignored_tasks, valid_tasks = load_and_filter_orders()
        
        if not filtered_tasks:
            messagebox.showwarning("警告", "無有效任務可執行")
            return
            
        # 準備任務摘要
        valid_summary = "\n".join(str(t) for t in valid_tasks)
        task_summary = "\n".join(str(t) for t in filtered_tasks)
        
        # 顯示準備提示
        show_preparation_gui(__VERSION__, valid_summary, task_summary, ignored_tasks, filtered_tasks)

    def run(self):
        """執行主程式"""
        self.window.mainloop()

if __name__ == "__main__":
    try:
        app = GrabberGUI()
        app.run()
    except Exception as e:
        print(f"[ERROR] 主程式執行失敗: {str(e)}")
        safe_exit()