2025-06-28 09:43:36,002 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250628_094336.log
2025-06-28 09:43:36,004 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-28 09:43:36,004 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-28 09:43:36,616 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-06-28 09:43:36,616 - DEBUG - chromedriver not found in PATH
2025-06-28 09:43:36,617 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 09:43:36,617 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-28 09:43:36,617 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-06-28 09:43:36,617 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-28 09:43:36,617 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-28 09:43:36,617 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-28 09:43:36,617 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 09:43:36,621 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 32372 using 0 to output -3
2025-06-28 09:43:37,142 - DEBUG - POST http://localhost:49852/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-28 09:43:37,143 - DEBUG - Starting new HTTP connection (1): localhost:49852
2025-06-28 09:43:37,705 - DEBUG - http://localhost:49852 "POST /session HTTP/1.1" 200 0
2025-06-28 09:43:37,705 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir32372_1002004342"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:49859"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"9564b840235c4e8d4d244604ab99d0d2"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:37,705 - DEBUG - Finished Request
2025-06-28 09:43:37,706 - DEBUG - POST http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-28 09:43:38,768 - DEBUG - http://localhost:49852 "POST /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:38,769 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:38,769 - DEBUG - Finished Request
2025-06-28 09:43:38,770 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 09:43:38,770 - DEBUG - POST http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 09:43:38,779 - DEBUG - http://localhost:49852 "POST /session/9564b840235c4e8d4d244604ab99d0d2/execute/sync HTTP/1.1" 200 0
2025-06-28 09:43:38,780 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:38,780 - DEBUG - Finished Request
2025-06-28 09:43:38,780 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 09:43:38,781 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:38,789 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:38,789 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:38,789 - DEBUG - Finished Request
2025-06-28 09:43:39,790 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:39,800 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:39,800 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:39,800 - DEBUG - Finished Request
2025-06-28 09:43:40,801 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:40,807 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:40,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:40,807 - DEBUG - Finished Request
2025-06-28 09:43:41,808 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:41,815 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:41,815 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:41,815 - DEBUG - Finished Request
2025-06-28 09:43:42,816 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:42,823 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:42,823 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:42,823 - DEBUG - Finished Request
2025-06-28 09:43:43,825 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:43,831 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:43,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:43,832 - DEBUG - Finished Request
2025-06-28 09:43:44,833 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:44,844 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:44,844 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:44,845 - DEBUG - Finished Request
2025-06-28 09:43:45,845 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:45,854 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:45,855 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:45,855 - DEBUG - Finished Request
2025-06-28 09:43:46,856 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:46,868 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:46,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:46,869 - DEBUG - Finished Request
2025-06-28 09:43:47,870 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:47,880 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:47,880 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:47,880 - DEBUG - Finished Request
2025-06-28 09:43:48,881 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:48,891 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:48,891 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:48,891 - DEBUG - Finished Request
2025-06-28 09:43:49,892 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:49,901 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:49,902 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:49,902 - DEBUG - Finished Request
2025-06-28 09:43:50,903 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:50,912 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:50,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:50,912 - DEBUG - Finished Request
2025-06-28 09:43:51,913 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:51,921 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:51,921 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:51,922 - DEBUG - Finished Request
2025-06-28 09:43:52,923 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:52,931 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:52,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:52,932 - DEBUG - Finished Request
2025-06-28 09:43:53,932 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:53,943 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:53,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:53,944 - DEBUG - Finished Request
2025-06-28 09:43:54,944 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:54,954 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:54,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:54,955 - DEBUG - Finished Request
2025-06-28 09:43:55,956 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:55,964 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:55,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:55,965 - DEBUG - Finished Request
2025-06-28 09:43:56,967 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:56,977 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:56,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:56,978 - DEBUG - Finished Request
2025-06-28 09:43:57,978 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:57,989 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:57,989 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:57,990 - DEBUG - Finished Request
2025-06-28 09:43:58,991 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:43:58,998 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:43:58,999 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:43:58,999 - DEBUG - Finished Request
2025-06-28 09:44:00,000 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:00,010 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:00,010 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:00,010 - DEBUG - Finished Request
2025-06-28 09:44:01,011 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:01,021 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:01,022 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:01,022 - DEBUG - Finished Request
2025-06-28 09:44:02,022 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:02,034 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:02,035 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:02,035 - DEBUG - Finished Request
2025-06-28 09:44:03,036 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:03,048 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:03,048 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:03,048 - DEBUG - Finished Request
2025-06-28 09:44:04,049 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:04,058 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:04,059 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:04,059 - DEBUG - Finished Request
2025-06-28 09:44:05,060 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:05,070 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:05,071 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:05,071 - DEBUG - Finished Request
2025-06-28 09:44:06,072 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:06,083 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:06,084 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:06,084 - DEBUG - Finished Request
2025-06-28 09:44:07,085 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:07,094 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:07,095 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:07,095 - DEBUG - Finished Request
2025-06-28 09:44:08,096 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:08,106 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:08,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:08,107 - DEBUG - Finished Request
2025-06-28 09:44:09,108 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:09,118 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:09,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:09,119 - DEBUG - Finished Request
2025-06-28 09:44:10,119 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:10,128 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:10,128 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:10,128 - DEBUG - Finished Request
2025-06-28 09:44:11,129 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:11,139 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:11,139 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:11,139 - DEBUG - Finished Request
2025-06-28 09:44:12,140 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:12,149 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:12,150 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:12,150 - DEBUG - Finished Request
2025-06-28 09:44:13,151 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:13,160 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:13,160 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:13,160 - DEBUG - Finished Request
2025-06-28 09:44:14,161 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:14,170 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:14,170 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:14,170 - DEBUG - Finished Request
2025-06-28 09:44:15,171 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:15,179 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:15,180 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:15,180 - DEBUG - Finished Request
2025-06-28 09:44:16,182 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:16,190 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:16,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:16,190 - DEBUG - Finished Request
2025-06-28 09:44:17,191 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:17,199 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:17,200 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:17,200 - DEBUG - Finished Request
2025-06-28 09:44:18,202 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:18,212 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:18,212 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:18,213 - DEBUG - Finished Request
2025-06-28 09:44:19,213 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:19,225 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:19,225 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:19,226 - DEBUG - Finished Request
2025-06-28 09:44:20,227 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:20,235 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:20,235 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:20,235 - DEBUG - Finished Request
2025-06-28 09:44:21,237 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:21,244 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:21,244 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:21,244 - DEBUG - Finished Request
2025-06-28 09:44:22,246 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:22,253 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:22,253 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:22,253 - DEBUG - Finished Request
2025-06-28 09:44:23,255 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:23,262 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:23,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:23,263 - DEBUG - Finished Request
2025-06-28 09:44:24,264 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:24,273 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:24,273 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:24,274 - DEBUG - Finished Request
2025-06-28 09:44:25,274 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:25,283 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:25,283 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:25,284 - DEBUG - Finished Request
2025-06-28 09:44:26,284 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:26,292 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:26,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:26,293 - DEBUG - Finished Request
2025-06-28 09:44:27,294 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:27,303 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:27,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:27,304 - DEBUG - Finished Request
2025-06-28 09:44:28,305 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:28,313 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:28,313 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:28,314 - DEBUG - Finished Request
2025-06-28 09:44:29,315 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:29,324 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:29,325 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:29,325 - DEBUG - Finished Request
2025-06-28 09:44:30,326 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:30,334 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:30,334 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:30,334 - DEBUG - Finished Request
2025-06-28 09:44:31,336 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:31,344 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:31,344 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:31,345 - DEBUG - Finished Request
2025-06-28 09:44:32,346 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:32,355 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:32,355 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:32,356 - DEBUG - Finished Request
2025-06-28 09:44:33,357 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:33,367 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:33,367 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:33,368 - DEBUG - Finished Request
2025-06-28 09:44:34,368 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:34,377 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:34,378 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:34,378 - DEBUG - Finished Request
2025-06-28 09:44:35,379 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:35,386 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:35,386 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:35,386 - DEBUG - Finished Request
2025-06-28 09:44:36,387 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:36,396 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:36,396 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:36,397 - DEBUG - Finished Request
2025-06-28 09:44:37,398 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:37,405 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:37,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:37,406 - DEBUG - Finished Request
2025-06-28 09:44:38,407 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:38,415 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:38,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:38,416 - DEBUG - Finished Request
2025-06-28 09:44:39,416 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:39,737 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:39,738 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:39,738 - DEBUG - Finished Request
2025-06-28 09:44:40,739 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:40,747 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:40,747 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:40,747 - DEBUG - Finished Request
2025-06-28 09:44:41,748 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:41,756 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:41,756 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:41,757 - DEBUG - Finished Request
2025-06-28 09:44:42,757 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:42,803 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:42,803 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:42,804 - DEBUG - Finished Request
2025-06-28 09:44:43,805 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:43,813 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:43,813 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:43,814 - DEBUG - Finished Request
2025-06-28 09:44:44,814 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:44,822 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:44,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:44,822 - DEBUG - Finished Request
2025-06-28 09:44:45,823 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:45,831 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:45,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:45,832 - DEBUG - Finished Request
2025-06-28 09:44:46,833 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:46,841 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:46,842 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:46,842 - DEBUG - Finished Request
2025-06-28 09:44:47,843 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:47,851 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:47,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:47,852 - DEBUG - Finished Request
2025-06-28 09:44:48,852 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:48,860 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:48,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:48,861 - DEBUG - Finished Request
2025-06-28 09:44:49,862 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:49,870 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:49,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:49,871 - DEBUG - Finished Request
2025-06-28 09:44:50,872 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:50,880 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:50,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:50,881 - DEBUG - Finished Request
2025-06-28 09:44:51,882 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:51,889 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:51,890 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:51,890 - DEBUG - Finished Request
2025-06-28 09:44:52,892 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:52,917 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:52,917 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:52,918 - DEBUG - Finished Request
2025-06-28 09:44:53,918 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:53,926 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:53,926 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:53,927 - DEBUG - Finished Request
2025-06-28 09:44:54,927 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:54,935 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:54,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:54,936 - DEBUG - Finished Request
2025-06-28 09:44:55,937 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:55,943 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:55,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:55,943 - DEBUG - Finished Request
2025-06-28 09:44:56,944 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:56,953 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:56,953 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:56,953 - DEBUG - Finished Request
2025-06-28 09:44:57,954 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:57,962 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:57,962 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:57,962 - DEBUG - Finished Request
2025-06-28 09:44:58,963 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:58,972 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:58,973 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:58,973 - DEBUG - Finished Request
2025-06-28 09:44:59,974 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:44:59,983 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:44:59,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:44:59,983 - DEBUG - Finished Request
2025-06-28 09:45:00,985 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:00,992 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:00,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:00,993 - DEBUG - Finished Request
2025-06-28 09:45:01,994 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:02,002 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:02,002 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:02,002 - DEBUG - Finished Request
2025-06-28 09:45:03,003 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:03,011 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:03,011 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:03,011 - DEBUG - Finished Request
2025-06-28 09:45:04,012 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:04,022 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:04,022 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:04,022 - DEBUG - Finished Request
2025-06-28 09:45:05,023 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:05,031 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:05,032 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:05,032 - DEBUG - Finished Request
2025-06-28 09:45:06,033 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:06,041 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:06,042 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:06,042 - DEBUG - Finished Request
2025-06-28 09:45:07,043 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:07,052 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:07,052 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:07,052 - DEBUG - Finished Request
2025-06-28 09:45:08,053 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:08,063 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:08,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:08,064 - DEBUG - Finished Request
2025-06-28 09:45:09,066 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:09,074 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:09,074 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:09,075 - DEBUG - Finished Request
2025-06-28 09:45:10,075 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:10,082 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:10,083 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:10,083 - DEBUG - Finished Request
2025-06-28 09:45:11,084 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:11,091 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:11,092 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:11,092 - DEBUG - Finished Request
2025-06-28 09:45:12,093 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:12,102 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:12,102 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:12,103 - DEBUG - Finished Request
2025-06-28 09:45:13,104 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:13,112 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:13,112 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:13,113 - DEBUG - Finished Request
2025-06-28 09:45:14,114 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:14,122 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:14,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:14,123 - DEBUG - Finished Request
2025-06-28 09:45:15,124 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:15,132 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:15,132 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:15,133 - DEBUG - Finished Request
2025-06-28 09:45:16,133 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:16,143 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:16,143 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:16,144 - DEBUG - Finished Request
2025-06-28 09:45:17,144 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:17,154 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:17,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:17,154 - DEBUG - Finished Request
2025-06-28 09:45:18,155 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:18,165 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:18,165 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:18,165 - DEBUG - Finished Request
2025-06-28 09:45:19,166 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:19,175 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:19,175 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:19,176 - DEBUG - Finished Request
2025-06-28 09:45:20,177 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:20,185 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:20,185 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:20,185 - DEBUG - Finished Request
2025-06-28 09:45:21,186 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:21,195 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:21,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:21,196 - DEBUG - Finished Request
2025-06-28 09:45:22,198 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:22,207 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:22,208 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:22,208 - DEBUG - Finished Request
2025-06-28 09:45:23,209 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:23,218 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:23,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:23,219 - DEBUG - Finished Request
2025-06-28 09:45:24,220 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:24,230 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:24,231 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:24,231 - DEBUG - Finished Request
2025-06-28 09:45:25,232 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:25,241 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:25,241 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:25,241 - DEBUG - Finished Request
2025-06-28 09:45:26,242 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:26,250 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:26,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:26,251 - DEBUG - Finished Request
2025-06-28 09:45:27,252 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:27,259 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:27,259 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:27,260 - DEBUG - Finished Request
2025-06-28 09:45:28,260 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:28,269 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:28,269 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:28,269 - DEBUG - Finished Request
2025-06-28 09:45:29,270 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:29,280 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:29,280 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:29,280 - DEBUG - Finished Request
2025-06-28 09:45:30,281 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:30,290 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:30,291 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:30,291 - DEBUG - Finished Request
2025-06-28 09:45:31,292 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:31,300 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:31,301 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:31,301 - DEBUG - Finished Request
2025-06-28 09:45:32,302 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:32,311 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:32,311 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:32,312 - DEBUG - Finished Request
2025-06-28 09:45:33,313 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:33,326 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:33,326 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:33,327 - DEBUG - Finished Request
2025-06-28 09:45:34,327 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:34,335 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:34,335 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:34,335 - DEBUG - Finished Request
2025-06-28 09:45:35,337 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:35,347 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:35,347 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:35,348 - DEBUG - Finished Request
2025-06-28 09:45:36,349 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:36,358 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:36,358 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:36,359 - DEBUG - Finished Request
2025-06-28 09:45:37,360 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:37,385 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 200 0
2025-06-28 09:45:37,386 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:37,386 - DEBUG - Finished Request
2025-06-28 09:45:38,387 - DEBUG - GET http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2/url {}
2025-06-28 09:45:38,389 - DEBUG - http://localhost:49852 "GET /session/9564b840235c4e8d4d244604ab99d0d2/url HTTP/1.1" 404 0
2025-06-28 09:45:38,389 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7edaccda5+78885]\n\tGetHandleVerifier [0x0x7ff7edacce00+78976]\n\t(No symbol) [0x0x7ff7ed889bca]\n\t(No symbol) [0x0x7ff7ed861e71]\n\t(No symbol) [0x0x7ff7ed91039e]\n\t(No symbol) [0x0x7ff7ed930b42]\n\t(No symbol) [0x0x7ff7ed908963]\n\t(No symbol) [0x0x7ff7ed8d16b1]\n\t(No symbol) [0x0x7ff7ed8d2443]\n\tGetHandleVerifier [0x0x7ff7edda4eed+3061101]\n\tGetHandleVerifier [0x0x7ff7edd9f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7eddbe592+3165202]\n\tGetHandleVerifier [0x0x7ff7edae730e+186766]\n\tGetHandleVerifier [0x0x7ff7edaeeb3f+217535]\n\tGetHandleVerifier [0x0x7ff7edad59b4+114740]\n\tGetHandleVerifier [0x0x7ff7edad5b69+115177]\n\tGetHandleVerifier [0x0x7ff7edabc368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '987', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:38,389 - DEBUG - Finished Request
2025-06-28 09:45:38,390 - DEBUG - DELETE http://localhost:49852/session/9564b840235c4e8d4d244604ab99d0d2 {}
2025-06-28 09:45:38,392 - DEBUG - http://localhost:49852 "DELETE /session/9564b840235c4e8d4d244604ab99d0d2 HTTP/1.1" 200 0
2025-06-28 09:45:38,392 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 09:45:38,393 - DEBUG - Finished Request
