[02:24:13] 🚀 獨立追蹤工具已啟動
[02:24:13] 💡 使用說明:
[02:24:13]    1. 點擊 '啟動瀏覽器' 開始
[02:24:13]    2. 手動登入並進入編輯頁面
[02:24:13]    3. 點擊 '啟動追蹤' 開始記錄
[02:24:13]    4. 執行操作後點擊 '停止追蹤'
[02:24:13]    5. 點擊 '分析結果' 查看送出按鈕
[02:24:23] 🌐 啟動瀏覽器...
[02:24:28] ✅ 瀏覽器啟動成功
[02:24:28] 📍 已導航到: https://wmc.kcg.gov.tw/
[02:24:28] 💡 請手動登入並進入編輯頁面
[02:28:26] 🎯 啟動滑鼠鍵盤追蹤...
[02:28:26] 📍 當前頁面: https://wmc.kcg.gov.tw/
[02:28:26] 🔧 正在設置事件監控...
[02:28:26] 🎯 設置瀏覽器事件監控...
[02:28:26] 🔧 正在注入事件監控腳本...
[02:28:26] 🔍 驗證事件監控設置...
[02:28:26] 📊 監控狀態: {'hasEventLog': True, 'hasListeners': True, 'iframeCount': 1, 'listenerCount': 20}
[02:28:26] ✅ 瀏覽器事件監控已啟動
[02:28:26] 📋 監控事件類型: click, mousedown, mouseup, keydown, keyup, input, change, submit, focus, blur
[02:28:26] 🎯 監聽器數量: 20
[02:28:26] 🖼️ iframe 數量: 1
[02:28:26] ❌ 啟動追蹤失敗: unknown option "-fg"
[02:28:26] 🔍 詳細錯誤: Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\Project\AGES-KH-Bot\independent_tracking_tool.py", line 475, in start_tracking
    self.status_label.config(text="🔴 追蹤中...", fg="red")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\tkinter\__init__.py", line 1721, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\tkinter\__init__.py", line 1711, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: unknown option "-fg"

[02:30:51] ❌ 檢查瀏覽器狀態失敗: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=137.0.7151.120)
Stacktrace:
	GetHandleVerifier [0x0x7ff6eff8cda5+78885]
	GetHandleVerifier [0x0x7ff6eff8ce00+78976]
	(No symbol) [0x0x7ff6efd49bca]
	(No symbol) [0x0x7ff6efd359b5]
	(No symbol) [0x0x7ff6efd5a9ca]
	(No symbol) [0x0x7ff6efdd05e5]
	(No symbol) [0x0x7ff6efdf0b42]
	(No symbol) [0x0x7ff6efdc8963]
	(No symbol) [0x0x7ff6efd916b1]
	(No symbol) [0x0x7ff6efd92443]
	GetHandleVerifier [0x0x7ff6f0264eed+3061101]
	GetHandleVerifier [0x0x7ff6f025f33d+3037629]
	GetHandleVerifier [0x0x7ff6f027e592+3165202]
	GetHandleVerifier [0x0x7ff6effa730e+186766]
	GetHandleVerifier [0x0x7ff6effaeb3f+217535]
	GetHandleVerifier [0x0x7ff6eff959b4+114740]
	GetHandleVerifier [0x0x7ff6eff95b69+115177]
	GetHandleVerifier [0x0x7ff6eff7c368+10728]
	BaseThreadInitThunk [0x0x7fff8519e8d7+23]
	RtlUserThreadStart [0x0x7fff86c7c34c+44]

[02:30:54] 🧹 清理瀏覽器資源...
