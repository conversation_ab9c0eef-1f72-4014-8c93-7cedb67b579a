2025-06-30 18:44:52,998 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_184452.log
2025-06-30 18:45:13,060 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 18:45:13,060 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 18:45:13,120 - DEBUG - chromedriver not found in PATH
2025-06-30 18:45:13,120 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:45:13,121 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 18:45:13,121 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 18:45:13,121 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 18:45:13,121 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 18:45:13,122 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:45:13,127 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 12392 using 0 to output -3
2025-06-30 18:45:13,652 - DEBUG - POST http://localhost:53736/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 18:45:13,653 - DEBUG - Starting new HTTP connection (1): localhost:53736
2025-06-30 18:45:14,196 - DEBUG - http://localhost:53736 "POST /session HTTP/1.1" 200 0
2025-06-30 18:45:14,197 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir12392_1340320894"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53739"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"b5899f6dda4adf615cba9b7f5311f9c4"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:14,197 - DEBUG - Finished Request
2025-06-30 18:45:14,198 - DEBUG - POST http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 18:45:16,172 - DEBUG - http://localhost:53736 "POST /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:16,172 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:16,172 - DEBUG - Finished Request
2025-06-30 18:45:16,172 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 18:45:16,173 - DEBUG - POST http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 18:45:16,178 - DEBUG - http://localhost:53736 "POST /session/b5899f6dda4adf615cba9b7f5311f9c4/execute/sync HTTP/1.1" 200 0
2025-06-30 18:45:16,178 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:16,179 - DEBUG - Finished Request
2025-06-30 18:45:16,179 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 18:45:16,179 - DEBUG - GET http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {}
2025-06-30 18:45:16,205 - DEBUG - http://localhost:53736 "GET /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:16,205 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:16,206 - DEBUG - Finished Request
2025-06-30 18:45:17,207 - DEBUG - GET http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {}
2025-06-30 18:45:17,215 - DEBUG - http://localhost:53736 "GET /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:17,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:17,216 - DEBUG - Finished Request
2025-06-30 18:45:18,217 - DEBUG - GET http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {}
2025-06-30 18:45:18,224 - DEBUG - http://localhost:53736 "GET /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:18,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:18,224 - DEBUG - Finished Request
2025-06-30 18:45:19,226 - DEBUG - GET http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {}
2025-06-30 18:45:19,232 - DEBUG - http://localhost:53736 "GET /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:19,232 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:19,233 - DEBUG - Finished Request
2025-06-30 18:45:20,233 - DEBUG - GET http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {}
2025-06-30 18:45:20,239 - DEBUG - http://localhost:53736 "GET /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:20,239 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:20,239 - DEBUG - Finished Request
2025-06-30 18:45:21,240 - DEBUG - GET http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4/url {}
2025-06-30 18:45:21,247 - DEBUG - http://localhost:53736 "GET /session/b5899f6dda4adf615cba9b7f5311f9c4/url HTTP/1.1" 200 0
2025-06-30 18:45:21,247 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:21,247 - DEBUG - Finished Request
2025-06-30 18:45:21,958 - DEBUG - DELETE http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4 {}
2025-06-30 18:45:21,997 - DEBUG - http://localhost:53736 "DELETE /session/b5899f6dda4adf615cba9b7f5311f9c4 HTTP/1.1" 200 0
2025-06-30 18:45:21,998 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:45:21,998 - DEBUG - Finished Request
2025-06-30 18:45:22,268 - DEBUG - DELETE http://localhost:53736/session/b5899f6dda4adf615cba9b7f5311f9c4 {}
2025-06-30 18:45:22,269 - DEBUG - Starting new HTTP connection (1): localhost:53736
