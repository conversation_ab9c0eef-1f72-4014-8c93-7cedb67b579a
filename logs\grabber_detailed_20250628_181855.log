2025-06-28 18:18:55,285 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250628_181855.log
2025-06-28 18:18:55,286 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-28 18:18:55,286 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-28 18:18:55,920 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-06-28 18:18:55,920 - DEBUG - chromedriver not found in PATH
2025-06-28 18:18:55,921 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 18:18:55,921 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-28 18:18:55,921 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-06-28 18:18:55,921 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-28 18:18:55,921 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-28 18:18:55,922 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-28 18:18:55,922 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-28 18:18:55,925 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 28280 using 0 to output -3
2025-06-28 18:18:56,442 - DEBUG - POST http://localhost:51438/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-28 18:18:56,443 - DEBUG - Starting new HTTP connection (1): localhost:51438
2025-06-28 18:18:56,999 - DEBUG - http://localhost:51438 "POST /session HTTP/1.1" 200 0
2025-06-28 18:18:57,000 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir28280_807639927"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51443"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"52ee7cb8e53151163ba588b04be05fe7"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:18:57,000 - DEBUG - Finished Request
2025-06-28 18:18:57,000 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-28 18:18:58,071 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:18:58,071 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:18:58,072 - DEBUG - Finished Request
2025-06-28 18:18:58,072 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:18:58,072 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:18:58,081 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:18:58,081 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:18:58,082 - DEBUG - Finished Request
2025-06-28 18:18:58,082 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:18:58,083 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:18:58,107 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:18:58,108 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:18:58,108 - DEBUG - Finished Request
2025-06-28 18:18:59,109 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:18:59,119 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:18:59,120 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:18:59,120 - DEBUG - Finished Request
2025-06-28 18:19:00,121 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:00,129 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:00,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:00,129 - DEBUG - Finished Request
2025-06-28 18:19:01,130 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:01,135 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:01,136 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:01,136 - DEBUG - Finished Request
2025-06-28 18:19:02,137 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:02,141 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:02,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:02,142 - DEBUG - Finished Request
2025-06-28 18:19:03,143 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:03,150 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:03,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:03,151 - DEBUG - Finished Request
2025-06-28 18:19:04,152 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:04,159 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:04,160 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:04,160 - DEBUG - Finished Request
2025-06-28 18:19:05,161 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:05,171 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:05,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:05,172 - DEBUG - Finished Request
2025-06-28 18:19:06,173 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:06,180 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:06,180 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:06,180 - DEBUG - Finished Request
2025-06-28 18:19:07,181 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:07,190 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:07,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:07,190 - DEBUG - Finished Request
2025-06-28 18:19:08,191 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:08,201 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:08,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:08,202 - DEBUG - Finished Request
2025-06-28 18:19:09,203 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:09,212 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:09,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:09,213 - DEBUG - Finished Request
2025-06-28 18:19:10,214 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:10,223 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:10,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:10,224 - DEBUG - Finished Request
2025-06-28 18:19:11,226 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:11,234 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:11,235 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:11,235 - DEBUG - Finished Request
2025-06-28 18:19:12,236 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:12,245 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:12,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:12,246 - DEBUG - Finished Request
2025-06-28 18:19:13,247 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:13,255 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:13,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:13,256 - DEBUG - Finished Request
2025-06-28 18:19:14,256 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:14,266 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:14,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:14,267 - DEBUG - Finished Request
2025-06-28 18:19:15,268 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:15,278 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:15,278 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:15,279 - DEBUG - Finished Request
2025-06-28 18:19:16,279 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:16,288 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:16,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:16,289 - DEBUG - Finished Request
2025-06-28 18:19:17,291 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:17,299 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:17,300 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:17,300 - DEBUG - Finished Request
2025-06-28 18:19:18,301 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:18,310 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:18,310 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:18,310 - DEBUG - Finished Request
2025-06-28 18:19:19,311 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:19,319 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:19,320 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:19,320 - DEBUG - Finished Request
2025-06-28 18:19:20,321 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:20,329 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:20,329 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:20,330 - DEBUG - Finished Request
2025-06-28 18:19:21,331 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:21,340 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:21,340 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:21,341 - DEBUG - Finished Request
2025-06-28 18:19:22,341 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:22,350 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:22,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:22,351 - DEBUG - Finished Request
2025-06-28 18:19:23,352 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:23,360 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:23,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:23,361 - DEBUG - Finished Request
2025-06-28 18:19:24,362 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:24,370 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:24,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:24,371 - DEBUG - Finished Request
2025-06-28 18:19:25,373 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:25,380 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:25,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:25,381 - DEBUG - Finished Request
2025-06-28 18:19:26,382 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:26,389 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:26,389 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:26,390 - DEBUG - Finished Request
2025-06-28 18:19:27,391 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:27,399 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:27,399 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:27,400 - DEBUG - Finished Request
2025-06-28 18:19:28,401 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:28,409 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:28,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:28,410 - DEBUG - Finished Request
2025-06-28 18:19:29,411 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:29,418 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:29,418 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:29,419 - DEBUG - Finished Request
2025-06-28 18:19:30,420 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:30,428 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:30,428 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:30,429 - DEBUG - Finished Request
2025-06-28 18:19:31,429 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:31,437 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:31,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:31,438 - DEBUG - Finished Request
2025-06-28 18:19:32,439 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:32,447 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:32,447 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:32,447 - DEBUG - Finished Request
2025-06-28 18:19:33,448 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:33,455 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:33,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:33,456 - DEBUG - Finished Request
2025-06-28 18:19:34,457 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:34,472 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:34,472 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:34,473 - DEBUG - Finished Request
2025-06-28 18:19:35,474 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:35,483 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:35,484 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:35,484 - DEBUG - Finished Request
2025-06-28 18:19:36,485 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:36,494 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:36,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:36,495 - DEBUG - Finished Request
2025-06-28 18:19:37,496 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:37,505 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:37,506 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:37,506 - DEBUG - Finished Request
2025-06-28 18:19:38,507 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:38,516 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:38,517 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:38,517 - DEBUG - Finished Request
2025-06-28 18:19:39,517 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:39,918 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:39,918 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:39,919 - DEBUG - Finished Request
2025-06-28 18:19:40,920 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:40,929 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:40,929 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:40,930 - DEBUG - Finished Request
2025-06-28 18:19:41,931 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:41,939 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:41,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:41,940 - DEBUG - Finished Request
2025-06-28 18:19:42,941 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:42,949 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:42,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:42,949 - DEBUG - Finished Request
2025-06-28 18:19:43,950 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:43,959 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:43,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:43,960 - DEBUG - Finished Request
2025-06-28 18:19:44,960 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:44,968 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:44,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:44,969 - DEBUG - Finished Request
2025-06-28 18:19:45,970 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:45,979 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:45,979 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:45,980 - DEBUG - Finished Request
2025-06-28 18:19:46,980 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:46,988 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:46,988 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:46,989 - DEBUG - Finished Request
2025-06-28 18:19:47,990 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:47,997 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:47,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:47,998 - DEBUG - Finished Request
2025-06-28 18:19:48,999 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:49,006 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:49,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:49,007 - DEBUG - Finished Request
2025-06-28 18:19:50,008 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:50,014 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:50,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:50,015 - DEBUG - Finished Request
2025-06-28 18:19:51,016 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:51,023 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:51,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:51,024 - DEBUG - Finished Request
2025-06-28 18:19:52,025 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:52,033 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:52,034 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:52,034 - DEBUG - Finished Request
2025-06-28 18:19:53,035 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:53,042 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:53,042 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:53,043 - DEBUG - Finished Request
2025-06-28 18:19:54,044 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:54,052 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:54,052 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:54,053 - DEBUG - Finished Request
2025-06-28 18:19:55,053 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:55,065 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:55,066 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:55,066 - DEBUG - Finished Request
2025-06-28 18:19:56,068 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:56,082 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:56,082 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:56,082 - DEBUG - Finished Request
2025-06-28 18:19:57,083 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:57,090 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:57,090 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:57,091 - DEBUG - Finished Request
2025-06-28 18:19:58,092 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:58,099 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:58,099 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:58,100 - DEBUG - Finished Request
2025-06-28 18:19:59,101 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:19:59,109 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:19:59,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:19:59,110 - DEBUG - Finished Request
2025-06-28 18:20:00,111 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:00,123 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:00,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:00,124 - DEBUG - Finished Request
2025-06-28 18:20:01,125 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:01,133 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:01,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:01,134 - DEBUG - Finished Request
2025-06-28 18:20:02,134 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:02,144 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:02,144 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:02,145 - DEBUG - Finished Request
2025-06-28 18:20:03,146 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:03,153 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:03,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:03,154 - DEBUG - Finished Request
2025-06-28 18:20:04,155 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:04,165 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:04,165 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:04,165 - DEBUG - Finished Request
2025-06-28 18:20:05,166 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:05,175 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:05,175 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:05,176 - DEBUG - Finished Request
2025-06-28 18:20:06,177 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:06,186 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:06,186 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:06,187 - DEBUG - Finished Request
2025-06-28 18:20:07,188 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:07,198 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:07,198 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:07,198 - DEBUG - Finished Request
2025-06-28 18:20:08,199 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:08,208 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:08,209 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:08,209 - DEBUG - Finished Request
2025-06-28 18:20:09,210 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:09,219 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:09,220 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:09,220 - DEBUG - Finished Request
2025-06-28 18:20:10,221 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:10,231 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:10,231 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:10,232 - DEBUG - Finished Request
2025-06-28 18:20:11,233 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:11,242 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:11,242 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:11,242 - DEBUG - Finished Request
2025-06-28 18:20:12,244 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:12,254 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:12,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:12,255 - DEBUG - Finished Request
2025-06-28 18:20:13,255 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:13,266 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:13,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:13,267 - DEBUG - Finished Request
2025-06-28 18:20:14,268 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:14,276 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:14,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:14,276 - DEBUG - Finished Request
2025-06-28 18:20:15,278 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:15,321 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:15,321 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:15,322 - DEBUG - Finished Request
2025-06-28 18:20:16,323 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:16,331 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:16,332 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:16,332 - DEBUG - Finished Request
2025-06-28 18:20:17,333 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:17,341 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:17,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:17,342 - DEBUG - Finished Request
2025-06-28 18:20:18,342 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:18,350 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:18,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:18,351 - DEBUG - Finished Request
2025-06-28 18:20:19,352 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:19,362 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:19,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:19,363 - DEBUG - Finished Request
2025-06-28 18:20:20,364 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:20,371 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:20,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:20,372 - DEBUG - Finished Request
2025-06-28 18:20:21,373 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:21,380 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:21,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:21,380 - DEBUG - Finished Request
2025-06-28 18:20:22,382 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:22,390 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:22,390 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:22,391 - DEBUG - Finished Request
2025-06-28 18:20:23,392 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:23,401 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:23,402 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:23,402 - DEBUG - Finished Request
2025-06-28 18:20:24,404 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:24,414 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:24,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:24,414 - DEBUG - Finished Request
2025-06-28 18:20:25,415 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:25,423 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:25,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:25,424 - DEBUG - Finished Request
2025-06-28 18:20:26,424 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:26,433 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:26,433 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:26,433 - DEBUG - Finished Request
2025-06-28 18:20:27,434 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:27,441 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:27,442 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:27,442 - DEBUG - Finished Request
2025-06-28 18:20:28,443 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:28,453 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:28,453 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:28,453 - DEBUG - Finished Request
2025-06-28 18:20:29,455 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:29,464 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:29,464 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:29,465 - DEBUG - Finished Request
2025-06-28 18:20:30,465 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:30,474 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:30,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:30,475 - DEBUG - Finished Request
2025-06-28 18:20:31,475 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:31,483 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:31,484 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:31,484 - DEBUG - Finished Request
2025-06-28 18:20:32,485 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:32,494 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:32,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:32,494 - DEBUG - Finished Request
2025-06-28 18:20:33,495 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:33,503 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:33,503 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:33,504 - DEBUG - Finished Request
2025-06-28 18:20:34,505 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:34,513 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:34,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:34,514 - DEBUG - Finished Request
2025-06-28 18:20:35,516 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:35,522 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:35,522 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:35,523 - DEBUG - Finished Request
2025-06-28 18:20:36,524 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:36,533 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:36,534 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:36,534 - DEBUG - Finished Request
2025-06-28 18:20:37,535 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:37,541 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:37,541 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:37,542 - DEBUG - Finished Request
2025-06-28 18:20:38,543 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:38,553 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:38,553 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:38,554 - DEBUG - Finished Request
2025-06-28 18:20:39,555 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:39,563 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:39,564 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:39,564 - DEBUG - Finished Request
2025-06-28 18:20:40,565 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:40,576 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:40,576 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:40,576 - DEBUG - Finished Request
2025-06-28 18:20:41,577 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:41,585 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:41,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:41,586 - DEBUG - Finished Request
2025-06-28 18:20:42,587 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:42,596 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:42,597 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:42,597 - DEBUG - Finished Request
2025-06-28 18:20:43,598 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:43,607 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:43,607 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:43,607 - DEBUG - Finished Request
2025-06-28 18:20:44,608 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:44,616 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:44,617 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:44,617 - DEBUG - Finished Request
2025-06-28 18:20:45,617 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:45,626 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:45,626 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:45,626 - DEBUG - Finished Request
2025-06-28 18:20:46,628 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:46,637 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:46,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:46,637 - DEBUG - Finished Request
2025-06-28 18:20:47,638 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:47,646 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:47,646 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:47,646 - DEBUG - Finished Request
2025-06-28 18:20:48,647 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:48,656 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:48,657 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:48,657 - DEBUG - Finished Request
2025-06-28 18:20:49,657 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:49,665 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:49,665 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:49,665 - DEBUG - Finished Request
2025-06-28 18:20:50,667 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:50,674 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:50,674 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:50,674 - DEBUG - Finished Request
2025-06-28 18:20:51,675 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:51,683 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:51,683 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:51,683 - DEBUG - Finished Request
2025-06-28 18:20:52,685 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:52,694 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:52,694 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:52,694 - DEBUG - Finished Request
2025-06-28 18:20:53,696 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:53,703 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:53,704 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:53,704 - DEBUG - Finished Request
2025-06-28 18:20:54,705 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:54,715 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:54,715 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:54,715 - DEBUG - Finished Request
2025-06-28 18:20:55,716 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:55,724 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:55,724 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:55,724 - DEBUG - Finished Request
2025-06-28 18:20:56,725 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:56,734 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:56,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:56,734 - DEBUG - Finished Request
2025-06-28 18:20:57,735 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:57,742 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:57,742 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:57,742 - DEBUG - Finished Request
2025-06-28 18:20:58,744 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:58,751 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:58,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:58,752 - DEBUG - Finished Request
2025-06-28 18:20:59,753 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:20:59,762 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:20:59,762 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:20:59,762 - DEBUG - Finished Request
2025-06-28 18:21:00,763 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:00,772 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:00,772 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:00,773 - DEBUG - Finished Request
2025-06-28 18:21:01,775 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:01,784 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:01,785 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:01,785 - DEBUG - Finished Request
2025-06-28 18:21:02,786 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:02,794 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:02,795 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:02,795 - DEBUG - Finished Request
2025-06-28 18:21:03,797 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:03,807 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:03,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:03,808 - DEBUG - Finished Request
2025-06-28 18:21:04,809 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:04,818 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:04,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:04,819 - DEBUG - Finished Request
2025-06-28 18:21:05,819 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:05,828 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:05,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:05,829 - DEBUG - Finished Request
2025-06-28 18:21:06,830 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:06,840 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:06,841 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:06,841 - DEBUG - Finished Request
2025-06-28 18:21:07,842 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:07,851 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:07,852 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:07,853 - DEBUG - Finished Request
2025-06-28 18:21:08,854 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:08,863 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:08,864 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:08,864 - DEBUG - Finished Request
2025-06-28 18:21:09,865 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:09,874 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:09,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:09,875 - DEBUG - Finished Request
2025-06-28 18:21:10,876 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:10,886 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:10,887 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:10,887 - DEBUG - Finished Request
2025-06-28 18:21:11,888 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:11,897 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:11,897 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:11,898 - DEBUG - Finished Request
2025-06-28 18:21:12,900 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:12,908 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:12,908 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:12,909 - DEBUG - Finished Request
2025-06-28 18:21:13,910 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:13,919 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:13,920 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:13,920 - DEBUG - Finished Request
2025-06-28 18:21:14,922 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:14,930 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:14,930 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:14,931 - DEBUG - Finished Request
2025-06-28 18:21:15,454 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:21:15,454 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:21:15,461 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:15,461 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:15,462 - DEBUG - Finished Request
2025-06-28 18:21:15,462 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:21:15,486 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:15,492 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:15,493 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:15,493 - DEBUG - Finished Request
2025-06-28 18:21:15,932 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:15,939 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:15,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:15,939 - DEBUG - Finished Request
2025-06-28 18:21:15,994 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:16,000 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:16,000 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:16,000 - DEBUG - Finished Request
2025-06-28 18:21:16,506 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:16,514 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:16,514 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:16,514 - DEBUG - Finished Request
2025-06-28 18:21:16,940 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:16,947 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:16,947 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:16,947 - DEBUG - Finished Request
2025-06-28 18:21:17,021 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:17,028 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:17,028 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:17,028 - DEBUG - Finished Request
2025-06-28 18:21:17,535 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:17,544 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:17,544 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:17,544 - DEBUG - Finished Request
2025-06-28 18:21:17,949 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:17,958 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:17,958 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:17,958 - DEBUG - Finished Request
2025-06-28 18:21:18,045 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:18,053 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:18,053 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:18,053 - DEBUG - Finished Request
2025-06-28 18:21:18,559 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:18,567 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:18,568 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:18,568 - DEBUG - Finished Request
2025-06-28 18:21:18,959 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:18,969 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:18,970 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:18,970 - DEBUG - Finished Request
2025-06-28 18:21:19,072 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:19,080 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:19,080 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:19,081 - DEBUG - Finished Request
2025-06-28 18:21:19,586 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:19,594 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:19,594 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:19,594 - DEBUG - Finished Request
2025-06-28 18:21:19,972 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:19,981 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:19,981 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:19,982 - DEBUG - Finished Request
2025-06-28 18:21:20,100 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:20,109 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:20,109 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:20,109 - DEBUG - Finished Request
2025-06-28 18:21:20,613 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:20,620 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:20,620 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:20,621 - DEBUG - Finished Request
2025-06-28 18:21:20,983 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:20,990 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:20,991 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:20,991 - DEBUG - Finished Request
2025-06-28 18:21:21,130 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:21,136 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:21,137 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:21,137 - DEBUG - Finished Request
2025-06-28 18:21:21,638 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:21,646 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:21,646 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:21,647 - DEBUG - Finished Request
2025-06-28 18:21:21,993 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:22,000 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:22,001 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:22,001 - DEBUG - Finished Request
2025-06-28 18:21:22,154 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:22,162 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:22,162 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:22,163 - DEBUG - Finished Request
2025-06-28 18:21:22,665 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:22,674 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:22,674 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:22,675 - DEBUG - Finished Request
2025-06-28 18:21:23,002 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:23,009 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:23,010 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:23,010 - DEBUG - Finished Request
2025-06-28 18:21:23,183 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:23,190 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:23,190 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:23,190 - DEBUG - Finished Request
2025-06-28 18:21:23,694 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:23,700 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:23,700 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:23,701 - DEBUG - Finished Request
2025-06-28 18:21:24,011 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:24,017 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:24,017 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:24,018 - DEBUG - Finished Request
2025-06-28 18:21:24,206 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:24,215 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:24,215 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:24,216 - DEBUG - Finished Request
2025-06-28 18:21:24,720 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:24,728 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:24,729 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:24,729 - DEBUG - Finished Request
2025-06-28 18:21:25,018 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:25,025 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:25,025 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:25,025 - DEBUG - Finished Request
2025-06-28 18:21:25,231 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:25,237 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:25,237 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:25,238 - DEBUG - Finished Request
2025-06-28 18:21:25,745 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:25,750 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:25,751 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:25,752 - DEBUG - Finished Request
2025-06-28 18:21:26,027 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:26,034 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:26,034 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:26,035 - DEBUG - Finished Request
2025-06-28 18:21:26,260 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:26,266 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:26,266 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:26,267 - DEBUG - Finished Request
2025-06-28 18:21:26,771 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:26,779 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:26,780 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:26,780 - DEBUG - Finished Request
2025-06-28 18:21:27,036 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:27,044 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:27,044 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:27,044 - DEBUG - Finished Request
2025-06-28 18:21:27,285 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:27,292 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:27,293 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:27,293 - DEBUG - Finished Request
2025-06-28 18:21:27,797 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:27,804 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:27,805 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:27,805 - DEBUG - Finished Request
2025-06-28 18:21:28,045 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:28,052 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:28,053 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:28,053 - DEBUG - Finished Request
2025-06-28 18:21:28,316 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:28,323 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:28,323 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:28,324 - DEBUG - Finished Request
2025-06-28 18:21:28,832 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:28,840 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:28,840 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:28,841 - DEBUG - Finished Request
2025-06-28 18:21:29,054 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:29,065 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:29,065 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:29,066 - DEBUG - Finished Request
2025-06-28 18:21:29,342 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:29,349 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:29,350 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:29,350 - DEBUG - Finished Request
2025-06-28 18:21:29,853 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:29,860 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:29,861 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:29,861 - DEBUG - Finished Request
2025-06-28 18:21:30,067 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:30,073 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:30,073 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:30,074 - DEBUG - Finished Request
2025-06-28 18:21:30,366 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:30,372 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:30,372 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:30,373 - DEBUG - Finished Request
2025-06-28 18:21:30,882 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:30,891 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:30,891 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:30,892 - DEBUG - Finished Request
2025-06-28 18:21:31,075 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:31,083 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:31,084 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:31,084 - DEBUG - Finished Request
2025-06-28 18:21:31,399 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:31,407 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:31,407 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:31,408 - DEBUG - Finished Request
2025-06-28 18:21:31,915 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:31,923 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:31,923 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:31,924 - DEBUG - Finished Request
2025-06-28 18:21:32,085 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:32,092 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:32,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:32,093 - DEBUG - Finished Request
2025-06-28 18:21:32,433 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:32,441 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:32,441 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:32,442 - DEBUG - Finished Request
2025-06-28 18:21:32,944 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:32,950 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:32,951 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:32,951 - DEBUG - Finished Request
2025-06-28 18:21:33,095 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:33,102 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:33,103 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:33,103 - DEBUG - Finished Request
2025-06-28 18:21:33,457 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:33,465 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:33,465 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:33,466 - DEBUG - Finished Request
2025-06-28 18:21:33,967 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:33,974 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:33,974 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:33,975 - DEBUG - Finished Request
2025-06-28 18:21:34,104 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:34,110 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:34,111 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:34,111 - DEBUG - Finished Request
2025-06-28 18:21:34,482 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:34,488 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:34,488 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:34,489 - DEBUG - Finished Request
2025-06-28 18:21:34,995 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:35,002 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:35,002 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:35,003 - DEBUG - Finished Request
2025-06-28 18:21:35,112 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:35,118 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:35,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:35,119 - DEBUG - Finished Request
2025-06-28 18:21:35,510 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:35,518 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:35,518 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:35,519 - DEBUG - Finished Request
2025-06-28 18:21:36,021 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:36,028 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:36,028 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:36,029 - DEBUG - Finished Request
2025-06-28 18:21:36,121 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:36,128 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:36,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:36,129 - DEBUG - Finished Request
2025-06-28 18:21:36,536 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:36,543 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:36,543 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:36,544 - DEBUG - Finished Request
2025-06-28 18:21:37,056 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:37,063 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:37,064 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:37,064 - DEBUG - Finished Request
2025-06-28 18:21:37,130 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:37,138 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:37,138 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:37,139 - DEBUG - Finished Request
2025-06-28 18:21:37,571 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:37,579 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:37,580 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:37,580 - DEBUG - Finished Request
2025-06-28 18:21:38,083 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:38,091 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:38,091 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:38,091 - DEBUG - Finished Request
2025-06-28 18:21:38,139 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:38,147 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:38,147 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:38,147 - DEBUG - Finished Request
2025-06-28 18:21:38,599 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:38,608 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:38,608 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:38,609 - DEBUG - Finished Request
2025-06-28 18:21:39,116 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:39,123 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:39,123 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:39,124 - DEBUG - Finished Request
2025-06-28 18:21:39,148 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:39,154 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:39,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:39,155 - DEBUG - Finished Request
2025-06-28 18:21:39,630 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:39,637 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:39,637 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:39,637 - DEBUG - Finished Request
2025-06-28 18:21:40,147 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:40,156 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:40,156 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:40,156 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:40,157 - DEBUG - Finished Request
2025-06-28 18:21:40,162 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:40,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:40,162 - DEBUG - Finished Request
2025-06-28 18:21:40,665 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:40,672 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:40,673 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:40,673 - DEBUG - Finished Request
2025-06-28 18:21:41,164 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:41,171 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:41,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:41,172 - DEBUG - Finished Request
2025-06-28 18:21:41,182 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:41,188 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:41,188 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:41,188 - DEBUG - Finished Request
2025-06-28 18:21:41,691 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:41,699 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:41,699 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:41,700 - DEBUG - Finished Request
2025-06-28 18:21:42,173 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:42,180 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:42,180 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:42,181 - DEBUG - Finished Request
2025-06-28 18:21:42,202 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:42,208 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:42,209 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:42,210 - DEBUG - Finished Request
2025-06-28 18:21:42,723 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:42,729 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:42,730 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:42,730 - DEBUG - Finished Request
2025-06-28 18:21:43,182 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:43,189 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:43,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:43,190 - DEBUG - Finished Request
2025-06-28 18:21:43,235 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:43,240 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:43,241 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:43,242 - DEBUG - Finished Request
2025-06-28 18:21:43,748 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:43,755 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:43,756 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:43,756 - DEBUG - Finished Request
2025-06-28 18:21:44,191 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:44,200 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:44,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:44,201 - DEBUG - Finished Request
2025-06-28 18:21:44,262 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:44,269 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:44,269 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:44,269 - DEBUG - Finished Request
2025-06-28 18:21:44,776 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:44,782 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:44,783 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:44,783 - DEBUG - Finished Request
2025-06-28 18:21:45,203 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:45,212 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:45,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:45,213 - DEBUG - Finished Request
2025-06-28 18:21:45,286 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:45,292 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:45,293 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:45,293 - DEBUG - Finished Request
2025-06-28 18:21:45,799 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:45,806 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:45,807 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:45,807 - DEBUG - Finished Request
2025-06-28 18:21:46,214 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:46,222 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:46,222 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:46,223 - DEBUG - Finished Request
2025-06-28 18:21:46,314 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:46,321 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:46,322 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:46,322 - DEBUG - Finished Request
2025-06-28 18:21:46,828 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:46,837 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:46,838 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:46,838 - DEBUG - Finished Request
2025-06-28 18:21:47,223 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:47,232 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:47,232 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:47,233 - DEBUG - Finished Request
2025-06-28 18:21:47,342 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:47,350 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:47,350 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:47,351 - DEBUG - Finished Request
2025-06-28 18:21:47,855 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:47,864 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:47,865 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:47,865 - DEBUG - Finished Request
2025-06-28 18:21:48,234 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:48,241 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:48,241 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:48,242 - DEBUG - Finished Request
2025-06-28 18:21:48,370 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:48,377 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:48,378 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:48,378 - DEBUG - Finished Request
2025-06-28 18:21:48,885 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:48,894 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:48,894 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:48,895 - DEBUG - Finished Request
2025-06-28 18:21:49,243 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:49,252 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:49,252 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:49,253 - DEBUG - Finished Request
2025-06-28 18:21:49,398 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:49,406 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:49,406 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:49,407 - DEBUG - Finished Request
2025-06-28 18:21:49,913 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:49,922 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:49,922 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:49,922 - DEBUG - Finished Request
2025-06-28 18:21:50,253 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:50,259 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:50,260 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:50,260 - DEBUG - Finished Request
2025-06-28 18:21:50,428 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:50,435 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:50,435 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:50,435 - DEBUG - Finished Request
2025-06-28 18:21:50,939 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:50,946 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:50,946 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:50,947 - DEBUG - Finished Request
2025-06-28 18:21:51,263 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:51,272 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:51,272 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:51,273 - DEBUG - Finished Request
2025-06-28 18:21:51,452 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:51,458 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:51,458 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:51,459 - DEBUG - Finished Request
2025-06-28 18:21:51,963 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:51,971 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:51,971 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:51,972 - DEBUG - Finished Request
2025-06-28 18:21:52,273 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:52,280 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:52,280 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:52,281 - DEBUG - Finished Request
2025-06-28 18:21:52,476 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:21:52,483 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:21:52,484 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:52,484 - DEBUG - Finished Request
2025-06-28 18:21:53,282 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:53,289 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:53,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:53,289 - DEBUG - Finished Request
2025-06-28 18:21:54,290 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:54,298 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:54,298 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:54,298 - DEBUG - Finished Request
2025-06-28 18:21:55,299 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:55,306 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:55,306 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:55,306 - DEBUG - Finished Request
2025-06-28 18:21:56,307 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:56,312 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:56,313 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:56,313 - DEBUG - Finished Request
2025-06-28 18:21:57,313 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:57,320 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:57,320 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:57,321 - DEBUG - Finished Request
2025-06-28 18:21:58,321 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:58,330 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:58,330 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:58,330 - DEBUG - Finished Request
2025-06-28 18:21:59,331 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:21:59,339 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:21:59,339 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:21:59,339 - DEBUG - Finished Request
2025-06-28 18:22:00,341 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:00,350 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:00,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:00,350 - DEBUG - Finished Request
2025-06-28 18:22:01,351 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:01,359 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:01,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:01,360 - DEBUG - Finished Request
2025-06-28 18:22:02,361 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:02,370 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:02,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:02,371 - DEBUG - Finished Request
2025-06-28 18:22:03,372 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:03,380 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:03,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:03,380 - DEBUG - Finished Request
2025-06-28 18:22:04,382 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:04,390 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:04,390 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:04,390 - DEBUG - Finished Request
2025-06-28 18:22:05,391 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:05,399 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:05,400 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:05,400 - DEBUG - Finished Request
2025-06-28 18:22:06,400 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:06,409 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:06,409 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:06,409 - DEBUG - Finished Request
2025-06-28 18:22:07,411 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:07,420 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:07,420 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:07,420 - DEBUG - Finished Request
2025-06-28 18:22:08,421 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:08,430 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:08,430 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:08,430 - DEBUG - Finished Request
2025-06-28 18:22:09,432 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:09,444 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:09,444 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:09,444 - DEBUG - Finished Request
2025-06-28 18:22:10,444 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:10,452 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:10,452 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:10,452 - DEBUG - Finished Request
2025-06-28 18:22:11,454 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:11,464 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:11,464 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:11,465 - DEBUG - Finished Request
2025-06-28 18:22:12,465 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:12,474 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:12,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:12,474 - DEBUG - Finished Request
2025-06-28 18:22:13,474 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:13,482 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:13,482 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:13,483 - DEBUG - Finished Request
2025-06-28 18:22:14,483 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:14,491 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:14,491 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:14,491 - DEBUG - Finished Request
2025-06-28 18:22:15,491 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:15,497 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:15,497 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:15,497 - DEBUG - Finished Request
2025-06-28 18:22:16,499 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:16,507 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:16,508 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:16,508 - DEBUG - Finished Request
2025-06-28 18:22:17,510 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:17,517 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:17,517 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:17,517 - DEBUG - Finished Request
2025-06-28 18:22:18,518 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:18,524 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:18,524 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:18,525 - DEBUG - Finished Request
2025-06-28 18:22:19,526 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:19,535 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:19,535 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:19,535 - DEBUG - Finished Request
2025-06-28 18:22:20,535 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:20,544 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:20,544 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:20,545 - DEBUG - Finished Request
2025-06-28 18:22:21,547 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:21,555 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:21,555 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:21,556 - DEBUG - Finished Request
2025-06-28 18:22:22,556 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:22,566 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:22,566 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:22,566 - DEBUG - Finished Request
2025-06-28 18:22:23,568 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:23,575 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:23,575 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:23,575 - DEBUG - Finished Request
2025-06-28 18:22:24,576 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:24,582 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:24,583 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:24,583 - DEBUG - Finished Request
2025-06-28 18:22:25,584 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:25,595 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:25,595 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:25,595 - DEBUG - Finished Request
2025-06-28 18:22:26,597 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:26,605 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:26,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:26,605 - DEBUG - Finished Request
2025-06-28 18:22:27,606 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:27,615 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:27,615 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:27,616 - DEBUG - Finished Request
2025-06-28 18:22:28,617 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:28,626 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:28,626 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:28,627 - DEBUG - Finished Request
2025-06-28 18:22:29,628 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:29,636 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:29,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:29,637 - DEBUG - Finished Request
2025-06-28 18:22:30,638 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:30,646 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:30,647 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:30,647 - DEBUG - Finished Request
2025-06-28 18:22:31,648 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:31,656 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:31,657 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:31,657 - DEBUG - Finished Request
2025-06-28 18:22:32,658 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:32,667 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:32,667 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:32,668 - DEBUG - Finished Request
2025-06-28 18:22:33,669 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:33,675 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:33,676 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:33,676 - DEBUG - Finished Request
2025-06-28 18:22:34,678 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:34,687 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:34,687 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:34,687 - DEBUG - Finished Request
2025-06-28 18:22:35,688 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:35,698 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:35,698 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:35,699 - DEBUG - Finished Request
2025-06-28 18:22:36,700 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:36,708 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:36,708 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:36,708 - DEBUG - Finished Request
2025-06-28 18:22:37,709 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:37,720 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:37,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:37,720 - DEBUG - Finished Request
2025-06-28 18:22:38,721 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:38,729 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:38,730 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:38,730 - DEBUG - Finished Request
2025-06-28 18:22:39,731 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:39,740 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:39,740 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:39,740 - DEBUG - Finished Request
2025-06-28 18:22:40,742 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:40,751 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:40,751 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:40,751 - DEBUG - Finished Request
2025-06-28 18:22:41,752 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:41,760 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:41,760 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:41,760 - DEBUG - Finished Request
2025-06-28 18:22:42,761 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:42,770 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:42,770 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:42,770 - DEBUG - Finished Request
2025-06-28 18:22:43,771 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:43,780 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:43,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:43,780 - DEBUG - Finished Request
2025-06-28 18:22:44,781 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:44,789 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:44,790 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:44,790 - DEBUG - Finished Request
2025-06-28 18:22:45,791 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:45,801 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:45,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:45,801 - DEBUG - Finished Request
2025-06-28 18:22:46,802 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:46,810 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:46,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:46,811 - DEBUG - Finished Request
2025-06-28 18:22:47,811 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:47,818 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:47,819 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:47,819 - DEBUG - Finished Request
2025-06-28 18:22:48,820 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:48,828 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:48,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:48,829 - DEBUG - Finished Request
2025-06-28 18:22:49,830 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:49,840 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:49,840 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:49,840 - DEBUG - Finished Request
2025-06-28 18:22:50,841 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:50,849 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:50,849 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:50,849 - DEBUG - Finished Request
2025-06-28 18:22:51,851 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:51,859 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:51,860 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:51,860 - DEBUG - Finished Request
2025-06-28 18:22:52,860 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:52,869 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:52,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:52,869 - DEBUG - Finished Request
2025-06-28 18:22:53,870 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:53,877 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:53,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:53,877 - DEBUG - Finished Request
2025-06-28 18:22:54,286 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:22:54,287 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:22:54,294 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:54,295 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:54,295 - DEBUG - Finished Request
2025-06-28 18:22:54,295 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:22:54,299 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:54,305 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:54,306 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:54,306 - DEBUG - Finished Request
2025-06-28 18:22:54,814 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:54,823 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:54,823 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:54,823 - DEBUG - Finished Request
2025-06-28 18:22:54,878 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:54,887 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:54,887 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:54,887 - DEBUG - Finished Request
2025-06-28 18:22:55,325 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:55,333 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:55,333 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:55,333 - DEBUG - Finished Request
2025-06-28 18:22:55,834 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:55,842 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:55,842 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:55,842 - DEBUG - Finished Request
2025-06-28 18:22:55,888 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:55,896 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:55,896 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:55,896 - DEBUG - Finished Request
2025-06-28 18:22:56,351 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:56,359 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:56,359 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:56,359 - DEBUG - Finished Request
2025-06-28 18:22:56,865 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:56,873 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:56,873 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:56,873 - DEBUG - Finished Request
2025-06-28 18:22:56,897 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:56,904 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:56,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:56,904 - DEBUG - Finished Request
2025-06-28 18:22:57,376 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:57,383 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:57,384 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:57,384 - DEBUG - Finished Request
2025-06-28 18:22:57,891 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:57,898 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:57,898 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:57,898 - DEBUG - Finished Request
2025-06-28 18:22:57,905 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:57,911 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:57,911 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:57,912 - DEBUG - Finished Request
2025-06-28 18:22:58,409 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:58,417 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:58,417 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:58,417 - DEBUG - Finished Request
2025-06-28 18:22:58,912 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:58,919 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:58,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:58,920 - DEBUG - Finished Request
2025-06-28 18:22:58,926 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:58,931 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:58,932 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:58,932 - DEBUG - Finished Request
2025-06-28 18:22:59,442 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:59,450 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:59,450 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:59,450 - DEBUG - Finished Request
2025-06-28 18:22:59,921 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:22:59,931 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:22:59,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:59,932 - DEBUG - Finished Request
2025-06-28 18:22:59,958 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:22:59,966 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:22:59,966 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:22:59,966 - DEBUG - Finished Request
2025-06-28 18:23:00,471 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:00,479 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:00,479 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:00,479 - DEBUG - Finished Request
2025-06-28 18:23:00,933 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:00,945 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:00,945 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:00,945 - DEBUG - Finished Request
2025-06-28 18:23:00,984 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:00,993 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:00,993 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:00,993 - DEBUG - Finished Request
2025-06-28 18:23:01,500 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:01,509 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:01,509 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:01,509 - DEBUG - Finished Request
2025-06-28 18:23:01,947 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:01,957 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:01,957 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:01,957 - DEBUG - Finished Request
2025-06-28 18:23:02,015 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:02,022 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:02,022 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:02,022 - DEBUG - Finished Request
2025-06-28 18:23:02,523 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:02,531 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:02,531 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:02,531 - DEBUG - Finished Request
2025-06-28 18:23:02,957 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:02,964 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:02,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:02,965 - DEBUG - Finished Request
2025-06-28 18:23:03,038 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:03,046 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:03,046 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:03,046 - DEBUG - Finished Request
2025-06-28 18:23:03,547 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:03,554 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:03,554 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:03,554 - DEBUG - Finished Request
2025-06-28 18:23:03,966 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:03,974 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:03,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:03,974 - DEBUG - Finished Request
2025-06-28 18:23:04,056 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:04,064 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:04,064 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:04,064 - DEBUG - Finished Request
2025-06-28 18:23:04,570 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:04,577 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:04,577 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:04,577 - DEBUG - Finished Request
2025-06-28 18:23:04,975 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:04,983 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:04,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:04,983 - DEBUG - Finished Request
2025-06-28 18:23:05,080 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:05,088 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:05,088 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:05,089 - DEBUG - Finished Request
2025-06-28 18:23:05,593 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:05,601 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:05,601 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:05,602 - DEBUG - Finished Request
2025-06-28 18:23:05,984 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:05,993 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:05,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:05,994 - DEBUG - Finished Request
2025-06-28 18:23:06,104 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:06,112 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:06,112 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:06,112 - DEBUG - Finished Request
2025-06-28 18:23:06,614 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:06,622 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:06,622 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:06,622 - DEBUG - Finished Request
2025-06-28 18:23:06,994 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:07,003 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:07,003 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:07,004 - DEBUG - Finished Request
2025-06-28 18:23:07,126 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:07,134 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:07,134 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:07,134 - DEBUG - Finished Request
2025-06-28 18:23:07,639 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:07,647 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:07,647 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:07,647 - DEBUG - Finished Request
2025-06-28 18:23:08,004 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:08,012 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:08,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:08,012 - DEBUG - Finished Request
2025-06-28 18:23:08,151 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:08,157 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:08,158 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:08,158 - DEBUG - Finished Request
2025-06-28 18:23:08,665 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:08,674 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:08,674 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:08,674 - DEBUG - Finished Request
2025-06-28 18:23:09,013 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:09,056 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:09,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:09,057 - DEBUG - Finished Request
2025-06-28 18:23:09,178 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:09,185 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:09,185 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:09,185 - DEBUG - Finished Request
2025-06-28 18:23:09,690 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:09,697 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:09,697 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:09,697 - DEBUG - Finished Request
2025-06-28 18:23:10,058 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:10,068 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:10,068 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:10,068 - DEBUG - Finished Request
2025-06-28 18:23:10,204 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:10,211 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:10,211 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:10,211 - DEBUG - Finished Request
2025-06-28 18:23:10,717 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:10,723 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:10,724 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:10,724 - DEBUG - Finished Request
2025-06-28 18:23:11,069 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:11,076 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:11,076 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:11,076 - DEBUG - Finished Request
2025-06-28 18:23:11,234 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:11,242 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:11,243 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:11,243 - DEBUG - Finished Request
2025-06-28 18:23:12,077 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:12,085 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:12,085 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:12,086 - DEBUG - Finished Request
2025-06-28 18:23:13,087 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:13,095 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:13,096 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:13,096 - DEBUG - Finished Request
2025-06-28 18:23:14,096 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:14,104 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:14,104 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:14,104 - DEBUG - Finished Request
2025-06-28 18:23:15,105 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:15,112 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:15,112 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:15,112 - DEBUG - Finished Request
2025-06-28 18:23:16,113 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:16,121 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:16,121 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:16,121 - DEBUG - Finished Request
2025-06-28 18:23:17,121 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:17,129 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:17,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:17,129 - DEBUG - Finished Request
2025-06-28 18:23:18,130 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:18,136 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:18,136 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:18,136 - DEBUG - Finished Request
2025-06-28 18:23:19,071 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-28 18:23:19,071 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-28 18:23:19,078 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:19,078 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:19,078 - DEBUG - Finished Request
2025-06-28 18:23:19,078 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-28 18:23:19,084 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:19,089 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:19,090 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:19,090 - DEBUG - Finished Request
2025-06-28 18:23:19,137 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:19,144 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:19,144 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:19,144 - DEBUG - Finished Request
2025-06-28 18:23:19,591 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:19,597 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:19,597 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:19,597 - DEBUG - Finished Request
2025-06-28 18:23:20,102 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:20,107 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:20,108 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:20,108 - DEBUG - Finished Request
2025-06-28 18:23:20,144 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:20,151 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:20,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:20,151 - DEBUG - Finished Request
2025-06-28 18:23:20,616 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:20,623 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:20,623 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:20,623 - DEBUG - Finished Request
2025-06-28 18:23:21,130 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:21,137 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:21,137 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:21,137 - DEBUG - Finished Request
2025-06-28 18:23:21,152 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:21,157 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:21,157 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:21,157 - DEBUG - Finished Request
2025-06-28 18:23:21,643 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:21,649 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:21,649 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:21,650 - DEBUG - Finished Request
2025-06-28 18:23:22,157 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:22,158 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:22,159 - DEBUG - Starting new HTTP connection (2): localhost:51438
2025-06-28 18:23:22,165 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:22,165 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:22,165 - DEBUG - Finished Request
2025-06-28 18:23:22,176 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:22,177 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-28 18:23:22,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:22,177 - DEBUG - Finished Request
2025-06-28 18:23:22,673 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:22,679 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:22,679 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:22,680 - DEBUG - Finished Request
2025-06-28 18:23:23,177 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:23,184 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:23,184 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:23,184 - DEBUG - Finished Request
2025-06-28 18:23:23,188 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:23,193 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:23,193 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:23,193 - DEBUG - Finished Request
2025-06-28 18:23:23,702 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:23,708 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:23,709 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:23,709 - DEBUG - Finished Request
2025-06-28 18:23:24,185 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:24,192 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:24,193 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:24,193 - DEBUG - Finished Request
2025-06-28 18:23:24,214 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:24,220 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:24,220 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:24,220 - DEBUG - Finished Request
2025-06-28 18:23:24,727 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:24,734 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:24,735 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:24,735 - DEBUG - Finished Request
2025-06-28 18:23:25,193 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:25,198 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:25,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:25,199 - DEBUG - Finished Request
2025-06-28 18:23:25,242 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:25,250 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:25,250 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:25,250 - DEBUG - Finished Request
2025-06-28 18:23:25,755 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:25,763 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:25,763 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:25,763 - DEBUG - Finished Request
2025-06-28 18:23:26,200 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:26,208 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:26,208 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:26,208 - DEBUG - Finished Request
2025-06-28 18:23:26,269 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:26,276 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:26,276 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:26,276 - DEBUG - Finished Request
2025-06-28 18:23:26,784 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:26,791 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:26,791 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:26,791 - DEBUG - Finished Request
2025-06-28 18:23:27,209 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:27,216 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:27,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:27,216 - DEBUG - Finished Request
2025-06-28 18:23:27,296 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:27,303 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:27,303 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:27,303 - DEBUG - Finished Request
2025-06-28 18:23:27,811 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:27,817 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:27,817 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:27,817 - DEBUG - Finished Request
2025-06-28 18:23:28,218 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:28,225 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:28,225 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:28,226 - DEBUG - Finished Request
2025-06-28 18:23:28,322 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:28,329 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:28,329 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:28,330 - DEBUG - Finished Request
2025-06-28 18:23:28,837 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:28,845 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:28,845 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:28,846 - DEBUG - Finished Request
2025-06-28 18:23:29,226 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:29,234 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:29,235 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:29,235 - DEBUG - Finished Request
2025-06-28 18:23:29,352 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:29,361 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:29,362 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:29,362 - DEBUG - Finished Request
2025-06-28 18:23:29,866 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:29,875 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:29,876 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:29,876 - DEBUG - Finished Request
2025-06-28 18:23:30,237 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:30,245 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:30,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:30,246 - DEBUG - Finished Request
2025-06-28 18:23:30,381 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:30,389 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:30,389 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:30,390 - DEBUG - Finished Request
2025-06-28 18:23:30,897 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:30,908 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:30,908 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:30,908 - DEBUG - Finished Request
2025-06-28 18:23:31,247 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:31,256 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:31,256 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:31,257 - DEBUG - Finished Request
2025-06-28 18:23:31,413 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:31,421 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:31,421 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:31,422 - DEBUG - Finished Request
2025-06-28 18:23:31,930 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:31,937 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:31,937 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:31,938 - DEBUG - Finished Request
2025-06-28 18:23:32,258 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:32,265 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:32,265 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:32,265 - DEBUG - Finished Request
2025-06-28 18:23:32,445 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:32,453 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:32,453 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:32,453 - DEBUG - Finished Request
2025-06-28 18:23:32,958 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:32,966 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:32,966 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:32,967 - DEBUG - Finished Request
2025-06-28 18:23:33,266 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:33,278 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:33,278 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:33,279 - DEBUG - Finished Request
2025-06-28 18:23:33,474 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:33,483 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:33,484 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:33,484 - DEBUG - Finished Request
2025-06-28 18:23:33,985 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:33,993 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:33,994 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:33,994 - DEBUG - Finished Request
2025-06-28 18:23:34,280 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:34,287 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:34,287 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:34,288 - DEBUG - Finished Request
2025-06-28 18:23:34,498 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:34,508 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:34,508 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:34,508 - DEBUG - Finished Request
2025-06-28 18:23:35,011 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:35,020 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:35,021 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:35,021 - DEBUG - Finished Request
2025-06-28 18:23:35,288 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:35,296 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:35,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:35,297 - DEBUG - Finished Request
2025-06-28 18:23:35,522 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': 'return window.AGES_EVENT_LOG || [];', 'args': []}
2025-06-28 18:23:35,532 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:23:35,532 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:35,533 - DEBUG - Finished Request
2025-06-28 18:23:36,298 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:36,306 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:36,306 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:36,307 - DEBUG - Finished Request
2025-06-28 18:23:37,307 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:37,315 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:37,315 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:37,315 - DEBUG - Finished Request
2025-06-28 18:23:38,317 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:38,324 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:38,324 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:38,324 - DEBUG - Finished Request
2025-06-28 18:23:39,325 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:39,333 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:39,333 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:39,334 - DEBUG - Finished Request
2025-06-28 18:23:40,334 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:40,341 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:40,341 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:40,342 - DEBUG - Finished Request
2025-06-28 18:23:41,343 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:41,352 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:41,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:41,353 - DEBUG - Finished Request
2025-06-28 18:23:42,353 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:42,359 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:42,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:42,360 - DEBUG - Finished Request
2025-06-28 18:23:43,361 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:43,371 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:43,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:43,372 - DEBUG - Finished Request
2025-06-28 18:23:44,372 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:44,382 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:44,382 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:44,382 - DEBUG - Finished Request
2025-06-28 18:23:45,384 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:45,393 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:45,393 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:45,393 - DEBUG - Finished Request
2025-06-28 18:23:46,393 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:46,402 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:46,402 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:46,402 - DEBUG - Finished Request
2025-06-28 18:23:47,404 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:47,412 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:47,412 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:47,412 - DEBUG - Finished Request
2025-06-28 18:23:48,413 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:48,421 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:48,421 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:48,421 - DEBUG - Finished Request
2025-06-28 18:23:49,422 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:49,430 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:49,430 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:49,431 - DEBUG - Finished Request
2025-06-28 18:23:50,431 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:50,439 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:50,439 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:50,439 - DEBUG - Finished Request
2025-06-28 18:23:51,440 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:51,448 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:51,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:51,449 - DEBUG - Finished Request
2025-06-28 18:23:52,449 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:52,459 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:52,459 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:52,459 - DEBUG - Finished Request
2025-06-28 18:23:53,460 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:53,469 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:53,470 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:53,470 - DEBUG - Finished Request
2025-06-28 18:23:54,471 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:54,480 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:54,481 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:54,481 - DEBUG - Finished Request
2025-06-28 18:23:55,483 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:55,491 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:55,492 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:55,492 - DEBUG - Finished Request
2025-06-28 18:23:56,493 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:56,501 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:56,502 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:56,502 - DEBUG - Finished Request
2025-06-28 18:23:57,503 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:57,512 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:57,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:57,513 - DEBUG - Finished Request
2025-06-28 18:23:58,515 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:58,524 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:58,524 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:58,525 - DEBUG - Finished Request
2025-06-28 18:23:59,526 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:23:59,536 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:23:59,537 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:23:59,537 - DEBUG - Finished Request
2025-06-28 18:24:00,538 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:00,547 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:00,547 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:00,548 - DEBUG - Finished Request
2025-06-28 18:24:01,549 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:01,558 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:01,559 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:01,559 - DEBUG - Finished Request
2025-06-28 18:24:02,560 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:02,569 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:02,570 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:02,570 - DEBUG - Finished Request
2025-06-28 18:24:03,571 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:03,580 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:03,580 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:03,581 - DEBUG - Finished Request
2025-06-28 18:24:04,582 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:04,591 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:04,592 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:04,592 - DEBUG - Finished Request
2025-06-28 18:24:05,594 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:05,603 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:05,603 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:05,604 - DEBUG - Finished Request
2025-06-28 18:24:06,604 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:06,613 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:06,614 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:06,614 - DEBUG - Finished Request
2025-06-28 18:24:07,615 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:07,623 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:07,624 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:07,624 - DEBUG - Finished Request
2025-06-28 18:24:08,625 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:08,634 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:08,635 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:08,635 - DEBUG - Finished Request
2025-06-28 18:24:09,636 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:09,644 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:09,645 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:09,645 - DEBUG - Finished Request
2025-06-28 18:24:10,647 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:10,655 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:10,655 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:10,655 - DEBUG - Finished Request
2025-06-28 18:24:11,656 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:11,668 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:11,668 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:11,669 - DEBUG - Finished Request
2025-06-28 18:24:12,670 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:12,681 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:12,681 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:12,682 - DEBUG - Finished Request
2025-06-28 18:24:13,682 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:13,693 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:13,693 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:13,694 - DEBUG - Finished Request
2025-06-28 18:24:14,694 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:14,704 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:14,705 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:14,705 - DEBUG - Finished Request
2025-06-28 18:24:15,706 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:15,716 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:15,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:15,717 - DEBUG - Finished Request
2025-06-28 18:24:16,718 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:16,726 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:16,727 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:16,727 - DEBUG - Finished Request
2025-06-28 18:24:17,728 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:17,739 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:17,740 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:17,740 - DEBUG - Finished Request
2025-06-28 18:24:18,741 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:18,750 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:18,751 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:18,751 - DEBUG - Finished Request
2025-06-28 18:24:19,752 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:19,762 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:19,762 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:19,763 - DEBUG - Finished Request
2025-06-28 18:24:20,763 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:20,775 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:20,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:20,775 - DEBUG - Finished Request
2025-06-28 18:24:21,776 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:21,784 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:21,784 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:21,785 - DEBUG - Finished Request
2025-06-28 18:24:22,785 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:22,795 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:22,795 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:22,796 - DEBUG - Finished Request
2025-06-28 18:24:23,799 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:23,808 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:23,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:23,809 - DEBUG - Finished Request
2025-06-28 18:24:24,809 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:24,818 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:24,819 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:24,819 - DEBUG - Finished Request
2025-06-28 18:24:25,820 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:25,829 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:25,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:25,830 - DEBUG - Finished Request
2025-06-28 18:24:26,831 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:26,838 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:26,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:26,839 - DEBUG - Finished Request
2025-06-28 18:24:27,841 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:27,851 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:27,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:27,852 - DEBUG - Finished Request
2025-06-28 18:24:28,853 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:28,862 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:28,863 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:28,863 - DEBUG - Finished Request
2025-06-28 18:24:29,865 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:29,874 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:29,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:29,875 - DEBUG - Finished Request
2025-06-28 18:24:30,877 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:30,886 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:30,887 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:30,887 - DEBUG - Finished Request
2025-06-28 18:24:31,888 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:31,897 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:31,897 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:31,897 - DEBUG - Finished Request
2025-06-28 18:24:32,898 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:32,908 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:32,909 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:32,909 - DEBUG - Finished Request
2025-06-28 18:24:33,910 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:33,919 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:33,920 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:33,920 - DEBUG - Finished Request
2025-06-28 18:24:34,922 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:34,928 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:34,929 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:34,929 - DEBUG - Finished Request
2025-06-28 18:24:35,929 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:35,937 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:35,937 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:35,937 - DEBUG - Finished Request
2025-06-28 18:24:36,938 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:36,946 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:36,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:36,946 - DEBUG - Finished Request
2025-06-28 18:24:37,948 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:37,956 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:37,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:37,956 - DEBUG - Finished Request
2025-06-28 18:24:38,957 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:38,966 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:38,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:38,966 - DEBUG - Finished Request
2025-06-28 18:24:39,967 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:39,975 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:39,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:39,975 - DEBUG - Finished Request
2025-06-28 18:24:40,613 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:40,620 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:40,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:40,620 - DEBUG - Finished Request
2025-06-28 18:24:40,620 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/title {}
2025-06-28 18:24:40,627 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/title HTTP/1.1" 200 0
2025-06-28 18:24:40,627 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:40,627 - DEBUG - Finished Request
2025-06-28 18:24:40,642 - DEBUG - POST http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/execute/sync {'script': "return document.body.innerText || '';", 'args': []}
2025-06-28 18:24:40,650 - DEBUG - http://localhost:51438 "POST /session/52ee7cb8e53151163ba588b04be05fe7/execute/sync HTTP/1.1" 200 0
2025-06-28 18:24:40,650 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/28 18:19:40\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:40,650 - DEBUG - Finished Request
2025-06-28 18:24:40,977 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:40,984 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:40,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:40,984 - DEBUG - Finished Request
2025-06-28 18:24:41,985 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:41,994 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:41,995 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:41,995 - DEBUG - Finished Request
2025-06-28 18:24:42,996 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:43,004 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:43,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:43,005 - DEBUG - Finished Request
2025-06-28 18:24:44,005 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:44,013 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:44,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:44,014 - DEBUG - Finished Request
2025-06-28 18:24:45,014 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:45,023 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:45,023 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:45,023 - DEBUG - Finished Request
2025-06-28 18:24:46,024 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:46,034 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:46,034 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:46,035 - DEBUG - Finished Request
2025-06-28 18:24:47,035 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:47,043 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:47,043 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:47,044 - DEBUG - Finished Request
2025-06-28 18:24:48,045 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:48,052 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:48,052 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:48,052 - DEBUG - Finished Request
2025-06-28 18:24:49,053 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:49,062 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:49,062 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:49,062 - DEBUG - Finished Request
2025-06-28 18:24:50,063 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:50,071 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:50,071 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:50,071 - DEBUG - Finished Request
2025-06-28 18:24:51,072 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:51,080 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:51,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:51,081 - DEBUG - Finished Request
2025-06-28 18:24:52,083 - DEBUG - GET http://localhost:51438/session/52ee7cb8e53151163ba588b04be05fe7/url {}
2025-06-28 18:24:52,091 - DEBUG - http://localhost:51438 "GET /session/52ee7cb8e53151163ba588b04be05fe7/url HTTP/1.1" 200 0
2025-06-28 18:24:52,092 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-28 18:24:52,092 - DEBUG - Finished Request
