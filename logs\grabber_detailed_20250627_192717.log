2025-06-27 19:27:17,923 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250627_192717.log
2025-06-27 19:27:35,556 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-27 19:27:35,556 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-27 19:27:35,641 - DEBUG - chromedriver not found in PATH
2025-06-27 19:27:35,641 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:27:35,642 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-27 19:27:35,642 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-27 19:27:35,642 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-27 19:27:35,642 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-27 19:27:35,642 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:27:35,646 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 24352 using 0 to output -3
2025-06-27 19:27:36,161 - DEBUG - POST http://localhost:55501/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-27 19:27:36,161 - DEBUG - Starting new HTTP connection (1): localhost:55501
2025-06-27 19:27:36,707 - DEBUG - http://localhost:55501 "POST /session HTTP/1.1" 200 0
2025-06-27 19:27:36,707 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir24352_2138109737"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55504"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"927b9a4b03a0003a25bb3c78e8ece229"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:36,707 - DEBUG - Finished Request
2025-06-27 19:27:36,708 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-27 19:27:37,699 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:37,699 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:37,699 - DEBUG - Finished Request
2025-06-27 19:27:37,701 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:37,746 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:37,746 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:37,747 - DEBUG - Finished Request
2025-06-27 19:27:38,748 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:38,754 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:38,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:38,754 - DEBUG - Finished Request
2025-06-27 19:27:39,755 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:39,761 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:39,761 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:39,761 - DEBUG - Finished Request
2025-06-27 19:27:40,763 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:40,771 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:40,772 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:40,772 - DEBUG - Finished Request
2025-06-27 19:27:41,772 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:41,778 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:41,779 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:41,779 - DEBUG - Finished Request
2025-06-27 19:27:42,780 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:42,787 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:42,788 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:42,789 - DEBUG - Finished Request
2025-06-27 19:27:43,790 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:43,796 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:43,797 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:43,797 - DEBUG - Finished Request
2025-06-27 19:27:44,800 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:44,809 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:44,809 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:44,809 - DEBUG - Finished Request
2025-06-27 19:27:45,811 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:45,820 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:45,821 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:45,821 - DEBUG - Finished Request
2025-06-27 19:27:46,821 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:46,833 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:46,833 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:46,834 - DEBUG - Finished Request
2025-06-27 19:27:47,835 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:47,846 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:47,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:47,847 - DEBUG - Finished Request
2025-06-27 19:27:48,848 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:48,856 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:48,858 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:48,858 - DEBUG - Finished Request
2025-06-27 19:27:49,859 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:49,866 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:49,867 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:49,867 - DEBUG - Finished Request
2025-06-27 19:27:50,868 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:50,875 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:50,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:50,876 - DEBUG - Finished Request
2025-06-27 19:27:51,877 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:51,884 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:51,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:51,885 - DEBUG - Finished Request
2025-06-27 19:27:52,886 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:52,896 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:52,897 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:52,897 - DEBUG - Finished Request
2025-06-27 19:27:53,898 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:53,908 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:53,908 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:53,909 - DEBUG - Finished Request
2025-06-27 19:27:54,910 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:54,918 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:54,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:54,919 - DEBUG - Finished Request
2025-06-27 19:27:55,921 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:55,928 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:55,928 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:55,929 - DEBUG - Finished Request
2025-06-27 19:27:56,930 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:56,938 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:56,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:56,939 - DEBUG - Finished Request
2025-06-27 19:27:57,940 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:57,947 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:57,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:57,949 - DEBUG - Finished Request
2025-06-27 19:27:58,949 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:58,957 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:58,957 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:58,958 - DEBUG - Finished Request
2025-06-27 19:27:59,959 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:27:59,966 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:27:59,967 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:27:59,967 - DEBUG - Finished Request
2025-06-27 19:28:00,968 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:00,976 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:00,977 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:00,977 - DEBUG - Finished Request
2025-06-27 19:28:01,978 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:01,987 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:01,987 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:01,987 - DEBUG - Finished Request
2025-06-27 19:28:02,988 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:02,997 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:02,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:02,997 - DEBUG - Finished Request
2025-06-27 19:28:03,999 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:04,007 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:04,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:04,008 - DEBUG - Finished Request
2025-06-27 19:28:05,008 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:05,017 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:05,018 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:05,018 - DEBUG - Finished Request
2025-06-27 19:28:06,019 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:06,028 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:06,028 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:06,029 - DEBUG - Finished Request
2025-06-27 19:28:07,030 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:07,037 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:07,037 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:07,038 - DEBUG - Finished Request
2025-06-27 19:28:08,039 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:08,047 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:08,048 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:08,048 - DEBUG - Finished Request
2025-06-27 19:28:09,049 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:09,058 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:09,058 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:09,058 - DEBUG - Finished Request
2025-06-27 19:28:10,060 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:10,069 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:10,069 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:10,069 - DEBUG - Finished Request
2025-06-27 19:28:11,071 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:11,080 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:11,081 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:11,082 - DEBUG - Finished Request
2025-06-27 19:28:12,083 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:12,090 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:12,090 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:12,090 - DEBUG - Finished Request
2025-06-27 19:28:13,092 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:13,099 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:13,100 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:13,100 - DEBUG - Finished Request
2025-06-27 19:28:14,101 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:14,109 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:14,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:14,110 - DEBUG - Finished Request
2025-06-27 19:28:15,111 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:15,119 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:15,120 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:15,120 - DEBUG - Finished Request
2025-06-27 19:28:16,121 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:16,129 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:16,130 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:16,130 - DEBUG - Finished Request
2025-06-27 19:28:17,131 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:17,138 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:17,139 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:17,139 - DEBUG - Finished Request
2025-06-27 19:28:18,141 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:18,154 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:18,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:18,155 - DEBUG - Finished Request
2025-06-27 19:28:19,156 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:19,165 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:19,165 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:19,166 - DEBUG - Finished Request
2025-06-27 19:28:20,167 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:20,174 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:20,175 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:20,175 - DEBUG - Finished Request
2025-06-27 19:28:21,176 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:21,184 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:21,184 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:21,184 - DEBUG - Finished Request
2025-06-27 19:28:22,186 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:22,194 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:22,194 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:22,194 - DEBUG - Finished Request
2025-06-27 19:28:23,196 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:23,205 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:23,206 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:23,206 - DEBUG - Finished Request
2025-06-27 19:28:24,207 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:24,216 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:24,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:24,216 - DEBUG - Finished Request
2025-06-27 19:28:25,217 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:25,224 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:25,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:25,225 - DEBUG - Finished Request
2025-06-27 19:28:26,226 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:26,234 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:26,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:26,234 - DEBUG - Finished Request
2025-06-27 19:28:27,235 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:27,244 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:27,244 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:27,245 - DEBUG - Finished Request
2025-06-27 19:28:28,245 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:28,252 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:28,252 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:28,252 - DEBUG - Finished Request
2025-06-27 19:28:29,254 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:29,262 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:29,262 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:29,262 - DEBUG - Finished Request
2025-06-27 19:28:30,262 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:30,270 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:30,270 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:30,270 - DEBUG - Finished Request
2025-06-27 19:28:31,272 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:31,279 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:31,279 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:31,280 - DEBUG - Finished Request
2025-06-27 19:28:32,281 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:32,288 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:32,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:32,289 - DEBUG - Finished Request
2025-06-27 19:28:33,289 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:33,295 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:33,295 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:33,296 - DEBUG - Finished Request
2025-06-27 19:28:34,297 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:34,304 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:34,304 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:34,305 - DEBUG - Finished Request
2025-06-27 19:28:35,306 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:35,313 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:35,313 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:35,313 - DEBUG - Finished Request
2025-06-27 19:28:36,314 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:36,319 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:36,320 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:36,320 - DEBUG - Finished Request
2025-06-27 19:28:37,322 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:37,328 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:37,329 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:37,329 - DEBUG - Finished Request
2025-06-27 19:28:38,330 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:38,337 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:38,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:38,338 - DEBUG - Finished Request
2025-06-27 19:28:39,339 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:39,345 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:39,345 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:39,345 - DEBUG - Finished Request
2025-06-27 19:28:40,346 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:40,352 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:40,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:40,353 - DEBUG - Finished Request
2025-06-27 19:28:41,354 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:41,362 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:41,363 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:41,363 - DEBUG - Finished Request
2025-06-27 19:28:42,364 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:42,370 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:42,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:42,371 - DEBUG - Finished Request
2025-06-27 19:28:43,372 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:43,379 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:43,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:43,380 - DEBUG - Finished Request
2025-06-27 19:28:44,381 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:44,388 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:44,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:44,389 - DEBUG - Finished Request
2025-06-27 19:28:45,390 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:45,398 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:45,398 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:45,398 - DEBUG - Finished Request
2025-06-27 19:28:46,400 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:46,405 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:46,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:46,406 - DEBUG - Finished Request
2025-06-27 19:28:47,407 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:47,415 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:47,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:47,415 - DEBUG - Finished Request
2025-06-27 19:28:48,416 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:48,423 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:48,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:48,423 - DEBUG - Finished Request
2025-06-27 19:28:49,425 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:49,431 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:49,431 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:49,431 - DEBUG - Finished Request
2025-06-27 19:28:50,433 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:50,439 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:50,440 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:50,440 - DEBUG - Finished Request
2025-06-27 19:28:51,441 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:51,448 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:51,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:51,448 - DEBUG - Finished Request
2025-06-27 19:28:52,449 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:52,455 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:52,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:52,455 - DEBUG - Finished Request
2025-06-27 19:28:53,457 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:53,465 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:53,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:53,465 - DEBUG - Finished Request
2025-06-27 19:28:54,466 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:54,472 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:54,472 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:54,472 - DEBUG - Finished Request
2025-06-27 19:28:55,473 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:55,480 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:55,480 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:55,481 - DEBUG - Finished Request
2025-06-27 19:28:56,482 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:56,489 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:56,489 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:56,489 - DEBUG - Finished Request
2025-06-27 19:28:57,489 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:57,496 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:57,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:57,497 - DEBUG - Finished Request
2025-06-27 19:28:58,498 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:58,505 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:58,505 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:58,505 - DEBUG - Finished Request
2025-06-27 19:28:59,506 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:28:59,512 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:28:59,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:28:59,513 - DEBUG - Finished Request
2025-06-27 19:29:00,514 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:00,521 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:00,521 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:00,522 - DEBUG - Finished Request
2025-06-27 19:29:01,523 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:01,529 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:01,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:01,529 - DEBUG - Finished Request
2025-06-27 19:29:02,530 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:02,537 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:02,537 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:02,538 - DEBUG - Finished Request
2025-06-27 19:29:03,539 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:03,545 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:03,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:03,546 - DEBUG - Finished Request
2025-06-27 19:29:04,548 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:04,566 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:04,566 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:04,567 - DEBUG - Finished Request
2025-06-27 19:29:05,568 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:05,579 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:05,579 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:05,579 - DEBUG - Finished Request
2025-06-27 19:29:06,323 - INFO - 🎯 用戶點擊準備完成按鈕，開始詳細檢測...
2025-06-27 19:29:06,324 - INFO - 🔍 [用戶點擊準備完成] 開始記錄頁面內容...
2025-06-27 19:29:06,324 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:06,342 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:06,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,343 - DEBUG - Finished Request
2025-06-27 19:29:06,343 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/title {}
2025-06-27 19:29:06,351 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/title HTTP/1.1" 200 0
2025-06-27 19:29:06,351 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,351 - DEBUG - Finished Request
2025-06-27 19:29:06,351 - INFO - 🔍 [用戶點擊準備完成] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:29:06,351 - INFO - 🔍 [用戶點擊準備完成] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:29:06,351 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/source {}
2025-06-27 19:29:06,357 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/source HTTP/1.1" 200 0
2025-06-27 19:29:06,357 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '2CwSlSb41U01z5yth1-SO_MGAigJvVM1CqKEEWkCG8ypk6fAmvrbt_JqzH8qlw1bNGMGX6wWFYFNPh7wRIWTVsg91gnmLN2pM5RIO2ymJW01:pKQPcGv4CbnWavbyYjpo0QpvqyTCiqOqZwxdO_NLBx8yxBBkmxJXWF9_Hi9ImoA2aPdJX5vjYryghR0gTcZ6eC-LhscfGDD1jVPF-ox_M7k1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:28:55\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95649a8978034aa3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,359 - DEBUG - Finished Request
2025-06-27 19:29:06,359 - INFO - 🔍 [用戶點擊準備完成] page_source 長度: 8479
2025-06-27 19:29:06,359 - INFO - 🔍 [用戶點擊準備完成] page_source 包含 E48B: False
2025-06-27 19:29:06,359 - INFO - 🔍 [用戶點擊準備完成] page_source 包含目標訂單: False
2025-06-27 19:29:06,360 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:29:06,366 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:29:06,367 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:28:55\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,367 - DEBUG - Finished Request
2025-06-27 19:29:06,367 - INFO - 🔍 [用戶點擊準備完成] innerText 長度: 97
2025-06-27 19:29:06,367 - INFO - 🔍 [用戶點擊準備完成] innerText 包含 E48B: False
2025-06-27 19:29:06,367 - INFO - 🔍 [用戶點擊準備完成] innerText 包含目標訂單: False
2025-06-27 19:29:06,368 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:29:06,379 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,379 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,379 - DEBUG - Finished Request
2025-06-27 19:29:06,380 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:29:06,389 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,389 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,389 - DEBUG - Finished Request
2025-06-27 19:29:06,389 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個表格，0 個表格行
2025-06-27 19:29:06,390 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:06,400 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,401 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,401 - DEBUG - Finished Request
2025-06-27 19:29:06,401 - INFO - 🔍 [用戶點擊準備完成] 包含 'E48B' 的元素數量: 0
2025-06-27 19:29:06,401 - INFO - 🔍 [用戶點擊準備完成] innerText 前300字符:
2025-06-27 19:29:06,402 - INFO - 🔍 [用戶點擊準備完成]  環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:28:55

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-06-27 19:29:06,402 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:29:06,412 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,412 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,412 - DEBUG - Finished Request
2025-06-27 19:29:06,412 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個編輯按鈕
2025-06-27 19:29:06,412 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:29:06,423 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,423 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40"}]} | headers=HTTPHeaderDict({'Content-Length': '128', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,424 - DEBUG - Finished Request
2025-06-27 19:29:06,424 - INFO - 🔍 [用戶點擊準備完成] 檢測到 1 個 iframe
2025-06-27 19:29:06,478 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40'}, 'id']}
2025-06-27 19:29:06,487 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:29:06,487 - DEBUG - Remote response: status=200 | data={"value":"frameid"} | headers=HTTPHeaderDict({'Content-Length': '19', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,487 - DEBUG - Finished Request
2025-06-27 19:29:06,488 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40'}, 'src']}
2025-06-27 19:29:06,494 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:29:06,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00"} | headers=HTTPHeaderDict({'Content-Length': '58', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,494 - DEBUG - Finished Request
2025-06-27 19:29:06,494 - INFO - 🔍 [用戶點擊準備完成] iframe 1: id='frameid', src='https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00'
2025-06-27 19:29:06,495 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/frame {'id': {'element-6066-11e4-a52e-4f735466cecf': 'f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40'}}
2025-06-27 19:29:06,517 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/frame HTTP/1.1" 200 0
2025-06-27 19:29:06,518 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,518 - DEBUG - Finished Request
2025-06-27 19:29:06,519 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:29:06,526 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:29:06,526 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t815.691\t76.89\t0\t453.919\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406230886\t(NEW)H4 2808 星期五(30%)\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406200782\tH4 2808義大遊樂\tKEP-2808\t專車清運\t1.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181141\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181137\t仁\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181136\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181135\t岡\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406181133\t(NEW)H9 5580星期五 只有一車(25%)\tKEJ-5580\t一般清運\t6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110721\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110719\t南\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110718\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110717\t南\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110715\t岡\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110714\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261022\t岡\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261021\t南\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210775\t岡\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210774\t仁\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210773\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190957\t(NEW)119星期五(50%)\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190956\t南\tKED-9671\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190955\tH2 119 義大\t119-BR\t專車清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190954\t南\t117-BR\t一般清運\t3.5\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190953\t仁\t119-BR\t一般清運\t3.6\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190949\t南\t119-BR\t一般清運\t3.6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190948\t仁\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190947\t岡\t120-BR\t一般清運\t3.2\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190945\t仁\t121-BR\t一般清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190944\t岡\t121-BR\t一般清運\t3.5\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190943\t仁\t129-BR\t一般清運\t4.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190940\t岡\t129-BR\t一般清運\t4.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190938\t(NEW)H72560星期五(有美生) (20%)\tKEP-2560\t一般清運\t4.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190936\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190934\t岡\t937-N6\t一般清運\t4.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190932\t南\t937-N6\t一般清運\t4.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190931\t南\tKEH-9278\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190929\t仁\tKEH-9278\t一般清運\t8.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190928\t岡\tKER-2807\t一般清運\t7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190698\t仁\tKER-2807\t一般清運\t7\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n顯示第 1 至 38 項結果，共 38 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '7514', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,527 - DEBUG - Finished Request
2025-06-27 19:29:06,527 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:29:06,534 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,534 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.131"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.132"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,535 - DEBUG - Finished Request
2025-06-27 19:29:06,535 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:29:06,543 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,543 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.170"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.172"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.173"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,543 - DEBUG - Finished Request
2025-06-27 19:29:06,544 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:29:06,551 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,553 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.80"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.239"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.241"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.242"}]} | headers=HTTPHeaderDict({'Content-Length': '8270', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,554 - DEBUG - Finished Request
2025-06-27 19:29:06,554 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:06,562 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:06,562 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.243"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.277"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.278"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.279"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.280"}]} | headers=HTTPHeaderDict({'Content-Length': '4495', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,563 - DEBUG - Finished Request
2025-06-27 19:29:06,563 - INFO - 🔍 [用戶點擊準備完成] iframe 1 內容:
2025-06-27 19:29:06,563 - INFO - 🔍 [用戶點擊準備完成]   - 文字長度: 3955
2025-06-27 19:29:06,563 - INFO - 🔍 [用戶點擊準備完成]   - 包含目標訂單: True
2025-06-27 19:29:06,563 - INFO - 🔍 [用戶點擊準備完成]   - 包含 E48B: True
2025-06-27 19:29:06,564 - INFO - 🔍 [用戶點擊準備完成]   - 表格數量: 2
2025-06-27 19:29:06,564 - INFO - 🔍 [用戶點擊準備完成]   - 表格行數: 41
2025-06-27 19:29:06,564 - INFO - 🔍 [用戶點擊準備完成]   - 編輯按鈕數量: 70
2025-06-27 19:29:06,564 - INFO - 🔍 [用戶點擊準備完成]   - E48B 元素數量: 38
2025-06-27 19:29:06,564 - INFO - 🔍 [用戶點擊準備完成]   - 內容前300字符: 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

6月
7月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	815.691	76.89	0	453.919
每日開放查詢時日1...
2025-06-27 19:29:06,565 - INFO - 🔍 [用戶點擊準備完成]   - 內容後300字符: ...		高南廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190929	仁	KEH-9278	一般清運	8.5	2025-07-04		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190928	岡	KER-2807	一般清運	7	2025-07-04		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190698	仁	KER-2807	一般清運	7	2025-07-04		仁武廠	0	一般	取消(刪除)
顯示第 1 至 38 項結果，共 38 項
上一頁
1
下一頁
2025-06-27 19:29:06,565 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/frame {'id': None}
2025-06-27 19:29:06,568 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/frame HTTP/1.1" 200 0
2025-06-27 19:29:06,568 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,569 - DEBUG - Finished Request
2025-06-27 19:29:06,580 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:06,585 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:06,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:06,585 - DEBUG - Finished Request
2025-06-27 19:29:07,587 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:07,593 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:07,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:07,594 - DEBUG - Finished Request
2025-06-27 19:29:08,570 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:08,577 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:08,577 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:08,577 - DEBUG - Finished Request
2025-06-27 19:29:08,578 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/title {}
2025-06-27 19:29:08,583 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/title HTTP/1.1" 200 0
2025-06-27 19:29:08,583 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:08,583 - DEBUG - Finished Request
2025-06-27 19:29:08,584 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:29:08,591 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:08,591 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:08,591 - DEBUG - Finished Request
2025-06-27 19:29:08,593 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:29:08,595 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:08,595 - DEBUG - Starting new HTTP connection (2): localhost:55501
2025-06-27 19:29:08,600 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:08,601 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:08,601 - DEBUG - Finished Request
2025-06-27 19:29:08,603 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/source {}
2025-06-27 19:29:08,604 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:08,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:08,605 - DEBUG - Finished Request
2025-06-27 19:29:08,606 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/source HTTP/1.1" 200 0
2025-06-27 19:29:08,606 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:29:08,607 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '2CwSlSb41U01z5yth1-SO_MGAigJvVM1CqKEEWkCG8ypk6fAmvrbt_JqzH8qlw1bNGMGX6wWFYFNPh7wRIWTVsg91gnmLN2pM5RIO2ymJW01:pKQPcGv4CbnWavbyYjpo0QpvqyTCiqOqZwxdO_NLBx8yxBBkmxJXWF9_Hi9ImoA2aPdJX5vjYryghR0gTcZ6eC-LhscfGDD1jVPF-ox_M7k1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:28:55\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"4f3d73f9320e0c41772ab84bf43c4497\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95649a8978034aa3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:08,608 - DEBUG - Finished Request
2025-06-27 19:29:09,606 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:09,612 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:09,612 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:09,613 - DEBUG - Finished Request
2025-06-27 19:29:10,609 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:29:10,613 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:10,613 - DEBUG - Starting new HTTP connection (3): localhost:55501
2025-06-27 19:29:10,617 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:10,617 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:10,617 - DEBUG - Finished Request
2025-06-27 19:29:10,617 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:29:10,624 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:10,624 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:10,624 - DEBUG - Finished Request
2025-06-27 19:29:10,625 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/source {}
2025-06-27 19:29:10,628 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/source HTTP/1.1" 200 0
2025-06-27 19:29:10,629 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '2CwSlSb41U01z5yth1-SO_MGAigJvVM1CqKEEWkCG8ypk6fAmvrbt_JqzH8qlw1bNGMGX6wWFYFNPh7wRIWTVsg91gnmLN2pM5RIO2ymJW01:pKQPcGv4CbnWavbyYjpo0QpvqyTCiqOqZwxdO_NLBx8yxBBkmxJXWF9_Hi9ImoA2aPdJX5vjYryghR0gTcZ6eC-LhscfGDD1jVPF-ox_M7k1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:28:55\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"4f3d73f9320e0c41772ab84bf43c4497\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95649a8978034aa3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:10,630 - DEBUG - Finished Request
2025-06-27 19:29:10,648 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:10,648 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:29:10,648 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:10,648 - DEBUG - Finished Request
2025-06-27 19:29:11,650 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:11,655 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:11,655 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:11,656 - DEBUG - Finished Request
2025-06-27 19:29:12,631 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:29:12,638 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:12,638 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:12,638 - DEBUG - Finished Request
2025-06-27 19:29:12,638 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:29:12,645 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:12,645 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:12,646 - DEBUG - Finished Request
2025-06-27 19:29:12,646 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/source {}
2025-06-27 19:29:12,649 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/source HTTP/1.1" 200 0
2025-06-27 19:29:12,649 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.05px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '2CwSlSb41U01z5yth1-SO_MGAigJvVM1CqKEEWkCG8ypk6fAmvrbt_JqzH8qlw1bNGMGX6wWFYFNPh7wRIWTVsg91gnmLN2pM5RIO2ymJW01:pKQPcGv4CbnWavbyYjpo0QpvqyTCiqOqZwxdO_NLBx8yxBBkmxJXWF9_Hi9ImoA2aPdJX5vjYryghR0gTcZ6eC-LhscfGDD1jVPF-ox_M7k1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:28:55\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"4f3d73f9320e0c41772ab84bf43c4497\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95649a8978034aa3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10265', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:12,650 - DEBUG - Finished Request
2025-06-27 19:29:12,656 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:12,661 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:12,661 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:12,661 - DEBUG - Finished Request
2025-06-27 19:29:13,663 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:13,669 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:13,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:13,669 - DEBUG - Finished Request
2025-06-27 19:29:14,670 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,677 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,677 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,678 - DEBUG - Finished Request
2025-06-27 19:29:14,790 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,795 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,795 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,796 - DEBUG - Finished Request
2025-06-27 19:29:14,796 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,801 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,801 - DEBUG - Finished Request
2025-06-27 19:29:14,801 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,808 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,808 - DEBUG - Finished Request
2025-06-27 19:29:14,808 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:29:14,818 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,818 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,818 - DEBUG - Finished Request
2025-06-27 19:29:14,819 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:29:14,828 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,829 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,829 - DEBUG - Finished Request
2025-06-27 19:29:14,829 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:29:14,835 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,836 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,836 - DEBUG - Finished Request
2025-06-27 19:29:14,836 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:29:14,841 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,842 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,842 - DEBUG - Finished Request
2025-06-27 19:29:14,842 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': '.btn-edit'}
2025-06-27 19:29:14,849 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,849 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,850 - DEBUG - Finished Request
2025-06-27 19:29:14,850 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'a[href*="edit"]'}
2025-06-27 19:29:14,860 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,860 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,860 - DEBUG - Finished Request
2025-06-27 19:29:14,860 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,864 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,864 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,865 - DEBUG - Finished Request
2025-06-27 19:29:14,865 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[name*="captcha"]'}
2025-06-27 19:29:14,874 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,874 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,874 - DEBUG - Finished Request
2025-06-27 19:29:14,874 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[id*="captcha"]'}
2025-06-27 19:29:14,882 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,882 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,882 - DEBUG - Finished Request
2025-06-27 19:29:14,882 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證"]'}
2025-06-27 19:29:14,891 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,891 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,891 - DEBUG - Finished Request
2025-06-27 19:29:14,891 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證碼"]'}
2025-06-27 19:29:14,899 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,900 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,900 - DEBUG - Finished Request
2025-06-27 19:29:14,900 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[name*="code"]'}
2025-06-27 19:29:14,908 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,908 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,909 - DEBUG - Finished Request
2025-06-27 19:29:14,909 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[name*="verify"]'}
2025-06-27 19:29:14,916 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,916 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,917 - DEBUG - Finished Request
2025-06-27 19:29:14,917 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="4"]'}
2025-06-27 19:29:14,925 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,925 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,925 - DEBUG - Finished Request
2025-06-27 19:29:14,925 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="5"]'}
2025-06-27 19:29:14,933 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,933 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,933 - DEBUG - Finished Request
2025-06-27 19:29:14,933 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,938 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,938 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,938 - DEBUG - Finished Request
2025-06-27 19:29:14,938 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'img[src*="captcha"]'}
2025-06-27 19:29:14,945 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,945 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,945 - DEBUG - Finished Request
2025-06-27 19:29:14,945 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'img[src*="verify"]'}
2025-06-27 19:29:14,953 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,953 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,953 - DEBUG - Finished Request
2025-06-27 19:29:14,953 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'img[alt*="驗證"]'}
2025-06-27 19:29:14,960 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,961 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,961 - DEBUG - Finished Request
2025-06-27 19:29:14,961 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'img[alt*="驗證碼"]'}
2025-06-27 19:29:14,969 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,969 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,969 - DEBUG - Finished Request
2025-06-27 19:29:14,970 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:14,973 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:14,973 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,973 - DEBUG - Finished Request
2025-06-27 19:29:14,974 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '確認取得驗證碼')]"}
2025-06-27 19:29:14,981 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,981 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,981 - DEBUG - Finished Request
2025-06-27 19:29:14,981 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:29:14,989 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,989 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,989 - DEBUG - Finished Request
2025-06-27 19:29:14,989 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:29:14,994 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:14,994 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:14,994 - DEBUG - Finished Request
2025-06-27 19:29:14,994 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': '.captcha-refresh'}
2025-06-27 19:29:15,005 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,005 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,005 - DEBUG - Finished Request
2025-06-27 19:29:15,006 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:15,012 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:15,013 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,013 - DEBUG - Finished Request
2025-06-27 19:29:15,013 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '送出')]"}
2025-06-27 19:29:15,026 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,026 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,027 - DEBUG - Finished Request
2025-06-27 19:29:15,027 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[value="送出"]'}
2025-06-27 19:29:15,038 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,039 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,039 - DEBUG - Finished Request
2025-06-27 19:29:15,039 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[type="submit"]'}
2025-06-27 19:29:15,045 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,045 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,045 - DEBUG - Finished Request
2025-06-27 19:29:15,046 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'button[type="submit"]'}
2025-06-27 19:29:15,053 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,053 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,053 - DEBUG - Finished Request
2025-06-27 19:29:15,053 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': '.btn-submit'}
2025-06-27 19:29:15,060 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,061 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,061 - DEBUG - Finished Request
2025-06-27 19:29:15,061 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:15,065 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:15,065 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,065 - DEBUG - Finished Request
2025-06-27 19:29:15,065 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), '取消')]"}
2025-06-27 19:29:15,073 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,073 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,073 - DEBUG - Finished Request
2025-06-27 19:29:15,073 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'input[value="取消"]'}
2025-06-27 19:29:15,081 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,081 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,081 - DEBUG - Finished Request
2025-06-27 19:29:15,081 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': '.btn-cancel'}
2025-06-27 19:29:15,089 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,089 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,090 - DEBUG - Finished Request
2025-06-27 19:29:15,090 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:15,094 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:15,094 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,094 - DEBUG - Finished Request
2025-06-27 19:29:15,094 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'table'}
2025-06-27 19:29:15,103 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,104 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,104 - DEBUG - Finished Request
2025-06-27 19:29:15,104 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': '.table'}
2025-06-27 19:29:15,111 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,111 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,111 - DEBUG - Finished Request
2025-06-27 19:29:15,112 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'tbody'}
2025-06-27 19:29:15,120 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,120 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,120 - DEBUG - Finished Request
2025-06-27 19:29:15,120 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'css selector', 'value': 'tr'}
2025-06-27 19:29:15,127 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:29:15,127 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,127 - DEBUG - Finished Request
2025-06-27 19:29:15,127 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:15,136 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:15,136 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,136 - DEBUG - Finished Request
2025-06-27 19:29:15,637 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:15,646 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:15,646 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,646 - DEBUG - Finished Request
2025-06-27 19:29:15,678 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:15,684 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:15,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:15,685 - DEBUG - Finished Request
2025-06-27 19:29:16,147 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:16,155 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:16,156 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:16,156 - DEBUG - Finished Request
2025-06-27 19:29:16,657 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:16,666 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:16,666 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:16,666 - DEBUG - Finished Request
2025-06-27 19:29:16,685 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:16,691 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:16,691 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:16,691 - DEBUG - Finished Request
2025-06-27 19:29:17,168 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:17,176 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:17,176 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:17,176 - DEBUG - Finished Request
2025-06-27 19:29:17,677 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:17,685 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:17,685 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:17,686 - DEBUG - Finished Request
2025-06-27 19:29:17,692 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:17,696 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:17,696 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:17,696 - DEBUG - Finished Request
2025-06-27 19:29:18,187 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:18,196 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:18,196 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:18,196 - DEBUG - Finished Request
2025-06-27 19:29:18,697 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:18,697 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:18,699 - DEBUG - Starting new HTTP connection (4): localhost:55501
2025-06-27 19:29:18,706 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:18,706 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:18,706 - DEBUG - Finished Request
2025-06-27 19:29:18,727 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:18,727 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:29:18,727 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:18,728 - DEBUG - Finished Request
2025-06-27 19:29:19,228 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:19,239 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:19,239 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:19,239 - DEBUG - Finished Request
2025-06-27 19:29:19,707 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:19,714 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:19,714 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:19,714 - DEBUG - Finished Request
2025-06-27 19:29:19,741 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:19,748 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:19,748 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:19,748 - DEBUG - Finished Request
2025-06-27 19:29:20,251 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:20,261 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:20,262 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:20,262 - DEBUG - Finished Request
2025-06-27 19:29:20,715 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:20,723 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:20,723 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:20,723 - DEBUG - Finished Request
2025-06-27 19:29:20,764 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:20,775 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:20,775 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:20,775 - DEBUG - Finished Request
2025-06-27 19:29:21,276 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:21,286 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:21,287 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:21,287 - DEBUG - Finished Request
2025-06-27 19:29:21,724 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:21,775 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:21,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:21,775 - DEBUG - Finished Request
2025-06-27 19:29:21,787 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:21,848 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:21,849 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:21,849 - DEBUG - Finished Request
2025-06-27 19:29:22,350 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:22,359 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:22,360 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:22,360 - DEBUG - Finished Request
2025-06-27 19:29:22,776 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:22,783 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:22,783 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:22,783 - DEBUG - Finished Request
2025-06-27 19:29:22,861 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:22,871 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:22,871 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:22,873 - DEBUG - Finished Request
2025-06-27 19:29:23,374 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:23,385 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:23,385 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:23,385 - DEBUG - Finished Request
2025-06-27 19:29:23,785 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:23,791 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:23,791 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:23,792 - DEBUG - Finished Request
2025-06-27 19:29:23,886 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:23,895 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:23,895 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:23,895 - DEBUG - Finished Request
2025-06-27 19:29:24,397 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:24,403 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:24,404 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:24,404 - DEBUG - Finished Request
2025-06-27 19:29:24,793 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:24,800 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:24,800 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:24,800 - DEBUG - Finished Request
2025-06-27 19:29:24,905 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:24,916 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:24,916 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:24,916 - DEBUG - Finished Request
2025-06-27 19:29:25,417 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:25,426 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:25,426 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:25,427 - DEBUG - Finished Request
2025-06-27 19:29:25,801 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:25,809 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:25,809 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:25,809 - DEBUG - Finished Request
2025-06-27 19:29:25,928 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:25,937 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:25,937 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:25,937 - DEBUG - Finished Request
2025-06-27 19:29:26,438 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:26,449 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:26,449 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:26,449 - DEBUG - Finished Request
2025-06-27 19:29:26,810 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:26,820 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:26,821 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:26,821 - DEBUG - Finished Request
2025-06-27 19:29:26,950 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:26,961 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:26,962 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:26,962 - DEBUG - Finished Request
2025-06-27 19:29:27,463 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:27,476 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:27,476 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:27,476 - DEBUG - Finished Request
2025-06-27 19:29:27,822 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:27,831 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:27,832 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:27,832 - DEBUG - Finished Request
2025-06-27 19:29:27,978 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:27,989 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:27,989 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:27,990 - DEBUG - Finished Request
2025-06-27 19:29:28,491 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:28,504 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:28,504 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:28,505 - DEBUG - Finished Request
2025-06-27 19:29:28,833 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:28,842 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:28,842 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:28,842 - DEBUG - Finished Request
2025-06-27 19:29:29,006 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:29,018 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:29,019 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:29,019 - DEBUG - Finished Request
2025-06-27 19:29:29,520 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:29,532 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:29,532 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:29,532 - DEBUG - Finished Request
2025-06-27 19:29:29,843 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:29,850 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:29,851 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:29,851 - DEBUG - Finished Request
2025-06-27 19:29:30,034 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:30,044 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:30,045 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:30,045 - DEBUG - Finished Request
2025-06-27 19:29:30,547 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:30,559 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:30,559 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:30,560 - DEBUG - Finished Request
2025-06-27 19:29:30,852 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:30,861 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:30,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:30,861 - DEBUG - Finished Request
2025-06-27 19:29:31,061 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:31,072 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:31,073 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:31,073 - DEBUG - Finished Request
2025-06-27 19:29:31,575 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:31,586 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:31,587 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:31,587 - DEBUG - Finished Request
2025-06-27 19:29:31,862 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:31,870 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:31,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:31,871 - DEBUG - Finished Request
2025-06-27 19:29:32,088 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:32,100 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:32,101 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:32,101 - DEBUG - Finished Request
2025-06-27 19:29:32,602 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:32,613 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:32,614 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:32,614 - DEBUG - Finished Request
2025-06-27 19:29:32,872 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:32,880 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:32,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:32,881 - DEBUG - Finished Request
2025-06-27 19:29:33,115 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:33,126 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:33,126 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:33,126 - DEBUG - Finished Request
2025-06-27 19:29:33,627 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:33,639 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:33,640 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:33,640 - DEBUG - Finished Request
2025-06-27 19:29:33,881 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:33,889 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:33,889 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:33,890 - DEBUG - Finished Request
2025-06-27 19:29:34,141 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:34,153 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:34,154 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:34,155 - DEBUG - Finished Request
2025-06-27 19:29:34,655 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:34,667 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:34,667 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:34,668 - DEBUG - Finished Request
2025-06-27 19:29:34,891 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:34,899 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:34,900 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:34,900 - DEBUG - Finished Request
2025-06-27 19:29:35,170 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:35,180 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:35,180 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:35,181 - DEBUG - Finished Request
2025-06-27 19:29:35,682 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:35,692 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:35,692 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:35,692 - DEBUG - Finished Request
2025-06-27 19:29:35,901 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:35,909 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:35,909 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:35,910 - DEBUG - Finished Request
2025-06-27 19:29:36,194 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:36,206 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:36,206 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:36,207 - DEBUG - Finished Request
2025-06-27 19:29:36,708 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:36,719 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:36,719 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:36,720 - DEBUG - Finished Request
2025-06-27 19:29:36,911 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:36,919 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:36,920 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:36,920 - DEBUG - Finished Request
2025-06-27 19:29:37,222 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:37,233 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:37,234 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:37,234 - DEBUG - Finished Request
2025-06-27 19:29:37,735 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:37,746 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:37,747 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:37,748 - DEBUG - Finished Request
2025-06-27 19:29:37,921 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:37,931 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:37,932 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:37,932 - DEBUG - Finished Request
2025-06-27 19:29:38,249 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:38,260 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:38,261 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:38,261 - DEBUG - Finished Request
2025-06-27 19:29:38,764 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:38,774 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:38,775 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:38,776 - DEBUG - Finished Request
2025-06-27 19:29:38,933 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:38,941 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:38,942 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:38,942 - DEBUG - Finished Request
2025-06-27 19:29:39,277 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:39,287 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:39,287 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:39,288 - DEBUG - Finished Request
2025-06-27 19:29:39,789 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:39,802 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:39,804 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:39,805 - DEBUG - Finished Request
2025-06-27 19:29:39,943 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:39,952 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:39,952 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:39,953 - DEBUG - Finished Request
2025-06-27 19:29:40,306 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:40,317 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:40,317 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:40,318 - DEBUG - Finished Request
2025-06-27 19:29:40,819 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:40,831 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:40,831 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:40,832 - DEBUG - Finished Request
2025-06-27 19:29:40,953 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:40,962 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:40,963 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:40,964 - DEBUG - Finished Request
2025-06-27 19:29:41,333 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:41,345 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:41,345 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:41,346 - DEBUG - Finished Request
2025-06-27 19:29:41,847 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:41,858 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:41,859 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:41,859 - DEBUG - Finished Request
2025-06-27 19:29:41,965 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:41,974 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:41,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:41,975 - DEBUG - Finished Request
2025-06-27 19:29:42,360 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:42,371 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:42,372 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:42,372 - DEBUG - Finished Request
2025-06-27 19:29:42,873 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:42,885 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:42,886 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:42,886 - DEBUG - Finished Request
2025-06-27 19:29:42,976 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:42,984 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:42,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:42,985 - DEBUG - Finished Request
2025-06-27 19:29:43,387 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:43,398 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:43,399 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:43,400 - DEBUG - Finished Request
2025-06-27 19:29:43,901 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:43,913 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:43,914 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:43,914 - DEBUG - Finished Request
2025-06-27 19:29:43,986 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:43,995 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:43,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:43,996 - DEBUG - Finished Request
2025-06-27 19:29:44,415 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:44,428 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:44,428 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:44,429 - DEBUG - Finished Request
2025-06-27 19:29:44,930 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:44,943 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:44,944 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:44,944 - DEBUG - Finished Request
2025-06-27 19:29:44,997 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:45,004 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:45,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:45,005 - DEBUG - Finished Request
2025-06-27 19:29:45,446 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:29:45,457 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:45,457 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:45,458 - DEBUG - Finished Request
2025-06-27 19:29:45,459 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:45,470 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:45,470 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:45,471 - DEBUG - Finished Request
2025-06-27 19:29:45,973 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:45,984 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:45,984 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:45,985 - DEBUG - Finished Request
2025-06-27 19:29:46,006 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:46,013 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:46,013 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:46,014 - DEBUG - Finished Request
2025-06-27 19:29:46,486 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:46,497 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:46,498 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:46,498 - DEBUG - Finished Request
2025-06-27 19:29:46,999 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:47,010 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:47,010 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:47,011 - DEBUG - Finished Request
2025-06-27 19:29:47,015 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:47,021 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:47,022 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:47,022 - DEBUG - Finished Request
2025-06-27 19:29:47,513 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:47,524 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:47,524 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:47,524 - DEBUG - Finished Request
2025-06-27 19:29:48,023 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:48,026 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:48,027 - DEBUG - Starting new HTTP connection (5): localhost:55501
2025-06-27 19:29:48,033 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:48,033 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:48,033 - DEBUG - Finished Request
2025-06-27 19:29:48,054 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:48,054 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:29:48,055 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:48,055 - DEBUG - Finished Request
2025-06-27 19:29:48,557 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:48,569 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:48,570 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:48,570 - DEBUG - Finished Request
2025-06-27 19:29:49,034 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:49,043 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:49,043 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:49,044 - DEBUG - Finished Request
2025-06-27 19:29:49,071 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:49,081 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:49,082 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:49,082 - DEBUG - Finished Request
2025-06-27 19:29:49,584 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:49,597 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:49,597 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:49,598 - DEBUG - Finished Request
2025-06-27 19:29:50,045 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:50,054 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:50,054 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:50,055 - DEBUG - Finished Request
2025-06-27 19:29:50,100 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:50,110 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:50,110 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:50,110 - DEBUG - Finished Request
2025-06-27 19:29:50,612 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:50,623 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:50,624 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:50,624 - DEBUG - Finished Request
2025-06-27 19:29:51,056 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:51,062 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:51,062 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:51,062 - DEBUG - Finished Request
2025-06-27 19:29:51,126 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:51,137 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:51,138 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:51,138 - DEBUG - Finished Request
2025-06-27 19:29:51,639 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:51,652 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:51,653 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:51,653 - DEBUG - Finished Request
2025-06-27 19:29:52,064 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:52,072 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:52,072 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:52,072 - DEBUG - Finished Request
2025-06-27 19:29:52,154 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:52,164 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:52,165 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:52,166 - DEBUG - Finished Request
2025-06-27 19:29:52,667 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:52,678 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:52,678 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:52,678 - DEBUG - Finished Request
2025-06-27 19:29:53,074 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:53,083 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:53,083 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:53,083 - DEBUG - Finished Request
2025-06-27 19:29:53,180 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:53,192 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:53,193 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:53,193 - DEBUG - Finished Request
2025-06-27 19:29:53,694 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:53,704 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:53,704 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:53,705 - DEBUG - Finished Request
2025-06-27 19:29:54,085 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:54,093 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:54,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:54,094 - DEBUG - Finished Request
2025-06-27 19:29:54,206 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:54,219 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:54,219 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:54,220 - DEBUG - Finished Request
2025-06-27 19:29:54,720 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:54,731 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:54,732 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:54,732 - DEBUG - Finished Request
2025-06-27 19:29:55,094 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:55,102 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:55,102 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:55,104 - DEBUG - Finished Request
2025-06-27 19:29:55,233 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:55,242 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:55,242 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:55,243 - DEBUG - Finished Request
2025-06-27 19:29:55,744 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:55,754 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:55,755 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:55,756 - DEBUG - Finished Request
2025-06-27 19:29:56,105 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:56,112 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:56,112 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:56,113 - DEBUG - Finished Request
2025-06-27 19:29:56,256 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:56,266 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:56,267 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:56,268 - DEBUG - Finished Request
2025-06-27 19:29:56,769 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:56,781 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:56,782 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:56,782 - DEBUG - Finished Request
2025-06-27 19:29:57,114 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:57,121 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:57,122 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:57,122 - DEBUG - Finished Request
2025-06-27 19:29:57,284 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:57,294 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:57,294 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:57,295 - DEBUG - Finished Request
2025-06-27 19:29:57,797 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:57,809 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:57,809 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:57,810 - DEBUG - Finished Request
2025-06-27 19:29:58,124 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:58,132 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:58,132 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:58,132 - DEBUG - Finished Request
2025-06-27 19:29:58,311 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:58,321 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:58,321 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:58,322 - DEBUG - Finished Request
2025-06-27 19:29:58,823 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:58,835 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:58,835 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:58,835 - DEBUG - Finished Request
2025-06-27 19:29:59,134 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:29:59,142 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:29:59,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:59,142 - DEBUG - Finished Request
2025-06-27 19:29:59,337 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:59,348 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:59,348 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:59,349 - DEBUG - Finished Request
2025-06-27 19:29:59,850 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:29:59,860 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:29:59,860 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:29:59,861 - DEBUG - Finished Request
2025-06-27 19:30:00,144 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:00,152 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:00,152 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:00,152 - DEBUG - Finished Request
2025-06-27 19:30:00,362 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:00,372 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:00,372 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:00,374 - DEBUG - Finished Request
2025-06-27 19:30:00,875 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:00,886 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:00,886 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:00,887 - DEBUG - Finished Request
2025-06-27 19:30:01,154 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:01,162 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:01,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:01,162 - DEBUG - Finished Request
2025-06-27 19:30:01,388 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:01,399 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:01,399 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:01,399 - DEBUG - Finished Request
2025-06-27 19:30:01,901 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:01,911 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:01,911 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:01,912 - DEBUG - Finished Request
2025-06-27 19:30:02,164 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:02,171 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:02,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:02,172 - DEBUG - Finished Request
2025-06-27 19:30:02,413 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:02,423 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:02,423 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:02,424 - DEBUG - Finished Request
2025-06-27 19:30:02,925 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:02,937 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:02,938 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:02,938 - DEBUG - Finished Request
2025-06-27 19:30:03,173 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:03,180 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:03,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:03,181 - DEBUG - Finished Request
2025-06-27 19:30:03,439 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:03,450 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:03,450 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:03,450 - DEBUG - Finished Request
2025-06-27 19:30:03,952 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:03,963 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:03,963 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:03,964 - DEBUG - Finished Request
2025-06-27 19:30:04,182 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:04,189 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:04,189 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:04,190 - DEBUG - Finished Request
2025-06-27 19:30:04,465 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:04,478 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:04,478 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:04,478 - DEBUG - Finished Request
2025-06-27 19:30:04,979 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:04,990 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:04,990 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:04,991 - DEBUG - Finished Request
2025-06-27 19:30:05,191 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:05,197 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:05,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:05,199 - DEBUG - Finished Request
2025-06-27 19:30:05,492 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:05,504 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/element HTTP/1.1" 404 0
2025-06-27 19:30:05,504 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:05,505 - DEBUG - Finished Request
2025-06-27 19:30:05,506 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:05,514 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:05,515 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:05,515 - DEBUG - Finished Request
2025-06-27 19:30:06,017 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:06,027 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:06,028 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:06,028 - DEBUG - Finished Request
2025-06-27 19:30:06,200 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:06,209 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:06,209 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:06,210 - DEBUG - Finished Request
2025-06-27 19:30:06,529 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:06,539 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:06,540 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:06,540 - DEBUG - Finished Request
2025-06-27 19:30:07,041 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:07,049 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:07,050 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:07,051 - DEBUG - Finished Request
2025-06-27 19:30:07,211 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:07,219 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:07,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:07,220 - DEBUG - Finished Request
2025-06-27 19:30:07,551 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:07,561 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:07,562 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:07,562 - DEBUG - Finished Request
2025-06-27 19:30:08,063 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:08,077 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:08,077 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:08,078 - DEBUG - Finished Request
2025-06-27 19:30:08,221 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:08,229 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:08,230 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:08,230 - DEBUG - Finished Request
2025-06-27 19:30:08,578 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:08,588 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:08,589 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:08,589 - DEBUG - Finished Request
2025-06-27 19:30:09,090 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:09,099 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:09,099 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:09,100 - DEBUG - Finished Request
2025-06-27 19:30:09,232 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:09,239 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:09,240 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:09,240 - DEBUG - Finished Request
2025-06-27 19:30:09,601 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:09,610 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:09,611 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:09,611 - DEBUG - Finished Request
2025-06-27 19:30:10,112 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:10,120 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:10,121 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:10,121 - DEBUG - Finished Request
2025-06-27 19:30:10,242 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:10,249 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:10,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:10,250 - DEBUG - Finished Request
2025-06-27 19:30:10,622 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:10,630 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:10,631 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:10,631 - DEBUG - Finished Request
2025-06-27 19:30:11,132 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:11,142 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:11,143 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:11,143 - DEBUG - Finished Request
2025-06-27 19:30:11,250 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:11,256 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:11,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:11,257 - DEBUG - Finished Request
2025-06-27 19:30:11,644 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:11,655 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:11,656 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:11,656 - DEBUG - Finished Request
2025-06-27 19:30:12,156 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:12,164 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:12,165 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:12,165 - DEBUG - Finished Request
2025-06-27 19:30:12,258 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:12,263 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:12,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:12,263 - DEBUG - Finished Request
2025-06-27 19:30:12,666 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:12,676 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:12,676 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:12,676 - DEBUG - Finished Request
2025-06-27 19:30:13,177 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:13,188 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:13,188 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:13,189 - DEBUG - Finished Request
2025-06-27 19:30:13,264 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:13,272 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:13,272 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:13,272 - DEBUG - Finished Request
2025-06-27 19:30:13,689 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:13,698 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:13,698 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:13,699 - DEBUG - Finished Request
2025-06-27 19:30:14,200 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:14,209 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:14,209 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:14,209 - DEBUG - Finished Request
2025-06-27 19:30:14,273 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:14,279 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:14,280 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:14,280 - DEBUG - Finished Request
2025-06-27 19:30:14,711 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:14,718 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:14,718 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:14,719 - DEBUG - Finished Request
2025-06-27 19:30:15,220 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:15,228 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:15,228 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:15,228 - DEBUG - Finished Request
2025-06-27 19:30:15,282 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:15,289 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:15,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:15,289 - DEBUG - Finished Request
2025-06-27 19:30:15,729 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:15,745 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:15,745 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:15,747 - DEBUG - Finished Request
2025-06-27 19:30:16,247 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:16,256 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:16,257 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:16,257 - DEBUG - Finished Request
2025-06-27 19:30:16,291 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:16,297 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:16,298 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:16,298 - DEBUG - Finished Request
2025-06-27 19:30:16,757 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:16,767 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:16,768 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:16,768 - DEBUG - Finished Request
2025-06-27 19:30:17,269 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:17,279 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:17,279 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:17,280 - DEBUG - Finished Request
2025-06-27 19:30:17,298 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:17,304 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:17,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:17,305 - DEBUG - Finished Request
2025-06-27 19:30:17,781 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:17,791 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:17,792 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:17,792 - DEBUG - Finished Request
2025-06-27 19:30:18,293 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:18,303 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:18,303 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:18,303 - DEBUG - Finished Request
2025-06-27 19:30:18,307 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:18,314 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:18,315 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:18,315 - DEBUG - Finished Request
2025-06-27 19:30:18,805 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:18,813 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:18,813 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:18,813 - DEBUG - Finished Request
2025-06-27 19:30:19,315 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:19,316 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:19,317 - DEBUG - Starting new HTTP connection (6): localhost:55501
2025-06-27 19:30:19,327 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:19,328 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:19,328 - DEBUG - Finished Request
2025-06-27 19:30:19,352 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:19,352 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:30:19,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:19,353 - DEBUG - Finished Request
2025-06-27 19:30:19,829 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:19,841 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:19,841 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:19,841 - DEBUG - Finished Request
2025-06-27 19:30:20,343 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:20,352 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:20,352 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:20,352 - DEBUG - Finished Request
2025-06-27 19:30:20,355 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:20,361 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:20,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:20,362 - DEBUG - Finished Request
2025-06-27 19:30:20,854 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:20,865 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:20,865 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:20,865 - DEBUG - Finished Request
2025-06-27 19:30:21,363 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:21,371 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:21,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:21,372 - DEBUG - Finished Request
2025-06-27 19:30:22,373 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:22,379 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:22,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,380 - DEBUG - Finished Request
2025-06-27 19:30:22,866 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': '\n            // 檢查頁面最終狀態\n            var bodyText = document.body.innerText || document.body.textCon...', 'args': []}
2025-06-27 19:30:22,874 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:30:22,875 - DEBUG - Remote response: status=200 | data={"value":{"bodyTextLength":97,"editButtonCount":0,"hasTargetOrder":false,"rowCount":0,"sampleText":" 環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:28:55\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "}} | headers=HTTPHeaderDict({'Content-Length': '328', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,875 - DEBUG - Finished Request
2025-06-27 19:30:22,877 - INFO - 🔍 [WebDriverWait完成後] 開始記錄頁面內容...
2025-06-27 19:30:22,877 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:22,884 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:22,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,885 - DEBUG - Finished Request
2025-06-27 19:30:22,886 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/title {}
2025-06-27 19:30:22,891 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/title HTTP/1.1" 200 0
2025-06-27 19:30:22,892 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,893 - DEBUG - Finished Request
2025-06-27 19:30:22,893 - INFO - 🔍 [WebDriverWait完成後] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:30:22,893 - INFO - 🔍 [WebDriverWait完成後] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:30:22,893 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/source {}
2025-06-27 19:30:22,897 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/source HTTP/1.1" 200 0
2025-06-27 19:30:22,897 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 10.13px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '2CwSlSb41U01z5yth1-SO_MGAigJvVM1CqKEEWkCG8ypk6fAmvrbt_JqzH8qlw1bNGMGX6wWFYFNPh7wRIWTVsg91gnmLN2pM5RIO2ymJW01:pKQPcGv4CbnWavbyYjpo0QpvqyTCiqOqZwxdO_NLBx8yxBBkmxJXWF9_Hi9ImoA2aPdJX5vjYryghR0gTcZ6eC-LhscfGDD1jVPF-ox_M7k1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:28:55\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"4f3d73f9320e0c41772ab84bf43c4497\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95649a8978034aa3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10265', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,899 - DEBUG - Finished Request
2025-06-27 19:30:22,899 - INFO - 🔍 [WebDriverWait完成後] page_source 長度: 8514
2025-06-27 19:30:22,900 - INFO - 🔍 [WebDriverWait完成後] page_source 包含 E48B: False
2025-06-27 19:30:22,900 - INFO - 🔍 [WebDriverWait完成後] page_source 包含目標訂單: False
2025-06-27 19:30:22,901 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:30:22,907 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:30:22,907 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:28:55\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,908 - DEBUG - Finished Request
2025-06-27 19:30:22,908 - INFO - 🔍 [WebDriverWait完成後] innerText 長度: 97
2025-06-27 19:30:22,908 - INFO - 🔍 [WebDriverWait完成後] innerText 包含 E48B: False
2025-06-27 19:30:22,908 - INFO - 🔍 [WebDriverWait完成後] innerText 包含目標訂單: False
2025-06-27 19:30:22,909 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:30:22,920 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:22,920 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,920 - DEBUG - Finished Request
2025-06-27 19:30:22,921 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:30:22,930 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:22,931 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,931 - DEBUG - Finished Request
2025-06-27 19:30:22,931 - INFO - 🔍 [WebDriverWait完成後] 檢測到 0 個表格，0 個表格行
2025-06-27 19:30:22,932 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:22,941 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:22,942 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,942 - DEBUG - Finished Request
2025-06-27 19:30:22,942 - INFO - 🔍 [WebDriverWait完成後] 包含 'E48B' 的元素數量: 0
2025-06-27 19:30:22,943 - INFO - 🔍 [WebDriverWait完成後] innerText 前300字符:
2025-06-27 19:30:22,943 - INFO - 🔍 [WebDriverWait完成後]  環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:28:55

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-06-27 19:30:22,943 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:30:22,953 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:22,955 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,955 - DEBUG - Finished Request
2025-06-27 19:30:22,956 - INFO - 🔍 [WebDriverWait完成後] 檢測到 0 個編輯按鈕
2025-06-27 19:30:22,956 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:30:22,966 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:22,966 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40"}]} | headers=HTTPHeaderDict({'Content-Length': '128', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,967 - DEBUG - Finished Request
2025-06-27 19:30:22,967 - INFO - 🔍 [WebDriverWait完成後] 檢測到 1 個 iframe
2025-06-27 19:30:22,967 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40'}, 'id']}
2025-06-27 19:30:22,976 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:30:22,977 - DEBUG - Remote response: status=200 | data={"value":"frameid"} | headers=HTTPHeaderDict({'Content-Length': '19', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,977 - DEBUG - Finished Request
2025-06-27 19:30:22,977 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40'}, 'src']}
2025-06-27 19:30:22,985 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:30:22,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00"} | headers=HTTPHeaderDict({'Content-Length': '58', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:22,985 - DEBUG - Finished Request
2025-06-27 19:30:22,986 - INFO - 🔍 [WebDriverWait完成後] iframe 1: id='frameid', src='https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00'
2025-06-27 19:30:22,986 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/frame {'id': {'element-6066-11e4-a52e-4f735466cecf': 'f.98DBD590F0EB2F994EBA837F3EFFECB6.d.55767054129E9B0513BD39342773DCEE.e.40'}}
2025-06-27 19:30:23,011 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/frame HTTP/1.1" 200 0
2025-06-27 19:30:23,011 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,012 - DEBUG - Finished Request
2025-06-27 19:30:23,012 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:30:23,020 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/execute/sync HTTP/1.1" 200 0
2025-06-27 19:30:23,020 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t815.691\t76.89\t0\t453.919\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406230886\t(NEW)H4 2808 星期五(30%)\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406200782\tH4 2808義大遊樂\tKEP-2808\t專車清運\t1.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181141\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181137\t仁\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181136\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181135\t岡\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406181133\t(NEW)H9 5580星期五 只有一車(25%)\tKEJ-5580\t一般清運\t6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110721\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110719\t南\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110718\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110717\t南\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110715\t岡\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110714\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261022\t岡\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261021\t南\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210775\t岡\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210774\t仁\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210773\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190957\t(NEW)119星期五(50%)\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190956\t南\tKED-9671\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190955\tH2 119 義大\t119-BR\t專車清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190954\t南\t117-BR\t一般清運\t3.5\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190953\t仁\t119-BR\t一般清運\t3.6\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190949\t南\t119-BR\t一般清運\t3.6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190948\t仁\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190947\t岡\t120-BR\t一般清運\t3.2\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190945\t仁\t121-BR\t一般清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190944\t岡\t121-BR\t一般清運\t3.5\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190943\t仁\t129-BR\t一般清運\t4.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190940\t岡\t129-BR\t一般清運\t4.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190938\t(NEW)H72560星期五(有美生) (20%)\tKEP-2560\t一般清運\t4.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190936\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190934\t岡\t937-N6\t一般清運\t4.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190932\t南\t937-N6\t一般清運\t4.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190931\t南\tKEH-9278\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190929\t仁\tKEH-9278\t一般清運\t8.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190928\t岡\tKER-2807\t一般清運\t7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190698\t仁\tKER-2807\t一般清運\t7\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n顯示第 1 至 38 項結果，共 38 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '7514', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,022 - DEBUG - Finished Request
2025-06-27 19:30:23,022 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:30:23,030 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,030 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.131"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.132"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,031 - DEBUG - Finished Request
2025-06-27 19:30:23,031 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:30:23,039 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,040 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.581"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.582"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.583"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.584"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.585"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.586"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.587"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.588"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.589"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.590"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.591"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.592"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.593"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.594"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.595"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.596"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.597"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.598"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.599"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.600"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.601"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.602"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.603"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.604"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.605"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.606"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.607"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.608"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.609"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.610"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.611"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.612"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.613"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.614"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.615"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.616"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.617"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.618"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.619"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,041 - DEBUG - Finished Request
2025-06-27 19:30:23,041 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:30:23,054 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,054 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.620"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.621"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.622"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.623"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.624"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.625"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.626"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.627"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.628"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.629"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.630"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.631"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.632"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.633"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.634"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.635"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.636"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.637"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.638"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.639"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.640"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.641"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.642"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.643"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.644"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.645"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.646"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.647"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.648"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.649"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.650"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.651"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.652"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.653"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.654"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.655"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.656"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.657"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.658"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.659"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.660"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.661"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.662"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.663"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.664"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.665"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.666"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.667"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.668"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.669"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.670"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.671"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.672"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.673"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.674"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.675"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.676"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.677"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.678"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.679"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.680"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.681"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.682"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.683"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.684"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.685"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.686"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.687"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.688"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.689"}]} | headers=HTTPHeaderDict({'Content-Length': '8271', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,055 - DEBUG - Finished Request
2025-06-27 19:30:23,055 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:30:23,066 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,067 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.690"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.691"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.692"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.693"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.694"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.695"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.696"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.697"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.698"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.699"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.700"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.701"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.702"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.703"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.704"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.705"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.706"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.707"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.708"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.709"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.710"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.711"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.712"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.713"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.714"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.715"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.716"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.717"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.718"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.719"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.720"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.721"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.722"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.723"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.724"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.725"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.726"},{"element-6066-11e4-a52e-4f735466cecf":"f.E0F57BF151FA8E4379B2ABD824CF6910.d.2A72ABCDC830EB5F41F8F20E957C7F9B.e.727"}]} | headers=HTTPHeaderDict({'Content-Length': '4495', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,068 - DEBUG - Finished Request
2025-06-27 19:30:23,068 - INFO - 🔍 [WebDriverWait完成後] iframe 1 內容:
2025-06-27 19:30:23,069 - INFO - 🔍 [WebDriverWait完成後]   - 文字長度: 3955
2025-06-27 19:30:23,069 - INFO - 🔍 [WebDriverWait完成後]   - 包含目標訂單: True
2025-06-27 19:30:23,069 - INFO - 🔍 [WebDriverWait完成後]   - 包含 E48B: True
2025-06-27 19:30:23,070 - INFO - 🔍 [WebDriverWait完成後]   - 表格數量: 2
2025-06-27 19:30:23,070 - INFO - 🔍 [WebDriverWait完成後]   - 表格行數: 41
2025-06-27 19:30:23,070 - INFO - 🔍 [WebDriverWait完成後]   - 編輯按鈕數量: 70
2025-06-27 19:30:23,071 - INFO - 🔍 [WebDriverWait完成後]   - E48B 元素數量: 38
2025-06-27 19:30:23,071 - INFO - 🔍 [WebDriverWait完成後]   - 內容前300字符: 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

6月
7月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	815.691	76.89	0	453.919
每日開放查詢時日1...
2025-06-27 19:30:23,071 - INFO - 🔍 [WebDriverWait完成後]   - 內容後300字符: ...		高南廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190929	仁	KEH-9278	一般清運	8.5	2025-07-04		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190928	岡	KER-2807	一般清運	7	2025-07-04		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190698	仁	KER-2807	一般清運	7	2025-07-04		仁武廠	0	一般	取消(刪除)
顯示第 1 至 38 項結果，共 38 項
上一頁
1
下一頁
2025-06-27 19:30:23,071 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/frame {'id': None}
2025-06-27 19:30:23,075 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/frame HTTP/1.1" 200 0
2025-06-27 19:30:23,075 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,075 - DEBUG - Finished Request
2025-06-27 19:30:23,076 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:23,081 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:23,082 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,082 - DEBUG - Finished Request
2025-06-27 19:30:23,083 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/title {}
2025-06-27 19:30:23,089 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/title HTTP/1.1" 200 0
2025-06-27 19:30:23,090 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,090 - DEBUG - Finished Request
2025-06-27 19:30:23,091 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//tr[contains(., 'E48B201611405190953')]"}
2025-06-27 19:30:23,100 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,100 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,100 - DEBUG - Finished Request
2025-06-27 19:30:23,101 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//tbody//tr[contains(., 'E48B201611405190953')]"}
2025-06-27 19:30:23,112 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,112 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,113 - DEBUG - Finished Request
2025-06-27 19:30:23,113 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//table//tr[contains(., 'E48B201611405190953')]"}
2025-06-27 19:30:23,123 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,125 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,125 - DEBUG - Finished Request
2025-06-27 19:30:23,125 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//tr[td[contains(text(), 'E48B201611405190953')]]"}
2025-06-27 19:30:23,136 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,136 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,137 - DEBUG - Finished Request
2025-06-27 19:30:23,137 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//tr[td[contains(., 'E48B201611405190953')]]"}
2025-06-27 19:30:23,147 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,148 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,148 - DEBUG - Finished Request
2025-06-27 19:30:23,148 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//tr[descendant::*[contains(text(), 'E48B201611405190953')]]"}
2025-06-27 19:30:23,158 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,158 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,158 - DEBUG - Finished Request
2025-06-27 19:30:23,159 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]/ancestor::tr"}
2025-06-27 19:30:23,169 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,170 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,170 - DEBUG - Finished Request
2025-06-27 19:30:23,171 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': "//div[contains(., 'E48B201611405190953')]/ancestor::tr"}
2025-06-27 19:30:23,179 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,180 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,180 - DEBUG - Finished Request
2025-06-27 19:30:23,181 - DEBUG - POST http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:30:23,188 - DEBUG - http://localhost:55501 "POST /session/927b9a4b03a0003a25bb3c78e8ece229/elements HTTP/1.1" 200 0
2025-06-27 19:30:23,188 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,189 - DEBUG - Finished Request
2025-06-27 19:30:23,189 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/source {}
2025-06-27 19:30:23,193 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/source HTTP/1.1" 200 0
2025-06-27 19:30:23,194 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 10.13px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': '2CwSlSb41U01z5yth1-SO_MGAigJvVM1CqKEEWkCG8ypk6fAmvrbt_JqzH8qlw1bNGMGX6wWFYFNPh7wRIWTVsg91gnmLN2pM5RIO2ymJW01:pKQPcGv4CbnWavbyYjpo0QpvqyTCiqOqZwxdO_NLBx8yxBBkmxJXWF9_Hi9ImoA2aPdJX5vjYryghR0gTcZ6eC-LhscfGDD1jVPF-ox_M7k1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:28:55\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"319169a810bd74d4710a113ac11ae539\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95649a8978034aa3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10265', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,195 - DEBUG - Finished Request
2025-06-27 19:30:23,381 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:23,389 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:23,390 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:23,390 - DEBUG - Finished Request
2025-06-27 19:30:24,391 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:24,398 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 200 0
2025-06-27 19:30:24,398 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:24,399 - DEBUG - Finished Request
2025-06-27 19:30:25,400 - DEBUG - GET http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229/url {}
2025-06-27 19:30:25,402 - DEBUG - http://localhost:55501 "GET /session/927b9a4b03a0003a25bb3c78e8ece229/url HTTP/1.1" 404 0
2025-06-27 19:30:25,402 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d059b5]\n\t(No symbol) [0x0x7ff7d7d2a9ca]\n\t(No symbol) [0x0x7ff7d7da05e5]\n\t(No symbol) [0x0x7ff7d7dc0b42]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:25,403 - DEBUG - Finished Request
2025-06-27 19:30:25,403 - DEBUG - DELETE http://localhost:55501/session/927b9a4b03a0003a25bb3c78e8ece229 {}
2025-06-27 19:30:25,522 - DEBUG - http://localhost:55501 "DELETE /session/927b9a4b03a0003a25bb3c78e8ece229 HTTP/1.1" 200 0
2025-06-27 19:30:25,522 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:30:25,523 - DEBUG - Finished Request
