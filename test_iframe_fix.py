#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 v1.5.0-dev03 iframe 切換修復
驗證 log_page_content 函數是否正確切換回主頁面
"""

import sys
import os

def test_iframe_switch_fix():
    """測試 iframe 切換修復"""
    print("🔧 測試 v1.5.0-dev03 iframe 切換修復")
    print("=" * 50)
    
    try:
        # 導入模組
        import mvp_grabber
        import inspect
        
        # 檢查版本
        version = getattr(mvp_grabber, '__VERSION__', 'Unknown')
        print(f"📋 當前版本: {version}")
        
        if version != "1.5.0-dev03":
            print(f"❌ 版本不匹配，期望: 1.5.0-dev03，實際: {version}")
            return False
        
        # 檢查 log_page_content 函數
        if not hasattr(mvp_grabber, 'log_page_content'):
            print("❌ 找不到 log_page_content 函數")
            return False
        
        # 獲取函數源碼
        source = inspect.getsource(mvp_grabber.log_page_content)
        
        # 檢查修復內容
        checks = [
            ("切換回主頁面", "driver.switch_to.default_content()" in source),
            ("移除註釋", "# 註釋掉，保持在 iframe 內" not in source),
            ("錯誤處理", "切換回主頁面失敗" in source),
            ("日誌記錄", "已切換回主頁面" in source)
        ]
        
        print("\n🔍 檢查修復內容:")
        all_passed = True
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"  {status} {check_name}: {'通過' if result else '失敗'}")
            if not result:
                all_passed = False
        
        # 檢查關鍵修復點
        print("\n🎯 關鍵修復點檢查:")
        
        # 檢查是否移除了保持在 iframe 內的註釋
        if "# 🎯 不要切換回主頁面，保持在 iframe 內進行後續搜尋" in source:
            print("  ❌ 仍然包含舊的註釋，修復不完整")
            all_passed = False
        else:
            print("  ✅ 已移除舊的註釋")
        
        # 檢查是否添加了新的切換邏輯
        if "driver.switch_to.default_content()  # 恢復切換回主頁面" in source:
            print("  ✅ 已添加切換回主頁面的邏輯")
        else:
            print("  ❌ 缺少切換回主頁面的邏輯")
            all_passed = False
        
        # 檢查錯誤處理
        if "try:" in source and "driver.switch_to.default_content()" in source:
            print("  ✅ 包含錯誤處理的切換邏輯")
        else:
            print("  ❌ 缺少錯誤處理的切換邏輯")
            all_passed = False
        
        print("\n" + "=" * 50)
        if all_passed:
            print("✅ 所有檢查通過！iframe 切換修復已正確實現")
            print("💡 修復說明：")
            print("   - log_page_content 函數現在會在檢測完 iframe 內容後切換回主頁面")
            print("   - 添加了錯誤處理，確保即使發生異常也會嘗試切換回主頁面")
            print("   - 這應該解決用戶點擊'準備完成'後程序停止執行的問題")
            return True
        else:
            print("❌ 部分檢查失敗，修復可能不完整")
            return False
            
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        return False

def test_expected_workflow():
    """測試預期的工作流程"""
    print("\n🔄 預期工作流程測試:")
    print("=" * 50)
    
    workflow_steps = [
        "1. 用戶點擊'準備完成'按鈕",
        "2. 系統調用 log_page_content 記錄頁面內容",
        "3. log_page_content 切換到 iframe 檢測內容",
        "4. 檢測完成後切換回主頁面 ← 關鍵修復點",
        "5. 系統調用 _verify_correct_page 驗證頁面",
        "6. 驗證通過後調用 execute_order_grabbing",
        "7. 開始自動搶單流程"
    ]
    
    for step in workflow_steps:
        if "關鍵修復點" in step:
            print(f"  🎯 {step}")
        else:
            print(f"  📋 {step}")
    
    print("\n💡 修復前的問題：")
    print("   - log_page_content 切換到 iframe 後沒有切換回主頁面")
    print("   - 後續操作在 iframe 內執行，被 footer 元素阻擋")
    print("   - 程序因 'element click intercepted' 錯誤停止執行")
    
    print("\n✅ 修復後的改善：")
    print("   - log_page_content 檢測完 iframe 內容後自動切換回主頁面")
    print("   - 後續操作在主頁面執行，避免被 iframe 內元素阻擋")
    print("   - 程序可以正常繼續執行搶單流程")

if __name__ == "__main__":
    print("🧪 AGES-KH-Bot v1.5.0-dev03 iframe 切換修復測試")
    print("=" * 60)
    
    # 執行測試
    success = test_iframe_switch_fix()
    test_expected_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 測試完成！修復已正確實現，可以進行實際測試")
        print("📝 建議下一步：")
        print("   1. 啟動程序進行實際測試")
        print("   2. 手動登入平台並操作至清單畫面")
        print("   3. 點擊'準備完成'按鈕")
        print("   4. 觀察程序是否正常進入搶單流程")
    else:
        print("❌ 測試失敗，需要檢查修復實現")
    
    print("=" * 60)
