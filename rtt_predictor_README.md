# RTT Predictor 模組說明文件

## 版本資訊
- **當前版本**: v1.3.0
- **最後更新**: 2024-06-18
- **作者**: Will Wang

## 模組概述
RTT Predictor 是一個網路延遲測試模組，用於在指定時間區間內對外部伺服器進行 RTT（Round Trip Time）採樣，並提供統計分析結果。本模組可單獨執行或由主程式調用，支援多模型架構（目前僅 Model A）。

## 主要功能
1. **RTT 採樣**: 在指定時間區間內對目標伺服器進行連續 RTT 測試
2. **統計分析**: 計算平均 RTT、錯誤率等關鍵指標
3. **數據記錄**: 自動產生 log 檔案與 raw data CSV 檔案
4. **多模型支援**: 預留多模型架構，便於未來擴充
5. **錯誤處理**: 統計網路錯誤次數與錯誤率，便於網路健康度監控

## 架構設計

### 數據流程
```
RTT 採樣 → rawdata (共用數據)
    ↓
模型A → 模型A計算結果
    ↓
模型B → 模型B計算結果
    ↓
模型C → 模型C計算結果
```

### 核心概念
- **rawdata 是共用的**：所有模型都使用同一份 RTT 採樣數據
- **採樣參數決定 rawdata**：freq、duration、server_url 決定如何採樣
- **不同模型有不同的計算邏輯**：Model A、B、C 對同一份 rawdata 進行不同計算

## API 介面

### 參數優先級與使用邏輯
API 參數的使用遵循以下優先級：
1. **明確指定參數**：`get_rtt_result(freq=5, duration=30)` - 使用使用者指定的值
2. **JSON 設定檔**：當參數為 None 時，讀取 `config/rtt_config.json` 的值
3. **程式預設值**：僅在初次啟動且 JSON 檔案不存在時使用

### 使用流程
- **初次啟動**：使用程式預設值，自動建立 JSON 設定檔
- **使用者修改**：透過 GUI 修改 JSON 設定檔
- **再次啟動**：API 自動讀取 JSON 設定檔的值
- **明確指定**：API 參數優先於 JSON 設定檔

### 主要 API: get_rtt_result()
```python
from rtt_predictor import get_rtt_result

result = get_rtt_result(
    model="A",                    # 預測模型（目前支援 "A"）
    freq=2,                       # 採樣頻率（次/秒），預設 2
    duration=60,                  # 採樣時長（秒），預設 60
    server_url="https://wmc.kcg.gov.tw/",  # 目標伺服器 URL
    sample_start_time=None,       # 採樣開始時間（datetime，可選）
    sample_end_time=None          # 採樣結束時間（datetime，可選）
)

# 回傳結果範例
{
    "model": "A",
    "avg_rtt": 318,               # 平均 RTT（毫秒）
    "error_count": 47,            # 錯誤次數
    "samples": 73                 # 成功採樣筆數
}
```

### 相容性 API: get_avg_rtt()
```python
from rtt_predictor import get_avg_rtt

avg_rtt, rtts, timestamps = get_avg_rtt(
    model="A",                    # 只支援 "A"
    freq=2,                       # 採樣頻率
    duration=60,                  # 採樣時長
    server_url="https://wmc.kcg.gov.tw/",
    sample_start_time=None,
    sample_end_time=None
)
```

## 數據格式

### Log 檔案格式
```
[時間] model=A avg_rtt=318ms samples=73/120 error_count=47 error_rate=39.17% sample_start=2024-06-18 15:05:13.185 sample_end=2024-06-18 15:06:13.185
```

**欄位說明**:
- `model`: 使用的預測模型
- `avg_rtt`: 平均 RTT（毫秒）
- `samples`: 成功採樣筆數/總嘗試次數
- `error_count`: 錯誤次數
- `error_rate`: 錯誤率（錯誤次數/總嘗試次數）
- `sample_start`: 採樣開始時間
- `sample_end`: 採樣結束時間

### CSV 檔案格式
```csv
seq,timestamp,device_name,device_id,user,local_ip,target_url,target_ip,rtt_ms
1,2024-06-18 15:05:13.185,DESKTOP-ABC123,aa:bb:cc:dd:ee:ff,username,*************,https://wmc.kcg.gov.tw/,************,245.67
```

**欄位說明**:
- `seq`: 序號
- `timestamp`: 採樣時間戳
- `device_name`: 設備名稱（hostname）
- `device_id`: 設備識別碼（MAC 位址）
- `user`: 使用者帳號
- `local_ip`: 本地 IP 位址
- `target_url`: 目標伺服器 URL
- `target_ip`: 目標伺服器 IP 位址
- `rtt_ms`: RTT 值（毫秒）

## 錯誤率指標說明

### error_rate 計算方式
```
error_rate = error_count / (sample_count + error_count) * 100%
```

### 錯誤率意義
- **0%**: 網路完全正常，所有請求都成功
- **1-20%**: 網路輕微不穩定，偶有延遲
- **21-50%**: 網路中度不穩定，需要關注
- **51-80%**: 網路嚴重不穩定，可能影響業務
- **81-99%**: 網路幾乎中斷，僅少數請求成功
- **100%**: 網路完全中斷，所有請求都失敗

### 應用場景
1. **網路監控**: 即時監控網路健康度
2. **故障排除**: 快速定位網路問題
3. **多設備管理**: 比較不同設備的網路狀況
4. **服務品質評估**: 評估網路服務的穩定性

## 設計邊界與限制

### 設計邊界
- **單一目標**: 每次執行僅測試單一目標伺服器
- **單一模型**: 每次執行僅使用單一預測模型
- **單執行緒**: 不支援多線程並行採樣
- **本地執行**: 所有採樣在本地設備執行

### 適用情境
- 單設備網路延遲測試
- 定期網路健康度監控
- 網路故障診斷
- 服務品質評估

### 不適用情境
- 多目標同時測試
- 多模型並行執行
- 分散式網路監控
- 高頻率即時監控（建議間隔 > 1 秒）

## 檔案結構
```
logs/
├── rtt_20240618_150513.1856.csv    # Raw data
├── rtt_20240618_150513.1856.log    # 摘要 log
└── ...
```

## 使用範例

### 獨立執行
```bash
python rtt_predictor.py
```

### 程式調用
```python
from rtt_predictor import get_rtt_result

# 基本使用（使用 JSON 設定檔的值）
result = get_rtt_result()
print(f"平均 RTT: {result['avg_rtt']} ms")
print(f"錯誤率: {result['error_count']} / {result['samples'] + result['error_count']}")

# 自訂參數（覆蓋 JSON 設定檔）
result = get_rtt_result(
    freq=5,           # 每秒 5 次（覆蓋 JSON 中的設定）
    duration=30,      # 30 秒（覆蓋 JSON 中的設定）
    server_url="https://www.google.com/"  # 覆蓋 JSON 中的設定
)

# 部分自訂（只覆蓋特定參數）
result = get_rtt_result(
    freq=10,          # 覆蓋為 10 次/秒
    # duration 和 server_url 使用 JSON 設定檔的值
)
```

## 版本紀錄

### v1.3.0 (2024-06-18)
- **重大架構簡化**：移除 default 和 models 的巢狀結構
- **統一採樣參數**：所有模型使用相同的採樣設定
- **簡化 JSON 格式**：直接使用 freq、duration、server_url
- **更新 GUI 介面**：移除混淆的「預設設定」和「Model A 設定」區分
- **明確架構概念**：rawdata 共用，不同模型進行不同計算

### v1.2.3 (2024-06-18)
- 新增錯誤率統計功能
- 修正 error_count 計算邏輯
- 完善錯誤處理機制
- 更新文件說明

### v1.2.2 (2024-06-18)
- 新增 error_count 與 error_rate 欄位
- 優化 socket 資源管理
- 完善 API 設計

### v1.2.0 (2024-06-18)
- 新增 get_rtt_result() API
- 支援多模型架構
- 新增 device_id、device_name、user 等欄位
- 完善 CSV 與 log 檔案格式

### v1.1.0 (2024-06-18)
- 新增自動產生 log 與 CSV 檔案
- 新增 get_mac()、get_local_ip()、get_target_ip() 功能
- 完善錯誤處理

### v1.0.0 (2024-06-18)
- 初始版本
- 基本 RTT 採樣功能
- 支援自訂頻率與時長 

### 設定檔格式

#### rtt_config.json
```json
{
    "rtt_module_version": "1.3.0",
    "freq": 2,
    "duration": 60,
    "server_url": "https://wmc.kcg.gov.tw/"
}
```

**欄位說明**:
- `rtt_module_version`: RTT 模組版本號
- `freq`: 採樣頻率（次/秒）
- `duration`: 採樣時長（秒）
- `server_url`: 目標伺服器 URL 