# mvp_grabber.py v1.0
# AGES-KH 搶單主程式

import tkinter as tk
from tkinter import simpledialog, messagebox
import csv
from datetime import datetime, date
import os
import sys

__VERSION__ = "1.0"

# ===== GUI：詢問使用者輸入觸發時間 =====
def ask_trigger_time_gui(default="09:30:00") -> str:
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗

    answer = simpledialog.askstring("觸發時間設定", 
                                     "請輸入本次搶單觸發時間 (格式 HH:MM:SS)：",
                                     initialvalue=default)
    if not answer:
        return default
    return answer.strip()

# ===== 載入 orders.csv，回傳今日或萬用單號列表 =====
def normalize_date(date_str: str) -> str:
    try:
        dt = datetime.strptime(date_str.replace("/", "-"), "%Y-%m-%d")
        return dt.strftime("%Y-%m-%d")
    except Exception:
        return date_str  # 保留原始格式供比對

def load_orders_from_csv(path="orders/orders.csv") -> list:
    today_str = date.today().strftime("%Y-%m-%d")
    orders = []
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            row_date = normalize_date(row['date'])
            if row_date == today_str or row_date == '*-*-*':
                orders.append(row['order_id'])
    return orders

# ===== 主執行區 =====
def main():
    print("[INFO] 啟動 AGES-KH 搶單主程式 v" + __VERSION__)

    # Step 0: GUI 詢問觸發時間
    trigger_time_str = ask_trigger_time_gui()
    print(f"[INFO] 使用觸發時間：{trigger_time_str}")

    # Step 1: 載入單號
    try:
        order_list = load_orders_from_csv()
        if not order_list:
            messagebox.showwarning("無單可處理", "今天無需處理的單據。請確認 orders.csv。")
            sys.exit(0)
        print(f"[INFO] 今日需處理 {len(order_list)} 筆單據：{order_list}")
    except Exception as e:
        messagebox.showerror("讀取 orders.csv 失敗", str(e))
        sys.exit(1)

    # TODO: 進入後續流程（顯示登入提示 → selenium 操作 → GUI 驗證碼 → 倒數送出）

if __name__ == '__main__':
    main()
