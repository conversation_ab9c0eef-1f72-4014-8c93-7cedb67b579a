2025-06-30 19:11:03,051 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_191103.log
2025-06-30 19:11:15,184 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 19:11:15,184 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 19:11:15,251 - DEBUG - chromedriver not found in PATH
2025-06-30 19:11:15,252 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:11:15,252 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 19:11:15,252 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 19:11:15,252 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 19:11:15,252 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 19:11:15,253 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 19:11:15,258 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 4456 using 0 to output -3
2025-06-30 19:11:15,781 - DEBUG - POST http://localhost:54478/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 19:11:15,782 - DEBUG - Starting new HTTP connection (1): localhost:54478
2025-06-30 19:11:16,329 - DEBUG - http://localhost:54478 "POST /session HTTP/1.1" 200 0
2025-06-30 19:11:16,330 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir4456_226914900"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54481"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"1f82f72d1d15599947b727927a4d1efc"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:16,330 - DEBUG - Finished Request
2025-06-30 19:11:16,331 - DEBUG - POST http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 19:11:33,053 - DEBUG - http://localhost:54478 "POST /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:33,053 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:33,054 - DEBUG - Finished Request
2025-06-30 19:11:33,054 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 19:11:33,054 - DEBUG - POST http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 19:11:33,061 - DEBUG - http://localhost:54478 "POST /session/1f82f72d1d15599947b727927a4d1efc/execute/sync HTTP/1.1" 200 0
2025-06-30 19:11:33,062 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:33,062 - DEBUG - Finished Request
2025-06-30 19:11:33,063 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 19:11:33,064 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:33,105 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:33,106 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:33,106 - DEBUG - Finished Request
2025-06-30 19:11:34,107 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:34,114 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:34,115 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:34,115 - DEBUG - Finished Request
2025-06-30 19:11:35,116 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:35,124 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:35,125 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:35,125 - DEBUG - Finished Request
2025-06-30 19:11:36,126 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:36,133 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:36,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:36,134 - DEBUG - Finished Request
2025-06-30 19:11:37,135 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:37,143 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:37,143 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:37,143 - DEBUG - Finished Request
2025-06-30 19:11:38,144 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:38,151 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:38,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:38,152 - DEBUG - Finished Request
2025-06-30 19:11:39,153 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:39,159 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:39,159 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:39,159 - DEBUG - Finished Request
2025-06-30 19:11:40,160 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:40,168 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:40,168 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:40,168 - DEBUG - Finished Request
2025-06-30 19:11:41,170 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:41,177 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:41,178 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:41,178 - DEBUG - Finished Request
2025-06-30 19:11:42,178 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:42,186 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:42,186 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:42,187 - DEBUG - Finished Request
2025-06-30 19:11:43,188 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:43,196 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:43,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:43,197 - DEBUG - Finished Request
2025-06-30 19:11:44,197 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:44,204 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:44,204 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:44,204 - DEBUG - Finished Request
2025-06-30 19:11:45,205 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:45,213 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:45,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:45,213 - DEBUG - Finished Request
2025-06-30 19:11:46,214 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:46,221 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:46,221 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:46,222 - DEBUG - Finished Request
2025-06-30 19:11:47,222 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:47,229 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:47,229 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:47,230 - DEBUG - Finished Request
2025-06-30 19:11:48,230 - DEBUG - GET http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc/url {}
2025-06-30 19:11:48,238 - DEBUG - http://localhost:54478 "GET /session/1f82f72d1d15599947b727927a4d1efc/url HTTP/1.1" 200 0
2025-06-30 19:11:48,238 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:48,239 - DEBUG - Finished Request
2025-06-30 19:11:48,936 - DEBUG - DELETE http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc {}
2025-06-30 19:11:48,973 - DEBUG - http://localhost:54478 "DELETE /session/1f82f72d1d15599947b727927a4d1efc HTTP/1.1" 200 0
2025-06-30 19:11:48,973 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 19:11:48,974 - DEBUG - Finished Request
2025-06-30 19:11:49,251 - DEBUG - DELETE http://localhost:54478/session/1f82f72d1d15599947b727927a4d1efc {}
2025-06-30 19:11:49,252 - DEBUG - Starting new HTTP connection (1): localhost:54478
