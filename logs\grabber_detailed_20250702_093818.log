2025-07-02 09:38:18,591 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_093818.log
2025-07-02 09:39:23,295 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 09:39:23,295 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 09:39:23,371 - DEBUG - chromedriver not found in PATH
2025-07-02 09:39:23,372 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:39:23,372 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 09:39:23,372 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 09:39:23,372 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 09:39:23,372 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 09:39:23,373 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:39:23,378 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 7292 using 0 to output -3
2025-07-02 09:39:23,890 - DEBUG - POST http://localhost:54318/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 09:39:23,891 - DEBUG - Starting new HTTP connection (1): localhost:54318
2025-07-02 09:39:24,437 - DEBUG - http://localhost:54318 "POST /session HTTP/1.1" 200 0
2025-07-02 09:39:24,437 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir7292_184468816"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54321"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"0ed66fd9f5678ca627ab557bc85ad9ae"}} | headers=HTTPHeaderDict({'Content-Length': '880', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:24,438 - DEBUG - Finished Request
2025-07-02 09:39:24,439 - DEBUG - POST http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 09:39:26,359 - DEBUG - http://localhost:54318 "POST /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:26,360 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:26,360 - DEBUG - Finished Request
2025-07-02 09:39:26,360 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 09:39:26,360 - DEBUG - POST http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 09:39:26,367 - DEBUG - http://localhost:54318 "POST /session/0ed66fd9f5678ca627ab557bc85ad9ae/execute/sync HTTP/1.1" 200 0
2025-07-02 09:39:26,367 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:26,367 - DEBUG - Finished Request
2025-07-02 09:39:26,367 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-02 09:39:26,368 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:26,398 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:26,399 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:26,399 - DEBUG - Finished Request
2025-07-02 09:39:27,399 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:27,405 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:27,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:27,407 - DEBUG - Finished Request
2025-07-02 09:39:28,408 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:28,415 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:28,416 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:28,416 - DEBUG - Finished Request
2025-07-02 09:39:29,416 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:29,422 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:29,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:29,423 - DEBUG - Finished Request
2025-07-02 09:39:30,423 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:30,430 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:30,431 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:30,431 - DEBUG - Finished Request
2025-07-02 09:39:31,432 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:31,446 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:31,446 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:31,446 - DEBUG - Finished Request
2025-07-02 09:39:32,447 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:32,454 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:32,454 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:32,454 - DEBUG - Finished Request
2025-07-02 09:39:33,455 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:33,462 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:33,463 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:33,463 - DEBUG - Finished Request
2025-07-02 09:39:34,464 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:34,471 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:34,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:34,471 - DEBUG - Finished Request
2025-07-02 09:39:35,473 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:35,479 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:35,479 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:35,480 - DEBUG - Finished Request
2025-07-02 09:39:36,480 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:36,486 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:36,487 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:36,488 - DEBUG - Finished Request
2025-07-02 09:39:37,488 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:37,494 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:37,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:37,495 - DEBUG - Finished Request
2025-07-02 09:39:38,496 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:38,503 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:38,503 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:38,503 - DEBUG - Finished Request
2025-07-02 09:39:39,504 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:39,510 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:39,511 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:39,512 - DEBUG - Finished Request
2025-07-02 09:39:40,512 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:40,518 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:40,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:40,519 - DEBUG - Finished Request
2025-07-02 09:39:41,520 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:41,525 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:41,526 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:41,527 - DEBUG - Finished Request
2025-07-02 09:39:42,528 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:42,534 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:42,534 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:42,534 - DEBUG - Finished Request
2025-07-02 09:39:43,535 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:43,542 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:43,542 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:43,543 - DEBUG - Finished Request
2025-07-02 09:39:44,543 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:44,549 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:44,550 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:44,550 - DEBUG - Finished Request
2025-07-02 09:39:45,551 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:45,557 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:45,558 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:45,558 - DEBUG - Finished Request
2025-07-02 09:39:46,559 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:46,565 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:46,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:46,565 - DEBUG - Finished Request
2025-07-02 09:39:47,566 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:47,573 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:47,573 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:47,574 - DEBUG - Finished Request
2025-07-02 09:39:48,575 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:49,533 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:49,534 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:49,534 - DEBUG - Finished Request
2025-07-02 09:39:50,535 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:50,540 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:50,541 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:50,541 - DEBUG - Finished Request
2025-07-02 09:39:51,542 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:51,549 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:51,549 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:51,549 - DEBUG - Finished Request
2025-07-02 09:39:52,550 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:52,556 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:52,556 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:52,557 - DEBUG - Finished Request
2025-07-02 09:39:53,558 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:53,563 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:53,563 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:53,564 - DEBUG - Finished Request
2025-07-02 09:39:54,565 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:54,572 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:54,573 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:54,573 - DEBUG - Finished Request
2025-07-02 09:39:55,574 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:55,580 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:55,580 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:55,580 - DEBUG - Finished Request
2025-07-02 09:39:56,581 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:56,587 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:56,587 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:56,588 - DEBUG - Finished Request
2025-07-02 09:39:57,589 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:57,595 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:57,596 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:57,596 - DEBUG - Finished Request
2025-07-02 09:39:58,597 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:58,602 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:58,603 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:58,603 - DEBUG - Finished Request
2025-07-02 09:39:59,605 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:39:59,611 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:39:59,611 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:39:59,611 - DEBUG - Finished Request
2025-07-02 09:40:00,612 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:00,618 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:00,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:00,618 - DEBUG - Finished Request
2025-07-02 09:40:01,620 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:01,625 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:01,625 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:01,625 - DEBUG - Finished Request
2025-07-02 09:40:02,627 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:02,633 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:02,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:02,633 - DEBUG - Finished Request
2025-07-02 09:40:03,633 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:03,655 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:03,656 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:03,656 - DEBUG - Finished Request
2025-07-02 09:40:04,657 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:04,664 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:04,665 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:04,665 - DEBUG - Finished Request
2025-07-02 09:40:05,665 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:05,670 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:05,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:05,671 - DEBUG - Finished Request
2025-07-02 09:40:06,672 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:06,679 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:06,680 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:06,680 - DEBUG - Finished Request
2025-07-02 09:40:07,682 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:07,687 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:07,688 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:07,688 - DEBUG - Finished Request
2025-07-02 09:40:08,689 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:08,697 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:08,697 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:08,697 - DEBUG - Finished Request
2025-07-02 09:40:09,698 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:09,705 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:09,705 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:09,705 - DEBUG - Finished Request
2025-07-02 09:40:10,706 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:10,714 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:10,714 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:10,714 - DEBUG - Finished Request
2025-07-02 09:40:11,716 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:11,723 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:11,724 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:11,724 - DEBUG - Finished Request
2025-07-02 09:40:12,726 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:12,733 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:12,733 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:12,733 - DEBUG - Finished Request
2025-07-02 09:40:13,735 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:13,743 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:13,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:13,743 - DEBUG - Finished Request
2025-07-02 09:40:14,744 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:14,750 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:14,750 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:14,750 - DEBUG - Finished Request
2025-07-02 09:40:15,751 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:15,758 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:15,758 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:15,758 - DEBUG - Finished Request
2025-07-02 09:40:16,759 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:16,764 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:16,764 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:16,764 - DEBUG - Finished Request
2025-07-02 09:40:17,765 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:17,772 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:17,772 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:17,772 - DEBUG - Finished Request
2025-07-02 09:40:18,773 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:18,778 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:18,778 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:18,778 - DEBUG - Finished Request
2025-07-02 09:40:19,779 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:19,784 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:19,784 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:19,785 - DEBUG - Finished Request
2025-07-02 09:40:20,785 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:20,795 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:20,796 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:20,796 - DEBUG - Finished Request
2025-07-02 09:40:21,797 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:21,804 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:21,804 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:21,804 - DEBUG - Finished Request
2025-07-02 09:40:22,805 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:22,811 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:22,811 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:22,812 - DEBUG - Finished Request
2025-07-02 09:40:23,813 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:23,818 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:23,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:23,818 - DEBUG - Finished Request
2025-07-02 09:40:24,819 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:24,825 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:24,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:24,826 - DEBUG - Finished Request
2025-07-02 09:40:25,827 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:25,832 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:25,833 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:25,833 - DEBUG - Finished Request
2025-07-02 09:40:26,834 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:26,839 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:26,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:26,839 - DEBUG - Finished Request
2025-07-02 09:40:27,840 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:27,846 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:27,846 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:27,846 - DEBUG - Finished Request
2025-07-02 09:40:28,847 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:28,853 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:28,853 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:28,854 - DEBUG - Finished Request
2025-07-02 09:40:29,855 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:29,862 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:29,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:29,862 - DEBUG - Finished Request
2025-07-02 09:40:30,863 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:30,869 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:30,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:30,870 - DEBUG - Finished Request
2025-07-02 09:40:31,871 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:31,876 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:31,876 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:31,876 - DEBUG - Finished Request
2025-07-02 09:40:32,878 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:32,885 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:32,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:32,885 - DEBUG - Finished Request
2025-07-02 09:40:33,887 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:33,894 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:33,894 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:33,894 - DEBUG - Finished Request
2025-07-02 09:40:34,894 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:34,901 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:34,902 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:34,902 - DEBUG - Finished Request
2025-07-02 09:40:35,903 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:35,910 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:35,911 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:35,911 - DEBUG - Finished Request
2025-07-02 09:40:36,912 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:36,918 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:36,918 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:36,919 - DEBUG - Finished Request
2025-07-02 09:40:37,919 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:37,926 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:37,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:37,927 - DEBUG - Finished Request
2025-07-02 09:40:38,928 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:38,935 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:38,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:38,935 - DEBUG - Finished Request
2025-07-02 09:40:39,936 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:39,942 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:39,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:39,943 - DEBUG - Finished Request
2025-07-02 09:40:40,943 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:40,950 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:40,950 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:40,950 - DEBUG - Finished Request
2025-07-02 09:40:41,952 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:41,959 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:41,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:41,959 - DEBUG - Finished Request
2025-07-02 09:40:42,960 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:42,967 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:42,967 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:42,968 - DEBUG - Finished Request
2025-07-02 09:40:43,969 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:43,975 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:43,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:43,976 - DEBUG - Finished Request
2025-07-02 09:40:44,977 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:44,985 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:44,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:44,985 - DEBUG - Finished Request
2025-07-02 09:40:45,986 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:45,993 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:45,994 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:45,994 - DEBUG - Finished Request
2025-07-02 09:40:46,994 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:46,999 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:46,999 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:47,000 - DEBUG - Finished Request
2025-07-02 09:40:48,001 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:48,006 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:48,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:48,006 - DEBUG - Finished Request
2025-07-02 09:40:49,007 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:49,014 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:49,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:49,015 - DEBUG - Finished Request
2025-07-02 09:40:50,015 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:50,047 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 200 0
2025-07-02 09:40:50,047 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:50,047 - DEBUG - Finished Request
2025-07-02 09:40:51,048 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:51,049 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:51,049 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:51,050 - DEBUG - Finished Request
2025-07-02 09:40:52,052 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:52,053 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:52,053 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:52,053 - DEBUG - Finished Request
2025-07-02 09:40:53,054 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:53,056 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:53,056 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:53,056 - DEBUG - Finished Request
2025-07-02 09:40:54,058 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:54,060 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:54,060 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:54,060 - DEBUG - Finished Request
2025-07-02 09:40:55,062 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:55,063 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:55,064 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:55,064 - DEBUG - Finished Request
2025-07-02 09:40:56,066 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:56,067 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:56,067 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:56,068 - DEBUG - Finished Request
2025-07-02 09:40:57,069 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:57,071 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:57,071 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:57,071 - DEBUG - Finished Request
2025-07-02 09:40:58,073 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:58,075 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:58,075 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:58,075 - DEBUG - Finished Request
2025-07-02 09:40:59,076 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:40:59,077 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:40:59,078 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:40:59,078 - DEBUG - Finished Request
2025-07-02 09:41:00,079 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:00,080 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:00,080 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:00,080 - DEBUG - Finished Request
2025-07-02 09:41:01,082 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:01,083 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:01,083 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:01,084 - DEBUG - Finished Request
2025-07-02 09:41:02,085 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:02,086 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:02,086 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:02,086 - DEBUG - Finished Request
2025-07-02 09:41:03,087 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:03,089 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:03,089 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:03,089 - DEBUG - Finished Request
2025-07-02 09:41:04,091 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:04,092 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:04,092 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:04,093 - DEBUG - Finished Request
2025-07-02 09:41:05,094 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:05,095 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:05,095 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:05,096 - DEBUG - Finished Request
2025-07-02 09:41:06,097 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:06,098 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:06,098 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:06,099 - DEBUG - Finished Request
2025-07-02 09:41:07,101 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:07,101 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:07,102 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:07,102 - DEBUG - Finished Request
2025-07-02 09:41:08,103 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:08,103 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:08,104 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:08,104 - DEBUG - Finished Request
2025-07-02 09:41:09,106 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:09,107 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:09,108 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:09,108 - DEBUG - Finished Request
2025-07-02 09:41:10,109 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:10,110 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:10,110 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:10,110 - DEBUG - Finished Request
2025-07-02 09:41:11,112 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:11,113 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:11,114 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:11,114 - DEBUG - Finished Request
2025-07-02 09:41:12,115 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:12,116 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:12,117 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:12,117 - DEBUG - Finished Request
2025-07-02 09:41:13,119 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:13,120 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:13,121 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:13,121 - DEBUG - Finished Request
2025-07-02 09:41:14,122 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:14,124 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:14,124 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:14,125 - DEBUG - Finished Request
2025-07-02 09:41:15,125 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:15,127 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:15,127 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:15,128 - DEBUG - Finished Request
2025-07-02 09:41:16,129 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:16,131 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:16,131 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:16,132 - DEBUG - Finished Request
2025-07-02 09:41:17,134 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:17,135 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:17,135 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:17,135 - DEBUG - Finished Request
2025-07-02 09:41:18,138 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:18,139 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:18,139 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:18,140 - DEBUG - Finished Request
2025-07-02 09:41:19,142 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:19,143 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:19,144 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:19,144 - DEBUG - Finished Request
2025-07-02 09:41:20,146 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:20,148 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:20,148 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:20,149 - DEBUG - Finished Request
2025-07-02 09:41:21,150 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:21,152 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:21,152 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:21,152 - DEBUG - Finished Request
2025-07-02 09:41:22,154 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:22,156 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:22,156 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:22,157 - DEBUG - Finished Request
2025-07-02 09:41:23,158 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:23,159 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:23,160 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:23,160 - DEBUG - Finished Request
2025-07-02 09:41:24,162 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:24,164 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:24,165 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:24,165 - DEBUG - Finished Request
2025-07-02 09:41:25,166 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:25,167 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:25,168 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:25,168 - DEBUG - Finished Request
2025-07-02 09:41:26,170 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:26,172 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:26,172 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:26,173 - DEBUG - Finished Request
2025-07-02 09:41:27,175 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:27,176 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:27,177 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:27,177 - DEBUG - Finished Request
2025-07-02 09:41:28,178 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:28,180 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:28,181 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:28,181 - DEBUG - Finished Request
2025-07-02 09:41:29,183 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:29,185 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:29,185 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:29,186 - DEBUG - Finished Request
2025-07-02 09:41:30,187 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:30,188 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:30,189 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:30,189 - DEBUG - Finished Request
2025-07-02 09:41:31,191 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:31,193 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:31,193 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:31,194 - DEBUG - Finished Request
2025-07-02 09:41:32,195 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:32,196 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:32,196 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:32,197 - DEBUG - Finished Request
2025-07-02 09:41:33,199 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:33,200 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:33,201 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:33,201 - DEBUG - Finished Request
2025-07-02 09:41:34,202 - DEBUG - GET http://localhost:54318/session/0ed66fd9f5678ca627ab557bc85ad9ae/url {}
2025-07-02 09:41:34,204 - DEBUG - http://localhost:54318 "GET /session/0ed66fd9f5678ca627ab557bc85ad9ae/url HTTP/1.1" 404 0
2025-07-02 09:41:34,204 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such window","message":"no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668912191]\n\t(No symbol) [0x0x7ff6689bf83e]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '986', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:41:34,205 - DEBUG - Finished Request
