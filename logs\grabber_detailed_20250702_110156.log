2025-07-02 11:01:56,010 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_110156.log
2025-07-02 11:02:10,952 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 11:02:10,952 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 11:02:11,005 - DEBUG - chromedriver not found in PATH
2025-07-02 11:02:11,006 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 11:02:11,006 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 11:02:11,006 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 11:02:11,006 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 11:02:11,006 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 11:02:11,006 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 11:02:11,010 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 5440 using 0 to output -3
2025-07-02 11:02:11,529 - DEBUG - POST http://localhost:51989/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 11:02:11,530 - DEBUG - Starting new HTTP connection (1): localhost:51989
2025-07-02 11:02:12,134 - DEBUG - http://localhost:51989 "POST /session HTTP/1.1" 200 0
2025-07-02 11:02:12,134 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir5440_1056370061"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51992"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"e3c272a2a110a4202a957c3a89fbd02d"}} | headers=HTTPHeaderDict({'Content-Length': '881', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 11:02:12,134 - DEBUG - Finished Request
2025-07-02 11:02:12,135 - DEBUG - POST http://localhost:51989/session/e3c272a2a110a4202a957c3a89fbd02d/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 11:02:13,166 - DEBUG - http://localhost:51989 "POST /session/e3c272a2a110a4202a957c3a89fbd02d/url HTTP/1.1" 200 0
2025-07-02 11:02:13,167 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 11:02:13,167 - DEBUG - Finished Request
2025-07-02 11:02:13,168 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 11:02:13,168 - DEBUG - POST http://localhost:51989/session/e3c272a2a110a4202a957c3a89fbd02d/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 11:02:13,176 - DEBUG - http://localhost:51989 "POST /session/e3c272a2a110a4202a957c3a89fbd02d/execute/sync HTTP/1.1" 200 0
2025-07-02 11:02:13,177 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 11:02:13,177 - DEBUG - Finished Request
2025-07-02 11:02:13,177 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-02 11:02:54,991 - INFO - 用戶點擊 [✅ 準備完成]
