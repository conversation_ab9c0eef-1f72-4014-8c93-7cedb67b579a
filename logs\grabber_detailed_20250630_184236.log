2025-06-30 18:42:36,324 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_184236.log
2025-06-30 18:42:55,493 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 18:42:55,493 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 18:42:55,560 - DEBUG - chromedriver not found in PATH
2025-06-30 18:42:55,560 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:42:55,560 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 18:42:55,560 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 18:42:55,561 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 18:42:55,561 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 18:42:55,561 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:42:55,565 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 17860 using 0 to output -3
2025-06-30 18:42:56,073 - DEBUG - POST http://localhost:53667/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 18:42:56,074 - DEBUG - Starting new HTTP connection (1): localhost:53667
2025-06-30 18:42:56,617 - DEBUG - http://localhost:53667 "POST /session HTTP/1.1" 200 0
2025-06-30 18:42:56,617 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir17860_556221753"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53671"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"50320e1c858c359f7c64817b0fcbabcf"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:56,617 - DEBUG - Finished Request
2025-06-30 18:42:56,618 - DEBUG - POST http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 18:42:57,524 - DEBUG - http://localhost:53667 "POST /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:42:57,525 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:57,525 - DEBUG - Finished Request
2025-06-30 18:42:57,525 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 18:42:57,526 - DEBUG - POST http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 18:42:57,536 - DEBUG - http://localhost:53667 "POST /session/50320e1c858c359f7c64817b0fcbabcf/execute/sync HTTP/1.1" 200 0
2025-06-30 18:42:57,536 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:57,536 - DEBUG - Finished Request
2025-06-30 18:42:57,537 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 18:42:57,537 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:42:57,575 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:42:57,576 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:57,576 - DEBUG - Finished Request
2025-06-30 18:42:58,577 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:42:58,587 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:42:58,588 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:58,588 - DEBUG - Finished Request
2025-06-30 18:42:59,588 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:42:59,594 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:42:59,594 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:59,594 - DEBUG - Finished Request
2025-06-30 18:43:00,595 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:00,602 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:00,602 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:00,602 - DEBUG - Finished Request
2025-06-30 18:43:01,603 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:01,609 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:01,609 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:01,609 - DEBUG - Finished Request
2025-06-30 18:43:02,611 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:02,618 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:02,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:02,618 - DEBUG - Finished Request
2025-06-30 18:43:03,618 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:03,626 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:03,626 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:03,626 - DEBUG - Finished Request
2025-06-30 18:43:04,627 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:04,634 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:04,634 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:04,634 - DEBUG - Finished Request
2025-06-30 18:43:05,635 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:05,643 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:05,643 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:05,644 - DEBUG - Finished Request
2025-06-30 18:43:06,645 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:06,650 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:06,651 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:06,651 - DEBUG - Finished Request
2025-06-30 18:43:07,652 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:07,660 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:07,660 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:07,660 - DEBUG - Finished Request
2025-06-30 18:43:08,662 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:08,669 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:08,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:08,669 - DEBUG - Finished Request
2025-06-30 18:43:09,670 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:09,679 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:09,679 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:09,680 - DEBUG - Finished Request
2025-06-30 18:43:10,680 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:10,686 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:10,687 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:10,687 - DEBUG - Finished Request
2025-06-30 18:43:11,688 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:11,696 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:11,696 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:11,696 - DEBUG - Finished Request
2025-06-30 18:43:12,697 - DEBUG - GET http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf/url {}
2025-06-30 18:43:12,704 - DEBUG - http://localhost:53667 "GET /session/50320e1c858c359f7c64817b0fcbabcf/url HTTP/1.1" 200 0
2025-06-30 18:43:12,704 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:12,704 - DEBUG - Finished Request
2025-06-30 18:43:13,026 - DEBUG - DELETE http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf {}
2025-06-30 18:43:13,076 - DEBUG - http://localhost:53667 "DELETE /session/50320e1c858c359f7c64817b0fcbabcf HTTP/1.1" 200 0
2025-06-30 18:43:13,076 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:43:13,076 - DEBUG - Finished Request
2025-06-30 18:43:13,717 - DEBUG - DELETE http://localhost:53667/session/50320e1c858c359f7c64817b0fcbabcf {}
2025-06-30 18:43:13,718 - DEBUG - Starting new HTTP connection (1): localhost:53667
