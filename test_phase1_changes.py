#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Phase 1 的修改
驗證 GUI#09 和模式選擇功能
"""

import sys
import os

def test_imports():
    """測試導入是否正常"""
    print("🔍 測試導入...")
    try:
        # 強制清理模塊緩存
        import sys
        modules_to_remove = [k for k in sys.modules.keys() if k.startswith('mvp_grabber')]
        for module in modules_to_remove:
            del sys.modules[module]

        import mvp_grabber
        print("✅ mvp_grabber 導入成功")
        
        # 檢查版本
        version = getattr(mvp_grabber, '__VERSION__', 'Unknown')
        print(f"📋 版本: {version}")
        
        # 檢查新的全局變量
        if hasattr(mvp_grabber, 'current_execution_mode'):
            print(f"✅ current_execution_mode: {mvp_grabber.current_execution_mode}")
        else:
            print("❌ current_execution_mode 變量缺失")
        
        return True
    except Exception as e:
        print(f"❌ 導入失敗: {e}")
        return False

def test_new_functions():
    """測試新函數是否存在"""
    print("\n🔍 測試新函數...")
    try:
        import mvp_grabber
        
        # 檢查新函數
        new_functions = [
            'show_verification_reminder_gui',
            'click_cancel_button'
        ]
        
        for func_name in new_functions:
            if hasattr(mvp_grabber, func_name):
                print(f"✅ {func_name} 函數存在")
            else:
                print(f"❌ {func_name} 函數缺失")
        
        # 檢查修改的函數
        if hasattr(mvp_grabber, 'click_submit_button'):
            print("✅ click_submit_button 函數存在（已修改）")
        else:
            print("❌ click_submit_button 函數缺失")
        
        return True
    except Exception as e:
        print(f"❌ 函數檢查失敗: {e}")
        return False

def test_function_signatures():
    """測試函數簽名"""
    print("\n🔍 測試函數簽名...")
    try:
        import mvp_grabber
        import inspect
        
        # 檢查 show_verification_reminder_gui 函數
        if hasattr(mvp_grabber, 'show_verification_reminder_gui'):
            func = getattr(mvp_grabber, 'show_verification_reminder_gui')
            sig = inspect.signature(func)
            print(f"✅ show_verification_reminder_gui 簽名: {sig}")
        
        # 檢查 click_cancel_button 函數
        if hasattr(mvp_grabber, 'click_cancel_button'):
            func = getattr(mvp_grabber, 'click_cancel_button')
            sig = inspect.signature(func)
            print(f"✅ click_cancel_button 簽名: {sig}")
        
        return True
    except Exception as e:
        print(f"❌ 簽名檢查失敗: {e}")
        return False

def test_execute_single_order_grab_changes():
    """測試 execute_single_order_grab 的修改"""
    print("\n🔍 測試 execute_single_order_grab 修改...")
    try:
        import mvp_grabber
        import inspect

        # 獲取函數源碼
        source = inspect.getsource(mvp_grabber.execute_single_order_grab)

        # 檢查是否包含新的調用
        if 'show_verification_reminder_gui' in source:
            print("✅ execute_single_order_grab 包含 show_verification_reminder_gui 調用")
        else:
            print("❌ execute_single_order_grab 缺少 show_verification_reminder_gui 調用")

        # 檢查是否移除了舊的調用
        if 'handle_captcha_input()' not in source:
            print("✅ execute_single_order_grab 已移除 handle_captcha_input 調用")
        else:
            print("⚠️ execute_single_order_grab 仍包含 handle_captcha_input 調用")

        # 檢查模式設定
        if 'current_execution_mode' in source:
            print("✅ execute_single_order_grab 包含模式設定邏輯")
        else:
            print("❌ execute_single_order_grab 缺少模式設定邏輯")

        return True
    except Exception as e:
        print(f"❌ 源碼檢查失敗: {e}")
        return False

def test_auto_trigger_mechanism():
    """測試自動觸發機制"""
    print("\n🔍 測試自動觸發機制...")
    try:
        import mvp_grabber
        import inspect

        # 檢查 monitor_dialog_opening 函數（實際的函數名）
        if hasattr(mvp_grabber, 'monitor_dialog_opening'):
            source = inspect.getsource(mvp_grabber.monitor_dialog_opening)

            if 'show_verification_reminder_gui' in source:
                print("✅ monitor_dialog_opening 包含自動觸發邏輯")
            else:
                print("❌ monitor_dialog_opening 缺少自動觸發邏輯")

            if 'threading' in source:
                print("✅ 使用線程避免阻塞")
            else:
                print("❌ 缺少線程處理")
        else:
            print("❌ monitor_dialog_opening 函數不存在")

        return True
    except Exception as e:
        print(f"❌ 自動觸發測試失敗: {e}")
        return False

def test_duplicate_trigger_fix():
    """測試重複觸發修復"""
    print("\n🔍 測試重複觸發修復...")
    try:
        import mvp_grabber
        import inspect

        # 檢查全局變量
        if hasattr(mvp_grabber, 'gui_operation_completed'):
            print("✅ 包含操作完成標記變量")
        else:
            print("❌ 缺少操作完成標記變量")

        # 檢查 execute_single_order_grab 是否移除了重複調用
        if hasattr(mvp_grabber, 'execute_single_order_grab'):
            source = inspect.getsource(mvp_grabber.execute_single_order_grab)

            # 檢查是否包含等待邏輯而非直接調用
            if 'gui_operation_completed' in source and 'wait' in source.lower():
                print("✅ execute_single_order_grab 使用等待機制而非重複調用")
            else:
                print("❌ execute_single_order_grab 可能仍有重複調用問題")

        return True
    except Exception as e:
        print(f"❌ 重複觸發測試失敗: {e}")
        return False

def test_gui_parameter_support():
    """測試 GUI 參數支持"""
    print("\n🔍 測試 GUI 參數支持...")
    try:
        import mvp_grabber
        import inspect

        # 檢查 show_verification_reminder_gui 函數簽名
        if hasattr(mvp_grabber, 'show_verification_reminder_gui'):
            func = getattr(mvp_grabber, 'show_verification_reminder_gui')
            sig = inspect.signature(func)

            if 'detection_result' in str(sig):
                print("✅ show_verification_reminder_gui 支持 detection_result 參數")
            else:
                print("❌ show_verification_reminder_gui 缺少 detection_result 參數")

            # 檢查函數內容
            source = inspect.getsource(func)
            if 'if detection_result is None' in source:
                print("✅ 包含參數檢查邏輯")
            else:
                print("❌ 缺少參數檢查邏輯")
        else:
            print("❌ show_verification_reminder_gui 函數不存在")

        return True
    except Exception as e:
        print(f"❌ GUI 參數測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("="*60)
    print("AGES-KH-Bot v1.5.0 Phase 1 測試")
    print("="*60)
    
    tests = [
        test_imports,
        test_new_functions,
        test_function_signatures,
        test_execute_single_order_grab_changes,
        test_auto_trigger_mechanism,
        test_duplicate_trigger_fix,
        test_gui_parameter_support
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 測試異常: {e}")
    
    print("\n" + "="*60)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！Phase 1 修改成功")
        return True
    else:
        print("⚠️ 部分測試失敗，需要檢查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
