2025-07-01 16:07:24,625 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_160724.log
2025-07-01 16:08:48,978 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:08:48,978 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:08:49,036 - DEBUG - chromedriver not found in PATH
2025-07-01 16:08:49,036 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:08:49,036 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:08:49,037 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:08:49,037 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:08:49,037 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:08:49,037 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:08:49,043 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 22556 using 0 to output -3
2025-07-01 16:08:49,566 - DEBUG - POST http://localhost:54925/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:08:49,567 - DEBUG - Starting new HTTP connection (1): localhost:54925
2025-07-01 16:08:50,109 - DEBUG - http://localhost:54925 "POST /session HTTP/1.1" 200 0
2025-07-01 16:08:50,110 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir22556_929575126"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54928"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"cf0cdf9ee42d4835f6ee6cc3788515cb"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:50,110 - DEBUG - Finished Request
2025-07-01 16:08:50,112 - DEBUG - POST http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:08:51,474 - DEBUG - http://localhost:54925 "POST /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:51,475 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:51,475 - DEBUG - Finished Request
2025-07-01 16:08:51,475 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:08:51,475 - DEBUG - POST http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:08:51,483 - DEBUG - http://localhost:54925 "POST /session/cf0cdf9ee42d4835f6ee6cc3788515cb/execute/sync HTTP/1.1" 200 0
2025-07-01 16:08:51,483 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:51,483 - DEBUG - Finished Request
2025-07-01 16:08:51,483 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:08:51,485 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:51,541 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:51,542 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:51,542 - DEBUG - Finished Request
2025-07-01 16:08:52,543 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:52,550 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:52,550 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:52,550 - DEBUG - Finished Request
2025-07-01 16:08:53,551 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:53,559 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:53,560 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:53,560 - DEBUG - Finished Request
2025-07-01 16:08:54,561 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:54,568 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:54,568 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:54,569 - DEBUG - Finished Request
2025-07-01 16:08:55,569 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:55,575 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:55,575 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:55,576 - DEBUG - Finished Request
2025-07-01 16:08:56,577 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:56,584 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:56,584 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:56,584 - DEBUG - Finished Request
2025-07-01 16:08:57,585 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:57,593 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:57,593 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:57,594 - DEBUG - Finished Request
2025-07-01 16:08:58,595 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:58,601 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:58,601 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:58,601 - DEBUG - Finished Request
2025-07-01 16:08:59,603 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:08:59,611 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:08:59,611 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:08:59,612 - DEBUG - Finished Request
2025-07-01 16:09:00,613 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:00,620 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:00,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:00,621 - DEBUG - Finished Request
2025-07-01 16:09:01,622 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:01,633 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:01,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:01,634 - DEBUG - Finished Request
2025-07-01 16:09:02,634 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:02,642 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:02,642 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:02,643 - DEBUG - Finished Request
2025-07-01 16:09:03,643 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:03,652 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:03,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:03,653 - DEBUG - Finished Request
2025-07-01 16:09:04,654 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:04,662 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:04,662 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:04,662 - DEBUG - Finished Request
2025-07-01 16:09:05,664 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:05,673 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:05,673 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:05,673 - DEBUG - Finished Request
2025-07-01 16:09:06,675 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:06,682 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:06,683 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:06,683 - DEBUG - Finished Request
2025-07-01 16:09:07,684 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:07,694 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:07,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:07,695 - DEBUG - Finished Request
2025-07-01 16:09:08,696 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:08,702 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:08,702 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:08,703 - DEBUG - Finished Request
2025-07-01 16:09:09,704 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:09,710 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:09,710 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:09,712 - DEBUG - Finished Request
2025-07-01 16:09:10,713 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:10,720 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:10,721 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:10,721 - DEBUG - Finished Request
2025-07-01 16:09:11,722 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:11,730 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:11,731 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:11,731 - DEBUG - Finished Request
2025-07-01 16:09:12,732 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:12,739 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:12,740 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:12,740 - DEBUG - Finished Request
2025-07-01 16:09:13,741 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:13,753 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:13,753 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:13,754 - DEBUG - Finished Request
2025-07-01 16:09:14,754 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:14,763 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:14,763 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:14,764 - DEBUG - Finished Request
2025-07-01 16:09:15,765 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:15,773 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:15,773 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:15,773 - DEBUG - Finished Request
2025-07-01 16:09:16,775 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:16,784 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:16,785 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:16,785 - DEBUG - Finished Request
2025-07-01 16:09:17,786 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:17,797 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:17,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:17,798 - DEBUG - Finished Request
2025-07-01 16:09:18,800 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:18,808 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:18,809 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:18,809 - DEBUG - Finished Request
2025-07-01 16:09:19,811 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:19,820 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:19,820 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:19,821 - DEBUG - Finished Request
2025-07-01 16:09:20,822 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:20,831 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:20,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:20,832 - DEBUG - Finished Request
2025-07-01 16:09:21,833 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:21,841 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:21,842 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:21,842 - DEBUG - Finished Request
2025-07-01 16:09:22,843 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:22,851 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:22,852 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:22,852 - DEBUG - Finished Request
2025-07-01 16:09:23,854 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:23,861 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:23,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:23,862 - DEBUG - Finished Request
2025-07-01 16:09:24,863 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:24,871 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:24,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:24,872 - DEBUG - Finished Request
2025-07-01 16:09:25,872 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:25,881 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:25,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:25,882 - DEBUG - Finished Request
2025-07-01 16:09:26,883 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:26,889 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:26,890 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:26,890 - DEBUG - Finished Request
2025-07-01 16:09:27,891 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:27,900 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:27,900 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:27,901 - DEBUG - Finished Request
2025-07-01 16:09:28,901 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:28,908 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:28,910 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:28,910 - DEBUG - Finished Request
2025-07-01 16:09:29,911 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:29,918 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:29,918 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:29,919 - DEBUG - Finished Request
2025-07-01 16:09:30,920 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:30,927 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:30,927 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:30,928 - DEBUG - Finished Request
2025-07-01 16:09:31,929 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:31,936 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:31,937 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:31,937 - DEBUG - Finished Request
2025-07-01 16:09:32,938 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:32,947 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:32,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:32,948 - DEBUG - Finished Request
2025-07-01 16:09:33,949 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:33,955 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 200 0
2025-07-01 16:09:33,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:33,955 - DEBUG - Finished Request
2025-07-01 16:09:34,944 - DEBUG - DELETE http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb {}
2025-07-01 16:09:34,957 - DEBUG - GET http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb/url {}
2025-07-01 16:09:34,958 - DEBUG - Starting new HTTP connection (2): localhost:54925
2025-07-01 16:09:34,984 - DEBUG - http://localhost:54925 "DELETE /session/cf0cdf9ee42d4835f6ee6cc3788515cb HTTP/1.1" 200 0
2025-07-01 16:09:34,984 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:34,985 - DEBUG - Finished Request
2025-07-01 16:09:35,015 - DEBUG - http://localhost:54925 "GET /session/cf0cdf9ee42d4835f6ee6cc3788515cb/url HTTP/1.1" 404 0
2025-07-01 16:09:35,016 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 16:09:35,017 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b99fc]\n\t(No symbol) [0x0x7ff6918007df]\n\t(No symbol) [0x0x7ff691838a52]\n\t(No symbol) [0x0x7ff691833413]\n\t(No symbol) [0x0x7ff6918324d9]\n\t(No symbol) [0x0x7ff691785d55]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\t(No symbol) [0x0x7ff691784dca]\n\tGetHandleVerifier [0x0x7ff691df45e8+4238440]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:09:35,018 - DEBUG - Finished Request
2025-07-01 16:09:35,019 - DEBUG - DELETE http://localhost:54925/session/cf0cdf9ee42d4835f6ee6cc3788515cb {}
2025-07-01 16:09:35,019 - DEBUG - Starting new HTTP connection (1): localhost:54925
