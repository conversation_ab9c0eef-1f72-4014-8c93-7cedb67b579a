2025-07-01 09:50:55,765 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_095055.log
2025-07-01 09:51:13,840 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 09:51:13,840 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 09:51:13,907 - DEBUG - chromedriver not found in PATH
2025-07-01 09:51:13,907 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 09:51:13,907 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 09:51:13,907 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 09:51:13,907 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 09:51:13,907 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 09:51:13,907 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 09:51:13,911 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 11424 using 0 to output -3
2025-07-01 09:51:14,436 - DEBUG - POST http://localhost:51327/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 09:51:14,438 - DEBUG - Starting new HTTP connection (1): localhost:51327
2025-07-01 09:51:14,984 - DEBUG - http://localhost:51327 "POST /session HTTP/1.1" 200 0
2025-07-01 09:51:14,985 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir11424_324606998"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51330"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"6125b69970a3f7d67ab2cf6f762b9cf4"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:14,985 - DEBUG - Finished Request
2025-07-01 09:51:14,986 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 09:51:16,055 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:16,055 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:16,055 - DEBUG - Finished Request
2025-07-01 09:51:16,056 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 09:51:16,056 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 09:51:16,076 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync HTTP/1.1" 200 0
2025-07-01 09:51:16,077 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:16,077 - DEBUG - Finished Request
2025-07-01 09:51:16,077 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 09:51:16,077 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:16,107 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:16,108 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:16,108 - DEBUG - Finished Request
2025-07-01 09:51:17,109 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:17,119 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:17,119 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:17,119 - DEBUG - Finished Request
2025-07-01 09:51:18,119 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:18,124 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:18,125 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:18,125 - DEBUG - Finished Request
2025-07-01 09:51:19,126 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:19,132 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:19,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:19,133 - DEBUG - Finished Request
2025-07-01 09:51:20,135 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:20,140 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:20,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:20,141 - DEBUG - Finished Request
2025-07-01 09:51:21,142 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:21,148 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:21,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:21,148 - DEBUG - Finished Request
2025-07-01 09:51:22,149 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:22,156 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:22,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:22,157 - DEBUG - Finished Request
2025-07-01 09:51:23,158 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:23,168 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:23,168 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:23,168 - DEBUG - Finished Request
2025-07-01 09:51:24,170 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:24,177 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:24,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:24,178 - DEBUG - Finished Request
2025-07-01 09:51:25,178 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:25,186 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:25,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:25,187 - DEBUG - Finished Request
2025-07-01 09:51:26,188 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:26,195 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:26,195 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:26,196 - DEBUG - Finished Request
2025-07-01 09:51:27,197 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:27,204 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:27,204 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:27,204 - DEBUG - Finished Request
2025-07-01 09:51:28,206 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:28,213 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:28,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:28,214 - DEBUG - Finished Request
2025-07-01 09:51:29,215 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:29,224 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:29,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:29,224 - DEBUG - Finished Request
2025-07-01 09:51:30,225 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:30,233 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:30,233 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:30,233 - DEBUG - Finished Request
2025-07-01 09:51:31,234 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:31,241 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:31,241 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:31,242 - DEBUG - Finished Request
2025-07-01 09:51:32,243 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:32,249 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:32,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:32,250 - DEBUG - Finished Request
2025-07-01 09:51:33,251 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:33,258 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:33,258 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:33,258 - DEBUG - Finished Request
2025-07-01 09:51:34,259 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:34,266 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:34,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:34,266 - DEBUG - Finished Request
2025-07-01 09:51:35,267 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:35,274 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:35,274 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:35,274 - DEBUG - Finished Request
2025-07-01 09:51:36,275 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:36,282 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:36,282 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:36,283 - DEBUG - Finished Request
2025-07-01 09:51:37,284 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:37,291 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:37,292 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:37,292 - DEBUG - Finished Request
2025-07-01 09:51:38,293 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:38,300 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:38,300 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:38,300 - DEBUG - Finished Request
2025-07-01 09:51:39,301 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:39,307 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:39,308 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:39,308 - DEBUG - Finished Request
2025-07-01 09:51:40,309 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:40,314 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:40,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:40,314 - DEBUG - Finished Request
2025-07-01 09:51:41,316 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:41,322 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:41,323 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:41,323 - DEBUG - Finished Request
2025-07-01 09:51:42,324 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:42,330 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:42,330 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:42,330 - DEBUG - Finished Request
2025-07-01 09:51:43,331 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:43,337 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:43,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:43,338 - DEBUG - Finished Request
2025-07-01 09:51:44,339 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:44,345 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:44,345 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:44,346 - DEBUG - Finished Request
2025-07-01 09:51:45,346 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:45,353 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:45,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:45,353 - DEBUG - Finished Request
2025-07-01 09:51:46,354 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:46,361 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:46,361 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:46,361 - DEBUG - Finished Request
2025-07-01 09:51:47,362 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:47,368 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:47,368 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:47,368 - DEBUG - Finished Request
2025-07-01 09:51:48,369 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:48,374 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:48,375 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:48,375 - DEBUG - Finished Request
2025-07-01 09:51:49,376 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:49,382 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:49,382 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:49,383 - DEBUG - Finished Request
2025-07-01 09:51:50,384 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:50,390 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:50,390 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:50,390 - DEBUG - Finished Request
2025-07-01 09:51:51,390 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:51,397 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:51,397 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:51,397 - DEBUG - Finished Request
2025-07-01 09:51:52,398 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:52,404 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:52,404 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:52,404 - DEBUG - Finished Request
2025-07-01 09:51:53,405 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:53,411 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:53,411 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:53,411 - DEBUG - Finished Request
2025-07-01 09:51:54,413 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:54,418 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:54,419 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:54,419 - DEBUG - Finished Request
2025-07-01 09:51:55,420 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:55,427 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:55,427 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:55,427 - DEBUG - Finished Request
2025-07-01 09:51:56,428 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:56,434 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:56,435 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:56,435 - DEBUG - Finished Request
2025-07-01 09:51:57,436 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:57,442 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:57,442 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:57,442 - DEBUG - Finished Request
2025-07-01 09:51:58,444 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:58,450 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:58,450 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:58,450 - DEBUG - Finished Request
2025-07-01 09:51:59,450 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:51:59,455 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:51:59,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:51:59,456 - DEBUG - Finished Request
2025-07-01 09:52:00,457 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:00,464 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:00,464 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:00,464 - DEBUG - Finished Request
2025-07-01 09:52:01,465 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:01,471 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:01,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:01,471 - DEBUG - Finished Request
2025-07-01 09:52:02,472 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:02,478 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:02,478 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:02,478 - DEBUG - Finished Request
2025-07-01 09:52:03,479 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:03,487 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:03,487 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:03,487 - DEBUG - Finished Request
2025-07-01 09:52:04,489 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:04,496 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:04,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:04,496 - DEBUG - Finished Request
2025-07-01 09:52:05,497 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:05,503 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:05,503 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:05,503 - DEBUG - Finished Request
2025-07-01 09:52:06,505 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:06,512 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:06,512 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:06,512 - DEBUG - Finished Request
2025-07-01 09:52:07,513 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:07,519 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:07,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:07,519 - DEBUG - Finished Request
2025-07-01 09:52:08,521 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:08,527 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:08,527 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:08,527 - DEBUG - Finished Request
2025-07-01 09:52:09,528 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:09,535 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:09,535 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:09,535 - DEBUG - Finished Request
2025-07-01 09:52:10,536 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:10,542 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:10,543 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:10,543 - DEBUG - Finished Request
2025-07-01 09:52:11,544 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:12,265 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:12,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:12,266 - DEBUG - Finished Request
2025-07-01 09:52:13,267 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:13,275 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:13,275 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:13,275 - DEBUG - Finished Request
2025-07-01 09:52:14,276 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:14,281 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:14,281 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:14,282 - DEBUG - Finished Request
2025-07-01 09:52:15,283 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:15,289 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:15,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:15,289 - DEBUG - Finished Request
2025-07-01 09:52:16,290 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:16,295 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:16,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:16,296 - DEBUG - Finished Request
2025-07-01 09:52:17,297 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:17,303 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:17,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:17,303 - DEBUG - Finished Request
2025-07-01 09:52:18,304 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:18,311 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:18,312 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:18,312 - DEBUG - Finished Request
2025-07-01 09:52:19,312 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:19,319 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:19,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:19,320 - DEBUG - Finished Request
2025-07-01 09:52:20,321 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:20,326 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:20,327 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:20,327 - DEBUG - Finished Request
2025-07-01 09:52:21,328 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:21,335 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:21,335 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:21,335 - DEBUG - Finished Request
2025-07-01 09:52:22,336 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:22,342 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:22,342 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:22,342 - DEBUG - Finished Request
2025-07-01 09:52:23,343 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:23,350 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:23,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:23,351 - DEBUG - Finished Request
2025-07-01 09:52:24,351 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:24,358 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:24,359 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:24,359 - DEBUG - Finished Request
2025-07-01 09:52:25,360 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:25,366 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:25,366 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:25,367 - DEBUG - Finished Request
2025-07-01 09:52:26,368 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:26,373 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:26,373 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:26,374 - DEBUG - Finished Request
2025-07-01 09:52:27,375 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:27,381 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:27,382 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:27,382 - DEBUG - Finished Request
2025-07-01 09:52:28,382 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:28,388 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:28,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:28,388 - DEBUG - Finished Request
2025-07-01 09:52:29,389 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:29,393 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:29,393 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:29,394 - DEBUG - Finished Request
2025-07-01 09:52:30,395 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:30,401 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:30,401 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:30,401 - DEBUG - Finished Request
2025-07-01 09:52:31,402 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:31,407 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:31,407 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:31,407 - DEBUG - Finished Request
2025-07-01 09:52:32,408 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:32,414 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:32,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:32,414 - DEBUG - Finished Request
2025-07-01 09:52:33,415 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:33,421 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:33,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:33,422 - DEBUG - Finished Request
2025-07-01 09:52:34,001 - INFO - 🎯 用戶點擊準備完成按鈕，開始詳細檢測...
2025-07-01 09:52:34,002 - INFO - 🔍 [用戶點擊準備完成] 開始記錄頁面內容...
2025-07-01 09:52:34,002 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:34,021 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:34,021 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,022 - DEBUG - Finished Request
2025-07-01 09:52:34,022 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/title {}
2025-07-01 09:52:34,030 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/title HTTP/1.1" 200 0
2025-07-01 09:52:34,030 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,030 - DEBUG - Finished Request
2025-07-01 09:52:34,030 - INFO - 🔍 [用戶點擊準備完成] 當前 URL: https://wmc.kcg.gov.tw/
2025-07-01 09:52:34,030 - INFO - 🔍 [用戶點擊準備完成] 頁面標題: 高雄市廢棄物調度中心
2025-07-01 09:52:34,031 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/source {}
2025-07-01 09:52:34,035 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/source HTTP/1.1" 200 0
2025-07-01 09:52:34,035 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'x6cQ7uwbR50vuiSFEoPBf82HuQ0NEf3-0eOo8aHC3WXNBgUiShRXOvlrJdbF3ap0ZtoCTUJUp-B-X85l0qDsHkSC91gXbq-kd34T4XhSDi41:eX2Amg3ATUezCYy_fiPeAT_TOKaFRfC3Mu-C0Gbr0n8rJtSxLDQUrkBUILX1PXfjzWe2F7rWKduQpFsosAen5TFXxZAhDSAu9TFR107twn41' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 09:52:11\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95824338af5a4a68&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,036 - DEBUG - Finished Request
2025-07-01 09:52:34,036 - INFO - 🔍 [用戶點擊準備完成] page_source 長度: 8479
2025-07-01 09:52:34,036 - INFO - 🔍 [用戶點擊準備完成] page_source 包含 E48B: False
2025-07-01 09:52:34,036 - INFO - 🔍 [用戶點擊準備完成] page_source 包含目標訂單: False
2025-07-01 09:52:34,037 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-07-01 09:52:34,045 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync HTTP/1.1" 200 0
2025-07-01 09:52:34,045 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/07/01 09:52:11\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,045 - DEBUG - Finished Request
2025-07-01 09:52:34,045 - INFO - 🔍 [用戶點擊準備完成] innerText 長度: 97
2025-07-01 09:52:34,045 - INFO - 🔍 [用戶點擊準備完成] innerText 包含 E48B: False
2025-07-01 09:52:34,046 - INFO - 🔍 [用戶點擊準備完成] innerText 包含目標訂單: False
2025-07-01 09:52:34,046 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'tag name', 'value': 'table'}
2025-07-01 09:52:34,059 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,060 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,060 - DEBUG - Finished Request
2025-07-01 09:52:34,060 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'tag name', 'value': 'tr'}
2025-07-01 09:52:34,072 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,072 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,072 - DEBUG - Finished Request
2025-07-01 09:52:34,072 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個表格，0 個表格行
2025-07-01 09:52:34,073 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 09:52:34,085 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,085 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,085 - DEBUG - Finished Request
2025-07-01 09:52:34,085 - INFO - 🔍 [用戶點擊準備完成] 包含 'E48B' 的元素數量: 0
2025-07-01 09:52:34,085 - INFO - 🔍 [用戶點擊準備完成] innerText 前300字符:
2025-07-01 09:52:34,086 - INFO - 🔍 [用戶點擊準備完成]  環碩環保工程股份有限公司|郭炯宏 2025/07/01 09:52:11

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-07-01 09:52:34,086 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-07-01 09:52:34,125 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,125 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,125 - DEBUG - Finished Request
2025-07-01 09:52:34,125 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個編輯按鈕
2025-07-01 09:52:34,125 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'tag name', 'value': 'iframe'}
2025-07-01 09:52:34,136 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,136 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.ED396EA85A350F9C09EA4F60953BF000.d.762C6343DCD77E567E2A15DB6E1F8400.e.39"}]} | headers=HTTPHeaderDict({'Content-Length': '128', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,136 - DEBUG - Finished Request
2025-07-01 09:52:34,137 - INFO - 🔍 [用戶點擊準備完成] 檢測到 1 個 iframe
2025-07-01 09:52:34,137 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.ED396EA85A350F9C09EA4F60953BF000.d.762C6343DCD77E567E2A15DB6E1F8400.e.39'}, 'id']}
2025-07-01 09:52:34,146 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync HTTP/1.1" 200 0
2025-07-01 09:52:34,147 - DEBUG - Remote response: status=200 | data={"value":"frameid"} | headers=HTTPHeaderDict({'Content-Length': '19', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,147 - DEBUG - Finished Request
2025-07-01 09:52:34,147 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.ED396EA85A350F9C09EA4F60953BF000.d.762C6343DCD77E567E2A15DB6E1F8400.e.39'}, 'src']}
2025-07-01 09:52:34,156 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync HTTP/1.1" 200 0
2025-07-01 09:52:34,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00"} | headers=HTTPHeaderDict({'Content-Length': '58', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,156 - DEBUG - Finished Request
2025-07-01 09:52:34,156 - INFO - 🔍 [用戶點擊準備完成] iframe 1: id='frameid', src='https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00'
2025-07-01 09:52:34,157 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/frame {'id': {'element-6066-11e4-a52e-4f735466cecf': 'f.ED396EA85A350F9C09EA4F60953BF000.d.762C6343DCD77E567E2A15DB6E1F8400.e.39'}}
2025-07-01 09:52:34,184 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/frame HTTP/1.1" 200 0
2025-07-01 09:52:34,184 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,184 - DEBUG - Finished Request
2025-07-01 09:52:34,185 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-07-01 09:52:34,191 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/execute/sync HTTP/1.1" 200 0
2025-07-01 09:52:34,191 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 現在非2050專案預約時間，預約開放時段：10:00~20:00\n\n7月\n8月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\n沒有符合的結果\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271670\t仁\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271669\t南\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271668\t仁\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271667\t南\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271666\t仁\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271665\t南\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271664\t仁\tKEP-2560\t一般清運\t6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406271663\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191221\t南\t119-BR\t一般清運\t3.6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191220\t岡\t119-BR\t一般清運\t3.6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191219\t南\t121-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191218\t岡\t121-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191217\t南\tKEH-9230\t一般清運\t3.7\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191207\t南\t120-BR\t一般清運\t3.2\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191206\t仁\t120-BR\t一般清運\t3.2\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406191204\t岡\t120-BR\t一般清運\t3.2\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121161\t仁\t937-N6\t一般清運\t4.9\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121160\t南\t937-N6\t一般清運\t4.9\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121157\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121156\t仁\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121155\t南\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121153\t南\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121152\t仁\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121151\t仁\tKED-9670\t一般清運\t5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121149\t仁\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121148\t南\tKEJ-5580\t一般清運\t7\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121146\t南\t121-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121145\t仁\t121-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121143\t岡\t121-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121142\t岡\t121-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121140\t仁\t121-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406121137\t南\t121-BR\t一般清運\t3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061576\t岡\t117-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061575\t南\t117-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061574\t南\t937-N6\t一般清運\t4.9\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061573\t仁\t937-N6\t一般清運\t4.9\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061572\t岡\t937-N6\t一般清運\t4.9\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061571\t南\t937-N6\t一般清運\t4.9\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061567\t岡\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406061566\t南\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270668\t岡\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270664\t南\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270660\t岡\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270659\t仁\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270655\t仁\tKEP-2808\t一般清運\t2\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405270654\t仁\t129-BR\t一般清運\t4.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405270652\t岡\tKEP-2560\t一般清運\t6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405260922\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405260920\t南\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405260918\t仁\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405250276\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200980\t岡\tKEH-9230\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200979\t仁\tKEH-9230\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200978\t南\tKEH-9230\t一般清運\t3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200977\t南\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200976\t仁\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200975\t南\tKEB-6030\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200974\t岡\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200973\t仁\tKEB-6030\t一般清運\t6.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200972\t南\tKEB-6030\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200971\t岡\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200970\t仁\tKED-9670\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405200969\t南\tKED-9670\t一般清運\t5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150349\t仁\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150344\t岡\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150341\t岡\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150339\t仁\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150336\t南\tKED-9671\t一般清運\t5.8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150333\t南\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405150331\t仁\tKEJ-5580\t一般清運\t8\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405121110\t南\t117-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405121109\t仁\t117-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405121106\t岡\t117-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020876\t南\t119-BR\t一般清運\t3.5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020875\t仁\t119-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020874\t岡\t119-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020872\t岡\t119-BR\t一般清運\t3.5\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405020871\t仁\t119-BR\t一般清運\t3.5\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404220584\t南\t119-BR\t一般清運\t3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404220582\t南\t120-BR\t一般清運\t3.2\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404150151\t仁\t120-BR\t一般清運\t3.2\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611404150148\t岡\t120-BR\t一般清運\t3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311318\t南\t129-BR\t一般清運\t4.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311308\t南\t129-BR\t一般清運\t4.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311301\t岡\t129-BR\t一般清運\t4.3\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311297\t南\t129-BR\t一般清運\t4.3\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611403311260\t仁\t129-BR\t一般清運\t4.3\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611402171100\t南\t129-BR\t一般清運\t4\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402171091\t仁\tKEP-2560\t一般清運\t6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402101032\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060832\t南\tKEP-2560\t一般清運\t5\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060792\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060773\t南\tKEP-2560\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060772\t岡\tKEP-2560\t一般清運\t6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060764\t南\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060756\t仁\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060755\t岡\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060731\t岡\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060730\t仁\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060729\t南\tKEH-9278\t一般清運\t8.6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060728\t南\tKER-2807\t一般清運\t7\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060727\t仁\tKER-2807\t一般清運\t7\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060725\t岡\tKER-2807\t一般清運\t6\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060724\t岡\tKER-2807\t一般清運\t7\t2025-07-08\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060723\t仁\tKER-2807\t一般清運\t7\t2025-07-08\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611402060722\t南\tKER-2807\t一般清運\t6\t2025-07-08\t\t高南廠\t0\t一般\t取消(刪除)\n顯示第 1 至 106 項結果，共 106 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '17529', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,192 - DEBUG - Finished Request
2025-07-01 09:52:34,193 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'tag name', 'value': 'table'}
2025-07-01 09:52:34,199 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,200 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.134"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,200 - DEBUG - Finished Request
2025-07-01 09:52:34,200 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'tag name', 'value': 'tr'}
2025-07-01 09:52:34,210 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,210 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.170"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.172"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.239"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.241"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.242"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.243"}]} | headers=HTTPHeaderDict({'Content-Length': '12873', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,211 - DEBUG - Finished Request
2025-07-01 09:52:34,211 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-07-01 09:52:34,229 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,230 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.277"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.278"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.279"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.280"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.281"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.282"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.283"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.284"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.285"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.286"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.287"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.288"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.289"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.290"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.291"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.292"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.293"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.294"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.295"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.296"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.297"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.298"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.299"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.300"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.301"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.302"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.303"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.304"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.305"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.306"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.307"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.308"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.309"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.310"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.311"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.312"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.313"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.314"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.315"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.316"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.317"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.318"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.319"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.320"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.321"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.322"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.323"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.324"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.325"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.326"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.327"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.328"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.329"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.330"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.331"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.332"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.333"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.334"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.335"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.336"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.337"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.338"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.339"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.340"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.341"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.342"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.343"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.344"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.345"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.346"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.347"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.348"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.349"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.350"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.351"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.352"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.353"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.354"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.355"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.356"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.357"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.358"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.359"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.360"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.361"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.362"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.363"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.364"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.365"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.366"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.367"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.368"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.369"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.370"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.371"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.372"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.373"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.374"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.375"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.376"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.377"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.378"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.379"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.380"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.381"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.382"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.383"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.384"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.385"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.386"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.387"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.388"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.389"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.390"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.391"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.392"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.393"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.394"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.395"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.396"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.397"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.398"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.399"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.400"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.401"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.402"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.403"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.404"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.405"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.406"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.407"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.408"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.409"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.410"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.411"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.412"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.413"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.414"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.415"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.416"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.417"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.418"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.419"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.420"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.421"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.422"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.423"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.424"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.425"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.426"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.427"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.428"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.429"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.430"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.431"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.432"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.433"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.434"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.435"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.436"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.437"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.438"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.439"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.440"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.441"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.442"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.443"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.444"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.445"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.446"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.447"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.448"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.449"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.450"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.451"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.452"}]} | headers=HTTPHeaderDict({'Content-Length': '24791', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,231 - DEBUG - Finished Request
2025-07-01 09:52:34,231 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 09:52:34,242 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:34,242 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.453"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.454"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.455"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.456"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.457"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.458"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.459"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.460"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.461"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.462"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.463"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.464"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.465"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.466"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.467"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.468"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.469"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.470"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.471"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.472"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.473"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.474"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.475"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.476"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.477"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.478"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.479"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.480"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.481"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.482"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.483"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.484"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.485"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.486"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.487"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.488"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.489"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.490"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.491"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.492"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.493"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.494"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.495"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.496"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.497"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.498"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.499"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.500"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.501"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.502"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.503"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.504"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.505"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.506"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.507"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.508"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.509"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.510"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.511"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.512"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.513"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.514"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.515"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.516"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.517"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.518"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.519"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.520"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.521"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.522"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.523"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.524"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.525"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.526"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.527"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.528"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.529"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.530"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.531"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.532"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.533"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.534"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.535"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.536"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.537"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.538"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.539"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.540"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.541"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.542"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.543"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.544"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.545"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.546"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.547"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.548"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.549"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.550"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.551"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.552"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.553"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.554"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.555"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.556"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.557"},{"element-6066-11e4-a52e-4f735466cecf":"f.389FB30C65BF01963BC95447B1255D1D.d.5AE887A8372D3D926A1A7A86AE53C47D.e.558"}]} | headers=HTTPHeaderDict({'Content-Length': '12519', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,243 - DEBUG - Finished Request
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成] iframe 1 內容:
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成]   - 文字長度: 9514
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成]   - 包含目標訂單: False
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成]   - 包含 E48B: True
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成]   - 表格數量: 2
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成]   - 表格行數: 109
2025-07-01 09:52:34,244 - INFO - 🔍 [用戶點擊準備完成]   - 編輯按鈕數量: 210
2025-07-01 09:52:34,245 - INFO - 🔍 [用戶點擊準備完成]   - E48B 元素數量: 106
2025-07-01 09:52:34,245 - INFO - 🔍 [用戶點擊準備完成]   - 內容前300字符: 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 現在非2050專案預約時間，預約開放時段：10:00~20:00

7月
8月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
沒有符合的結果
每日開放查詢時日10:...
2025-07-01 09:52:34,245 - INFO - 🔍 [用戶點擊準備完成]   - 內容後300字符: ...		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611402060724	岡	KER-2807	一般清運	7	2025-07-08		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611402060723	仁	KER-2807	一般清運	7	2025-07-08		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611402060722	南	KER-2807	一般清運	6	2025-07-08		高南廠	0	一般	取消(刪除)
顯示第 1 至 106 項結果，共 106 項
上一頁
1
下一頁
2025-07-01 09:52:34,245 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/frame {'id': None}
2025-07-01 09:52:34,247 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/frame HTTP/1.1" 200 0
2025-07-01 09:52:34,248 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,248 - DEBUG - Finished Request
2025-07-01 09:52:34,248 - INFO - 🔍 [用戶點擊準備完成] iframe 1 檢測完成，已切換回主頁面
2025-07-01 09:52:34,423 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:34,429 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:34,429 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:34,430 - DEBUG - Finished Request
2025-07-01 09:52:35,430 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:35,437 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:35,438 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:35,438 - DEBUG - Finished Request
2025-07-01 09:52:36,248 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:36,254 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:36,254 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:36,254 - DEBUG - Finished Request
2025-07-01 09:52:36,255 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/title {}
2025-07-01 09:52:36,259 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/title HTTP/1.1" 200 0
2025-07-01 09:52:36,259 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:36,260 - DEBUG - Finished Request
2025-07-01 09:52:36,260 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': '//table'}
2025-07-01 09:52:36,269 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:36,269 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:36,269 - DEBUG - Finished Request
2025-07-01 09:52:36,269 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 09:52:36,280 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:36,280 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:36,280 - DEBUG - Finished Request
2025-07-01 09:52:36,280 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/source {}
2025-07-01 09:52:36,283 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/source HTTP/1.1" 200 0
2025-07-01 09:52:36,284 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'x6cQ7uwbR50vuiSFEoPBf82HuQ0NEf3-0eOo8aHC3WXNBgUiShRXOvlrJdbF3ap0ZtoCTUJUp-B-X85l0qDsHkSC91gXbq-kd34T4XhSDi41:eX2Amg3ATUezCYy_fiPeAT_TOKaFRfC3Mu-C0Gbr0n8rJtSxLDQUrkBUILX1PXfjzWe2F7rWKduQpFsosAen5TFXxZAhDSAu9TFR107twn41' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 09:52:11\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"f084a38697f304ca06b3a6fb805324fb\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95824338af5a4a68&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:36,284 - DEBUG - Finished Request
2025-07-01 09:52:36,439 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:36,446 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:36,446 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:36,446 - DEBUG - Finished Request
2025-07-01 09:52:37,448 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:37,455 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:37,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:37,455 - DEBUG - Finished Request
2025-07-01 09:52:38,285 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': '//table'}
2025-07-01 09:52:38,293 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:38,293 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:38,293 - DEBUG - Finished Request
2025-07-01 09:52:38,293 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 09:52:38,300 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:38,300 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:38,301 - DEBUG - Finished Request
2025-07-01 09:52:38,301 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/source {}
2025-07-01 09:52:38,304 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/source HTTP/1.1" 200 0
2025-07-01 09:52:38,304 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'x6cQ7uwbR50vuiSFEoPBf82HuQ0NEf3-0eOo8aHC3WXNBgUiShRXOvlrJdbF3ap0ZtoCTUJUp-B-X85l0qDsHkSC91gXbq-kd34T4XhSDi41:eX2Amg3ATUezCYy_fiPeAT_TOKaFRfC3Mu-C0Gbr0n8rJtSxLDQUrkBUILX1PXfjzWe2F7rWKduQpFsosAen5TFXxZAhDSAu9TFR107twn41' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 09:52:11\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"f084a38697f304ca06b3a6fb805324fb\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95824338af5a4a68&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:38,304 - DEBUG - Finished Request
2025-07-01 09:52:38,456 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:38,463 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:38,463 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:38,463 - DEBUG - Finished Request
2025-07-01 09:52:39,464 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:39,470 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:39,470 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:39,470 - DEBUG - Finished Request
2025-07-01 09:52:40,306 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': '//table'}
2025-07-01 09:52:40,313 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:40,313 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:40,313 - DEBUG - Finished Request
2025-07-01 09:52:40,313 - DEBUG - POST http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 09:52:40,319 - DEBUG - http://localhost:51327 "POST /session/6125b69970a3f7d67ab2cf6f762b9cf4/elements HTTP/1.1" 200 0
2025-07-01 09:52:40,319 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:40,319 - DEBUG - Finished Request
2025-07-01 09:52:40,319 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/source {}
2025-07-01 09:52:40,322 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/source HTTP/1.1" 200 0
2025-07-01 09:52:40,322 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'x6cQ7uwbR50vuiSFEoPBf82HuQ0NEf3-0eOo8aHC3WXNBgUiShRXOvlrJdbF3ap0ZtoCTUJUp-B-X85l0qDsHkSC91gXbq-kd34T4XhSDi41:eX2Amg3ATUezCYy_fiPeAT_TOKaFRfC3Mu-C0Gbr0n8rJtSxLDQUrkBUILX1PXfjzWe2F7rWKduQpFsosAen5TFXxZAhDSAu9TFR107twn41' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/07/01 09:52:11\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\" cd_frame_id_=\"f084a38697f304ca06b3a6fb805324fb\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95824338af5a4a68&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10278', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:40,323 - DEBUG - Finished Request
2025-07-01 09:52:40,470 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:40,477 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:40,477 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:40,477 - DEBUG - Finished Request
2025-07-01 09:52:41,478 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:41,485 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:41,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:41,485 - DEBUG - Finished Request
2025-07-01 09:52:42,486 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:42,491 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:42,491 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:42,491 - DEBUG - Finished Request
2025-07-01 09:52:43,492 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:43,498 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:43,498 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:43,499 - DEBUG - Finished Request
2025-07-01 09:52:44,500 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:44,505 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:44,506 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:44,506 - DEBUG - Finished Request
2025-07-01 09:52:45,507 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:45,514 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:45,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:45,514 - DEBUG - Finished Request
2025-07-01 09:52:46,514 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:46,522 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:46,522 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:46,523 - DEBUG - Finished Request
2025-07-01 09:52:47,524 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:47,532 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:47,532 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:47,532 - DEBUG - Finished Request
2025-07-01 09:52:48,532 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:48,539 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:48,540 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:48,540 - DEBUG - Finished Request
2025-07-01 09:52:49,541 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:49,548 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:49,548 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:49,548 - DEBUG - Finished Request
2025-07-01 09:52:50,549 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:50,557 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:50,558 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:50,558 - DEBUG - Finished Request
2025-07-01 09:52:51,558 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:51,565 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:51,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:51,565 - DEBUG - Finished Request
2025-07-01 09:52:52,566 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:52,573 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:52,573 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:52,573 - DEBUG - Finished Request
2025-07-01 09:52:53,574 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:53,582 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:53,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:53,582 - DEBUG - Finished Request
2025-07-01 09:52:54,583 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:54,591 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:54,591 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:54,591 - DEBUG - Finished Request
2025-07-01 09:52:55,592 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:55,599 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:55,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:55,600 - DEBUG - Finished Request
2025-07-01 09:52:56,602 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:56,609 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:56,609 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:56,610 - DEBUG - Finished Request
2025-07-01 09:52:57,610 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:57,618 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:57,619 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:57,619 - DEBUG - Finished Request
2025-07-01 09:52:58,620 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:58,627 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:58,627 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:58,627 - DEBUG - Finished Request
2025-07-01 09:52:59,628 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:52:59,636 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:52:59,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:52:59,637 - DEBUG - Finished Request
2025-07-01 09:53:00,637 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:00,645 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:00,645 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:00,645 - DEBUG - Finished Request
2025-07-01 09:53:01,645 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:01,652 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:01,652 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:01,653 - DEBUG - Finished Request
2025-07-01 09:53:02,653 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:02,662 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:02,662 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:02,662 - DEBUG - Finished Request
2025-07-01 09:53:03,663 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:03,671 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:03,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:03,671 - DEBUG - Finished Request
2025-07-01 09:53:04,672 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:04,679 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:04,679 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:04,679 - DEBUG - Finished Request
2025-07-01 09:53:05,681 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:05,692 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:05,692 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:05,692 - DEBUG - Finished Request
2025-07-01 09:53:06,693 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:06,703 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:06,703 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:06,703 - DEBUG - Finished Request
2025-07-01 09:53:07,704 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:07,712 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:07,712 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:07,712 - DEBUG - Finished Request
2025-07-01 09:53:08,713 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:08,719 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:08,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:08,720 - DEBUG - Finished Request
2025-07-01 09:53:09,721 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:09,728 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:09,728 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:09,728 - DEBUG - Finished Request
2025-07-01 09:53:10,729 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:10,736 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:10,736 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:10,736 - DEBUG - Finished Request
2025-07-01 09:53:11,738 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:11,745 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:11,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:11,745 - DEBUG - Finished Request
2025-07-01 09:53:12,747 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:12,756 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:12,756 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:12,756 - DEBUG - Finished Request
2025-07-01 09:53:13,757 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:13,764 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:13,765 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:13,765 - DEBUG - Finished Request
2025-07-01 09:53:14,766 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:14,773 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:14,773 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:14,773 - DEBUG - Finished Request
2025-07-01 09:53:15,774 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:15,780 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:15,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:15,780 - DEBUG - Finished Request
2025-07-01 09:53:16,781 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:16,788 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:16,788 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:16,789 - DEBUG - Finished Request
2025-07-01 09:53:17,790 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:17,798 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:17,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:17,799 - DEBUG - Finished Request
2025-07-01 09:53:18,800 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:18,808 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:18,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:18,808 - DEBUG - Finished Request
2025-07-01 09:53:19,809 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:19,816 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:19,816 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:19,816 - DEBUG - Finished Request
2025-07-01 09:53:20,817 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:20,826 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:20,827 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:20,827 - DEBUG - Finished Request
2025-07-01 09:53:21,828 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:21,836 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:21,837 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:21,837 - DEBUG - Finished Request
2025-07-01 09:53:22,839 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:22,846 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:22,846 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:22,846 - DEBUG - Finished Request
2025-07-01 09:53:23,846 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:23,854 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:23,854 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:23,855 - DEBUG - Finished Request
2025-07-01 09:53:24,856 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:24,862 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:24,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:24,862 - DEBUG - Finished Request
2025-07-01 09:53:25,864 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:25,870 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:25,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:25,871 - DEBUG - Finished Request
2025-07-01 09:53:26,872 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:26,880 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:26,880 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:26,880 - DEBUG - Finished Request
2025-07-01 09:53:27,881 - DEBUG - GET http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4/url {}
2025-07-01 09:53:27,887 - DEBUG - http://localhost:51327 "GET /session/6125b69970a3f7d67ab2cf6f762b9cf4/url HTTP/1.1" 200 0
2025-07-01 09:53:27,887 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:27,887 - DEBUG - Finished Request
2025-07-01 09:53:28,599 - DEBUG - DELETE http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4 {}
2025-07-01 09:53:28,638 - DEBUG - http://localhost:51327 "DELETE /session/6125b69970a3f7d67ab2cf6f762b9cf4 HTTP/1.1" 200 0
2025-07-01 09:53:28,638 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 09:53:28,639 - DEBUG - Finished Request
2025-07-01 09:53:28,897 - DEBUG - DELETE http://localhost:51327/session/6125b69970a3f7d67ab2cf6f762b9cf4 {}
2025-07-01 09:53:28,897 - DEBUG - Starting new HTTP connection (1): localhost:51327
