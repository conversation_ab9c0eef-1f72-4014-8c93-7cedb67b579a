# 🚨 AGES-KH-Bot 錯誤處理系統文檔

## 📋 概述

基於用戶提供的錯誤分析反饋，我們開發了一個專門的錯誤處理系統來檢測和處理 SweetAlert2 彈窗錯誤。

## 🔍 支持的錯誤類型

### 1. 預約時間未開放錯誤
- **關鍵字**: `["尚未開放", "請於", "9:30"]`
- **錯誤訊息示例**: `"送出失敗：尚未開放 2025/07/04 預約進廠，請於 9:30 後預約。（現在時間：2025-06-27 09:11:50）"`
- **處理動作**: `wait_and_retry`
- **自動處理**: 等待 60 秒後自動重試

### 2. 工廠名額已滿錯誤
- **關鍵字**: `["仁武廠", "已滿", "請選擇其他廠"]`
- **錯誤訊息示例**: `"送出失敗：M1：仁武廠選擇的進廠數量已滿，請選擇其他廠。"`
- **處理動作**: `switch_factory`
- **自動處理**: 目前需要人工介入（未來可擴展自動切換工廠功能）

### 3. 一般送出失敗錯誤
- **關鍵字**: `["送出失敗"]`
- **處理動作**: `retry_or_manual`
- **自動處理**: 等待 2 秒後重試，最多重試 3 次

## 🏗️ 系統架構

### 核心文件

1. **`error_handler.py`** - 主要錯誤處理模組
2. **`test_error_handler.py`** - 單元測試
3. **`order_submission_with_error_handling.py`** - 整合示例
4. **`dom_elements_config.json`** - 更新的 DOM 配置

### 類別結構

```python
class ErrorHandler:
    - check_for_error_popup()     # 檢測錯誤彈窗
    - handle_error()              # 處理錯誤
    - _analyze_error_type()       # 分析錯誤類型
    - _close_popup()              # 關閉彈窗
```

## 🔧 使用方法

### 基本使用

```python
from error_handler import ErrorHandler

# 初始化
error_handler = ErrorHandler(driver, logger)

# 檢查錯誤
error_info = error_handler.check_for_error_popup()

if error_info:
    # 處理錯誤
    success = error_handler.handle_error(error_info)
```

### 整合到主程序

```python
# 提交訂單後檢查錯誤
submit_button.click()
time.sleep(2)

error_info = error_handler.check_for_error_popup(timeout=5)
if error_info:
    if error_info["action"] == "wait_and_retry":
        # 等待後重試
        error_handler.handle_error(error_info)
        # 重新提交
    elif error_info["action"] == "switch_factory":
        # 需要切換工廠
        print("需要切換到其他工廠")
```

## 📊 DOM 元素配置

更新的 `dom_elements_config.json` 包含：

- **錯誤彈窗檢測**: `div.swal2-popup`, `div#swal2-html-container`
- **錯誤訊息提取**: `div#swal2-html-container`
- **彈窗關閉**: 多種關閉按鈕選擇器

## 🧪 測試覆蓋

### 單元測試
- ✅ 錯誤類型分析測試
- ✅ 錯誤處理邏輯測試
- ✅ 彈窗關閉測試
- ✅ 配置驗證測試

### 整合測試
- ✅ 完整錯誤檢測流程測試
- ✅ Mock 瀏覽器元素測試

## 🚀 部署建議

### 1. 測試環境驗證
```bash
python test_error_handler.py
```

### 2. 整合到現有代碼
- 將 `error_handler.py` 加入主程序
- 在訂單提交後添加錯誤檢查
- 根據錯誤類型實施相應處理

### 3. 監控和日誌
- 所有錯誤都會記錄到日誌
- 包含錯誤類型、時間戳和原始訊息
- 便於後續分析和改進

## 🔮 未來擴展

### 1. 自動工廠切換
- 實現自動選擇替代工廠
- 基於歷史數據優化工廠選擇

### 2. 智能重試策略
- 根據錯誤類型調整重試間隔
- 實現指數退避算法

### 3. 錯誤預測
- 分析歷史錯誤模式
- 提前預警可能的錯誤

## 📝 配置參數

### 錯誤處理參數
```python
{
    "time_not_open": {
        "wait_time": 60,        # 等待時間（秒）
        "max_retries": 5        # 最大重試次數
    },
    "factory_full": {
        "alternative_factories": ["林園廠", "大社廠"]
    },
    "general_failure": {
        "max_retries": 3,
        "retry_delay": 2        # 重試延遲（秒）
    }
}
```

## 🛡️ 安全考量

### 1. 反檢測機制
- 錯誤處理不會觸發額外的反檢測風險
- 保持與正常用戶行為一致的等待時間

### 2. 日誌安全
- 敏感信息不會記錄到日誌
- 錯誤訊息經過適當過濾

## 📞 支援和維護

### 常見問題
1. **Q**: 如果檢測不到錯誤彈窗怎麼辦？
   **A**: 檢查 DOM 選擇器是否正確，可能需要更新配置

2. **Q**: 錯誤處理失敗怎麼辦？
   **A**: 系統會自動回退到人工介入模式

3. **Q**: 如何添加新的錯誤類型？
   **A**: 在 `error_patterns` 中添加新的模式配置

### 更新日誌
- **2025-06-27**: 初始版本發布
- 基於用戶反饋的 SweetAlert2 錯誤分析
- 支持三種主要錯誤類型
- 完整的測試覆蓋

---

## 🎯 總結

這個錯誤處理系統大大提高了 AGES-KH-Bot 的穩定性和自動化程度。通過智能錯誤檢測和處理，系統能夠：

- 🔍 **自動檢測** SweetAlert2 彈窗錯誤
- 🧠 **智能分析** 錯誤類型和原因
- 🔄 **自動重試** 可恢復的錯誤
- 📊 **詳細記錄** 錯誤信息供分析
- 🛡️ **安全處理** 避免觸發平台檢測

系統設計具有良好的擴展性，可以輕鬆添加新的錯誤類型和處理策略。
