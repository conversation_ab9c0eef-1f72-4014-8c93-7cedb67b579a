# AGES-KH-Bot 下一階段開發計劃

## 📋 計劃概述
- **目標版本**: v1.5.0
- **開發主題**: GUI 流程重構和用戶體驗優化
- **基於版本**: v1.4.33 穩定版本
- **預計開發時間**: 2-3 天

## 🎯 核心目標

### **主要目標**
1. **移除 GUI 驗證碼輸入** - 改為提醒式設計
2. **新增完整的狀態顯示** - 讓用戶清楚知道程式進度
3. **實現模擬送單功能** - 安全測試整個流程
4. **優化用戶體驗** - 避免技術術語，使用友好描述

### **次要目標**
1. 完善錯誤處理和恢復機制
2. 增加緊急取消功能
3. 優化實時狀態更新
4. 完善執行結果分析

## 🔧 技術實現計劃

### **Phase 1: 核心重構** (第1天)

#### **1.1 移除舊的驗證碼 GUI**
```python
# 需要修改的文件: mvp_grabber.py
# 位置: execute_single_order_grab() 函數

# 移除這行:
captcha_code = handle_captcha_input()  # 第1509行

# 替換為:
show_verification_reminder_gui()  # 新函數
```

#### **1.2 實現 GUI#09 - 驗證碼輸入提醒**
```python
def show_verification_reminder_gui():
    """GUI#09 - 驗證碼輸入提醒"""
    # 創建提醒視窗
    # 檢測定位狀態
    # 顯示狀態欄
    # 提供模式選擇按鈕
```

#### **1.3 實現模式控制邏輯**
```python
# 全局變量
current_execution_mode = 'normal'  # 'normal' 或 'test'

# 修改現有函數
def click_submit_button():
    if current_execution_mode == 'test':
        return click_cancel_button()
    else:
        return click_actual_submit_button()

# 新增函數
def click_cancel_button():
    """模擬送單：點擊取消按鈕"""
```

### **Phase 2: 等待和執行** (第2天)

#### **2.1 實現 GUI#10 - 等待觸發時間**
```python
def show_waiting_gui(mode, trigger_time, filtered_tasks):
    """GUI#10 - 等待觸發時間"""
    # 創建等待視窗
    # 實時倒數計時
    # 顯示執行階段
    # 顯示系統狀態
    # 提供緊急取消
```

#### **2.2 實現實時狀態更新**
```python
def update_waiting_status():
    """更新等待視窗的狀態"""
    # 更新倒數計時
    # 更新檢測狀態
    # 更新執行階段
```

#### **2.3 整合執行流程**
```python
def execute_with_gui_feedback(task, mode):
    """帶 GUI 反饋的執行流程"""
    # 顯示 GUI#10
    # 等待觸發時間
    # 執行點擊動作
    # 顯示 GUI#11
```

### **Phase 3: 結果和優化** (第3天)

#### **3.1 實現 GUI#11 - 執行結果**
```python
def show_result_gui(result, mode, execution_time):
    """GUI#11 - 執行結果"""
    # 顯示執行結果
    # 分析成功/失敗原因
    # 提供詳細日誌查看
    # 模擬送單特殊處理
```

#### **3.2 完善錯誤處理**
```python
def handle_execution_error(error, context):
    """統一的錯誤處理"""
    # 記錄錯誤日誌
    # 截圖保存
    # 用戶友好的錯誤描述
```

#### **3.3 整合測試**
- 完整流程測試
- 正式送單測試
- 模擬送單測試
- 錯誤情況測試

## 📱 GUI 設計規範

### **統一設計原則**
1. **用戶友好**: 避免技術術語
2. **狀態清晰**: 隨時知道程式在做什麼
3. **操作簡單**: 明確的按鈕和選項
4. **錯誤友好**: 清楚的錯誤說明

### **狀態描述規範**
```
✅ 正確: "定位狀態: 修改進廠確認單"
❌ 錯誤: "定位狀態: 第二層 iframe"

✅ 正確: "檢測結果: 找到送出按鈕"
❌ 錯誤: "檢測結果: submit button detected"

✅ 正確: "執行模式: 正式送單"
❌ 錯誤: "執行模式: normal mode"
```

### **圖標使用規範**
```
📍 定位狀態
🔍 檢測結果  
📊 統計信息
⏰ 時間相關
🎯 觸發時間
⏳ 倒數計時
🎮 執行模式
🔄 當前階段
✅ 成功狀態
❌ 失敗狀態
⚠️ 警告狀態
🚀 正式送單
🧪 模擬送單
```

## 🔄 開發流程

### **每日開發計劃**

#### **第1天: 核心重構**
- [ ] 備份 v1.4.33 (已完成)
- [ ] 創建開發分支
- [ ] 移除 GUI#08 驗證碼輸入
- [ ] 實現 GUI#09 驗證碼提醒
- [ ] 實現模式選擇邏輯
- [ ] 基礎測試

#### **第2天: 等待和執行**
- [ ] 實現 GUI#10 等待觸發時間
- [ ] 實現實時狀態更新
- [ ] 實現模擬送單邏輯
- [ ] 整合執行流程
- [ ] 功能測試

#### **第3天: 結果和優化**
- [ ] 實現 GUI#11 執行結果
- [ ] 完善錯誤處理
- [ ] 整合測試
- [ ] 用戶體驗優化
- [ ] 文檔更新

### **測試檢查清單**

#### **功能測試**
- [ ] GUI#09 狀態檢測正確
- [ ] 模式選擇功能正常
- [ ] GUI#10 倒數計時準確
- [ ] 實時狀態更新正常
- [ ] 正式送單功能正常
- [ ] 模擬送單功能正常
- [ ] GUI#11 結果顯示正確

#### **錯誤測試**
- [ ] 網路連接失敗處理
- [ ] 頁面結構變化處理
- [ ] 按鈕找不到處理
- [ ] 用戶取消操作處理
- [ ] 時間計算錯誤處理

#### **用戶體驗測試**
- [ ] 狀態描述用戶友好
- [ ] 操作流程清晰
- [ ] 錯誤信息易懂
- [ ] 緊急取消功能可用

## 📋 風險評估

### **高風險項目**
1. **GUI 流程重構** - 可能影響現有穩定功能
2. **模擬送單邏輯** - 需要確保不會意外送出
3. **實時狀態更新** - 可能影響性能

### **風險緩解策略**
1. **保留穩定版本** - v1.4.33 備份可隨時回滾
2. **分階段開發** - 每個階段獨立測試
3. **充分測試** - 特別是模擬送單功能

### **回滾計劃**
- 如果 v1.5.0 開發遇到重大問題
- 可以立即回滾到 v1.4.33 穩定版本
- 使用 `mvp_grabber_v1.4.33_backup.py`

## 📞 開發支援

### **參考文檔**
- `GUI_FLOW_DESIGN.md` - 詳細的 GUI 設計
- `VERSION_HISTORY.md` - 版本歷史和功能記錄
- 現有代碼中的 iframe 檢測邏輯

### **關鍵函數參考**
- `enhanced_dialog_button_detection()` - 按鈕檢測
- `log_page_content()` - 頁面內容記錄
- `execute_precise_submit()` - 精確執行邏輯
- `wait_for_trigger_time()` - 等待觸發時間

### **開發環境**
- Python 3.x
- Selenium WebDriver
- tkinter GUI
- 現有的 RTT 預測系統

---

## 📝 備註
- 本計劃基於 v1.4.33 穩定版本
- 開發過程中保持與用戶的溝通
- 隨時根據測試結果調整計劃
- 確保每個階段都有可用的版本

**開發開始日期**: 2025-06-30  
**預計完成日期**: 2025-07-02  
**負責人**: Will Wang
