# orders.csv v2.0 欄位說明

本檔案為 AGES-KH 搶單主程式的任務組態設定檔，每一列代表一筆搶單任務。

## 欄位說明

| 欄位名稱      | 說明                                   | 格式/範例                  | 必填 |
|---------------|----------------------------------------|----------------------------|------|
| date          | 任務執行日（YYYY/MM/DD 或 *-*-*）      | 2025/06/16、*-*-*          | ✅   |
| order_date    | 目標預約日（表單內日期）               | 2025/06/20                 | ✅   |
| order_id      | 單據代碼                               | E48B201611405150598        | ✅   |
| browser       | 指定瀏覽器（chrome/edge/firefox）      | chrome                     | ✅   |
| user_profile  | 使用者 profile 名稱（資料夾/帳號）     | xiaoming_chrome            | ✅   |
| model         | 使用的 RTT 模型（A/B/C...）            | A                          | ⛔   |
| trigger_time  | 預設觸發時間（HH:MM:SS.sss，可空）     | 09:30:00.001               | ⛔   |

- date：
  - 指定此任務要在哪一天執行，若填 *-*-* 則代表每天都執行。
- order_date：
  - 目標預約日，對應平台表單內的「預計進廠日」。
- order_id：
  - 要搶的單據編號。
- browser：
  - 執行本任務時要啟動的瀏覽器類型。
- user_profile：
  - 對應 user-data-dir，指定不同帳號/環境。
- model：
  - 指定要用的 RTT 模型（如 A/B/C），可空，預設用主程式設定。
- trigger_time：
  - 預設觸發時間（如 09:30:00.001），可空，若空則由主程式/RTT 模型決定。

## 範例內容

```
date,order_date,order_id,browser,user_profile,model,trigger_time
2025/06/16,2025/06/20,E48B201611405150598,chrome,xiaoming_chrome,A,09:30:00.001
*-*-*,2025/06/20,E48B201611405150598,chrome,xiaoming_chrome,A,
```

## 注意事項
- 欄位名稱不得更動，順序建議保持預設。
- 不可含空白行與註解行（實際使用時請移除 # 行）。
- 每一列代表一筆獨立的搶單任務。 