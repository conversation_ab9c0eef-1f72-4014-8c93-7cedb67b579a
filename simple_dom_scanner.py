# Simple DOM Scanner for AGES-KH
# 簡化版 DOM 掃描工具，用於快速測試和元素探測
# Version: 1.0
# Author: <PERSON>

import json
import time
from datetime import datetime

def check_dependencies():
    """檢查必要套件"""
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        return True
    except ImportError as e:
        print(f"[ERROR] 缺少套件: {e}")
        print("請執行: pip install selenium webdriver-manager")
        return False

if not check_dependencies():
    exit(1)

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import NoSuchElementException, WebDriverException

class SimpleDOMScanner:
    def __init__(self):
        self.driver = None
        
    def setup_browser(self):
        """設定瀏覽器"""
        try:
            options = webdriver.ChromeOptions()
            options.add_argument('--start-maximized')
            options.add_argument('--disable-gpu')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            
            print("[INFO] Chrome 瀏覽器已啟動")
            return True
        except Exception as e:
            print(f"[ERROR] 瀏覽器啟動失敗: {e}")
            return False
            
    def navigate_and_wait(self):
        """導航並等待手動操作"""
        try:
            self.driver.get("https://wmc.kcg.gov.tw/")
            print("[INFO] 已導航到平台")
            
            print("\n" + "="*50)
            print("請手動完成以下操作：")
            print("1. 登入您的帳號")
            print("2. 導航到搶單頁面")
            print("3. ⚠️  請保持瀏覽器開啟")
            print("4. 完成後請按 Enter 繼續...")
            print("="*50)
            
            input()
            
            # 檢查連線
            current_url = self.driver.current_url
            print(f"[INFO] 當前頁面: {current_url}")
            return True
            
        except Exception as e:
            print(f"[ERROR] 導航或連線失敗: {e}")
            return False
            
    def scan_current_page(self):
        """掃描當前頁面的所有表單元素"""
        elements_found = {}
        
        try:
            # 掃描所有輸入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            print(f"\n[INFO] 找到 {len(inputs)} 個輸入框:")
            
            for i, input_elem in enumerate(inputs):
                try:
                    element_info = {
                        'tag': 'input',
                        'type': input_elem.get_attribute('type') or '',
                        'name': input_elem.get_attribute('name') or '',
                        'id': input_elem.get_attribute('id') or '',
                        'class': input_elem.get_attribute('class') or '',
                        'placeholder': input_elem.get_attribute('placeholder') or '',
                        'value': input_elem.get_attribute('value') or ''
                    }
                    
                    elements_found[f'input_{i}'] = element_info
                    print(f"  Input {i}: type={element_info['type']}, name={element_info['name']}, id={element_info['id']}")
                    
                except Exception as e:
                    print(f"  Input {i}: 讀取失敗 - {e}")
            
            # 掃描所有按鈕
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            print(f"\n[INFO] 找到 {len(buttons)} 個按鈕:")
            
            for i, button in enumerate(buttons):
                try:
                    element_info = {
                        'tag': 'button',
                        'type': button.get_attribute('type') or '',
                        'name': button.get_attribute('name') or '',
                        'id': button.get_attribute('id') or '',
                        'class': button.get_attribute('class') or '',
                        'text': button.text[:30] if button.text else ''
                    }
                    
                    elements_found[f'button_{i}'] = element_info
                    print(f"  Button {i}: text='{element_info['text']}', type={element_info['type']}, id={element_info['id']}")
                    
                except Exception as e:
                    print(f"  Button {i}: 讀取失敗 - {e}")
            
            # 掃描所有連結
            links = self.driver.find_elements(By.TAG_NAME, "a")
            print(f"\n[INFO] 找到 {len(links)} 個連結:")
            
            for i, link in enumerate(links[:10]):  # 只顯示前10個
                try:
                    element_info = {
                        'tag': 'a',
                        'href': link.get_attribute('href') or '',
                        'id': link.get_attribute('id') or '',
                        'class': link.get_attribute('class') or '',
                        'text': link.text[:30] if link.text else ''
                    }
                    
                    elements_found[f'link_{i}'] = element_info
                    print(f"  Link {i}: text='{element_info['text']}', href={element_info['href'][:50]}")
                    
                except Exception as e:
                    print(f"  Link {i}: 讀取失敗 - {e}")
                    
        except Exception as e:
            print(f"[ERROR] 掃描頁面時發生錯誤: {e}")
            
        return elements_found
        
    def save_results(self, elements):
        """儲存掃描結果，包含時間標籤和變化追蹤"""
        timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")

        config = {
            'scan_info': {
                'timestamp': timestamp.isoformat(),
                'date': timestamp.strftime("%Y-%m-%d"),
                'time': timestamp.strftime("%H:%M:%S"),
                'url': self.driver.current_url if self.driver else '',
                'total_elements': len(elements),
                'scan_type': 'simple_full_page'
            },
            'elements': elements
        }

        # 主檔案
        main_filename = 'simple_dom_scan_result.json'
        # 帶時間標籤的備份檔
        backup_filename = f'simple_dom_scan_{timestamp_str}.json'

        try:
            # 儲存主檔案
            with open(main_filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 儲存備份檔
            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"\n[INFO] 掃描結果已儲存:")
            print(f"  - 主檔案: {main_filename}")
            print(f"  - 備份檔: {backup_filename}")

            # 檢查變化
            self._check_changes(config)

            return True
        except Exception as e:
            print(f"[ERROR] 儲存失敗: {e}")
            return False

    def _check_changes(self, current_config):
        """檢查與之前掃描的變化"""
        try:
            import glob

            previous_files = glob.glob('simple_dom_scan_*.json')
            if len(previous_files) <= 1:
                print(f"[INFO] 首次掃描，無法比較變化")
                return

            previous_files.sort()
            previous_file = previous_files[-2]

            with open(previous_file, 'r', encoding='utf-8') as f:
                previous_config = json.load(f)

            current_count = current_config['scan_info']['total_elements']
            previous_count = previous_config['scan_info']['total_elements']

            print(f"\n[INFO] 頁面變化分析:")
            print(f"  - 上次掃描: {previous_config['scan_info']['timestamp']}")
            print(f"  - 元素數量變化: {previous_count} → {current_count} ({current_count - previous_count:+d})")

            if current_count != previous_count:
                print(f"  - ⚠️  頁面結構可能有變化，請注意檢查")
            else:
                print(f"  - ✅ 頁面結構穩定")

        except Exception as e:
            print(f"[WARNING] 變化分析失敗: {e}")
            
    def cleanup(self):
        """清理資源"""
        if self.driver:
            try:
                self.driver.quit()
                print("[INFO] 瀏覽器已關閉")
            except:
                pass

def main():
    print("="*60)
    print("           簡化版 DOM 掃描工具")
    print("="*60)
    print("此工具會掃描當前頁面的所有表單元素")
    print("="*60)
    
    scanner = SimpleDOMScanner()
    
    try:
        # 啟動瀏覽器
        if not scanner.setup_browser():
            return
            
        # 導航並等待
        if not scanner.navigate_and_wait():
            return
            
        # 掃描頁面
        print("\n[開始掃描頁面元素...]")
        elements = scanner.scan_current_page()
        
        # 儲存結果
        if elements:
            scanner.save_results(elements)
            print(f"\n[完成] 共掃描到 {len(elements)} 個元素")
        else:
            print("\n[警告] 未掃描到任何元素")
            
    except KeyboardInterrupt:
        print("\n[INFO] 使用者中斷操作")
    except Exception as e:
        print(f"\n[ERROR] 發生錯誤: {e}")
    finally:
        scanner.cleanup()

if __name__ == "__main__":
    main()
