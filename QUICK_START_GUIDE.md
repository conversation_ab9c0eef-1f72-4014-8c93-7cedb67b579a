# AGES-KH-Bot v1.4 快速使用指南（修正版）

## 🚀 正確的使用流程

### 1. 啟動程式
```bash
python mvp_grabber.py
```

### 2. 設定搶單時間
- 在 GUI 中設定觸發時間（格式：HH:MM:SS.sss）
- 例如：`09:30:00.001`

### 3. 開始執行
1. 點擊「開始執行」
2. 確認搶單參數
3. 等待瀏覽器自動啟動

### 4. 手動操作階段
瀏覽器啟動後，請手動完成：
1. **登入帳號**
2. **輸入登入驗證碼**
3. **導航到進廠確認單列表頁面**
4. **確認能看到您要搶的訂單**（例如：E48B201611405190953）

### 5. 點擊「準備完成」
- 完成上述步驟後，點擊「準備完成」
- 程式將自動執行以下步驟：

## 🤖 自動執行階段

### 6. 系統自動找到訂單
- 程式會根據 orders.csv 中的訂單號自動搜尋
- **不需要您手動 Ctrl+F 搜尋**
- 自動點擊對應的「編輯」按鈕

### 7. 編輯頁面操作
- 平台跳出編輯頁面
- **您需要手動輸入驗證碼**
- 輸入完成後，點擊「準備完成」確認

### 8. 精確送出
- 程式根據 RTT 計算精確的送出時機
- 在觸發時間自動點擊「送出」按鈕
- 等待系統回覆結果
- 自動記錄搶單結果

## 🔧 重要功能

### 自動訂單搜尋
- **程式會自動根據 orders.csv 找到您的訂單**
- 不需要手動 Ctrl+F 搜尋
- 支援多種搜尋策略，提高成功率

### RTT 自動補償
- 程式會自動測量網路延遲
- 根據 RTT 數據調整觸發時間
- 在精確時機送出以補償網路延遲

### 智能頁面驗證
- 程式會檢查您是否在正確的搶單頁面
- 如果不在正確頁面會提醒您

### 人機協作設計
- 登入和驗證碼需要人工輸入（合規要求）
- 訂單搜尋和送出由程式自動執行
- 在關鍵步驟提供確認機制

## 📋 操作流程圖

```
啟動程式 → 設定時間 → 開始執行 → 瀏覽器啟動
    ↓
手動登入 → 導航到搶單頁面 → 點擊「準備完成」
    ↓
自動找訂單 → 點擊編輯按鈕 → 編輯頁面載入
    ↓
人工輸入驗證碼 → 點擊「準備完成」 → RTT 計算
    ↓
精確送出 → 檢測結果 → 記錄結果 → 完成
```

## ⚠️ 注意事項

### 必須手動操作的部分
1. **登入驗證碼** - 合規要求，必須人工輸入
2. **搶單驗證碼** - 在編輯頁面需要人工輸入
3. **頁面導航** - 需要手動導航到正確的搶單頁面

### 關鍵成功因素
1. **使用 Ctrl+F 搜尋** - 這是找到正確訂單的關鍵
2. **確認頁面正確** - 必須在進廠確認單列表頁面
3. **網路穩定** - RTT 補償需要穩定的網路環境

## 🐛 常見問題

### Q: 找不到編輯按鈕
**A**: 確保您已經：
- 在正確的搶單頁面（進廠確認單列表）
- 訂單確實存在且狀態允許編輯
- 檢查 orders.csv 中的訂單號是否正確

### Q: DOM 掃描失敗
**A**: 檢查：
- 是否在正確的頁面
- 頁面是否完全載入
- 網路連線是否正常

### Q: RTT 功能沒有工作
**A**: 檢查：
- logs 目錄是否存在
- 網路連線是否穩定
- 防火牆是否阻擋

## 📁 重要文件

- `mvp_grabber.py` - 主程式
- `dom_elements_config.json` - DOM 元素配置
- `config/rtt_config.json` - RTT 設定
- `logs/` - RTT 和執行日誌
- `results/` - 搶單結果記錄

## 🔄 版本更新

### v1.4.0 新功能
- ✅ 支援 Ctrl+F 高亮搜尋
- ✅ RTT 自動補償
- ✅ 智能頁面驗證
- ✅ 簡化操作流程
- ✅ 改進錯誤處理

---

**如有問題，請檢查 `FIXES_SUMMARY_v1.4.md` 了解詳細的修復內容**
