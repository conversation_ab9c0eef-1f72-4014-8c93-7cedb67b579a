2025-07-02 10:45:47,040 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_104547.log
2025-07-02 10:46:00,752 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 10:46:00,752 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 10:46:00,829 - DEBUG - chromedriver not found in PATH
2025-07-02 10:46:00,830 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 10:46:00,830 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 10:46:00,830 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 10:46:00,830 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 10:46:00,830 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 10:46:00,830 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 10:46:00,889 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 5332 using 0 to output -3
2025-07-02 10:46:01,403 - DEBUG - POST http://localhost:51586/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 10:46:01,404 - DEBUG - Starting new HTTP connection (1): localhost:51586
2025-07-02 10:46:01,938 - DEBUG - http://localhost:51586 "POST /session HTTP/1.1" 200 0
2025-07-02 10:46:01,939 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir5332_943941506"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51589"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"59bcd021b84b9922f5887da6be8624db"}} | headers=HTTPHeaderDict({'Content-Length': '880', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:46:01,939 - DEBUG - Finished Request
2025-07-02 10:46:01,940 - DEBUG - POST http://localhost:51586/session/59bcd021b84b9922f5887da6be8624db/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 10:46:03,139 - DEBUG - http://localhost:51586 "POST /session/59bcd021b84b9922f5887da6be8624db/url HTTP/1.1" 200 0
2025-07-02 10:46:03,139 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:46:03,139 - DEBUG - Finished Request
2025-07-02 10:46:03,139 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 10:46:03,140 - DEBUG - POST http://localhost:51586/session/59bcd021b84b9922f5887da6be8624db/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 10:46:03,148 - DEBUG - http://localhost:51586 "POST /session/59bcd021b84b9922f5887da6be8624db/execute/sync HTTP/1.1" 200 0
2025-07-02 10:46:03,149 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:46:03,149 - DEBUG - Finished Request
2025-07-02 10:46:03,149 - INFO - ✅ 瀏覽器事件監控已啟動
