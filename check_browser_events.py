#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查現有瀏覽器實例中的事件日誌
"""

import sys
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def connect_to_existing_browser():
    """連接到現有的瀏覽器實例"""
    try:
        # 嘗試連接到現有的 Chrome 實例
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ 成功連接到現有瀏覽器實例")
        return driver
        
    except Exception as e:
        print(f"❌ 連接現有瀏覽器失敗: {e}")
        print("💡 請確保瀏覽器是以調試模式啟動的")
        return None

def check_event_log(driver):
    """檢查事件日誌"""
    try:
        print("\n🔍 檢查主頁面事件日誌...")
        
        # 檢查主頁面的事件日誌
        main_events = driver.execute_script("return window.AGES_EVENT_LOG || [];")
        print(f"📊 主頁面事件數量: {len(main_events)}")
        
        # 檢查是否有 iframe
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print(f"📋 發現 {len(iframes)} 個 iframe")
        
        all_events = main_events.copy()
        
        # 檢查每個 iframe 的事件日誌
        for i, iframe in enumerate(iframes):
            try:
                print(f"\n🔍 檢查 iframe {i+1}...")
                
                # 切換到 iframe
                driver.switch_to.frame(iframe)
                
                # 獲取 iframe 中的事件日誌
                iframe_events = driver.execute_script("return window.AGES_EVENT_LOG || [];")
                print(f"📊 iframe {i+1} 事件數量: {len(iframe_events)}")
                
                if iframe_events:
                    all_events.extend(iframe_events)
                    print(f"✅ 從 iframe {i+1} 獲取到 {len(iframe_events)} 個事件")
                
                # 切換回主頁面
                driver.switch_to.default_content()
                
            except Exception as iframe_error:
                print(f"⚠️ 無法訪問 iframe {i+1}: {iframe_error}")
                driver.switch_to.default_content()
        
        return all_events
        
    except Exception as e:
        print(f"❌ 檢查事件日誌失敗: {e}")
        return []

def analyze_events(events):
    """分析事件"""
    if not events:
        print("❌ 沒有找到任何事件記錄")
        return
    
    print(f"\n📊 總共找到 {len(events)} 個事件")
    
    # 統計事件類型
    event_types = {}
    click_events = []
    key_events = []
    input_events = []
    
    for event in events:
        event_type = event.get('type', 'unknown')
        event_types[event_type] = event_types.get(event_type, 0) + 1
        
        if event_type == 'click':
            click_events.append(event)
        elif event_type == 'keydown':
            key_events.append(event)
        elif event_type == 'input':
            input_events.append(event)
    
    print(f"\n📋 事件類型統計:")
    for event_type, count in event_types.items():
        print(f"  {event_type}: {count} 個")
    
    # 分析點擊事件
    if click_events:
        print(f"\n🖱️ 點擊事件分析 ({len(click_events)} 個):")
        for i, event in enumerate(click_events[-10:]):  # 只顯示最後10個
            target = event.get('target', {})
            coords = event.get('coordinates', {})
            
            tag_name = target.get('tagName', '')
            text = target.get('textContent', '')[:30]
            value = target.get('value', '')
            class_name = target.get('className', '')[:30]
            
            client_x = coords.get('clientX', 0)
            client_y = coords.get('clientY', 0)
            
            # 檢查是否可能是送出按鈕
            submit_indicators = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save']
            all_text = f"{text} {value} {class_name}".lower()
            found_indicators = [ind for ind in submit_indicators if ind in all_text]
            
            indicator_text = f" 🎯送出指標={found_indicators}" if found_indicators else ""
            
            print(f"  點擊 {i+1}: <{tag_name}> '{text}' value='{value}' class='{class_name}' 座標=({client_x},{client_y}){indicator_text}")
    
    # 分析鍵盤事件
    if key_events:
        print(f"\n⌨️ 鍵盤事件分析 ({len(key_events)} 個):")
        for i, event in enumerate(key_events[-10:]):  # 只顯示最後10個
            key = event.get('key', '')
            target = event.get('target', {})
            target_type = target.get('type', '')
            value = target.get('value', '')[:10]
            
            print(f"  按鍵 {i+1}: '{key}' 目標類型='{target_type}' 值='{value}'")
    
    # 分析輸入事件
    if input_events:
        print(f"\n📝 輸入事件分析 ({len(input_events)} 個):")
        for i, event in enumerate(input_events[-10:]):  # 只顯示最後10個
            target = event.get('target', {})
            target_type = target.get('type', '')
            value = target.get('value', '')[:10]
            
            print(f"  輸入 {i+1}: 目標類型='{target_type}' 值='{value}'")

def main():
    """主函數"""
    print("🔍 檢查瀏覽器事件日誌工具")
    print("="*50)
    
    # 連接到現有瀏覽器
    driver = connect_to_existing_browser()
    if not driver:
        print("\n💡 如果您的瀏覽器不是以調試模式啟動，請：")
        print("1. 關閉所有 Chrome 瀏覽器")
        print("2. 以調試模式啟動: chrome.exe --remote-debugging-port=9222")
        print("3. 重新運行此腳本")
        return
    
    try:
        # 檢查當前頁面
        current_url = driver.current_url
        print(f"📍 當前頁面: {current_url}")
        
        # 檢查事件日誌
        events = check_event_log(driver)
        
        # 分析事件
        analyze_events(events)
        
        # 保存事件日誌
        if events:
            import json
            from datetime import datetime
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"browser_events_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(events, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 事件日誌已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 執行過程中發生錯誤: {e}")
    
    finally:
        # 不要關閉瀏覽器，因為用戶可能還在使用
        print("\n✅ 檢查完成")

if __name__ == "__main__":
    main()
