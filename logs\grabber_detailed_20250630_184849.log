2025-06-30 18:48:49,463 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_184849.log
2025-06-30 18:49:05,725 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 18:49:05,725 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 18:49:05,783 - DEBUG - chromedriver not found in PATH
2025-06-30 18:49:05,784 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:49:05,784 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 18:49:05,784 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 18:49:05,784 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 18:49:05,784 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 18:49:05,785 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:49:05,789 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 27180 using 0 to output -3
2025-06-30 18:49:06,317 - DEBUG - POST http://localhost:53848/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 18:49:06,318 - DEBUG - Starting new HTTP connection (1): localhost:53848
2025-06-30 18:49:06,863 - DEBUG - http://localhost:53848 "POST /session HTTP/1.1" 200 0
2025-06-30 18:49:06,864 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir27180_1202278321"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53851"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"fb146c9c9306f26433f80ac331d01b99"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:06,864 - DEBUG - Finished Request
2025-06-30 18:49:06,864 - DEBUG - POST http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 18:49:08,300 - DEBUG - http://localhost:53848 "POST /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:08,301 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:08,301 - DEBUG - Finished Request
2025-06-30 18:49:08,301 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 18:49:08,301 - DEBUG - POST http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 18:49:08,307 - DEBUG - http://localhost:53848 "POST /session/fb146c9c9306f26433f80ac331d01b99/execute/sync HTTP/1.1" 200 0
2025-06-30 18:49:08,307 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:08,307 - DEBUG - Finished Request
2025-06-30 18:49:08,307 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 18:49:08,308 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:08,334 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:08,334 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:08,334 - DEBUG - Finished Request
2025-06-30 18:49:09,335 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:09,340 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:09,341 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:09,341 - DEBUG - Finished Request
2025-06-30 18:49:10,342 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:10,350 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:10,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:10,350 - DEBUG - Finished Request
2025-06-30 18:49:11,351 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:11,357 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:11,358 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:11,358 - DEBUG - Finished Request
2025-06-30 18:49:12,359 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:12,366 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:12,367 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:12,367 - DEBUG - Finished Request
2025-06-30 18:49:13,368 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:13,373 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:13,374 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:13,374 - DEBUG - Finished Request
2025-06-30 18:49:14,375 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:14,380 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:14,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:14,380 - DEBUG - Finished Request
2025-06-30 18:49:15,381 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:15,389 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:15,390 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:15,390 - DEBUG - Finished Request
2025-06-30 18:49:16,391 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:16,397 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:16,397 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:16,397 - DEBUG - Finished Request
2025-06-30 18:49:17,398 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:17,406 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:17,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:17,407 - DEBUG - Finished Request
2025-06-30 18:49:18,407 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:18,415 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:18,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:18,416 - DEBUG - Finished Request
2025-06-30 18:49:19,416 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:19,423 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:19,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:19,423 - DEBUG - Finished Request
2025-06-30 18:49:20,424 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:20,432 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:20,433 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:20,433 - DEBUG - Finished Request
2025-06-30 18:49:21,433 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:21,440 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:21,440 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:21,440 - DEBUG - Finished Request
2025-06-30 18:49:22,441 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:22,448 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:22,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:22,448 - DEBUG - Finished Request
2025-06-30 18:49:23,449 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:23,458 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:23,459 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:23,459 - DEBUG - Finished Request
2025-06-30 18:49:24,460 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:24,469 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:24,469 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:24,470 - DEBUG - Finished Request
2025-06-30 18:49:25,470 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:25,479 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:25,480 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:25,481 - DEBUG - Finished Request
2025-06-30 18:49:26,482 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:26,489 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:26,490 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:26,490 - DEBUG - Finished Request
2025-06-30 18:49:27,492 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:27,500 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:27,500 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:27,501 - DEBUG - Finished Request
2025-06-30 18:49:28,502 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:28,514 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:28,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:28,514 - DEBUG - Finished Request
2025-06-30 18:49:29,516 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:29,524 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:29,525 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:29,525 - DEBUG - Finished Request
2025-06-30 18:49:30,527 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:30,535 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:30,535 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:30,536 - DEBUG - Finished Request
2025-06-30 18:49:31,536 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:31,545 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:31,545 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:31,546 - DEBUG - Finished Request
2025-06-30 18:49:32,547 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:32,556 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:32,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:32,557 - DEBUG - Finished Request
2025-06-30 18:49:33,558 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:33,567 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:33,567 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:33,568 - DEBUG - Finished Request
2025-06-30 18:49:34,569 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:34,577 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:34,577 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:34,578 - DEBUG - Finished Request
2025-06-30 18:49:35,578 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:35,587 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:35,587 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:35,588 - DEBUG - Finished Request
2025-06-30 18:49:36,589 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:36,597 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:36,598 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:36,598 - DEBUG - Finished Request
2025-06-30 18:49:37,599 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:37,608 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:37,608 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:37,609 - DEBUG - Finished Request
2025-06-30 18:49:38,610 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:38,619 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:38,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:38,620 - DEBUG - Finished Request
2025-06-30 18:49:39,621 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:39,631 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:39,632 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:39,632 - DEBUG - Finished Request
2025-06-30 18:49:40,633 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:40,642 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:40,642 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:40,643 - DEBUG - Finished Request
2025-06-30 18:49:41,644 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:41,652 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:41,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:41,653 - DEBUG - Finished Request
2025-06-30 18:49:42,654 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:42,664 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:42,664 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:42,665 - DEBUG - Finished Request
2025-06-30 18:49:43,666 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:43,674 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:43,674 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:43,675 - DEBUG - Finished Request
2025-06-30 18:49:44,675 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:44,683 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:44,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:44,684 - DEBUG - Finished Request
2025-06-30 18:49:45,685 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:45,695 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:45,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:45,695 - DEBUG - Finished Request
2025-06-30 18:49:46,696 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:46,704 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:46,704 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:46,704 - DEBUG - Finished Request
2025-06-30 18:49:47,706 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:47,715 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:47,715 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:47,715 - DEBUG - Finished Request
2025-06-30 18:49:48,716 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:48,724 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:48,724 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:48,725 - DEBUG - Finished Request
2025-06-30 18:49:49,726 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:49,734 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:49,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:49,734 - DEBUG - Finished Request
2025-06-30 18:49:50,735 - DEBUG - GET http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99/url {}
2025-06-30 18:49:50,744 - DEBUG - http://localhost:53848 "GET /session/fb146c9c9306f26433f80ac331d01b99/url HTTP/1.1" 200 0
2025-06-30 18:49:50,744 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:50,745 - DEBUG - Finished Request
2025-06-30 18:49:51,112 - DEBUG - DELETE http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99 {}
2025-06-30 18:49:51,151 - DEBUG - http://localhost:53848 "DELETE /session/fb146c9c9306f26433f80ac331d01b99 HTTP/1.1" 200 0
2025-06-30 18:49:51,151 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:49:51,152 - DEBUG - Finished Request
2025-06-30 18:49:51,761 - DEBUG - DELETE http://localhost:53848/session/fb146c9c9306f26433f80ac331d01b99 {}
2025-06-30 18:49:51,761 - DEBUG - Starting new HTTP connection (1): localhost:53848
