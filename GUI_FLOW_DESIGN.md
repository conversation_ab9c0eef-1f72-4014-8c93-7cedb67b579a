# AGES-KH-Bot GUI 流程設計文檔

## 📋 文檔信息
- **創建日期**: 2025-06-30
- **版本**: v1.5.0 設計規劃
- **狀態**: 設計階段
- **基於版本**: v1.4.33 穩定版本

## 🎯 設計目標
- 提供清晰的用戶操作流程
- 讓用戶隨時知道程式執行狀態
- 支援正式送單和模擬送單兩種模式
- 避免技術術語，使用用戶友好的描述

## 📊 完整 GUI 清單

### **GUI#01** - 觸發時間設定
- **位置**: `ask_trigger_time_gui()`
- **功能**: 詢問用戶輸入觸發時間
- **類型**: `simpledialog.askstring`
- **狀態**: 保持不變

### **GUI#02** - 準備提示視窗
- **位置**: `show_startup_gui()`
- **功能**: 顯示今日任務和啟動瀏覽器
- **類型**: 完整視窗
- **狀態**: 保持不變

### **GUI#03** - 登入提示
- **位置**: `wait_for_user_operation_and_start_grabbing()`
- **功能**: 提示用戶手動登入
- **類型**: 簡單視窗
- **狀態**: 保持不變

### **GUI#04** - 主搶單程式
- **位置**: `GrabberGUI` 類
- **功能**: 主要的搶單控制介面
- **類型**: 完整視窗
- **狀態**: 保持不變

### **GUI#05** - 操作指南
- **位置**: `_show_simplified_user_guide()`
- **功能**: 顯示操作步驟和時間資訊
- **類型**: 完整視窗（帶滾動條）
- **狀態**: 已優化（v1.4.33）

### **GUI#06** - 頁面確認對話框
- **位置**: `_verify_correct_page()`
- **功能**: 確認用戶是否在正確頁面
- **類型**: `messagebox.askyesno`
- **狀態**: 保持不變

### **GUI#07** - 準備完成確認
- **位置**: `wait_for_user_ready_confirmation()`
- **功能**: 確認用戶已輸入驗證碼並準備送出
- **類型**: `messagebox.askyesno`
- **狀態**: 保持不變

### **GUI#08** - 驗證碼輸入 ⚠️
- **位置**: `handle_captcha_input()`
- **功能**: 讓用戶輸入驗證碼
- **類型**: `simpledialog.askstring`
- **狀態**: **需要移除/重構**

### **GUI#09** - 驗證碼輸入提醒 ⭐ **新增**
- **功能**: 提醒用戶在修改進廠確認單中輸入驗證碼
- **類型**: 完整視窗（帶狀態欄）
- **狀態**: 待開發

### **GUI#10** - 等待觸發時間 ⭐ **新增**
- **功能**: 顯示等待狀態和倒數計時
- **類型**: 完整視窗（實時更新）
- **狀態**: 待開發

### **GUI#11** - 執行結果 ⭐ **新增**
- **功能**: 顯示最終執行結果
- **類型**: 完整視窗
- **狀態**: 待開發

## 🔄 流程設計

### **正式送單流程**
```
GUI#05 操作指南
    ↓ 用戶點擊「準備完成」
程式自動點擊編輯按鈕
    ↓ 編輯彈窗打開
GUI#09 驗證碼輸入提醒
    ↓ 用戶選擇「已輸入驗證碼-正式送單」
GUI#10 等待觸發時間 (正式模式)
    ↓ 觸發時間到達
程式點擊「送出」按鈕
    ↓ 執行完成
GUI#11 執行結果 (成功/失敗)
```

### **模擬送單流程**
```
GUI#05 操作指南
    ↓ 用戶點擊「準備完成」
程式自動點擊編輯按鈕
    ↓ 編輯彈窗打開
GUI#09 驗證碼輸入提醒
    ↓ 用戶選擇「已輸入驗證碼-模擬送單」
GUI#10 等待觸發時間 (模擬模式)
    ↓ 觸發時間到達
程式點擊「取消」按鈕
    ↓ 執行完成
GUI#11 執行結果 (模擬成功)
```

## 📱 GUI#09 詳細設計

### **視窗標題**
`GUI#09 - 驗證碼輸入提醒`

### **主要內容**
```
🔐 請在修改進廠確認單中輸入驗證碼

📋 操作說明：
1. 在彈出的修改進廠確認單中找到驗證碼輸入框
2. 手動輸入驗證碼
3. 確認所有資料正確
4. 選擇下方按鈕完成準備

⏰ 請在觸發時間前 5 分鐘內完成驗證碼輸入
```

### **狀態欄**
```
📍 定位狀態: ✅ 修改進廠確認單
🔍 檢測結果: ✅ 找到驗證碼輸入框 | ✅ 找到送出按鈕 | ✅ 找到取消按鈕
📊 元素統計: 按鈕 3 個 | 輸入框 5 個 | 內容長度 1250 字符
```

### **按鈕區域**
```
[已輸入驗證碼-正式送單]  [已輸入驗證碼-模擬送單]
```

## 📱 GUI#10 詳細設計

### **正式送單模式**
```
⏰ 程式正在等待觸發時間

🎯 觸發時間: 🌅明日 09:30:00.001 📡用戶輸入
⏳ 倒數計時: 14:23:45
🎮 執行模式: 🚀 正式送單

📊 系統準備狀態:
✅ 定位狀態: 修改進廠確認單
✅ 送出按鈕: 已找到 (1個)
✅ 取消按鈕: 已找到 (1個)  
✅ RTT 補償: 已計算 (-125ms)
✅ 驗證碼: 用戶已確認輸入

🔄 當前階段: 精確等待觸發時間中...

階段 1: 🔍 檢測頁面元素... (完成)
階段 2: 🔐 等待驗證碼輸入... (完成)  
階段 3: ⏰ 精確等待觸發時間... (進行中)
階段 4: 🚀 執行送出動作... (等待中)
階段 5: 📊 檢測執行結果... (等待中)

[緊急取消執行]  [最小化視窗]
```

### **模擬送單模式**
```
⏰ 程式正在等待觸發時間 (🧪 模擬模式)

🎯 觸發時間: 🌅明日 09:30:00.001 📡用戶輸入
⏳ 倒數計時: 14:23:45
🎮 執行模式: 🧪 模擬送單 (安全測試)

📊 系統準備狀態:
✅ 定位狀態: 修改進廠確認單
✅ 送出按鈕: 已找到 (1個) - 不會點擊
✅ 取消按鈕: 已找到 (1個) - 將會點擊 ⭐
✅ RTT 補償: 已計算 (-125ms)
✅ 驗證碼: 用戶已確認輸入

🔄 當前階段: 精確等待觸發時間中...
⚠️ 注意: 模擬模式不會真正送出訂單

階段 4: 🧪 執行取消動作... (等待中) ⭐

[緊急取消執行]  [最小化視窗]
```

## 📱 GUI#11 詳細設計

### **正式送單成功**
```
🎉 搶單執行完成

✅ 執行結果: 送出成功
⏰ 執行時間: 2025-06-30 09:30:00.126
🎯 目標時間: 2025-06-30 09:30:00.001
📊 時間誤差: +125ms (在可接受範圍內)

📋 執行摘要:
• 訂單號: E48B201611405190953
• 執行模式: 正式送單
• 按鈕點擊: 成功
• 頁面響應: 正常

[查看詳細日誌]  [關閉程式]
```

### **模擬送單成功**
```
🧪 模擬送單執行完成

✅ 執行結果: 取消成功 (模擬模式)
⏰ 執行時間: 2025-06-30 09:30:00.126
🎯 目標時間: 2025-06-30 09:30:00.001
📊 時間誤差: +125ms (在可接受範圍內)

📋 模擬摘要:
• 訂單號: E48B201611405190953
• 執行模式: 🧪 模擬送單
• 按鈕點擊: 取消按鈕 (成功)
• 頁面響應: 正常關閉彈窗
• 訂單狀態: 未提交 (符合預期)

✅ 模擬測試通過！正式送單時應該可以成功

[查看詳細日誌]  [關閉程式]
```

## 🔧 技術實現要點

### **狀態檢測**
- 使用現有的 `enhanced_dialog_button_detection()` 函數
- 檢測 iframe 定位狀態
- 檢測按鈕和輸入框元素

### **模式控制**
- 全局變量 `current_mode`: 'normal' 或 'test'
- 修改 `click_submit_button()` 支援模式選擇
- 新增 `click_cancel_button()` 函數

### **實時更新**
- GUI#10 需要每秒更新倒數計時
- 狀態欄需要實時反映檢測結果
- 階段進度需要動態更新

## 📋 開發優先級

### **Phase 1: 核心功能**
1. 移除 GUI#08 驗證碼輸入
2. 實現 GUI#09 驗證碼提醒
3. 實現模式選擇邏輯

### **Phase 2: 等待和執行**
1. 實現 GUI#10 等待觸發時間
2. 實現模擬送單邏輯
3. 實現實時狀態更新

### **Phase 3: 結果顯示**
1. 實現 GUI#11 執行結果
2. 完善錯誤處理
3. 優化用戶體驗

## 📝 注意事項

### **用戶友好性**
- 避免技術術語（如 "第二層 iframe"）
- 使用清晰的狀態描述（如 "修改進廠確認單"）
- 提供明確的操作指引

### **安全性**
- 模擬模式確保不會意外送出
- 緊急取消功能
- 詳細的執行日誌

### **可靠性**
- 完整的錯誤處理
- 狀態檢測和驗證
- 備份和恢復機制
