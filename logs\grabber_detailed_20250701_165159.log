2025-07-01 16:51:59,800 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_165159.log
2025-07-01 16:52:17,660 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:52:17,660 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:52:17,710 - DEBUG - chromedriver not found in PATH
2025-07-01 16:52:17,710 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:52:17,711 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:52:17,711 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:52:17,711 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:52:17,711 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:52:17,711 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:52:17,715 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 3064 using 0 to output -3
2025-07-01 16:52:18,221 - DEBUG - POST http://localhost:55629/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:52:18,222 - DEBUG - Starting new HTTP connection (1): localhost:55629
2025-07-01 16:52:18,789 - DEBUG - http://localhost:55629 "POST /session HTTP/1.1" 200 0
2025-07-01 16:52:18,789 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir3064_54486856"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55632"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"0603b354d05803a7b990c585248928fd"}} | headers=HTTPHeaderDict({'Content-Length': '881', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:18,790 - DEBUG - Finished Request
2025-07-01 16:52:18,790 - DEBUG - POST http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:52:19,853 - DEBUG - http://localhost:55629 "POST /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:19,853 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:19,853 - DEBUG - Finished Request
2025-07-01 16:52:19,855 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:52:19,855 - DEBUG - POST http://localhost:55629/session/0603b354d05803a7b990c585248928fd/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:52:19,870 - DEBUG - http://localhost:55629 "POST /session/0603b354d05803a7b990c585248928fd/execute/sync HTTP/1.1" 200 0
2025-07-01 16:52:19,871 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:19,871 - DEBUG - Finished Request
2025-07-01 16:52:19,871 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:52:19,872 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:19,910 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:19,910 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:19,910 - DEBUG - Finished Request
2025-07-01 16:52:20,911 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:20,917 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:20,917 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:20,918 - DEBUG - Finished Request
2025-07-01 16:52:21,918 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:21,929 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:21,929 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:21,929 - DEBUG - Finished Request
2025-07-01 16:52:22,930 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:22,935 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:22,936 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:22,936 - DEBUG - Finished Request
2025-07-01 16:52:23,937 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:23,944 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:23,945 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:23,945 - DEBUG - Finished Request
2025-07-01 16:52:24,947 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:24,953 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:24,954 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:24,954 - DEBUG - Finished Request
2025-07-01 16:52:25,955 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:25,962 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:25,962 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:25,963 - DEBUG - Finished Request
2025-07-01 16:52:26,964 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:26,971 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:26,971 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:26,971 - DEBUG - Finished Request
2025-07-01 16:52:27,972 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:27,977 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:27,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:27,978 - DEBUG - Finished Request
2025-07-01 16:52:28,979 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:28,989 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:28,990 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:28,990 - DEBUG - Finished Request
2025-07-01 16:52:29,991 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:29,997 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:29,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:29,998 - DEBUG - Finished Request
2025-07-01 16:52:30,999 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:31,004 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:31,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:31,004 - DEBUG - Finished Request
2025-07-01 16:52:32,006 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:32,012 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:32,013 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:32,013 - DEBUG - Finished Request
2025-07-01 16:52:33,014 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:33,024 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:33,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:33,024 - DEBUG - Finished Request
2025-07-01 16:52:34,026 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:34,031 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:34,032 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:34,032 - DEBUG - Finished Request
2025-07-01 16:52:35,033 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:35,039 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:35,039 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:35,039 - DEBUG - Finished Request
2025-07-01 16:52:36,040 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:36,046 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:36,047 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:36,048 - DEBUG - Finished Request
2025-07-01 16:52:37,049 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:37,056 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:37,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:37,056 - DEBUG - Finished Request
2025-07-01 16:52:38,057 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:38,063 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:38,063 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:38,063 - DEBUG - Finished Request
2025-07-01 16:52:39,065 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:39,071 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:39,072 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:39,072 - DEBUG - Finished Request
2025-07-01 16:52:40,073 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:40,080 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:40,081 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:40,081 - DEBUG - Finished Request
2025-07-01 16:52:41,082 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:41,088 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:41,088 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:41,088 - DEBUG - Finished Request
2025-07-01 16:52:42,089 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:42,096 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:42,096 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:42,096 - DEBUG - Finished Request
2025-07-01 16:52:43,097 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:43,104 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:43,104 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:43,105 - DEBUG - Finished Request
2025-07-01 16:52:44,106 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:44,112 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:44,112 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:44,113 - DEBUG - Finished Request
2025-07-01 16:52:45,114 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:45,122 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:45,122 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:45,123 - DEBUG - Finished Request
2025-07-01 16:52:46,123 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:46,130 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:46,131 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:46,131 - DEBUG - Finished Request
2025-07-01 16:52:47,132 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:47,138 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:47,139 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:47,139 - DEBUG - Finished Request
2025-07-01 16:52:48,140 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:48,148 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:48,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:48,148 - DEBUG - Finished Request
2025-07-01 16:52:49,149 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:49,154 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:49,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:49,154 - DEBUG - Finished Request
2025-07-01 16:52:50,156 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:50,162 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:50,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:50,162 - DEBUG - Finished Request
2025-07-01 16:52:51,163 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:51,169 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:51,170 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:51,170 - DEBUG - Finished Request
2025-07-01 16:52:52,171 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:52,177 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:52,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:52,177 - DEBUG - Finished Request
2025-07-01 16:52:53,179 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:53,187 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:53,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:53,187 - DEBUG - Finished Request
2025-07-01 16:52:54,188 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:54,195 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:54,195 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:54,195 - DEBUG - Finished Request
2025-07-01 16:52:55,196 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:55,203 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:55,203 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:55,204 - DEBUG - Finished Request
2025-07-01 16:52:56,204 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:56,211 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:56,212 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:56,212 - DEBUG - Finished Request
2025-07-01 16:52:57,213 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:57,219 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:57,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:57,219 - DEBUG - Finished Request
2025-07-01 16:52:58,221 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:58,228 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:58,228 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:58,228 - DEBUG - Finished Request
2025-07-01 16:52:59,229 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:52:59,236 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:52:59,236 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:52:59,236 - DEBUG - Finished Request
2025-07-01 16:53:00,237 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:00,244 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:00,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:00,245 - DEBUG - Finished Request
2025-07-01 16:53:01,246 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:01,254 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:01,254 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:01,255 - DEBUG - Finished Request
2025-07-01 16:53:02,255 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:02,263 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:02,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:02,263 - DEBUG - Finished Request
2025-07-01 16:53:03,264 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:03,510 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:03,510 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:03,510 - DEBUG - Finished Request
2025-07-01 16:53:04,511 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:04,519 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:04,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:04,519 - DEBUG - Finished Request
2025-07-01 16:53:05,521 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:05,527 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:05,528 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:05,528 - DEBUG - Finished Request
2025-07-01 16:53:06,529 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:06,539 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:06,539 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:06,539 - DEBUG - Finished Request
2025-07-01 16:53:07,540 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:07,547 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:07,547 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:07,547 - DEBUG - Finished Request
2025-07-01 16:53:08,548 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:08,555 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:08,555 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:08,556 - DEBUG - Finished Request
2025-07-01 16:53:09,557 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:09,564 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:09,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:09,565 - DEBUG - Finished Request
2025-07-01 16:53:10,566 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:10,573 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:10,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:10,574 - DEBUG - Finished Request
2025-07-01 16:53:11,574 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:11,581 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:11,581 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:11,581 - DEBUG - Finished Request
2025-07-01 16:53:12,582 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:12,590 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:12,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:12,591 - DEBUG - Finished Request
2025-07-01 16:53:13,592 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:13,598 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:13,598 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:13,598 - DEBUG - Finished Request
2025-07-01 16:53:14,599 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:14,607 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:14,607 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:14,607 - DEBUG - Finished Request
2025-07-01 16:53:15,608 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:15,615 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:15,616 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:15,616 - DEBUG - Finished Request
2025-07-01 16:53:16,617 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:16,625 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:16,625 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:16,626 - DEBUG - Finished Request
2025-07-01 16:53:17,627 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:17,634 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:17,634 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:17,634 - DEBUG - Finished Request
2025-07-01 16:53:18,635 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:18,641 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:18,642 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:18,642 - DEBUG - Finished Request
2025-07-01 16:53:19,643 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:19,652 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:19,652 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:19,653 - DEBUG - Finished Request
2025-07-01 16:53:20,654 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:20,663 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:20,664 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:20,664 - DEBUG - Finished Request
2025-07-01 16:53:21,665 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:21,673 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:21,673 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:21,673 - DEBUG - Finished Request
2025-07-01 16:53:22,674 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:22,688 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:22,688 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:22,688 - DEBUG - Finished Request
2025-07-01 16:53:23,689 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:23,696 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:23,698 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:23,698 - DEBUG - Finished Request
2025-07-01 16:53:24,698 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:24,706 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:24,706 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:24,706 - DEBUG - Finished Request
2025-07-01 16:53:25,708 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:25,719 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:25,719 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:25,719 - DEBUG - Finished Request
2025-07-01 16:53:26,720 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:26,727 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:26,727 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:26,728 - DEBUG - Finished Request
2025-07-01 16:53:27,729 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:27,738 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:27,738 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:27,738 - DEBUG - Finished Request
2025-07-01 16:53:28,738 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:28,744 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:28,744 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:28,745 - DEBUG - Finished Request
2025-07-01 16:53:29,746 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:29,751 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:29,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:29,752 - DEBUG - Finished Request
2025-07-01 16:53:30,754 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:30,761 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:30,761 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:30,761 - DEBUG - Finished Request
2025-07-01 16:53:31,762 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:31,768 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:31,768 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:31,768 - DEBUG - Finished Request
2025-07-01 16:53:32,769 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:32,775 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:32,776 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:32,776 - DEBUG - Finished Request
2025-07-01 16:53:33,777 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:33,786 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:33,786 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:33,786 - DEBUG - Finished Request
2025-07-01 16:53:34,787 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:34,793 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:34,793 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:34,793 - DEBUG - Finished Request
2025-07-01 16:53:35,794 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:35,799 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:35,800 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:35,800 - DEBUG - Finished Request
2025-07-01 16:53:36,801 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:36,807 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:36,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:36,808 - DEBUG - Finished Request
2025-07-01 16:53:37,808 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:37,815 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:37,816 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:37,816 - DEBUG - Finished Request
2025-07-01 16:53:38,817 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:38,824 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:38,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:38,825 - DEBUG - Finished Request
2025-07-01 16:53:39,825 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:39,831 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:39,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:39,831 - DEBUG - Finished Request
2025-07-01 16:53:40,833 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:40,837 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:40,838 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:40,838 - DEBUG - Finished Request
2025-07-01 16:53:41,839 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:41,845 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:41,846 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:41,846 - DEBUG - Finished Request
2025-07-01 16:53:42,847 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:42,855 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:42,855 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:42,855 - DEBUG - Finished Request
2025-07-01 16:53:43,855 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:43,861 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:43,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:43,862 - DEBUG - Finished Request
2025-07-01 16:53:44,863 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:44,870 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:44,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:44,871 - DEBUG - Finished Request
2025-07-01 16:53:45,872 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:45,877 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:45,878 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:45,878 - DEBUG - Finished Request
2025-07-01 16:53:46,878 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:46,885 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:46,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:46,886 - DEBUG - Finished Request
2025-07-01 16:53:47,887 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:47,894 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:47,894 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:47,895 - DEBUG - Finished Request
2025-07-01 16:53:48,895 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:48,902 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:48,902 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:48,902 - DEBUG - Finished Request
2025-07-01 16:53:49,903 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:49,911 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:49,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:49,912 - DEBUG - Finished Request
2025-07-01 16:53:50,912 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:50,919 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:50,919 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:50,920 - DEBUG - Finished Request
2025-07-01 16:53:51,921 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:51,929 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:51,929 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:51,929 - DEBUG - Finished Request
2025-07-01 16:53:52,930 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:52,938 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:52,938 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:52,938 - DEBUG - Finished Request
2025-07-01 16:53:53,938 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:53,946 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:53,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:53,946 - DEBUG - Finished Request
2025-07-01 16:53:54,946 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:54,956 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:54,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:54,956 - DEBUG - Finished Request
2025-07-01 16:53:55,956 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:55,965 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:55,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:55,965 - DEBUG - Finished Request
2025-07-01 16:53:56,966 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:56,973 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:56,973 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:56,973 - DEBUG - Finished Request
2025-07-01 16:53:57,975 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:57,984 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:57,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:57,984 - DEBUG - Finished Request
2025-07-01 16:53:58,984 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:53:58,992 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:53:58,992 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:53:58,993 - DEBUG - Finished Request
2025-07-01 16:53:59,993 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:00,003 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:00,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:00,004 - DEBUG - Finished Request
2025-07-01 16:54:01,004 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:01,012 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:01,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:01,012 - DEBUG - Finished Request
2025-07-01 16:54:02,013 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:02,021 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:02,021 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:02,021 - DEBUG - Finished Request
2025-07-01 16:54:03,022 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:03,029 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:03,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:03,029 - DEBUG - Finished Request
2025-07-01 16:54:04,031 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:04,041 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:04,041 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:04,042 - DEBUG - Finished Request
2025-07-01 16:54:05,042 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:05,047 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:05,047 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:05,047 - DEBUG - Finished Request
2025-07-01 16:54:06,048 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:06,055 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:06,055 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:06,055 - DEBUG - Finished Request
2025-07-01 16:54:07,056 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:07,063 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:07,063 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:07,063 - DEBUG - Finished Request
2025-07-01 16:54:08,064 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:08,075 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:08,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:08,075 - DEBUG - Finished Request
2025-07-01 16:54:09,076 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:09,083 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:09,083 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:09,083 - DEBUG - Finished Request
2025-07-01 16:54:10,084 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:10,092 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:10,092 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:10,092 - DEBUG - Finished Request
2025-07-01 16:54:11,093 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:11,101 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:11,101 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:11,101 - DEBUG - Finished Request
2025-07-01 16:54:12,102 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:12,110 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:12,110 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:12,110 - DEBUG - Finished Request
2025-07-01 16:54:13,111 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:13,120 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:13,120 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:13,120 - DEBUG - Finished Request
2025-07-01 16:54:14,121 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:14,128 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:14,128 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:14,128 - DEBUG - Finished Request
2025-07-01 16:54:15,129 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:15,140 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:15,140 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:15,140 - DEBUG - Finished Request
2025-07-01 16:54:16,141 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:16,149 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:16,149 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:16,149 - DEBUG - Finished Request
2025-07-01 16:54:17,150 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:17,159 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:17,159 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:17,159 - DEBUG - Finished Request
2025-07-01 16:54:18,160 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:18,169 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:18,169 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:18,169 - DEBUG - Finished Request
2025-07-01 16:54:19,169 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:19,177 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:19,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:19,177 - DEBUG - Finished Request
2025-07-01 16:54:20,178 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:20,184 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:20,184 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:20,184 - DEBUG - Finished Request
2025-07-01 16:54:21,185 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:21,192 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:21,192 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:21,192 - DEBUG - Finished Request
2025-07-01 16:54:22,194 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:22,202 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:22,202 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:22,202 - DEBUG - Finished Request
2025-07-01 16:54:23,203 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:23,210 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:23,210 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:23,210 - DEBUG - Finished Request
2025-07-01 16:54:24,210 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:24,217 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:24,217 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:24,217 - DEBUG - Finished Request
2025-07-01 16:54:25,219 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:25,227 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:25,227 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:25,227 - DEBUG - Finished Request
2025-07-01 16:54:26,228 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:26,234 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:26,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:26,234 - DEBUG - Finished Request
2025-07-01 16:54:27,235 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:27,242 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:27,242 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:27,242 - DEBUG - Finished Request
2025-07-01 16:54:28,243 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:28,250 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:28,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:28,250 - DEBUG - Finished Request
2025-07-01 16:54:29,251 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:29,258 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:29,258 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:29,258 - DEBUG - Finished Request
2025-07-01 16:54:30,259 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:30,266 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:30,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:30,267 - DEBUG - Finished Request
2025-07-01 16:54:31,268 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:31,275 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:31,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:31,276 - DEBUG - Finished Request
2025-07-01 16:54:32,276 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:32,283 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:32,283 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:32,283 - DEBUG - Finished Request
2025-07-01 16:54:33,284 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:33,293 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:33,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:33,293 - DEBUG - Finished Request
2025-07-01 16:54:34,294 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:34,302 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:34,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:34,303 - DEBUG - Finished Request
2025-07-01 16:54:35,303 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:35,309 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:35,309 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:35,309 - DEBUG - Finished Request
2025-07-01 16:54:36,311 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:36,319 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:36,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:36,319 - DEBUG - Finished Request
2025-07-01 16:54:37,321 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:37,330 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:37,330 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:37,330 - DEBUG - Finished Request
2025-07-01 16:54:38,331 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:38,339 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:38,339 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:38,339 - DEBUG - Finished Request
2025-07-01 16:54:39,340 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:39,350 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:39,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:39,351 - DEBUG - Finished Request
2025-07-01 16:54:40,352 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:40,362 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:40,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:40,362 - DEBUG - Finished Request
2025-07-01 16:54:41,363 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:41,371 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:41,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:41,372 - DEBUG - Finished Request
2025-07-01 16:54:42,373 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:42,381 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:42,381 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:42,381 - DEBUG - Finished Request
2025-07-01 16:54:43,383 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:43,393 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:43,393 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:43,394 - DEBUG - Finished Request
2025-07-01 16:54:44,395 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:44,402 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:44,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:44,403 - DEBUG - Finished Request
2025-07-01 16:54:45,404 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:45,411 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:45,412 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:45,412 - DEBUG - Finished Request
2025-07-01 16:54:46,413 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:46,422 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:46,422 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:46,422 - DEBUG - Finished Request
2025-07-01 16:54:47,424 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:47,432 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:47,432 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:47,432 - DEBUG - Finished Request
2025-07-01 16:54:48,434 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:48,442 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:48,443 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:48,443 - DEBUG - Finished Request
2025-07-01 16:54:49,444 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:49,452 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:49,452 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:49,453 - DEBUG - Finished Request
2025-07-01 16:54:50,454 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:50,462 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:50,463 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:50,463 - DEBUG - Finished Request
2025-07-01 16:54:51,464 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:51,472 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:51,472 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:51,473 - DEBUG - Finished Request
2025-07-01 16:54:52,474 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:52,479 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:52,479 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:52,480 - DEBUG - Finished Request
2025-07-01 16:54:53,480 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:53,488 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:53,488 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:53,488 - DEBUG - Finished Request
2025-07-01 16:54:54,489 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:54,496 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:54,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:54,497 - DEBUG - Finished Request
2025-07-01 16:54:55,498 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:55,504 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:55,504 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:55,505 - DEBUG - Finished Request
2025-07-01 16:54:56,506 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:56,515 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:56,515 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:56,515 - DEBUG - Finished Request
2025-07-01 16:54:57,516 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:57,525 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:57,525 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:57,525 - DEBUG - Finished Request
2025-07-01 16:54:58,525 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:58,533 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:58,534 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:58,534 - DEBUG - Finished Request
2025-07-01 16:54:59,535 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:54:59,543 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:54:59,543 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:54:59,543 - DEBUG - Finished Request
2025-07-01 16:55:00,545 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:00,556 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:00,556 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:00,556 - DEBUG - Finished Request
2025-07-01 16:55:01,558 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:01,567 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:01,567 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:01,568 - DEBUG - Finished Request
2025-07-01 16:55:02,569 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:02,579 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:02,580 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:02,580 - DEBUG - Finished Request
2025-07-01 16:55:03,581 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:03,590 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:03,591 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:03,591 - DEBUG - Finished Request
2025-07-01 16:55:04,592 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:04,601 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:04,602 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:04,602 - DEBUG - Finished Request
2025-07-01 16:55:05,603 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:05,610 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:05,611 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:05,611 - DEBUG - Finished Request
2025-07-01 16:55:06,611 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:06,621 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:06,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:06,622 - DEBUG - Finished Request
2025-07-01 16:55:07,622 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:07,632 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:07,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:07,633 - DEBUG - Finished Request
2025-07-01 16:55:08,634 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:08,643 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:08,643 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:08,643 - DEBUG - Finished Request
2025-07-01 16:55:09,645 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:09,655 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:09,655 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:09,655 - DEBUG - Finished Request
2025-07-01 16:55:10,656 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:10,667 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:10,667 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:10,667 - DEBUG - Finished Request
2025-07-01 16:55:11,668 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:11,677 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:11,677 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:11,677 - DEBUG - Finished Request
2025-07-01 16:55:12,679 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:12,689 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:12,689 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:12,690 - DEBUG - Finished Request
2025-07-01 16:55:13,690 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:13,700 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:13,700 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:13,700 - DEBUG - Finished Request
2025-07-01 16:55:14,702 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:14,715 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:14,716 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:14,716 - DEBUG - Finished Request
2025-07-01 16:55:15,717 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:15,724 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:15,724 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:15,724 - DEBUG - Finished Request
2025-07-01 16:55:16,726 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:16,737 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:16,737 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:16,737 - DEBUG - Finished Request
2025-07-01 16:55:17,738 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:17,746 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:17,747 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:17,747 - DEBUG - Finished Request
2025-07-01 16:55:18,748 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:18,764 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:18,765 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:18,766 - DEBUG - Finished Request
2025-07-01 16:55:19,767 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:19,775 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:19,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:19,776 - DEBUG - Finished Request
2025-07-01 16:55:20,776 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:20,785 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:20,785 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:20,785 - DEBUG - Finished Request
2025-07-01 16:55:21,786 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:21,796 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:21,797 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:21,797 - DEBUG - Finished Request
2025-07-01 16:55:22,798 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:22,806 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:22,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:22,807 - DEBUG - Finished Request
2025-07-01 16:55:23,808 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:23,817 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:23,817 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:23,817 - DEBUG - Finished Request
2025-07-01 16:55:24,818 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:24,827 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:24,827 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:24,828 - DEBUG - Finished Request
2025-07-01 16:55:25,829 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:25,838 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:25,838 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:25,838 - DEBUG - Finished Request
2025-07-01 16:55:26,839 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:26,848 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:26,848 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:26,848 - DEBUG - Finished Request
2025-07-01 16:55:27,849 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:27,859 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:27,859 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:27,860 - DEBUG - Finished Request
2025-07-01 16:55:28,860 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:28,869 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:28,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:28,869 - DEBUG - Finished Request
2025-07-01 16:55:29,871 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:29,905 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:29,905 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:29,906 - DEBUG - Finished Request
2025-07-01 16:55:30,907 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:30,915 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:30,916 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:30,916 - DEBUG - Finished Request
2025-07-01 16:55:31,917 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:31,925 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:31,925 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:31,925 - DEBUG - Finished Request
2025-07-01 16:55:32,925 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:32,934 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:32,934 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:32,935 - DEBUG - Finished Request
2025-07-01 16:55:33,936 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:33,944 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:33,944 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:33,945 - DEBUG - Finished Request
2025-07-01 16:55:34,945 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:34,955 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:34,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:34,955 - DEBUG - Finished Request
2025-07-01 16:55:35,956 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:35,965 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:35,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:35,966 - DEBUG - Finished Request
2025-07-01 16:55:36,967 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:36,976 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:36,977 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:36,977 - DEBUG - Finished Request
2025-07-01 16:55:37,978 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:37,987 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:37,987 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:37,988 - DEBUG - Finished Request
2025-07-01 16:55:38,989 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:38,997 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:38,997 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:38,998 - DEBUG - Finished Request
2025-07-01 16:55:39,998 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:40,007 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:40,008 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:40,008 - DEBUG - Finished Request
2025-07-01 16:55:41,009 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:41,017 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:41,017 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:41,018 - DEBUG - Finished Request
2025-07-01 16:55:42,019 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:42,030 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:42,031 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:42,031 - DEBUG - Finished Request
2025-07-01 16:55:43,032 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:43,039 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:43,039 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:43,039 - DEBUG - Finished Request
2025-07-01 16:55:44,040 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:44,049 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:44,050 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:44,050 - DEBUG - Finished Request
2025-07-01 16:55:45,051 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:45,059 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:45,059 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:45,059 - DEBUG - Finished Request
2025-07-01 16:55:46,061 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:46,071 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:46,071 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:46,071 - DEBUG - Finished Request
2025-07-01 16:55:47,073 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:47,082 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:47,082 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:47,082 - DEBUG - Finished Request
2025-07-01 16:55:48,083 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:48,092 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:48,093 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:48,093 - DEBUG - Finished Request
2025-07-01 16:55:49,094 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:49,101 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:49,101 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:49,103 - DEBUG - Finished Request
2025-07-01 16:55:50,103 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:50,110 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:50,111 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:50,111 - DEBUG - Finished Request
2025-07-01 16:55:51,112 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:51,123 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:51,123 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:51,124 - DEBUG - Finished Request
2025-07-01 16:55:52,125 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:52,132 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:52,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:52,133 - DEBUG - Finished Request
2025-07-01 16:55:53,134 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:53,144 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:53,144 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:53,145 - DEBUG - Finished Request
2025-07-01 16:55:54,146 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:54,154 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:54,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:54,154 - DEBUG - Finished Request
2025-07-01 16:55:55,155 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:55,163 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:55,164 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:55,165 - DEBUG - Finished Request
2025-07-01 16:55:56,166 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:56,173 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:56,173 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:56,173 - DEBUG - Finished Request
2025-07-01 16:55:57,175 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:57,184 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:57,184 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:57,184 - DEBUG - Finished Request
2025-07-01 16:55:58,185 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:58,195 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:58,195 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:58,196 - DEBUG - Finished Request
2025-07-01 16:55:59,197 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:55:59,206 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:55:59,206 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:55:59,207 - DEBUG - Finished Request
2025-07-01 16:56:00,207 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:00,216 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:00,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:00,216 - DEBUG - Finished Request
2025-07-01 16:56:01,218 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:01,225 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:01,226 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:01,226 - DEBUG - Finished Request
2025-07-01 16:56:02,227 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:02,233 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:02,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:02,234 - DEBUG - Finished Request
2025-07-01 16:56:03,235 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:03,244 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:03,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:03,245 - DEBUG - Finished Request
2025-07-01 16:56:04,246 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:04,257 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:04,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:04,257 - DEBUG - Finished Request
2025-07-01 16:56:05,258 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:05,266 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:05,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:05,267 - DEBUG - Finished Request
2025-07-01 16:56:06,268 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:06,275 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:06,275 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:06,275 - DEBUG - Finished Request
2025-07-01 16:56:07,276 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:07,285 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:07,285 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:07,285 - DEBUG - Finished Request
2025-07-01 16:56:08,287 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:08,296 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:08,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:08,296 - DEBUG - Finished Request
2025-07-01 16:56:09,298 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:09,308 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:09,308 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:09,308 - DEBUG - Finished Request
2025-07-01 16:56:10,309 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:10,317 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:10,317 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:10,317 - DEBUG - Finished Request
2025-07-01 16:56:11,319 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:11,327 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:11,327 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:11,328 - DEBUG - Finished Request
2025-07-01 16:56:12,328 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:12,337 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:12,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:12,337 - DEBUG - Finished Request
2025-07-01 16:56:13,338 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:13,346 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:13,346 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:13,347 - DEBUG - Finished Request
2025-07-01 16:56:14,347 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:14,357 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:14,357 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:14,357 - DEBUG - Finished Request
2025-07-01 16:56:15,358 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:15,367 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:15,367 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:15,367 - DEBUG - Finished Request
2025-07-01 16:56:16,368 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:16,376 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:16,377 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:16,377 - DEBUG - Finished Request
2025-07-01 16:56:17,379 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:17,391 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:17,391 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:17,391 - DEBUG - Finished Request
2025-07-01 16:56:18,392 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:18,401 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:18,401 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:18,402 - DEBUG - Finished Request
2025-07-01 16:56:19,403 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:19,415 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:19,416 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:19,416 - DEBUG - Finished Request
2025-07-01 16:56:20,417 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:20,426 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:20,426 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:20,426 - DEBUG - Finished Request
2025-07-01 16:56:21,428 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:21,439 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:21,439 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:21,440 - DEBUG - Finished Request
2025-07-01 16:56:22,441 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:22,450 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:22,451 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:22,451 - DEBUG - Finished Request
2025-07-01 16:56:23,452 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:23,464 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:23,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:23,465 - DEBUG - Finished Request
2025-07-01 16:56:24,466 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:24,474 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:24,474 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:24,474 - DEBUG - Finished Request
2025-07-01 16:56:25,475 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:25,485 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:25,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:25,485 - DEBUG - Finished Request
2025-07-01 16:56:26,486 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:26,496 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:26,496 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:26,497 - DEBUG - Finished Request
2025-07-01 16:56:27,497 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:27,506 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:27,507 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:27,507 - DEBUG - Finished Request
2025-07-01 16:56:28,508 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:28,516 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:28,516 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:28,517 - DEBUG - Finished Request
2025-07-01 16:56:29,518 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:29,528 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:29,528 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:29,529 - DEBUG - Finished Request
2025-07-01 16:56:30,530 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:30,539 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:30,539 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:30,539 - DEBUG - Finished Request
2025-07-01 16:56:31,540 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:31,551 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:31,551 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:31,552 - DEBUG - Finished Request
2025-07-01 16:56:32,553 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:32,561 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:32,561 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:32,561 - DEBUG - Finished Request
2025-07-01 16:56:33,562 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:33,571 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:33,571 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:33,571 - DEBUG - Finished Request
2025-07-01 16:56:34,572 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:34,582 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:34,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:34,582 - DEBUG - Finished Request
2025-07-01 16:56:35,583 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:35,592 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:35,592 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:35,593 - DEBUG - Finished Request
2025-07-01 16:56:36,594 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:36,602 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:36,603 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:36,603 - DEBUG - Finished Request
2025-07-01 16:56:37,604 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:37,612 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 200 0
2025-07-01 16:56:37,612 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:37,612 - DEBUG - Finished Request
2025-07-01 16:56:38,613 - DEBUG - GET http://localhost:55629/session/0603b354d05803a7b990c585248928fd/url {}
2025-07-01 16:56:38,617 - DEBUG - http://localhost:55629 "GET /session/0603b354d05803a7b990c585248928fd/url HTTP/1.1" 404 0
2025-07-01 16:56:38,617 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:38,618 - DEBUG - Finished Request
2025-07-01 16:56:38,619 - DEBUG - DELETE http://localhost:55629/session/0603b354d05803a7b990c585248928fd {}
2025-07-01 16:56:38,691 - DEBUG - http://localhost:55629 "DELETE /session/0603b354d05803a7b990c585248928fd HTTP/1.1" 200 0
2025-07-01 16:56:38,691 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:56:38,691 - DEBUG - Finished Request
