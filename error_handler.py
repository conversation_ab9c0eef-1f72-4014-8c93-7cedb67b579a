"""
錯誤處理模組 - 專門處理 SweetAlert2 彈窗錯誤
基於用戶提供的錯誤分析反饋開發
"""

import time
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from typing import Dict, Optional, Tuple

class ErrorHandler:
    """處理平台錯誤彈窗的類別"""
    
    def __init__(self, driver, logger=None):
        self.driver = driver
        self.logger = logger or logging.getLogger(__name__)
        
        # 錯誤模式定義（基於用戶反饋分析）
        self.error_patterns = {
            "time_not_open": {
                "keywords": ["尚未開放", "請於", "9:30"],
                "action": "wait_and_retry",
                "description": "預約時間未開放錯誤",
                "wait_time": 60  # 等待60秒後重試
            },
            "factory_full": {
                "keywords": ["仁武廠", "已滿", "請選擇其他廠"],
                "action": "switch_factory", 
                "description": "工廠名額已滿錯誤",
                "alternative_factories": ["林園廠", "大社廠"]  # 可選的替代工廠
            },
            "general_failure": {
                "keywords": ["送出失敗"],
                "action": "retry_or_manual",
                "description": "一般送出失敗錯誤",
                "max_retries": 3
            }
        }
    
    def check_for_error_popup(self, timeout: int = 5) -> Optional[Dict]:
        """
        檢查是否有 SweetAlert2 錯誤彈窗出現
        
        Args:
            timeout: 等待彈窗出現的超時時間
            
        Returns:
            Dict: 包含錯誤信息的字典，如果沒有錯誤則返回 None
        """
        try:
            # 等待 SweetAlert2 彈窗出現
            popup_selectors = [
                "div.swal2-popup.swal2-modal.swal2-show",
                "div.swal2-popup",
                "div#swal2-html-container"
            ]
            
            popup_element = None
            for selector in popup_selectors:
                try:
                    popup_element = WebDriverWait(self.driver, timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    self.logger.info(f"找到錯誤彈窗: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not popup_element:
                return None
            
            # 獲取錯誤訊息文字
            error_text = self._extract_error_text(popup_element)
            if not error_text:
                return None
            
            # 分析錯誤類型
            error_info = self._analyze_error_type(error_text)
            error_info["raw_text"] = error_text
            error_info["timestamp"] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            self.logger.warning(f"檢測到錯誤: {error_info}")
            return error_info
            
        except Exception as e:
            self.logger.error(f"檢查錯誤彈窗時發生異常: {e}")
            return None
    
    def _extract_error_text(self, popup_element) -> Optional[str]:
        """從彈窗元素中提取錯誤文字"""
        try:
            # 嘗試多種方式獲取錯誤文字
            text_selectors = [
                "div#swal2-html-container",
                ".swal2-html-container", 
                ".swal2-content",
                "div.swal2-content"
            ]
            
            for selector in text_selectors:
                try:
                    text_element = popup_element.find_element(By.CSS_SELECTOR, selector)
                    text = text_element.text.strip()
                    if text:
                        return text
                except NoSuchElementException:
                    continue
            
            # 如果上述方法都失敗，嘗試直接獲取彈窗文字
            return popup_element.text.strip()
            
        except Exception as e:
            self.logger.error(f"提取錯誤文字時發生異常: {e}")
            return None
    
    def _analyze_error_type(self, error_text: str) -> Dict:
        """分析錯誤類型並返回相應的處理建議"""
        error_text_lower = error_text.lower()
        
        for error_type, pattern in self.error_patterns.items():
            # 檢查是否包含關鍵字
            if all(keyword in error_text for keyword in pattern["keywords"]):
                return {
                    "type": error_type,
                    "action": pattern["action"],
                    "description": pattern["description"],
                    "pattern": pattern,
                    "confidence": "high"
                }
        
        # 如果沒有匹配到特定模式，返回通用錯誤
        return {
            "type": "unknown",
            "action": "manual_intervention",
            "description": "未知錯誤類型，需要人工介入",
            "pattern": None,
            "confidence": "low"
        }
    
    def handle_error(self, error_info: Dict) -> bool:
        """
        根據錯誤類型執行相應的處理動作
        
        Args:
            error_info: 錯誤信息字典
            
        Returns:
            bool: 處理是否成功
        """
        if not error_info:
            return False
        
        error_type = error_info.get("type")
        action = error_info.get("action")
        
        self.logger.info(f"開始處理錯誤: {error_type}, 動作: {action}")
        
        try:
            if action == "wait_and_retry":
                return self._handle_time_not_open(error_info)
            elif action == "switch_factory":
                return self._handle_factory_full(error_info)
            elif action == "retry_or_manual":
                return self._handle_general_failure(error_info)
            else:
                self.logger.warning(f"未知的處理動作: {action}")
                return False
                
        except Exception as e:
            self.logger.error(f"處理錯誤時發生異常: {e}")
            return False
    
    def _handle_time_not_open(self, error_info: Dict) -> bool:
        """處理預約時間未開放錯誤"""
        wait_time = error_info.get("pattern", {}).get("wait_time", 60)
        
        self.logger.info(f"預約時間未開放，等待 {wait_time} 秒後重試")
        
        # 關閉彈窗
        self._close_popup()
        
        # 等待指定時間
        time.sleep(wait_time)
        
        return True
    
    def _handle_factory_full(self, error_info: Dict) -> bool:
        """處理工廠名額已滿錯誤"""
        self.logger.warning("仁武廠名額已滿，需要切換到其他工廠")
        
        # 關閉彈窗
        self._close_popup()
        
        # 這裡應該實現切換工廠的邏輯
        # 目前先返回 False，表示需要人工介入
        return False
    
    def _handle_general_failure(self, error_info: Dict) -> bool:
        """處理一般送出失敗錯誤"""
        self.logger.warning("一般送出失敗，嘗試重新送出")
        
        # 關閉彈窗
        self._close_popup()
        
        # 等待一小段時間後重試
        time.sleep(2)
        
        return True
    
    def _close_popup(self) -> bool:
        """關閉 SweetAlert2 彈窗"""
        try:
            # 嘗試多種方式關閉彈窗
            close_selectors = [
                "button.swal2-confirm",
                "button.swal2-cancel", 
                ".swal2-close",
                "button:contains('確定')",
                "button:contains('OK')"
            ]
            
            for selector in close_selectors:
                try:
                    close_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if close_button.is_displayed():
                        close_button.click()
                        self.logger.info(f"使用選擇器關閉彈窗: {selector}")
                        time.sleep(1)
                        return True
                except NoSuchElementException:
                    continue
            
            # 如果找不到關閉按鈕，嘗試按 ESC 鍵
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            self.logger.info("使用 ESC 鍵關閉彈窗")
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"關閉彈窗時發生異常: {e}")
            return False
    
    def wait_for_popup_disappear(self, timeout: int = 10) -> bool:
        """等待彈窗消失"""
        try:
            WebDriverWait(self.driver, timeout).until_not(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.swal2-popup"))
            )
            return True
        except TimeoutException:
            return False
