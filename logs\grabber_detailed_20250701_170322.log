2025-07-01 17:03:22,505 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_170322.log
2025-07-01 17:03:36,704 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 17:03:36,704 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 17:03:36,762 - DEBUG - chromedriver not found in PATH
2025-07-01 17:03:36,762 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:03:36,762 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 17:03:36,762 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 17:03:36,763 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 17:03:36,763 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 17:03:36,763 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:03:36,768 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 15072 using 0 to output -3
2025-07-01 17:03:37,284 - DEBUG - POST http://localhost:55805/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 17:03:37,284 - DEBUG - Starting new HTTP connection (1): localhost:55805
2025-07-01 17:03:37,818 - DEBUG - http://localhost:55805 "POST /session HTTP/1.1" 200 0
2025-07-01 17:03:37,819 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir15072_1127905294"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55808"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"899169c33740e16113a94e239c6f2c88"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:37,819 - DEBUG - Finished Request
2025-07-01 17:03:37,820 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 17:03:39,207 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:39,208 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:39,208 - DEBUG - Finished Request
2025-07-01 17:03:39,208 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 17:03:39,209 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 17:03:39,216 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/execute/sync HTTP/1.1" 200 0
2025-07-01 17:03:39,216 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:39,216 - DEBUG - Finished Request
2025-07-01 17:03:39,216 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 17:03:39,217 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:39,247 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:39,249 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:39,249 - DEBUG - Finished Request
2025-07-01 17:03:40,250 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:40,256 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:40,256 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:40,257 - DEBUG - Finished Request
2025-07-01 17:03:41,258 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:41,266 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:41,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:41,266 - DEBUG - Finished Request
2025-07-01 17:03:42,268 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:42,276 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:42,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:42,277 - DEBUG - Finished Request
2025-07-01 17:03:43,278 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:43,285 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:43,285 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:43,285 - DEBUG - Finished Request
2025-07-01 17:03:44,286 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:44,293 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:44,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:44,294 - DEBUG - Finished Request
2025-07-01 17:03:45,294 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:45,303 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:45,303 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:45,303 - DEBUG - Finished Request
2025-07-01 17:03:46,305 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:46,312 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:46,313 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:46,313 - DEBUG - Finished Request
2025-07-01 17:03:47,314 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:47,322 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:47,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:47,322 - DEBUG - Finished Request
2025-07-01 17:03:48,324 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:48,331 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:48,331 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:48,332 - DEBUG - Finished Request
2025-07-01 17:03:49,333 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:49,340 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:49,340 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:49,340 - DEBUG - Finished Request
2025-07-01 17:03:50,342 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:50,349 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:50,350 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:50,350 - DEBUG - Finished Request
2025-07-01 17:03:51,350 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:51,357 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:51,357 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:51,358 - DEBUG - Finished Request
2025-07-01 17:03:52,359 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:52,367 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:52,367 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:52,367 - DEBUG - Finished Request
2025-07-01 17:03:53,368 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:53,376 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:53,376 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:53,377 - DEBUG - Finished Request
2025-07-01 17:03:54,377 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:54,385 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:54,385 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:54,385 - DEBUG - Finished Request
2025-07-01 17:03:55,386 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:55,395 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:55,395 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:55,396 - DEBUG - Finished Request
2025-07-01 17:03:56,398 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:56,406 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:56,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:56,407 - DEBUG - Finished Request
2025-07-01 17:03:57,407 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:57,414 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:57,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:57,415 - DEBUG - Finished Request
2025-07-01 17:03:58,416 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:58,425 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:58,425 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:58,425 - DEBUG - Finished Request
2025-07-01 17:03:59,426 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:03:59,434 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:03:59,434 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:03:59,434 - DEBUG - Finished Request
2025-07-01 17:04:00,436 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:00,445 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:00,445 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:00,446 - DEBUG - Finished Request
2025-07-01 17:04:01,446 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:02,118 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:02,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:02,119 - DEBUG - Finished Request
2025-07-01 17:04:03,120 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:03,127 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:03,128 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:03,128 - DEBUG - Finished Request
2025-07-01 17:04:04,128 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:04,134 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:04,134 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:04,134 - DEBUG - Finished Request
2025-07-01 17:04:05,135 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:05,142 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:05,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:05,142 - DEBUG - Finished Request
2025-07-01 17:04:06,143 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:06,150 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:06,151 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:06,151 - DEBUG - Finished Request
2025-07-01 17:04:07,152 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:07,159 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:07,159 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:07,159 - DEBUG - Finished Request
2025-07-01 17:04:08,160 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:08,166 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:08,167 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:08,167 - DEBUG - Finished Request
2025-07-01 17:04:09,168 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:09,174 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:09,174 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:09,174 - DEBUG - Finished Request
2025-07-01 17:04:10,175 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:10,181 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:10,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:10,181 - DEBUG - Finished Request
2025-07-01 17:04:11,182 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:11,187 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:11,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:11,187 - DEBUG - Finished Request
2025-07-01 17:04:12,188 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:12,194 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:12,195 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:12,195 - DEBUG - Finished Request
2025-07-01 17:04:13,196 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:13,205 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:13,205 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:13,205 - DEBUG - Finished Request
2025-07-01 17:04:14,206 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:14,211 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:14,211 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:14,211 - DEBUG - Finished Request
2025-07-01 17:04:15,212 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:15,219 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:15,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:15,219 - DEBUG - Finished Request
2025-07-01 17:04:16,220 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:16,225 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:16,225 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:16,226 - DEBUG - Finished Request
2025-07-01 17:04:17,227 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:17,233 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:17,233 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:17,233 - DEBUG - Finished Request
2025-07-01 17:04:18,234 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:18,239 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:18,240 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:18,240 - DEBUG - Finished Request
2025-07-01 17:04:19,241 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:19,247 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:19,247 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:19,247 - DEBUG - Finished Request
2025-07-01 17:04:20,249 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:20,254 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:20,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:20,255 - DEBUG - Finished Request
2025-07-01 17:04:21,256 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:21,264 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:21,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:21,264 - DEBUG - Finished Request
2025-07-01 17:04:22,266 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:22,273 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:22,273 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:22,273 - DEBUG - Finished Request
2025-07-01 17:04:23,275 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:23,280 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:23,281 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:23,281 - DEBUG - Finished Request
2025-07-01 17:04:24,283 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:24,289 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:24,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:24,289 - DEBUG - Finished Request
2025-07-01 17:04:25,290 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:25,297 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:25,297 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:25,297 - DEBUG - Finished Request
2025-07-01 17:04:26,298 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:26,305 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:26,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:26,305 - DEBUG - Finished Request
2025-07-01 17:04:27,306 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:27,310 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:27,311 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:27,311 - DEBUG - Finished Request
2025-07-01 17:04:28,312 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:28,318 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:28,318 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:28,319 - DEBUG - Finished Request
2025-07-01 17:04:29,320 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:29,328 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:29,328 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:29,328 - DEBUG - Finished Request
2025-07-01 17:04:30,329 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:30,335 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:30,336 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:30,336 - DEBUG - Finished Request
2025-07-01 17:04:30,544 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:30,564 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:30,564 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:30,564 - DEBUG - Finished Request
2025-07-01 17:04:31,065 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:31,074 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:31,074 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:31,074 - DEBUG - Finished Request
2025-07-01 17:04:31,337 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:31,342 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:31,343 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:31,343 - DEBUG - Finished Request
2025-07-01 17:04:31,575 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:31,583 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:31,583 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:31,583 - DEBUG - Finished Request
2025-07-01 17:04:32,085 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:32,096 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:32,096 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:32,097 - DEBUG - Finished Request
2025-07-01 17:04:32,344 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:32,351 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:32,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:32,351 - DEBUG - Finished Request
2025-07-01 17:04:32,598 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:32,609 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:32,609 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:32,609 - DEBUG - Finished Request
2025-07-01 17:04:33,110 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:33,121 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:33,121 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:33,122 - DEBUG - Finished Request
2025-07-01 17:04:33,352 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:33,358 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:33,358 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:33,358 - DEBUG - Finished Request
2025-07-01 17:04:33,623 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:33,633 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:33,633 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:33,634 - DEBUG - Finished Request
2025-07-01 17:04:34,134 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:34,143 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:34,144 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:34,144 - DEBUG - Finished Request
2025-07-01 17:04:34,359 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:34,366 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:34,366 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:34,366 - DEBUG - Finished Request
2025-07-01 17:04:34,645 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:34,654 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:34,656 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:34,656 - DEBUG - Finished Request
2025-07-01 17:04:35,157 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:35,165 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:35,165 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:35,166 - DEBUG - Finished Request
2025-07-01 17:04:35,368 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:35,376 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:35,376 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:35,377 - DEBUG - Finished Request
2025-07-01 17:04:35,667 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:35,676 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:35,677 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:35,677 - DEBUG - Finished Request
2025-07-01 17:04:36,178 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:36,185 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:36,185 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:36,185 - DEBUG - Finished Request
2025-07-01 17:04:36,378 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:36,386 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:36,387 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:36,387 - DEBUG - Finished Request
2025-07-01 17:04:36,686 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:36,696 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:36,697 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:36,697 - DEBUG - Finished Request
2025-07-01 17:04:37,198 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:37,207 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:37,207 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:37,207 - DEBUG - Finished Request
2025-07-01 17:04:37,387 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:37,394 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:37,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:37,394 - DEBUG - Finished Request
2025-07-01 17:04:37,708 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:37,717 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:37,717 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:37,717 - DEBUG - Finished Request
2025-07-01 17:04:38,218 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:38,226 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:38,226 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:38,226 - DEBUG - Finished Request
2025-07-01 17:04:38,396 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:38,403 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:38,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:38,403 - DEBUG - Finished Request
2025-07-01 17:04:38,727 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:38,736 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:38,737 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:38,737 - DEBUG - Finished Request
2025-07-01 17:04:39,237 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:39,245 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:39,245 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:39,245 - DEBUG - Finished Request
2025-07-01 17:04:39,404 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:39,409 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:39,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:39,410 - DEBUG - Finished Request
2025-07-01 17:04:39,746 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:39,754 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:39,755 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:39,755 - DEBUG - Finished Request
2025-07-01 17:04:40,255 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:40,264 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:40,264 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:40,265 - DEBUG - Finished Request
2025-07-01 17:04:40,410 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:40,416 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:40,416 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:40,416 - DEBUG - Finished Request
2025-07-01 17:04:40,765 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:40,774 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:40,774 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:40,775 - DEBUG - Finished Request
2025-07-01 17:04:41,275 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:41,283 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:41,283 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:41,283 - DEBUG - Finished Request
2025-07-01 17:04:41,417 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:41,424 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:41,424 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:41,424 - DEBUG - Finished Request
2025-07-01 17:04:41,784 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:41,793 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:41,793 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:41,794 - DEBUG - Finished Request
2025-07-01 17:04:42,295 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:42,302 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:42,303 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:42,303 - DEBUG - Finished Request
2025-07-01 17:04:42,425 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:42,431 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:42,431 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:42,433 - DEBUG - Finished Request
2025-07-01 17:04:42,803 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:42,810 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:42,811 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:42,811 - DEBUG - Finished Request
2025-07-01 17:04:43,312 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:43,319 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:43,319 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:43,319 - DEBUG - Finished Request
2025-07-01 17:04:43,433 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:43,440 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:43,440 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:43,440 - DEBUG - Finished Request
2025-07-01 17:04:43,821 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:43,829 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:43,829 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:43,829 - DEBUG - Finished Request
2025-07-01 17:04:44,330 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:44,340 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:44,340 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:44,340 - DEBUG - Finished Request
2025-07-01 17:04:44,442 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:44,449 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:44,449 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:44,450 - DEBUG - Finished Request
2025-07-01 17:04:44,842 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:44,850 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:44,850 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:44,850 - DEBUG - Finished Request
2025-07-01 17:04:45,351 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:45,361 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:45,361 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:45,361 - DEBUG - Finished Request
2025-07-01 17:04:45,450 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:45,457 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:45,457 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:45,458 - DEBUG - Finished Request
2025-07-01 17:04:45,862 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:45,873 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:45,873 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:45,874 - DEBUG - Finished Request
2025-07-01 17:04:46,375 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:46,385 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:46,386 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:46,386 - DEBUG - Finished Request
2025-07-01 17:04:46,458 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:46,467 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:46,467 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:46,467 - DEBUG - Finished Request
2025-07-01 17:04:46,887 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:46,895 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:46,896 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:46,896 - DEBUG - Finished Request
2025-07-01 17:04:47,398 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:47,408 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:47,408 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:47,408 - DEBUG - Finished Request
2025-07-01 17:04:47,468 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:47,473 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:47,473 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:47,474 - DEBUG - Finished Request
2025-07-01 17:04:47,909 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:47,921 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:47,921 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:47,921 - DEBUG - Finished Request
2025-07-01 17:04:48,422 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:48,436 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:48,436 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:48,436 - DEBUG - Finished Request
2025-07-01 17:04:48,474 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:48,482 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:48,482 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:48,482 - DEBUG - Finished Request
2025-07-01 17:04:48,937 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:48,949 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:48,949 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:48,949 - DEBUG - Finished Request
2025-07-01 17:04:49,450 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:49,462 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:49,462 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:49,462 - DEBUG - Finished Request
2025-07-01 17:04:49,483 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:49,489 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:49,489 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:49,489 - DEBUG - Finished Request
2025-07-01 17:04:49,963 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:49,973 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:49,973 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:49,974 - DEBUG - Finished Request
2025-07-01 17:04:50,474 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:50,483 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:50,483 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:50,483 - DEBUG - Finished Request
2025-07-01 17:04:50,491 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:50,495 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:50,495 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:50,496 - DEBUG - Finished Request
2025-07-01 17:04:50,984 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:50,993 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:50,993 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:50,993 - DEBUG - Finished Request
2025-07-01 17:04:51,494 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:51,496 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:51,496 - DEBUG - Starting new HTTP connection (2): localhost:55805
2025-07-01 17:04:51,505 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:51,505 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:51,505 - DEBUG - Finished Request
2025-07-01 17:04:51,519 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:51,520 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:04:51,520 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:51,520 - DEBUG - Finished Request
2025-07-01 17:04:52,006 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:52,017 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:52,017 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:52,017 - DEBUG - Finished Request
2025-07-01 17:04:52,518 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:52,521 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:52,521 - DEBUG - Starting new HTTP connection (3): localhost:55805
2025-07-01 17:04:52,529 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:52,529 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:52,529 - DEBUG - Finished Request
2025-07-01 17:04:52,551 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:52,551 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:04:52,553 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:52,553 - DEBUG - Finished Request
2025-07-01 17:04:53,031 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:53,040 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:53,041 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:53,041 - DEBUG - Finished Request
2025-07-01 17:04:53,541 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:53,554 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:53,554 - DEBUG - Starting new HTTP connection (4): localhost:55805
2025-07-01 17:04:53,555 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:53,555 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:53,556 - DEBUG - Finished Request
2025-07-01 17:04:53,589 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:53,590 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:04:53,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:53,590 - DEBUG - Finished Request
2025-07-01 17:04:54,057 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:54,066 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:54,067 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:54,067 - DEBUG - Finished Request
2025-07-01 17:04:54,568 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:54,579 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:54,579 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:54,580 - DEBUG - Finished Request
2025-07-01 17:04:54,591 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:54,599 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:54,599 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:54,600 - DEBUG - Finished Request
2025-07-01 17:04:55,081 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:55,091 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:55,091 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:55,092 - DEBUG - Finished Request
2025-07-01 17:04:55,592 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:55,601 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:55,601 - DEBUG - Starting new HTTP connection (5): localhost:55805
2025-07-01 17:04:55,602 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:55,603 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:55,603 - DEBUG - Finished Request
2025-07-01 17:04:55,620 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:55,620 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:04:55,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:55,620 - DEBUG - Finished Request
2025-07-01 17:04:56,104 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:56,114 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:56,114 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:56,115 - DEBUG - Finished Request
2025-07-01 17:04:56,616 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:56,621 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:56,621 - DEBUG - Starting new HTTP connection (6): localhost:55805
2025-07-01 17:04:56,626 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:56,626 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:56,627 - DEBUG - Finished Request
2025-07-01 17:04:56,650 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:56,650 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:04:56,650 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:56,650 - DEBUG - Finished Request
2025-07-01 17:04:57,128 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:57,137 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:57,137 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:57,137 - DEBUG - Finished Request
2025-07-01 17:04:57,638 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:57,647 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:57,648 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:57,648 - DEBUG - Finished Request
2025-07-01 17:04:57,651 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:57,658 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:57,658 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:57,658 - DEBUG - Finished Request
2025-07-01 17:04:58,149 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:58,159 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:58,159 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:58,160 - DEBUG - Finished Request
2025-07-01 17:04:58,659 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:58,660 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:58,660 - DEBUG - Starting new HTTP connection (7): localhost:55805
2025-07-01 17:04:58,669 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:58,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:58,669 - DEBUG - Finished Request
2025-07-01 17:04:58,691 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:58,692 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:04:58,692 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:58,692 - DEBUG - Finished Request
2025-07-01 17:04:59,194 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:59,202 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:59,202 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:59,202 - DEBUG - Finished Request
2025-07-01 17:04:59,670 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:04:59,676 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:04:59,676 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:59,676 - DEBUG - Finished Request
2025-07-01 17:04:59,703 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:04:59,712 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:04:59,712 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:04:59,712 - DEBUG - Finished Request
2025-07-01 17:05:00,213 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:05:00,221 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:00,221 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:00,221 - DEBUG - Finished Request
2025-07-01 17:05:00,677 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:05:00,685 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:05:00,685 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:00,685 - DEBUG - Finished Request
2025-07-01 17:05:00,723 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:05:00,731 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:00,731 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:00,731 - DEBUG - Finished Request
2025-07-01 17:05:00,733 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:00,742 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:00,742 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:00,742 - DEBUG - Finished Request
2025-07-01 17:05:01,243 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:01,253 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:01,253 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:01,254 - DEBUG - Finished Request
2025-07-01 17:05:01,685 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:05:01,691 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:05:01,691 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:01,691 - DEBUG - Finished Request
2025-07-01 17:05:01,755 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:01,764 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:01,764 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:01,764 - DEBUG - Finished Request
2025-07-01 17:05:02,265 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:02,273 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:02,274 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:02,274 - DEBUG - Finished Request
2025-07-01 17:05:02,693 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:05:02,699 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 200 0
2025-07-01 17:05:02,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:02,700 - DEBUG - Finished Request
2025-07-01 17:05:02,774 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:02,784 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:02,784 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:02,784 - DEBUG - Finished Request
2025-07-01 17:05:03,285 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:03,292 - DEBUG - http://localhost:55805 "POST /session/899169c33740e16113a94e239c6f2c88/element HTTP/1.1" 404 0
2025-07-01 17:05:03,292 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:03,292 - DEBUG - Finished Request
2025-07-01 17:05:03,701 - DEBUG - GET http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/url {}
2025-07-01 17:05:03,702 - DEBUG - http://localhost:55805 "GET /session/899169c33740e16113a94e239c6f2c88/url HTTP/1.1" 404 0
2025-07-01 17:05:03,702 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:03,703 - DEBUG - Finished Request
2025-07-01 17:05:03,704 - DEBUG - DELETE http://localhost:55805/session/899169c33740e16113a94e239c6f2c88 {}
2025-07-01 17:05:03,735 - DEBUG - http://localhost:55805 "DELETE /session/899169c33740e16113a94e239c6f2c88 HTTP/1.1" 200 0
2025-07-01 17:05:03,735 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:05:03,735 - DEBUG - Finished Request
2025-07-01 17:05:03,794 - DEBUG - POST http://localhost:55805/session/899169c33740e16113a94e239c6f2c88/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:05:03,794 - DEBUG - Starting new HTTP connection (1): localhost:55805
2025-07-01 17:05:07,886 - DEBUG - Incremented Retry for (url='/session/899169c33740e16113a94e239c6f2c88/element'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-01 17:05:07,886 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002214E278F90>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/899169c33740e16113a94e239c6f2c88/element
2025-07-01 17:05:07,887 - DEBUG - Starting new HTTP connection (2): localhost:55805
2025-07-01 17:05:11,946 - DEBUG - Incremented Retry for (url='/session/899169c33740e16113a94e239c6f2c88/element'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-01 17:05:11,947 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002214E279E50>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/899169c33740e16113a94e239c6f2c88/element
2025-07-01 17:05:11,947 - DEBUG - Starting new HTTP connection (3): localhost:55805
2025-07-01 17:05:16,000 - DEBUG - Incremented Retry for (url='/session/899169c33740e16113a94e239c6f2c88/element'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-01 17:05:16,000 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002214E27A8D0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/899169c33740e16113a94e239c6f2c88/element
2025-07-01 17:05:16,001 - DEBUG - Starting new HTTP connection (4): localhost:55805
2025-07-01 17:05:22,089 - ERROR - 尋找編輯按鈕失敗: 'NoneType' object has no attribute 'current_url'
