import json
import os
from tkinter import messagebox

class RTTConfigManager:
    def __init__(self):
        self.config_path = 'config/rtt_config.json'
        self.rtt_module_version = "1.3.0"  # 跟隨 rtt_predictor.py 版本
        self.default_config = {
            "rtt_module_version": self.rtt_module_version,
            "freq": 2,
            "duration": 60,
            "server_url": "https://wmc.kcg.gov.tw/"
        }

    def load_config(self):
        """載入 RTT 設定"""
        try:
            if not os.path.exists(self.config_path):
                # 如果設定檔不存在，建立預設設定檔
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                self.save_config(self.default_config)
                return self.default_config

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 檢查版本相容性
            if not self.check_version_compatibility(config):
                print(f"[WARNING] 設定檔版本與 RTT 模組版本不匹配，使用預設值")
                return self.default_config
                
            # 優先使用 JSON 檔案內容（使用者設定）
            return config
        except Exception as e:
            print(f"[ERROR] 載入 RTT 設定失敗: {str(e)}")
            return self.default_config

    def check_version_compatibility(self, config):
        """檢查設定檔版本與 RTT 模組版本是否相容"""
        config_version = config.get('rtt_module_version', 'unknown')
        if config_version != self.rtt_module_version:
            print(f"[WARNING] 設定檔版本 ({config_version}) 與 RTT 模組版本 ({self.rtt_module_version}) 不匹配")
            return False
        return True

    def save_config(self, config):
        """儲存 RTT 設定"""
        try:
            # 確保版本號正確
            config['rtt_module_version'] = self.rtt_module_version
            
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"[ERROR] 儲存 RTT 設定失敗: {str(e)}")
            return False

    def get_model_config(self, model):
        """取得指定 model 的設定，目前所有模型使用相同採樣參數"""
        config = self.load_config()
        return config  # 直接回傳整個設定

    def get_server_url(self):
        """取得伺服器網址"""
        config = self.load_config()
        return config['server_url'] 