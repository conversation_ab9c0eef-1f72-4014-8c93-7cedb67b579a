2025-07-02 09:16:17,989 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_091617.log
2025-07-02 09:16:22,353 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 09:16:22,354 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 09:16:22,430 - DEBUG - chromedriver not found in PATH
2025-07-02 09:16:22,430 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:16:22,431 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 09:16:22,431 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 09:16:22,431 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 09:16:22,431 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 09:16:22,431 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:16:22,438 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 27288 using 0 to output -3
2025-07-02 09:16:22,971 - DEBUG - POST http://localhost:53752/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 09:16:22,972 - DEBUG - Starting new HTTP connection (1): localhost:53752
2025-07-02 09:16:23,516 - DEBUG - http://localhost:53752 "POST /session HTTP/1.1" 200 0
2025-07-02 09:16:23,516 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir27288_1090801866"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53756"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"a49bb51e7bc5bfae16736e08fea5a814"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:23,516 - DEBUG - Finished Request
2025-07-02 09:16:23,517 - DEBUG - POST http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 09:16:24,896 - DEBUG - http://localhost:53752 "POST /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:24,896 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:24,896 - DEBUG - Finished Request
2025-07-02 09:16:24,897 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 09:16:24,897 - DEBUG - POST http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 09:16:24,905 - DEBUG - http://localhost:53752 "POST /session/a49bb51e7bc5bfae16736e08fea5a814/execute/sync HTTP/1.1" 200 0
2025-07-02 09:16:24,905 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:24,905 - DEBUG - Finished Request
2025-07-02 09:16:24,905 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-02 09:16:24,906 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:24,945 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:24,945 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:24,946 - DEBUG - Finished Request
2025-07-02 09:16:25,947 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:25,953 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:25,953 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:25,953 - DEBUG - Finished Request
2025-07-02 09:16:26,954 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:26,960 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:26,960 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:26,960 - DEBUG - Finished Request
2025-07-02 09:16:27,962 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:27,969 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:27,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:27,970 - DEBUG - Finished Request
2025-07-02 09:16:28,970 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:28,977 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:28,977 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:28,977 - DEBUG - Finished Request
2025-07-02 09:16:29,978 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:29,986 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:29,986 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:29,987 - DEBUG - Finished Request
2025-07-02 09:16:30,988 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:30,996 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:30,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:30,997 - DEBUG - Finished Request
2025-07-02 09:16:31,998 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:32,005 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:32,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:32,006 - DEBUG - Finished Request
2025-07-02 09:16:33,007 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:33,015 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:33,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:33,016 - DEBUG - Finished Request
2025-07-02 09:16:34,016 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:34,024 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:34,025 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:34,025 - DEBUG - Finished Request
2025-07-02 09:16:35,026 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:35,034 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:35,034 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:35,035 - DEBUG - Finished Request
2025-07-02 09:16:36,035 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:36,044 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:36,045 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:36,045 - DEBUG - Finished Request
2025-07-02 09:16:37,046 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:37,054 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:37,054 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:37,055 - DEBUG - Finished Request
2025-07-02 09:16:38,055 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:38,063 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:38,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:38,064 - DEBUG - Finished Request
2025-07-02 09:16:39,066 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:39,074 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:39,074 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:39,075 - DEBUG - Finished Request
2025-07-02 09:16:40,076 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:40,084 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:40,085 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:40,085 - DEBUG - Finished Request
2025-07-02 09:16:41,086 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:41,093 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:41,094 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:41,094 - DEBUG - Finished Request
2025-07-02 09:16:42,095 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:42,102 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:42,103 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:42,103 - DEBUG - Finished Request
2025-07-02 09:16:43,105 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:43,127 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:43,128 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:43,128 - DEBUG - Finished Request
2025-07-02 09:16:44,128 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:44,137 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:44,137 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:44,137 - DEBUG - Finished Request
2025-07-02 09:16:45,139 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:45,147 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:45,147 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:45,147 - DEBUG - Finished Request
2025-07-02 09:16:46,148 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:46,156 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:46,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:46,156 - DEBUG - Finished Request
2025-07-02 09:16:47,157 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:47,168 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:47,168 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:47,169 - DEBUG - Finished Request
2025-07-02 09:16:48,170 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:48,177 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:48,177 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:48,177 - DEBUG - Finished Request
2025-07-02 09:16:49,178 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:49,186 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:49,186 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:49,187 - DEBUG - Finished Request
2025-07-02 09:16:50,187 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:50,195 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:50,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:50,196 - DEBUG - Finished Request
2025-07-02 09:16:51,197 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:51,204 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:51,205 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:51,205 - DEBUG - Finished Request
2025-07-02 09:16:52,206 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:52,213 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:52,213 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:52,213 - DEBUG - Finished Request
2025-07-02 09:16:53,214 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:53,221 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:53,221 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:53,221 - DEBUG - Finished Request
2025-07-02 09:16:54,223 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:54,232 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:54,232 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:54,232 - DEBUG - Finished Request
2025-07-02 09:16:55,233 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:55,239 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:55,240 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:55,240 - DEBUG - Finished Request
2025-07-02 09:16:56,241 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:56,247 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:56,248 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:56,248 - DEBUG - Finished Request
2025-07-02 09:16:57,249 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:57,255 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:57,255 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:57,255 - DEBUG - Finished Request
2025-07-02 09:16:58,256 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:58,263 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:58,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:58,263 - DEBUG - Finished Request
2025-07-02 09:16:59,264 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:16:59,270 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:16:59,270 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:16:59,270 - DEBUG - Finished Request
2025-07-02 09:17:00,271 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:00,276 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:00,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:00,276 - DEBUG - Finished Request
2025-07-02 09:17:01,277 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:01,282 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:01,282 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:01,282 - DEBUG - Finished Request
2025-07-02 09:17:02,283 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:02,292 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:02,292 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:02,292 - DEBUG - Finished Request
2025-07-02 09:17:03,293 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:03,301 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:03,301 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:03,301 - DEBUG - Finished Request
2025-07-02 09:17:04,302 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:04,336 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:04,336 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:04,336 - DEBUG - Finished Request
2025-07-02 09:17:05,337 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:05,344 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:05,344 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:05,344 - DEBUG - Finished Request
2025-07-02 09:17:06,344 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:06,351 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:06,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:06,351 - DEBUG - Finished Request
2025-07-02 09:17:07,352 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:07,358 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:07,358 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:07,358 - DEBUG - Finished Request
2025-07-02 09:17:08,359 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:08,365 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:08,365 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:08,365 - DEBUG - Finished Request
2025-07-02 09:17:09,366 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:09,372 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:09,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:09,372 - DEBUG - Finished Request
2025-07-02 09:17:10,373 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:10,380 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:10,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:10,380 - DEBUG - Finished Request
2025-07-02 09:17:11,381 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:11,386 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:11,386 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:11,386 - DEBUG - Finished Request
2025-07-02 09:17:12,388 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:12,394 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:12,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:12,394 - DEBUG - Finished Request
2025-07-02 09:17:13,395 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:13,403 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:13,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:13,403 - DEBUG - Finished Request
2025-07-02 09:17:14,404 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:14,410 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:14,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:14,410 - DEBUG - Finished Request
2025-07-02 09:17:15,411 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:15,417 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:15,417 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:15,417 - DEBUG - Finished Request
2025-07-02 09:17:16,419 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:16,423 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:16,424 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:16,424 - DEBUG - Finished Request
2025-07-02 09:17:17,425 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:17,432 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:17,432 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:17,432 - DEBUG - Finished Request
2025-07-02 09:17:18,433 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:18,440 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:18,440 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:18,441 - DEBUG - Finished Request
2025-07-02 09:17:19,441 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:19,447 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:19,448 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:19,448 - DEBUG - Finished Request
2025-07-02 09:17:20,449 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:20,455 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:20,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:20,456 - DEBUG - Finished Request
2025-07-02 09:17:21,457 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:21,465 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:21,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:21,465 - DEBUG - Finished Request
2025-07-02 09:17:22,466 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:22,470 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:22,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:22,471 - DEBUG - Finished Request
2025-07-02 09:17:23,472 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:23,477 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:23,477 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:23,478 - DEBUG - Finished Request
2025-07-02 09:17:24,479 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:24,489 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:24,489 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:24,489 - DEBUG - Finished Request
2025-07-02 09:17:25,490 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:25,497 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:25,498 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:25,498 - DEBUG - Finished Request
2025-07-02 09:17:26,499 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:26,506 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:26,506 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:26,506 - DEBUG - Finished Request
2025-07-02 09:17:27,507 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:27,518 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:27,518 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:27,519 - DEBUG - Finished Request
2025-07-02 09:17:28,520 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:28,527 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:28,527 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:28,527 - DEBUG - Finished Request
2025-07-02 09:17:29,528 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:29,535 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:29,536 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:29,536 - DEBUG - Finished Request
2025-07-02 09:17:30,537 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:30,545 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:30,545 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:30,546 - DEBUG - Finished Request
2025-07-02 09:17:31,547 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:31,556 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:31,556 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:31,556 - DEBUG - Finished Request
2025-07-02 09:17:32,557 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:32,565 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:32,565 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:32,566 - DEBUG - Finished Request
2025-07-02 09:17:33,567 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:33,575 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:33,575 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:33,575 - DEBUG - Finished Request
2025-07-02 09:17:34,576 - DEBUG - GET http://localhost:53752/session/a49bb51e7bc5bfae16736e08fea5a814/url {}
2025-07-02 09:17:34,586 - DEBUG - http://localhost:53752 "GET /session/a49bb51e7bc5bfae16736e08fea5a814/url HTTP/1.1" 200 0
2025-07-02 09:17:34,586 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:17:34,586 - DEBUG - Finished Request
