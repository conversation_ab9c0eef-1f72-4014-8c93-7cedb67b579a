#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
專門檢測送出按鈕的調試腳本
Debug Submit Button Detection
"""

import os
import sys
import time
import tkinter as tk
from tkinter import messagebox, scrolledtext
from datetime import datetime
import threading

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SubmitButtonDebugGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 送出按鈕檢測調試")
        self.root.geometry("900x700")
        
        self.setup_ui()
        
    def setup_ui(self):
        """設置 UI"""
        # 標題
        title_label = tk.Label(self.root, text="🔍 送出按鈕檢測調試", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 說明
        info_text = """
此工具專門用於調試送出按鈕檢測問題。
請確保您已經在瀏覽器中打開編輯彈窗，然後點擊下方按鈕進行檢測。
        """
        
        info_label = tk.Label(self.root, text=info_text, 
                             font=("Arial", 10), justify="center")
        info_label.pack(pady=5)
        
        # 按鈕區域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 檢測按鈕
        self.detect_btn = tk.Button(button_frame, text="🔍 檢測送出按鈕", 
                                   command=self.detect_submit_button,
                                   font=("Arial", 12), bg="lightblue")
        self.detect_btn.pack(side="left", padx=10)
        
        # 檢測所有元素按鈕
        self.detect_all_btn = tk.Button(button_frame, text="📋 檢測所有元素", 
                                       command=self.detect_all_elements,
                                       font=("Arial", 12), bg="lightgreen")
        self.detect_all_btn.pack(side="left", padx=10)
        
        # 關閉按鈕
        self.close_btn = tk.Button(button_frame, text="❌ 關閉", 
                                 command=self.close_app,
                                 font=("Arial", 12), bg="lightcoral")
        self.close_btn.pack(side="right", padx=10)
        
        # 日誌顯示區域
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(log_frame, text="檢測日誌:", font=("Arial", 12, "bold")).pack(anchor="w")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=25, width=100)
        self.log_text.pack(fill="both", expand=True)
        
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def detect_submit_button(self):
        """檢測送出按鈕"""
        try:
            self.log("🔍 開始檢測送出按鈕...")
            self.detect_btn.config(state="disabled")
            
            def detection_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 檢測當前頁面信息
                    current_url = driver.current_url
                    page_title = driver.title
                    self.root.after(0, lambda: self.log(f"📍 當前頁面: {current_url}"))
                    self.root.after(0, lambda: self.log(f"📍 頁面標題: {page_title}"))
                    
                    # 檢測 iframe
                    iframes = driver.find_elements(By.TAG_NAME, "iframe")
                    self.root.after(0, lambda: self.log(f"📊 檢測到 {len(iframes)} 個 iframe"))
                    
                    # 檢測每個 iframe
                    for i, iframe in enumerate(iframes):
                        try:
                            self.root.after(0, lambda i=i: self.log(f"🔍 檢測 iframe {i+1}..."))
                            
                            # 切換到 iframe
                            driver.switch_to.frame(iframe)
                            
                            # 檢測 iframe 內容
                            iframe_content = driver.execute_script("return document.body.innerText || '';")
                            has_edit_content = any(keyword in iframe_content for keyword in ['編輯進廠確認單', '送出', '取消', '驗證碼'])
                            
                            self.root.after(0, lambda i=i, has=has_edit_content: self.log(f"  iframe {i+1} 包含編輯內容: {has}"))
                            
                            if has_edit_content:
                                self.root.after(0, lambda i=i: self.log(f"🎯 在 iframe {i+1} 中搜尋送出按鈕..."))
                                
                                # 搜尋送出相關的所有元素
                                submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok', '確定']
                                found_elements = []
                                
                                for keyword in submit_keywords:
                                    try:
                                        # 使用多種方式搜尋
                                        xpath_queries = [
                                            f"//*[contains(text(), '{keyword}')]",
                                            f"//*[@value='{keyword}']",
                                            f"//*[@title='{keyword}']",
                                            f"//button[contains(text(), '{keyword}')]",
                                            f"//input[@value='{keyword}']"
                                        ]
                                        
                                        for xpath in xpath_queries:
                                            try:
                                                elements = driver.find_elements(By.XPATH, xpath)
                                                if elements:
                                                    found_elements.extend(elements)
                                                    self.root.after(0, lambda k=keyword, x=xpath, c=len(elements): 
                                                                   self.log(f"    找到 {c} 個包含 '{k}' 的元素 ({x})"))
                                            except Exception as e:
                                                pass
                                                
                                    except Exception as e:
                                        pass
                                
                                # 去重並分析找到的元素
                                unique_elements = list(set(found_elements))
                                self.root.after(0, lambda c=len(unique_elements): self.log(f"  🎯 總共找到 {c} 個可能的送出元素"))
                                
                                for j, element in enumerate(unique_elements):
                                    try:
                                        tag_name = element.tag_name
                                        text = element.text.strip()
                                        value = element.get_attribute('value') or ''
                                        element_class = element.get_attribute('class') or ''
                                        element_id = element.get_attribute('id') or ''
                                        is_visible = element.is_displayed()
                                        is_enabled = element.is_enabled()
                                        
                                        self.root.after(0, lambda j=j, tag=tag_name, txt=text, val=value, 
                                                       cls=element_class, eid=element_id, vis=is_visible, en=is_enabled:
                                                       self.log(f"    元素 {j+1}: <{tag}> text='{txt}' value='{val}' class='{cls}' id='{eid}' visible={vis} enabled={en}"))
                                        
                                    except Exception as e:
                                        self.root.after(0, lambda j=j, e=str(e): self.log(f"    元素 {j+1} 分析失敗: {e}"))
                            
                            # 切換回主頁面
                            driver.switch_to.default_content()
                            
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"❌ iframe {i+1} 檢測失敗: {e}"))
                            try:
                                driver.switch_to.default_content()
                            except:
                                pass
                    
                    self.root.after(0, lambda: self.log("✅ 送出按鈕檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_btn.config(state="normal"))
            
            threading.Thread(target=detection_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_btn.config(state="normal")
            
    def detect_all_elements(self):
        """檢測所有元素"""
        try:
            self.log("📋 開始檢測所有可點擊元素...")
            self.detect_all_btn.config(state="disabled")
            
            def detection_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 檢測所有可能的可點擊元素
                    selectors = [
                        "button",
                        "input[type='submit']",
                        "input[type='button']",
                        "a[onclick]",
                        "div[onclick]",
                        "[role='button']"
                    ]
                    
                    all_elements = []
                    for selector in selectors:
                        try:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            all_elements.extend(elements)
                            self.root.after(0, lambda s=selector, c=len(elements): self.log(f"📊 {s}: {c} 個"))
                        except Exception as e:
                            pass
                    
                    self.root.after(0, lambda c=len(all_elements): self.log(f"📊 總計: {c} 個可點擊元素"))
                    
                    # 分析前20個元素
                    for i, element in enumerate(all_elements[:20]):
                        try:
                            tag_name = element.tag_name
                            text = element.text.strip()[:50]  # 限制長度
                            value = (element.get_attribute('value') or '')[:50]
                            is_visible = element.is_displayed()
                            
                            self.root.after(0, lambda i=i, tag=tag_name, txt=text, val=value, vis=is_visible:
                                           self.log(f"  {i+1}. <{tag}> '{txt}' value='{val}' visible={vis}"))
                            
                        except Exception as e:
                            pass
                    
                    if len(all_elements) > 20:
                        self.root.after(0, lambda: self.log(f"... 還有 {len(all_elements) - 20} 個元素未顯示"))
                    
                    self.root.after(0, lambda: self.log("✅ 所有元素檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_all_btn.config(state="normal"))
            
            threading.Thread(target=detection_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_all_btn.config(state="normal")
            
    def close_app(self):
        """關閉應用"""
        self.root.destroy()
        
    def run(self):
        """運行 GUI"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🔍 啟動送出按鈕檢測調試工具")
    
    try:
        app = SubmitButtonDebugGUI()
        app.run()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")

if __name__ == "__main__":
    main()
