2025-07-02 10:54:17,321 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_105417.log
2025-07-02 10:54:26,628 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 10:54:26,629 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 10:54:26,713 - DEBUG - chromedriver not found in PATH
2025-07-02 10:54:26,713 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 10:54:26,713 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 10:54:26,714 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 10:54:26,714 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 10:54:26,714 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 10:54:26,714 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 10:54:26,721 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 11276 using 0 to output -3
2025-07-02 10:54:27,253 - DEBUG - POST http://localhost:51786/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 10:54:27,254 - DEBUG - Starting new HTTP connection (1): localhost:51786
2025-07-02 10:54:27,775 - DEBUG - http://localhost:51786 "POST /session HTTP/1.1" 200 0
2025-07-02 10:54:27,776 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir11276_201125515"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:51789"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"d09d1635c17a11bac71129c78325c7cc"}} | headers=HTTPHeaderDict({'Content-Length': '881', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:54:27,776 - DEBUG - Finished Request
2025-07-02 10:54:27,777 - DEBUG - POST http://localhost:51786/session/d09d1635c17a11bac71129c78325c7cc/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 10:54:29,175 - DEBUG - http://localhost:51786 "POST /session/d09d1635c17a11bac71129c78325c7cc/url HTTP/1.1" 200 0
2025-07-02 10:54:29,175 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:54:29,176 - DEBUG - Finished Request
2025-07-02 10:54:29,176 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 10:54:29,176 - DEBUG - POST http://localhost:51786/session/d09d1635c17a11bac71129c78325c7cc/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 10:54:29,182 - DEBUG - http://localhost:51786 "POST /session/d09d1635c17a11bac71129c78325c7cc/execute/sync HTTP/1.1" 200 0
2025-07-02 10:54:29,182 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:54:29,183 - DEBUG - Finished Request
2025-07-02 10:54:29,183 - INFO - ✅ 瀏覽器事件監控已啟動
