2025-07-01 18:31:47,354 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_183147.log
2025-07-01 18:31:50,615 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 18:31:50,616 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 18:31:51,352 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 18:31:51,352 - DEBUG - chromedriver not found in PATH
2025-07-01 18:31:51,352 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:31:51,352 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 18:31:51,352 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 18:31:51,352 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 18:31:51,353 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 18:31:51,353 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 18:31:51,353 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:31:51,357 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 19488 using 0 to output -3
2025-07-01 18:31:51,881 - DEBUG - POST http://localhost:57371/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 18:31:51,882 - DEBUG - Starting new HTTP connection (1): localhost:57371
2025-07-01 18:31:52,424 - DEBUG - http://localhost:57371 "POST /session HTTP/1.1" 200 0
2025-07-01 18:31:52,425 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir19488_483488172"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:57377"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"**********4797d13ee307b570d399d1"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:52,425 - DEBUG - Finished Request
2025-07-01 18:31:52,426 - DEBUG - POST http://localhost:57371/session/**********4797d13ee307b570d399d1/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 18:31:53,928 - DEBUG - http://localhost:57371 "POST /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:53,929 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:53,929 - DEBUG - Finished Request
2025-07-01 18:31:53,929 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 18:31:53,929 - DEBUG - POST http://localhost:57371/session/**********4797d13ee307b570d399d1/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 18:31:53,936 - DEBUG - http://localhost:57371 "POST /session/**********4797d13ee307b570d399d1/execute/sync HTTP/1.1" 200 0
2025-07-01 18:31:53,936 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:53,936 - DEBUG - Finished Request
2025-07-01 18:31:53,937 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 18:31:53,937 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:31:53,969 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:53,970 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:53,970 - DEBUG - Finished Request
2025-07-01 18:31:54,971 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:31:54,977 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:54,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:54,978 - DEBUG - Finished Request
2025-07-01 18:31:55,978 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:31:55,985 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:55,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:55,985 - DEBUG - Finished Request
2025-07-01 18:31:56,986 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:31:56,992 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:56,992 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:56,992 - DEBUG - Finished Request
2025-07-01 18:31:57,993 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:31:58,000 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:58,001 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:58,001 - DEBUG - Finished Request
2025-07-01 18:31:59,001 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:31:59,007 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:31:59,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:31:59,007 - DEBUG - Finished Request
2025-07-01 18:32:00,008 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:00,014 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:00,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:00,014 - DEBUG - Finished Request
2025-07-01 18:32:01,015 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:01,023 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:01,023 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:01,023 - DEBUG - Finished Request
2025-07-01 18:32:02,024 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:02,029 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:02,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:02,030 - DEBUG - Finished Request
2025-07-01 18:32:03,031 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:03,038 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:03,038 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:03,039 - DEBUG - Finished Request
2025-07-01 18:32:04,040 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:04,049 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:04,049 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:04,049 - DEBUG - Finished Request
2025-07-01 18:32:05,050 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:05,056 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:05,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:05,056 - DEBUG - Finished Request
2025-07-01 18:32:06,057 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:06,063 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:06,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:06,064 - DEBUG - Finished Request
2025-07-01 18:32:07,065 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:07,071 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:07,071 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:07,072 - DEBUG - Finished Request
2025-07-01 18:32:08,074 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:08,080 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:08,081 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:08,081 - DEBUG - Finished Request
2025-07-01 18:32:09,082 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:09,088 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:09,088 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:09,088 - DEBUG - Finished Request
2025-07-01 18:32:10,089 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:10,095 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:10,095 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:10,095 - DEBUG - Finished Request
2025-07-01 18:32:11,097 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:11,103 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:11,103 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:11,103 - DEBUG - Finished Request
2025-07-01 18:32:12,104 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:12,110 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:12,110 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:12,110 - DEBUG - Finished Request
2025-07-01 18:32:13,111 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:13,119 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:13,119 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:13,119 - DEBUG - Finished Request
2025-07-01 18:32:14,121 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:14,127 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:14,127 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:14,127 - DEBUG - Finished Request
2025-07-01 18:32:15,129 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:15,135 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:15,135 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:15,135 - DEBUG - Finished Request
2025-07-01 18:32:16,136 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:16,142 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:16,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:16,142 - DEBUG - Finished Request
2025-07-01 18:32:17,143 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:17,149 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:17,149 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:17,150 - DEBUG - Finished Request
2025-07-01 18:32:18,151 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:18,157 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:18,158 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:18,158 - DEBUG - Finished Request
2025-07-01 18:32:19,159 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:19,165 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:19,165 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:19,165 - DEBUG - Finished Request
2025-07-01 18:32:20,166 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:20,172 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:20,173 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:20,173 - DEBUG - Finished Request
2025-07-01 18:32:21,175 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:21,181 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:21,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:21,182 - DEBUG - Finished Request
2025-07-01 18:32:22,183 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:22,190 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:22,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:22,190 - DEBUG - Finished Request
2025-07-01 18:32:23,191 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:23,196 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:23,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:23,197 - DEBUG - Finished Request
2025-07-01 18:32:24,197 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:24,203 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:24,203 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:24,203 - DEBUG - Finished Request
2025-07-01 18:32:25,204 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:25,212 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:25,212 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:25,213 - DEBUG - Finished Request
2025-07-01 18:32:26,214 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:26,219 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:26,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:26,219 - DEBUG - Finished Request
2025-07-01 18:32:27,221 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:27,228 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:27,228 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:27,228 - DEBUG - Finished Request
2025-07-01 18:32:28,229 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:28,235 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:28,235 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:28,235 - DEBUG - Finished Request
2025-07-01 18:32:29,236 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:29,241 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:29,242 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:29,242 - DEBUG - Finished Request
2025-07-01 18:32:30,243 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:30,250 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:30,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:30,250 - DEBUG - Finished Request
2025-07-01 18:32:31,251 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:31,257 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:31,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:31,257 - DEBUG - Finished Request
2025-07-01 18:32:32,259 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:32,264 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:32,264 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:32,264 - DEBUG - Finished Request
2025-07-01 18:32:33,266 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:33,275 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:33,275 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:33,275 - DEBUG - Finished Request
2025-07-01 18:32:34,276 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:34,281 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:34,281 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:34,281 - DEBUG - Finished Request
2025-07-01 18:32:35,283 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:35,288 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:35,289 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:35,289 - DEBUG - Finished Request
2025-07-01 18:32:36,289 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:36,297 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:36,297 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:36,297 - DEBUG - Finished Request
2025-07-01 18:32:37,298 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:37,305 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:37,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:37,305 - DEBUG - Finished Request
2025-07-01 18:32:38,306 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:38,311 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:38,312 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:38,312 - DEBUG - Finished Request
2025-07-01 18:32:39,313 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:39,319 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:39,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:39,319 - DEBUG - Finished Request
2025-07-01 18:32:40,321 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:40,326 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:40,327 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:40,327 - DEBUG - Finished Request
2025-07-01 18:32:41,328 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:41,335 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:41,335 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:41,336 - DEBUG - Finished Request
2025-07-01 18:32:42,337 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:42,345 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:42,345 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:42,345 - DEBUG - Finished Request
2025-07-01 18:32:43,346 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:43,353 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:43,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:43,353 - DEBUG - Finished Request
2025-07-01 18:32:44,354 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:44,361 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:44,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:44,362 - DEBUG - Finished Request
2025-07-01 18:32:45,363 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:45,370 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:45,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:45,370 - DEBUG - Finished Request
2025-07-01 18:32:46,371 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:46,378 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:46,378 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:46,378 - DEBUG - Finished Request
2025-07-01 18:32:47,380 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:47,386 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:47,386 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:47,386 - DEBUG - Finished Request
2025-07-01 18:32:48,387 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:48,393 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:48,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:48,394 - DEBUG - Finished Request
2025-07-01 18:32:49,395 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:49,405 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:49,405 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:49,405 - DEBUG - Finished Request
2025-07-01 18:32:50,406 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:50,413 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:50,413 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:50,414 - DEBUG - Finished Request
2025-07-01 18:32:51,415 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:51,421 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:51,421 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:51,421 - DEBUG - Finished Request
2025-07-01 18:32:52,422 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:52,427 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:52,428 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:52,428 - DEBUG - Finished Request
2025-07-01 18:32:53,429 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:53,435 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:53,435 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:53,435 - DEBUG - Finished Request
2025-07-01 18:32:54,436 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:54,443 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:54,444 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:54,444 - DEBUG - Finished Request
2025-07-01 18:32:55,445 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:55,455 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:55,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:55,455 - DEBUG - Finished Request
2025-07-01 18:32:56,456 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:56,462 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:56,462 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:56,463 - DEBUG - Finished Request
2025-07-01 18:32:57,463 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:57,470 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:57,470 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:57,471 - DEBUG - Finished Request
2025-07-01 18:32:58,471 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:58,477 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:58,478 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:58,478 - DEBUG - Finished Request
2025-07-01 18:32:59,479 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:32:59,485 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:32:59,485 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:32:59,485 - DEBUG - Finished Request
2025-07-01 18:33:00,486 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:00,492 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:00,492 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:00,492 - DEBUG - Finished Request
2025-07-01 18:33:01,493 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:01,499 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:01,499 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:01,500 - DEBUG - Finished Request
2025-07-01 18:33:02,501 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:02,507 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:02,507 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:02,507 - DEBUG - Finished Request
2025-07-01 18:33:03,509 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:03,515 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:03,515 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:03,515 - DEBUG - Finished Request
2025-07-01 18:33:04,516 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:04,522 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:04,522 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:04,522 - DEBUG - Finished Request
2025-07-01 18:33:05,523 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:05,530 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:05,530 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:05,530 - DEBUG - Finished Request
2025-07-01 18:33:06,531 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:06,539 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:06,539 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:06,539 - DEBUG - Finished Request
2025-07-01 18:33:07,539 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:07,546 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:07,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:07,546 - DEBUG - Finished Request
2025-07-01 18:33:08,547 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:08,553 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:08,553 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:08,554 - DEBUG - Finished Request
2025-07-01 18:33:09,555 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:09,562 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:09,562 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:09,562 - DEBUG - Finished Request
2025-07-01 18:33:10,563 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:10,570 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:10,571 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:10,571 - DEBUG - Finished Request
2025-07-01 18:33:11,572 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:11,579 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:11,579 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:11,580 - DEBUG - Finished Request
2025-07-01 18:33:12,580 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:12,588 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:12,588 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:12,589 - DEBUG - Finished Request
2025-07-01 18:33:13,589 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:13,596 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:13,596 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:13,596 - DEBUG - Finished Request
2025-07-01 18:33:14,598 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:14,606 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:14,606 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:14,606 - DEBUG - Finished Request
2025-07-01 18:33:15,607 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:15,614 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:15,614 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:15,614 - DEBUG - Finished Request
2025-07-01 18:33:16,615 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:16,622 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:16,622 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:16,623 - DEBUG - Finished Request
2025-07-01 18:33:17,624 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:17,629 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:17,629 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:17,629 - DEBUG - Finished Request
2025-07-01 18:33:18,630 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:18,637 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:18,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:18,638 - DEBUG - Finished Request
2025-07-01 18:33:19,639 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:19,645 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:19,645 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:19,645 - DEBUG - Finished Request
2025-07-01 18:33:20,646 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:20,652 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:20,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:20,653 - DEBUG - Finished Request
2025-07-01 18:33:21,654 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:21,661 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:21,661 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:21,662 - DEBUG - Finished Request
2025-07-01 18:33:22,663 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:22,670 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:22,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:22,671 - DEBUG - Finished Request
2025-07-01 18:33:23,671 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:23,678 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:23,678 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:23,678 - DEBUG - Finished Request
2025-07-01 18:33:24,679 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:24,686 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:24,686 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:24,686 - DEBUG - Finished Request
2025-07-01 18:33:25,687 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:25,694 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:25,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:25,695 - DEBUG - Finished Request
2025-07-01 18:33:26,695 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:26,703 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:26,703 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:26,704 - DEBUG - Finished Request
2025-07-01 18:33:27,705 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:27,713 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:27,713 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:27,714 - DEBUG - Finished Request
2025-07-01 18:33:28,715 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:28,723 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:28,725 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:28,725 - DEBUG - Finished Request
2025-07-01 18:33:29,726 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:29,735 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:29,735 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:29,735 - DEBUG - Finished Request
2025-07-01 18:33:30,736 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:30,744 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:30,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:30,745 - DEBUG - Finished Request
2025-07-01 18:33:31,746 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:31,752 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:31,752 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:31,752 - DEBUG - Finished Request
2025-07-01 18:33:32,753 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:32,758 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:32,759 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:32,759 - DEBUG - Finished Request
2025-07-01 18:33:33,759 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:33,770 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:33,770 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:33,770 - DEBUG - Finished Request
2025-07-01 18:33:34,771 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:34,777 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:34,777 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:34,778 - DEBUG - Finished Request
2025-07-01 18:33:35,779 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:35,785 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:35,785 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:35,785 - DEBUG - Finished Request
2025-07-01 18:33:36,785 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:36,792 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:36,792 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:36,792 - DEBUG - Finished Request
2025-07-01 18:33:37,793 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:37,799 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 200 0
2025-07-01 18:33:37,799 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:37,799 - DEBUG - Finished Request
2025-07-01 18:33:38,800 - DEBUG - GET http://localhost:57371/session/**********4797d13ee307b570d399d1/url {}
2025-07-01 18:33:38,802 - DEBUG - http://localhost:57371 "GET /session/**********4797d13ee307b570d399d1/url HTTP/1.1" 404 0
2025-07-01 18:33:38,802 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:38,802 - DEBUG - Finished Request
2025-07-01 18:33:38,803 - DEBUG - DELETE http://localhost:57371/session/**********4797d13ee307b570d399d1 {}
2025-07-01 18:33:38,833 - DEBUG - http://localhost:57371 "DELETE /session/**********4797d13ee307b570d399d1 HTTP/1.1" 200 0
2025-07-01 18:33:38,833 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:33:38,833 - DEBUG - Finished Request
