2025-07-02 09:58:59,722 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_095859.log
2025-07-02 09:59:34,483 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 09:59:34,484 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 09:59:34,563 - DEBUG - chromedriver not found in PATH
2025-07-02 09:59:34,563 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:59:34,564 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 09:59:34,564 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 09:59:34,564 - DEBUG - chromedriver 138.0.7204.92 already in the cache
2025-07-02 09:59:34,564 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 09:59:34,565 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:59:34,633 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 25292 using 0 to output -3
2025-07-02 09:59:35,170 - DEBUG - POST http://localhost:50370/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 09:59:35,172 - DEBUG - Starting new HTTP connection (1): localhost:50370
2025-07-02 09:59:35,722 - DEBUG - http://localhost:50370 "POST /session HTTP/1.1" 200 0
2025-07-02 09:59:35,722 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir25292_394594447"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:50374"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"930931a3c0caabf97317ae7d8c23179b"}} | headers=HTTPHeaderDict({'Content-Length': '881', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:35,722 - DEBUG - Finished Request
2025-07-02 09:59:35,723 - DEBUG - POST http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 09:59:36,800 - DEBUG - http://localhost:50370 "POST /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:36,801 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:36,801 - DEBUG - Finished Request
2025-07-02 09:59:36,801 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 09:59:36,802 - DEBUG - POST http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 09:59:36,843 - DEBUG - http://localhost:50370 "POST /session/930931a3c0caabf97317ae7d8c23179b/execute/sync HTTP/1.1" 200 0
2025-07-02 09:59:36,843 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:36,844 - DEBUG - Finished Request
2025-07-02 09:59:36,844 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-02 09:59:36,845 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:36,925 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:36,926 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:36,926 - DEBUG - Finished Request
2025-07-02 09:59:37,927 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:37,939 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:37,940 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:37,940 - DEBUG - Finished Request
2025-07-02 09:59:38,941 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:38,948 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:38,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:38,949 - DEBUG - Finished Request
2025-07-02 09:59:39,950 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:39,957 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:39,957 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:39,958 - DEBUG - Finished Request
2025-07-02 09:59:40,958 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:40,965 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:40,966 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:40,966 - DEBUG - Finished Request
2025-07-02 09:59:41,967 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:41,973 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:41,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:41,974 - DEBUG - Finished Request
2025-07-02 09:59:42,976 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:42,982 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:42,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:42,983 - DEBUG - Finished Request
2025-07-02 09:59:43,985 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:43,995 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:43,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:43,997 - DEBUG - Finished Request
2025-07-02 09:59:44,998 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:45,005 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:45,005 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:45,006 - DEBUG - Finished Request
2025-07-02 09:59:46,007 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:46,014 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:46,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:46,015 - DEBUG - Finished Request
2025-07-02 09:59:47,016 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:47,024 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:47,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:47,025 - DEBUG - Finished Request
2025-07-02 09:59:48,026 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:48,033 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:48,033 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:48,034 - DEBUG - Finished Request
2025-07-02 09:59:49,035 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:49,043 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:49,043 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:49,044 - DEBUG - Finished Request
2025-07-02 09:59:50,045 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:50,052 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:50,053 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:50,053 - DEBUG - Finished Request
2025-07-02 09:59:51,054 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:51,061 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:51,061 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:51,061 - DEBUG - Finished Request
2025-07-02 09:59:52,062 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:52,070 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:52,070 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:52,070 - DEBUG - Finished Request
2025-07-02 09:59:53,071 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:53,080 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:53,080 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:53,081 - DEBUG - Finished Request
2025-07-02 09:59:54,082 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:54,089 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:54,089 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:54,090 - DEBUG - Finished Request
2025-07-02 09:59:55,092 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:55,098 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:55,098 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:55,099 - DEBUG - Finished Request
2025-07-02 09:59:56,100 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:56,106 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:56,107 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:56,107 - DEBUG - Finished Request
2025-07-02 09:59:57,109 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:57,117 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:57,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:57,118 - DEBUG - Finished Request
2025-07-02 09:59:58,119 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:58,126 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:58,127 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:58,127 - DEBUG - Finished Request
2025-07-02 09:59:59,128 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 09:59:59,138 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 09:59:59,138 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:59:59,139 - DEBUG - Finished Request
2025-07-02 10:00:00,140 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:00,148 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:00,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:00,148 - DEBUG - Finished Request
2025-07-02 10:00:01,149 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:01,156 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:01,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:01,157 - DEBUG - Finished Request
2025-07-02 10:00:02,157 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:02,164 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:02,164 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:02,164 - DEBUG - Finished Request
2025-07-02 10:00:03,165 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:03,173 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:03,173 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:03,174 - DEBUG - Finished Request
2025-07-02 10:00:04,175 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:04,181 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:04,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:04,182 - DEBUG - Finished Request
2025-07-02 10:00:05,183 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:05,189 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:05,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:05,190 - DEBUG - Finished Request
2025-07-02 10:00:06,192 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:06,199 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:06,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:06,200 - DEBUG - Finished Request
2025-07-02 10:00:07,200 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:07,209 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:07,209 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:07,210 - DEBUG - Finished Request
2025-07-02 10:00:08,211 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:08,219 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:08,220 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:08,220 - DEBUG - Finished Request
2025-07-02 10:00:09,221 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:09,229 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:09,229 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:09,230 - DEBUG - Finished Request
2025-07-02 10:00:10,230 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:10,239 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:10,239 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:10,239 - DEBUG - Finished Request
2025-07-02 10:00:11,239 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:11,246 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:11,246 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:11,247 - DEBUG - Finished Request
2025-07-02 10:00:12,247 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:12,254 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:12,254 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:12,254 - DEBUG - Finished Request
2025-07-02 10:00:13,255 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:13,262 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:13,263 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:13,263 - DEBUG - Finished Request
2025-07-02 10:00:14,264 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:14,272 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:14,272 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:14,273 - DEBUG - Finished Request
2025-07-02 10:00:15,274 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:15,283 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:15,283 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:15,284 - DEBUG - Finished Request
2025-07-02 10:00:16,284 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:16,291 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:16,291 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:16,292 - DEBUG - Finished Request
2025-07-02 10:00:17,292 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:17,299 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:17,299 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:17,299 - DEBUG - Finished Request
2025-07-02 10:00:18,300 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:18,307 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:18,308 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:18,308 - DEBUG - Finished Request
2025-07-02 10:00:19,309 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:19,316 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:19,317 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:19,317 - DEBUG - Finished Request
2025-07-02 10:00:20,318 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:20,324 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:20,325 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:20,325 - DEBUG - Finished Request
2025-07-02 10:00:21,326 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:21,334 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:21,334 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:21,335 - DEBUG - Finished Request
2025-07-02 10:00:22,336 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:22,342 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:22,343 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:22,343 - DEBUG - Finished Request
2025-07-02 10:00:23,344 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:23,351 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:23,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:23,352 - DEBUG - Finished Request
2025-07-02 10:00:24,352 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:24,360 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:24,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:24,361 - DEBUG - Finished Request
2025-07-02 10:00:25,361 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:25,368 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:25,368 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:25,368 - DEBUG - Finished Request
2025-07-02 10:00:26,370 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:26,377 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:26,377 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:26,377 - DEBUG - Finished Request
2025-07-02 10:00:27,378 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:27,386 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:27,386 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:27,387 - DEBUG - Finished Request
2025-07-02 10:00:28,387 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:28,396 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:28,396 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:28,396 - DEBUG - Finished Request
2025-07-02 10:00:29,397 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:29,404 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:29,404 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:29,404 - DEBUG - Finished Request
2025-07-02 10:00:30,405 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:30,414 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:30,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:30,415 - DEBUG - Finished Request
2025-07-02 10:00:31,416 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:31,423 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:31,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:31,423 - DEBUG - Finished Request
2025-07-02 10:00:32,424 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:32,432 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:32,433 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:32,433 - DEBUG - Finished Request
2025-07-02 10:00:33,434 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:33,442 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:33,443 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:33,443 - DEBUG - Finished Request
2025-07-02 10:00:34,444 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:34,450 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:34,451 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:34,451 - DEBUG - Finished Request
2025-07-02 10:00:35,453 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:35,462 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:35,462 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:35,462 - DEBUG - Finished Request
2025-07-02 10:00:36,463 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:36,471 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:36,471 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:36,471 - DEBUG - Finished Request
2025-07-02 10:00:37,473 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:37,483 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:37,483 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:37,483 - DEBUG - Finished Request
2025-07-02 10:00:38,484 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:38,492 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:38,493 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:38,493 - DEBUG - Finished Request
2025-07-02 10:00:39,494 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:39,501 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:39,501 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:39,501 - DEBUG - Finished Request
2025-07-02 10:00:40,502 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:40,510 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:40,510 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:40,510 - DEBUG - Finished Request
2025-07-02 10:00:41,511 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:41,519 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:41,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:41,519 - DEBUG - Finished Request
2025-07-02 10:00:42,519 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:42,526 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:42,526 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:42,526 - DEBUG - Finished Request
2025-07-02 10:00:43,527 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:43,536 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:43,536 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:43,536 - DEBUG - Finished Request
2025-07-02 10:00:44,537 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:44,545 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:44,545 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:44,545 - DEBUG - Finished Request
2025-07-02 10:00:45,545 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:45,553 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:45,553 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:45,554 - DEBUG - Finished Request
2025-07-02 10:00:46,555 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:46,564 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:46,564 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:46,564 - DEBUG - Finished Request
2025-07-02 10:00:47,566 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:47,574 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:47,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:47,574 - DEBUG - Finished Request
2025-07-02 10:00:48,575 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:48,582 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:48,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:48,583 - DEBUG - Finished Request
2025-07-02 10:00:49,584 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:49,589 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:49,589 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:49,590 - DEBUG - Finished Request
2025-07-02 10:00:50,591 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:50,597 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:50,597 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:50,598 - DEBUG - Finished Request
2025-07-02 10:00:51,598 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:51,606 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 200 0
2025-07-02 10:00:51,606 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:51,606 - DEBUG - Finished Request
2025-07-02 10:00:52,607 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:52,608 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:52,608 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9dea]\n\t(No symbol) [0x0x7ff7935a5f15]\n\t(No symbol) [0x0x7ff7935cabf4]\n\t(No symbol) [0x0x7ff79363fa85]\n\t(No symbol) [0x0x7ff79365ff72]\n\t(No symbol) [0x0x7ff793638243]\n\t(No symbol) [0x0x7ff793601431]\n\t(No symbol) [0x0x7ff7936021c3]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\tGetHandleVerifier [0x0x7ff79380fb14+112628]\n\tGetHandleVerifier [0x0x7ff79380fcc9+113065]\n\tGetHandleVerifier [0x0x7ff7937f6c98+10616]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1062', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:52,608 - DEBUG - Finished Request
2025-07-02 10:00:53,610 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:53,611 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:53,611 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:53,612 - DEBUG - Finished Request
2025-07-02 10:00:54,612 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:54,613 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:54,613 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:54,613 - DEBUG - Finished Request
2025-07-02 10:00:55,615 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:55,616 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:55,616 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:55,616 - DEBUG - Finished Request
2025-07-02 10:00:56,617 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:56,618 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:56,619 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:56,619 - DEBUG - Finished Request
2025-07-02 10:00:57,620 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:57,621 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:57,621 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:57,621 - DEBUG - Finished Request
2025-07-02 10:00:58,623 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:58,623 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:58,623 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:58,623 - DEBUG - Finished Request
2025-07-02 10:00:59,625 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:00:59,626 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:00:59,627 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:00:59,627 - DEBUG - Finished Request
2025-07-02 10:01:00,628 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:00,629 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:00,629 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:00,630 - DEBUG - Finished Request
2025-07-02 10:01:01,630 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:01,631 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:01,631 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:01,631 - DEBUG - Finished Request
2025-07-02 10:01:02,632 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:02,633 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:02,634 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:02,634 - DEBUG - Finished Request
2025-07-02 10:01:03,635 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:03,635 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:03,636 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:03,636 - DEBUG - Finished Request
2025-07-02 10:01:04,637 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:04,639 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:04,639 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:04,639 - DEBUG - Finished Request
2025-07-02 10:01:05,640 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:05,641 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:05,641 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:05,641 - DEBUG - Finished Request
2025-07-02 10:01:06,642 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:06,644 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:06,645 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:06,645 - DEBUG - Finished Request
2025-07-02 10:01:07,646 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:07,647 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:07,647 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:07,647 - DEBUG - Finished Request
2025-07-02 10:01:08,649 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:08,650 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:08,651 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:08,651 - DEBUG - Finished Request
2025-07-02 10:01:09,652 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:09,653 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:09,653 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:09,654 - DEBUG - Finished Request
2025-07-02 10:01:10,654 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:10,655 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:10,655 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:10,655 - DEBUG - Finished Request
2025-07-02 10:01:11,657 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:11,658 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:11,658 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:11,658 - DEBUG - Finished Request
2025-07-02 10:01:12,659 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:12,659 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:12,659 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:12,660 - DEBUG - Finished Request
2025-07-02 10:01:13,661 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:13,662 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:13,663 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:13,663 - DEBUG - Finished Request
2025-07-02 10:01:14,663 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:14,665 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:14,666 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:14,667 - DEBUG - Finished Request
2025-07-02 10:01:15,668 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:15,670 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:15,670 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:15,670 - DEBUG - Finished Request
2025-07-02 10:01:16,671 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:16,674 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:16,674 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:16,674 - DEBUG - Finished Request
2025-07-02 10:01:17,675 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:17,677 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:17,678 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:17,678 - DEBUG - Finished Request
2025-07-02 10:01:18,679 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:18,680 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:18,680 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:18,681 - DEBUG - Finished Request
2025-07-02 10:01:19,682 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:19,683 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:19,683 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:19,683 - DEBUG - Finished Request
2025-07-02 10:01:20,684 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:20,686 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:20,686 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:20,687 - DEBUG - Finished Request
2025-07-02 10:01:21,688 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:21,689 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:21,689 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:21,689 - DEBUG - Finished Request
2025-07-02 10:01:22,691 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:22,692 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:22,692 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:22,692 - DEBUG - Finished Request
2025-07-02 10:01:23,693 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:23,695 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:23,695 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:23,696 - DEBUG - Finished Request
2025-07-02 10:01:24,696 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:24,696 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:24,698 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:24,698 - DEBUG - Finished Request
2025-07-02 10:01:25,699 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:25,700 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:25,701 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:25,701 - DEBUG - Finished Request
2025-07-02 10:01:26,702 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:26,702 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:26,703 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:26,703 - DEBUG - Finished Request
2025-07-02 10:01:27,704 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:27,705 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:27,705 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:27,705 - DEBUG - Finished Request
2025-07-02 10:01:28,707 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:28,708 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:28,708 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:28,709 - DEBUG - Finished Request
2025-07-02 10:01:29,710 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:29,710 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:29,710 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:29,711 - DEBUG - Finished Request
2025-07-02 10:01:30,712 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:30,713 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:30,713 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:30,714 - DEBUG - Finished Request
2025-07-02 10:01:31,715 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:31,716 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:31,716 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:31,716 - DEBUG - Finished Request
2025-07-02 10:01:32,718 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:32,718 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:32,718 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:32,719 - DEBUG - Finished Request
2025-07-02 10:01:33,720 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:33,721 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:33,721 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:33,722 - DEBUG - Finished Request
2025-07-02 10:01:34,723 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:34,724 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:34,724 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:34,725 - DEBUG - Finished Request
2025-07-02 10:01:35,726 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:35,726 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:35,726 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:35,728 - DEBUG - Finished Request
2025-07-02 10:01:36,728 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:36,729 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:36,729 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:36,730 - DEBUG - Finished Request
2025-07-02 10:01:37,731 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:37,733 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:37,733 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:37,733 - DEBUG - Finished Request
2025-07-02 10:01:38,734 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:38,735 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:38,735 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:38,735 - DEBUG - Finished Request
2025-07-02 10:01:39,736 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:39,736 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:39,736 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:39,738 - DEBUG - Finished Request
2025-07-02 10:01:40,739 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:40,740 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:40,740 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:40,740 - DEBUG - Finished Request
2025-07-02 10:01:41,741 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:41,742 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:41,742 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:41,742 - DEBUG - Finished Request
2025-07-02 10:01:42,743 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:42,744 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:42,744 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:42,745 - DEBUG - Finished Request
2025-07-02 10:01:43,745 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:43,746 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:43,746 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:43,747 - DEBUG - Finished Request
2025-07-02 10:01:44,748 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:44,750 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:44,750 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:44,750 - DEBUG - Finished Request
2025-07-02 10:01:45,750 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:45,752 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:45,752 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:45,753 - DEBUG - Finished Request
2025-07-02 10:01:46,754 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:46,755 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:46,755 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:46,755 - DEBUG - Finished Request
2025-07-02 10:01:47,756 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:47,758 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:47,758 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:47,758 - DEBUG - Finished Request
2025-07-02 10:01:48,760 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:48,760 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:48,761 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:48,761 - DEBUG - Finished Request
2025-07-02 10:01:49,763 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:49,764 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:49,764 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:49,765 - DEBUG - Finished Request
2025-07-02 10:01:50,765 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:50,765 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:50,765 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:50,767 - DEBUG - Finished Request
2025-07-02 10:01:51,768 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:51,769 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:51,769 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:51,769 - DEBUG - Finished Request
2025-07-02 10:01:52,770 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:52,772 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:52,772 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:52,773 - DEBUG - Finished Request
2025-07-02 10:01:53,774 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:53,775 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:53,775 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:53,775 - DEBUG - Finished Request
2025-07-02 10:01:54,776 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:54,777 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:54,777 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:54,777 - DEBUG - Finished Request
2025-07-02 10:01:55,779 - DEBUG - GET http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b/url {}
2025-07-02 10:01:55,780 - DEBUG - http://localhost:50370 "GET /session/930931a3c0caabf97317ae7d8c23179b/url HTTP/1.1" 404 0
2025-07-02 10:01:55,780 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff793806f95+76917]\n\tGetHandleVerifier [0x0x7ff793806ff0+77008]\n\t(No symbol) [0x0x7ff7935b9c1c]\n\t(No symbol) [0x0x7ff79360055f]\n\t(No symbol) [0x0x7ff793638332]\n\t(No symbol) [0x0x7ff793632e53]\n\t(No symbol) [0x0x7ff793631f19]\n\t(No symbol) [0x0x7ff793584b05]\n\tGetHandleVerifier [0x0x7ff793add2cd+3051437]\n\tGetHandleVerifier [0x0x7ff793ad7923+3028483]\n\tGetHandleVerifier [0x0x7ff793af58bd+3151261]\n\tGetHandleVerifier [0x0x7ff79382185e+185662]\n\tGetHandleVerifier [0x0x7ff79382971f+218111]\n\t(No symbol) [0x0x7ff793583b00]\n\tGetHandleVerifier [0x0x7ff793bf5f38+4201496]\n\tBaseThreadInitThunk [0x0x7ffd5f5ae8d7+23]\n\tRtlUserThreadStart [0x0x7ffd60afc34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 10:01:55,781 - DEBUG - Finished Request
2025-07-02 10:01:56,792 - DEBUG - DELETE http://localhost:50370/session/930931a3c0caabf97317ae7d8c23179b {}
2025-07-02 10:01:56,793 - DEBUG - Resetting dropped connection: localhost
2025-07-02 10:02:00,863 - DEBUG - Incremented Retry for (url='/session/930931a3c0caabf97317ae7d8c23179b'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-02 10:02:00,863 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D84FD4BF50>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/930931a3c0caabf97317ae7d8c23179b
2025-07-02 10:02:00,864 - DEBUG - Starting new HTTP connection (2): localhost:50370
2025-07-02 10:02:04,917 - DEBUG - Incremented Retry for (url='/session/930931a3c0caabf97317ae7d8c23179b'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-02 10:02:04,918 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D850A970D0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/930931a3c0caabf97317ae7d8c23179b
2025-07-02 10:02:04,918 - DEBUG - Starting new HTTP connection (3): localhost:50370
2025-07-02 10:02:08,966 - DEBUG - Incremented Retry for (url='/session/930931a3c0caabf97317ae7d8c23179b'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-02 10:02:08,967 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D850A97610>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/930931a3c0caabf97317ae7d8c23179b
2025-07-02 10:02:08,967 - DEBUG - Starting new HTTP connection (4): localhost:50370
