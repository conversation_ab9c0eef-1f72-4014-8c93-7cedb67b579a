2025-06-27 19:22:07,085 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250627_192207.log
2025-06-27 19:22:17,181 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-27 19:22:17,181 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-27 19:22:17,255 - DEBUG - chromedriver not found in PATH
2025-06-27 19:22:17,256 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:22:17,256 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-27 19:22:17,256 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-27 19:22:17,256 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-27 19:22:17,256 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-27 19:22:17,256 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:22:17,261 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 33276 using 0 to output -3
2025-06-27 19:22:17,772 - DEBUG - POST http://localhost:55196/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-27 19:22:17,773 - DEBUG - Starting new HTTP connection (1): localhost:55196
2025-06-27 19:22:18,319 - DEBUG - http://localhost:55196 "POST /session HTTP/1.1" 200 0
2025-06-27 19:22:18,319 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir33276_679432571"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55199"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"75db2ce6f6c55342ac0a1b54777b3744"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:18,320 - DEBUG - Finished Request
2025-06-27 19:22:18,321 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-27 19:22:20,246 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:20,247 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:20,247 - DEBUG - Finished Request
2025-06-27 19:22:20,247 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:20,286 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:20,286 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:20,287 - DEBUG - Finished Request
2025-06-27 19:22:21,288 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:21,296 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:21,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:21,296 - DEBUG - Finished Request
2025-06-27 19:22:22,297 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:22,304 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:22,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:22,305 - DEBUG - Finished Request
2025-06-27 19:22:23,306 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:23,315 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:23,315 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:23,316 - DEBUG - Finished Request
2025-06-27 19:22:24,317 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:24,324 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:24,325 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:24,325 - DEBUG - Finished Request
2025-06-27 19:22:25,326 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:25,336 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:25,337 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:25,337 - DEBUG - Finished Request
2025-06-27 19:22:26,338 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:26,346 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:26,347 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:26,347 - DEBUG - Finished Request
2025-06-27 19:22:27,348 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:27,372 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:27,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:27,373 - DEBUG - Finished Request
2025-06-27 19:22:28,374 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:28,387 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:28,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:28,388 - DEBUG - Finished Request
2025-06-27 19:22:29,390 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:29,396 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:29,396 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:29,397 - DEBUG - Finished Request
2025-06-27 19:22:30,398 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:30,460 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:30,464 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:30,468 - DEBUG - Finished Request
2025-06-27 19:22:31,470 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:31,477 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:31,477 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:31,477 - DEBUG - Finished Request
2025-06-27 19:22:32,479 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:32,488 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:32,489 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:32,489 - DEBUG - Finished Request
2025-06-27 19:22:33,490 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:33,500 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:33,501 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:33,501 - DEBUG - Finished Request
2025-06-27 19:22:34,502 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:34,510 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:34,510 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:34,510 - DEBUG - Finished Request
2025-06-27 19:22:35,511 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:35,518 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:35,518 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:35,519 - DEBUG - Finished Request
2025-06-27 19:22:36,520 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:36,529 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:36,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:36,530 - DEBUG - Finished Request
2025-06-27 19:22:37,530 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:37,538 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:37,538 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:37,538 - DEBUG - Finished Request
2025-06-27 19:22:38,539 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:38,546 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:38,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:38,546 - DEBUG - Finished Request
2025-06-27 19:22:39,547 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:39,553 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:39,555 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:39,555 - DEBUG - Finished Request
2025-06-27 19:22:40,556 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:40,564 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:40,564 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:40,564 - DEBUG - Finished Request
2025-06-27 19:22:41,566 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:41,572 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:41,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:41,574 - DEBUG - Finished Request
2025-06-27 19:22:42,575 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:42,581 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:42,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:42,582 - DEBUG - Finished Request
2025-06-27 19:22:43,582 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:43,589 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:43,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:43,590 - DEBUG - Finished Request
2025-06-27 19:22:44,591 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:44,601 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:44,602 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:44,602 - DEBUG - Finished Request
2025-06-27 19:22:45,603 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:45,610 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:45,611 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:45,611 - DEBUG - Finished Request
2025-06-27 19:22:46,612 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:46,619 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:46,620 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:46,620 - DEBUG - Finished Request
2025-06-27 19:22:47,622 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:47,629 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:47,629 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:47,629 - DEBUG - Finished Request
2025-06-27 19:22:48,630 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:48,637 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:48,638 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:48,638 - DEBUG - Finished Request
2025-06-27 19:22:49,639 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:49,645 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:49,646 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:49,646 - DEBUG - Finished Request
2025-06-27 19:22:50,647 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:50,653 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:50,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:50,654 - DEBUG - Finished Request
2025-06-27 19:22:51,655 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:51,661 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:51,662 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:51,662 - DEBUG - Finished Request
2025-06-27 19:22:52,663 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:52,669 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:52,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:52,669 - DEBUG - Finished Request
2025-06-27 19:22:53,670 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:53,676 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:53,677 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:53,677 - DEBUG - Finished Request
2025-06-27 19:22:54,678 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:54,684 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:54,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:54,684 - DEBUG - Finished Request
2025-06-27 19:22:55,685 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:55,691 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:55,691 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:55,691 - DEBUG - Finished Request
2025-06-27 19:22:56,693 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:56,699 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:56,699 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:56,699 - DEBUG - Finished Request
2025-06-27 19:22:57,700 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:57,707 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:57,708 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:57,708 - DEBUG - Finished Request
2025-06-27 19:22:58,708 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:58,715 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:58,715 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:58,715 - DEBUG - Finished Request
2025-06-27 19:22:59,716 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:22:59,722 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:22:59,722 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:22:59,722 - DEBUG - Finished Request
2025-06-27 19:23:00,723 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:00,732 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:00,732 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:00,732 - DEBUG - Finished Request
2025-06-27 19:23:01,733 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:01,739 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:01,739 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:01,740 - DEBUG - Finished Request
2025-06-27 19:23:02,741 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:02,770 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:02,771 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:02,771 - DEBUG - Finished Request
2025-06-27 19:23:03,772 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:03,777 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:03,778 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:03,778 - DEBUG - Finished Request
2025-06-27 19:23:04,779 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:04,786 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:04,786 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:04,787 - DEBUG - Finished Request
2025-06-27 19:23:05,788 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:05,794 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:05,794 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:05,794 - DEBUG - Finished Request
2025-06-27 19:23:06,794 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:06,801 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:06,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:06,802 - DEBUG - Finished Request
2025-06-27 19:23:07,803 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:07,809 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:07,810 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:07,810 - DEBUG - Finished Request
2025-06-27 19:23:08,811 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:08,818 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:08,819 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:08,819 - DEBUG - Finished Request
2025-06-27 19:23:09,820 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:09,828 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:09,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:09,829 - DEBUG - Finished Request
2025-06-27 19:23:10,830 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:10,837 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:10,837 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:10,837 - DEBUG - Finished Request
2025-06-27 19:23:11,838 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:11,844 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:11,845 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:11,845 - DEBUG - Finished Request
2025-06-27 19:23:12,846 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:12,853 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:12,853 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:12,853 - DEBUG - Finished Request
2025-06-27 19:23:13,855 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:13,861 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:13,861 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:13,861 - DEBUG - Finished Request
2025-06-27 19:23:14,863 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:14,869 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:14,869 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:14,869 - DEBUG - Finished Request
2025-06-27 19:23:15,871 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:15,877 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:15,877 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:15,877 - DEBUG - Finished Request
2025-06-27 19:23:16,164 - INFO - 🎯 用戶點擊準備完成按鈕，開始詳細檢測...
2025-06-27 19:23:16,164 - INFO - 🔍 [用戶點擊準備完成] 開始記錄頁面內容...
2025-06-27 19:23:16,165 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:16,182 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:16,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,183 - DEBUG - Finished Request
2025-06-27 19:23:16,183 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/title {}
2025-06-27 19:23:16,192 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/title HTTP/1.1" 200 0
2025-06-27 19:23:16,193 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,193 - DEBUG - Finished Request
2025-06-27 19:23:16,193 - INFO - 🔍 [用戶點擊準備完成] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:23:16,193 - INFO - 🔍 [用戶點擊準備完成] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:23:16,193 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/source {}
2025-06-27 19:23:16,199 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/source HTTP/1.1" 200 0
2025-06-27 19:23:16,199 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'jkMhwG_snMeWylbJUTBDOPv1Ir0JFlz2DuSeh2XY-57a--x2Vr8mp4V3KhACfNLvq2j7gwP432g08dT-rCmuNgWs8jOUU2V0hetT8p2D-sY1:FKyPZ9ZGFfa9PMw33HVQxOtzPk-lrLYirpyhwVnVq0SHpsPJohXywxIbjCnjPz9lvv2yB0GS2V2xiXx0bpc-eLcERBgD7ObTe9Ga4VuLcEI1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:22:59\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;956491dc286e4a9f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,200 - DEBUG - Finished Request
2025-06-27 19:23:16,201 - INFO - 🔍 [用戶點擊準備完成] page_source 長度: 8479
2025-06-27 19:23:16,201 - INFO - 🔍 [用戶點擊準備完成] page_source 包含 E48B: False
2025-06-27 19:23:16,201 - INFO - 🔍 [用戶點擊準備完成] page_source 包含目標訂單: False
2025-06-27 19:23:16,202 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:23:16,208 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync HTTP/1.1" 200 0
2025-06-27 19:23:16,209 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:22:59\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,209 - DEBUG - Finished Request
2025-06-27 19:23:16,209 - INFO - 🔍 [用戶點擊準備完成] innerText 長度: 97
2025-06-27 19:23:16,209 - INFO - 🔍 [用戶點擊準備完成] innerText 包含 E48B: False
2025-06-27 19:23:16,210 - INFO - 🔍 [用戶點擊準備完成] innerText 包含目標訂單: False
2025-06-27 19:23:16,210 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:23:16,220 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:16,220 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,220 - DEBUG - Finished Request
2025-06-27 19:23:16,221 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:23:16,229 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:16,230 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,230 - DEBUG - Finished Request
2025-06-27 19:23:16,230 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個表格，0 個表格行
2025-06-27 19:23:16,231 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:16,240 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:16,240 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,240 - DEBUG - Finished Request
2025-06-27 19:23:16,241 - INFO - 🔍 [用戶點擊準備完成] 包含 'E48B' 的元素數量: 0
2025-06-27 19:23:16,241 - INFO - 🔍 [用戶點擊準備完成] innerText 前300字符:
2025-06-27 19:23:16,241 - INFO - 🔍 [用戶點擊準備完成]  環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:22:59

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-06-27 19:23:16,241 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:23:16,249 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:16,250 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,250 - DEBUG - Finished Request
2025-06-27 19:23:16,250 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個編輯按鈕
2025-06-27 19:23:16,878 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:16,885 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:16,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:16,885 - DEBUG - Finished Request
2025-06-27 19:23:17,886 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:17,891 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:17,891 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:17,892 - DEBUG - Finished Request
2025-06-27 19:23:18,252 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:18,259 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:18,259 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:18,259 - DEBUG - Finished Request
2025-06-27 19:23:18,259 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/title {}
2025-06-27 19:23:18,265 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/title HTTP/1.1" 200 0
2025-06-27 19:23:18,265 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:18,265 - DEBUG - Finished Request
2025-06-27 19:23:18,266 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:23:18,274 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:18,274 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:18,275 - DEBUG - Finished Request
2025-06-27 19:23:18,275 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:23:18,283 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:18,283 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:18,283 - DEBUG - Finished Request
2025-06-27 19:23:18,283 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/source {}
2025-06-27 19:23:18,286 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/source HTTP/1.1" 200 0
2025-06-27 19:23:18,287 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'jkMhwG_snMeWylbJUTBDOPv1Ir0JFlz2DuSeh2XY-57a--x2Vr8mp4V3KhACfNLvq2j7gwP432g08dT-rCmuNgWs8jOUU2V0hetT8p2D-sY1:FKyPZ9ZGFfa9PMw33HVQxOtzPk-lrLYirpyhwVnVq0SHpsPJohXywxIbjCnjPz9lvv2yB0GS2V2xiXx0bpc-eLcERBgD7ObTe9Ga4VuLcEI1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:22:59\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;956491dc286e4a9f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:18,287 - DEBUG - Finished Request
2025-06-27 19:23:18,893 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:18,902 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:18,902 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:18,902 - DEBUG - Finished Request
2025-06-27 19:23:19,903 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:19,908 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:19,908 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:19,909 - DEBUG - Finished Request
2025-06-27 19:23:20,288 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:23:20,295 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:20,295 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:20,295 - DEBUG - Finished Request
2025-06-27 19:23:20,296 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:23:20,304 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:20,304 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:20,304 - DEBUG - Finished Request
2025-06-27 19:23:20,304 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/source {}
2025-06-27 19:23:20,309 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/source HTTP/1.1" 200 0
2025-06-27 19:23:20,309 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'jkMhwG_snMeWylbJUTBDOPv1Ir0JFlz2DuSeh2XY-57a--x2Vr8mp4V3KhACfNLvq2j7gwP432g08dT-rCmuNgWs8jOUU2V0hetT8p2D-sY1:FKyPZ9ZGFfa9PMw33HVQxOtzPk-lrLYirpyhwVnVq0SHpsPJohXywxIbjCnjPz9lvv2yB0GS2V2xiXx0bpc-eLcERBgD7ObTe9Ga4VuLcEI1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:22:59\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;956491dc286e4a9f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:20,309 - DEBUG - Finished Request
2025-06-27 19:23:20,909 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:20,915 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:20,915 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:20,915 - DEBUG - Finished Request
2025-06-27 19:23:21,916 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:21,923 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:21,923 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:21,923 - DEBUG - Finished Request
2025-06-27 19:23:22,310 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:23:22,318 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:22,318 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:22,318 - DEBUG - Finished Request
2025-06-27 19:23:22,319 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:23:22,326 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:22,326 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:22,326 - DEBUG - Finished Request
2025-06-27 19:23:22,326 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/source {}
2025-06-27 19:23:22,330 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/source HTTP/1.1" 200 0
2025-06-27 19:23:22,330 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.05px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'jkMhwG_snMeWylbJUTBDOPv1Ir0JFlz2DuSeh2XY-57a--x2Vr8mp4V3KhACfNLvq2j7gwP432g08dT-rCmuNgWs8jOUU2V0hetT8p2D-sY1:FKyPZ9ZGFfa9PMw33HVQxOtzPk-lrLYirpyhwVnVq0SHpsPJohXywxIbjCnjPz9lvv2yB0GS2V2xiXx0bpc-eLcERBgD7ObTe9Ga4VuLcEI1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:22:59\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;956491dc286e4a9f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10215', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:22,331 - DEBUG - Finished Request
2025-06-27 19:23:22,924 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:22,931 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:22,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:22,931 - DEBUG - Finished Request
2025-06-27 19:23:23,932 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:23,937 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:23,937 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:23,937 - DEBUG - Finished Request
2025-06-27 19:23:24,263 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,271 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,271 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,271 - DEBUG - Finished Request
2025-06-27 19:23:24,271 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,277 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,277 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,278 - DEBUG - Finished Request
2025-06-27 19:23:24,278 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,282 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,282 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,282 - DEBUG - Finished Request
2025-06-27 19:23:24,283 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:23:24,292 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,292 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,292 - DEBUG - Finished Request
2025-06-27 19:23:24,292 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:23:24,298 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,298 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,298 - DEBUG - Finished Request
2025-06-27 19:23:24,299 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:23:24,304 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,304 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,305 - DEBUG - Finished Request
2025-06-27 19:23:24,305 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:23:24,311 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,312 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,312 - DEBUG - Finished Request
2025-06-27 19:23:24,312 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': '.btn-edit'}
2025-06-27 19:23:24,320 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,321 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,321 - DEBUG - Finished Request
2025-06-27 19:23:24,321 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'a[href*="edit"]'}
2025-06-27 19:23:24,331 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,331 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,331 - DEBUG - Finished Request
2025-06-27 19:23:24,331 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,335 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,336 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,336 - DEBUG - Finished Request
2025-06-27 19:23:24,336 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[name*="captcha"]'}
2025-06-27 19:23:24,345 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,345 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,345 - DEBUG - Finished Request
2025-06-27 19:23:24,345 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[id*="captcha"]'}
2025-06-27 19:23:24,353 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,353 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,353 - DEBUG - Finished Request
2025-06-27 19:23:24,353 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證"]'}
2025-06-27 19:23:24,361 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,361 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,362 - DEBUG - Finished Request
2025-06-27 19:23:24,362 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證碼"]'}
2025-06-27 19:23:24,370 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,370 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,371 - DEBUG - Finished Request
2025-06-27 19:23:24,371 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[name*="code"]'}
2025-06-27 19:23:24,380 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,380 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,380 - DEBUG - Finished Request
2025-06-27 19:23:24,381 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[name*="verify"]'}
2025-06-27 19:23:24,388 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,389 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,389 - DEBUG - Finished Request
2025-06-27 19:23:24,389 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="4"]'}
2025-06-27 19:23:24,396 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,396 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,397 - DEBUG - Finished Request
2025-06-27 19:23:24,397 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="5"]'}
2025-06-27 19:23:24,405 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,405 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,406 - DEBUG - Finished Request
2025-06-27 19:23:24,406 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,410 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,410 - DEBUG - Finished Request
2025-06-27 19:23:24,410 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'img[src*="captcha"]'}
2025-06-27 19:23:24,417 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,418 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,418 - DEBUG - Finished Request
2025-06-27 19:23:24,418 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'img[src*="verify"]'}
2025-06-27 19:23:24,425 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,426 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,426 - DEBUG - Finished Request
2025-06-27 19:23:24,426 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'img[alt*="驗證"]'}
2025-06-27 19:23:24,433 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,434 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,434 - DEBUG - Finished Request
2025-06-27 19:23:24,434 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'img[alt*="驗證碼"]'}
2025-06-27 19:23:24,440 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,441 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,441 - DEBUG - Finished Request
2025-06-27 19:23:24,441 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,445 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,445 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,446 - DEBUG - Finished Request
2025-06-27 19:23:24,446 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '確認取得驗證碼')]"}
2025-06-27 19:23:24,453 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,453 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,453 - DEBUG - Finished Request
2025-06-27 19:23:24,453 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:23:24,462 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,462 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,462 - DEBUG - Finished Request
2025-06-27 19:23:24,462 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:23:24,467 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,467 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,467 - DEBUG - Finished Request
2025-06-27 19:23:24,467 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': '.captcha-refresh'}
2025-06-27 19:23:24,475 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,476 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,476 - DEBUG - Finished Request
2025-06-27 19:23:24,476 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,480 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,480 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,480 - DEBUG - Finished Request
2025-06-27 19:23:24,480 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '送出')]"}
2025-06-27 19:23:24,489 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,489 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,489 - DEBUG - Finished Request
2025-06-27 19:23:24,489 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[value="送出"]'}
2025-06-27 19:23:24,498 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,499 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,499 - DEBUG - Finished Request
2025-06-27 19:23:24,499 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[type="submit"]'}
2025-06-27 19:23:24,510 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,510 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,510 - DEBUG - Finished Request
2025-06-27 19:23:24,510 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'button[type="submit"]'}
2025-06-27 19:23:24,518 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,518 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,518 - DEBUG - Finished Request
2025-06-27 19:23:24,518 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': '.btn-submit'}
2025-06-27 19:23:24,525 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,525 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,525 - DEBUG - Finished Request
2025-06-27 19:23:24,526 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,529 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,530 - DEBUG - Finished Request
2025-06-27 19:23:24,530 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': "//*[contains(text(), '取消')]"}
2025-06-27 19:23:24,536 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,536 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,537 - DEBUG - Finished Request
2025-06-27 19:23:24,537 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'input[value="取消"]'}
2025-06-27 19:23:24,545 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,546 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,546 - DEBUG - Finished Request
2025-06-27 19:23:24,546 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': '.btn-cancel'}
2025-06-27 19:23:24,552 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,552 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,552 - DEBUG - Finished Request
2025-06-27 19:23:24,553 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,557 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,557 - DEBUG - Finished Request
2025-06-27 19:23:24,557 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'table'}
2025-06-27 19:23:24,565 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,565 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,565 - DEBUG - Finished Request
2025-06-27 19:23:24,565 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': '.table'}
2025-06-27 19:23:24,571 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,572 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,572 - DEBUG - Finished Request
2025-06-27 19:23:24,572 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'tbody'}
2025-06-27 19:23:24,579 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,579 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,579 - DEBUG - Finished Request
2025-06-27 19:23:24,579 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'css selector', 'value': 'tr'}
2025-06-27 19:23:24,587 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:23:24,587 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,587 - DEBUG - Finished Request
2025-06-27 19:23:24,587 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:24,595 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:24,596 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,596 - DEBUG - Finished Request
2025-06-27 19:23:24,939 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:24,943 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:24,944 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:24,944 - DEBUG - Finished Request
2025-06-27 19:23:25,097 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:25,107 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:25,107 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:25,107 - DEBUG - Finished Request
2025-06-27 19:23:25,609 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:25,619 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:25,619 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:25,619 - DEBUG - Finished Request
2025-06-27 19:23:25,945 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:25,951 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:25,952 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:25,952 - DEBUG - Finished Request
2025-06-27 19:23:26,120 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:26,133 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:26,133 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:26,133 - DEBUG - Finished Request
2025-06-27 19:23:26,634 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:26,642 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:26,643 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:26,643 - DEBUG - Finished Request
2025-06-27 19:23:26,953 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:26,960 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:26,960 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:26,960 - DEBUG - Finished Request
2025-06-27 19:23:27,144 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:27,152 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:27,152 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:27,153 - DEBUG - Finished Request
2025-06-27 19:23:27,654 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:27,664 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:27,664 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:27,664 - DEBUG - Finished Request
2025-06-27 19:23:27,961 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:27,968 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:27,969 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:27,969 - DEBUG - Finished Request
2025-06-27 19:23:28,165 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:28,172 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:28,172 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:28,173 - DEBUG - Finished Request
2025-06-27 19:23:28,674 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:28,684 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:28,684 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:28,685 - DEBUG - Finished Request
2025-06-27 19:23:28,969 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:28,976 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:28,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:28,976 - DEBUG - Finished Request
2025-06-27 19:23:29,185 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:29,193 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:29,193 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:29,194 - DEBUG - Finished Request
2025-06-27 19:23:29,696 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:29,705 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:29,705 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:29,705 - DEBUG - Finished Request
2025-06-27 19:23:29,976 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:29,984 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:29,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:29,984 - DEBUG - Finished Request
2025-06-27 19:23:30,206 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:30,216 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:30,216 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:30,216 - DEBUG - Finished Request
2025-06-27 19:23:30,718 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:30,728 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:30,728 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:30,728 - DEBUG - Finished Request
2025-06-27 19:23:30,985 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:30,991 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:30,992 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:30,992 - DEBUG - Finished Request
2025-06-27 19:23:31,230 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:31,240 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:31,240 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:31,241 - DEBUG - Finished Request
2025-06-27 19:23:31,742 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:31,749 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:31,749 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:31,750 - DEBUG - Finished Request
2025-06-27 19:23:31,993 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:32,000 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:32,001 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:32,001 - DEBUG - Finished Request
2025-06-27 19:23:32,250 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:32,325 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:32,325 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:32,325 - DEBUG - Finished Request
2025-06-27 19:23:32,827 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:32,836 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:32,836 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:32,837 - DEBUG - Finished Request
2025-06-27 19:23:33,002 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:33,010 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:33,010 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:33,010 - DEBUG - Finished Request
2025-06-27 19:23:33,339 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:33,349 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:33,349 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:33,350 - DEBUG - Finished Request
2025-06-27 19:23:33,851 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:33,860 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:33,860 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:33,861 - DEBUG - Finished Request
2025-06-27 19:23:34,011 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:34,044 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:34,046 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:34,046 - DEBUG - Finished Request
2025-06-27 19:23:34,362 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:34,371 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:34,371 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:34,372 - DEBUG - Finished Request
2025-06-27 19:23:34,873 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:34,883 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:34,883 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:34,885 - DEBUG - Finished Request
2025-06-27 19:23:35,047 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:35,055 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:35,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:35,056 - DEBUG - Finished Request
2025-06-27 19:23:35,386 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:35,399 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:35,399 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:35,400 - DEBUG - Finished Request
2025-06-27 19:23:35,901 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:35,910 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:35,910 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:35,911 - DEBUG - Finished Request
2025-06-27 19:23:36,057 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:36,064 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:36,065 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:36,065 - DEBUG - Finished Request
2025-06-27 19:23:36,412 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:36,422 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:36,423 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:36,423 - DEBUG - Finished Request
2025-06-27 19:23:36,925 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:36,934 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:36,934 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:36,935 - DEBUG - Finished Request
2025-06-27 19:23:37,066 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:37,073 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:37,073 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:37,074 - DEBUG - Finished Request
2025-06-27 19:23:37,435 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:37,445 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:37,445 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:37,445 - DEBUG - Finished Request
2025-06-27 19:23:37,946 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:37,958 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:37,958 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:37,958 - DEBUG - Finished Request
2025-06-27 19:23:38,074 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:38,082 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:38,082 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:38,083 - DEBUG - Finished Request
2025-06-27 19:23:38,459 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:38,470 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:38,470 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:38,471 - DEBUG - Finished Request
2025-06-27 19:23:38,972 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:38,983 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:38,983 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:38,983 - DEBUG - Finished Request
2025-06-27 19:23:39,084 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:39,092 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:39,092 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:39,092 - DEBUG - Finished Request
2025-06-27 19:23:39,485 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:39,495 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:39,496 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:39,496 - DEBUG - Finished Request
2025-06-27 19:23:39,997 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:40,006 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:40,006 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:40,006 - DEBUG - Finished Request
2025-06-27 19:23:40,093 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:40,100 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:40,101 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:40,102 - DEBUG - Finished Request
2025-06-27 19:23:40,508 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:40,516 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:40,516 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:40,517 - DEBUG - Finished Request
2025-06-27 19:23:41,018 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:41,027 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:41,027 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:41,028 - DEBUG - Finished Request
2025-06-27 19:23:41,103 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:41,112 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:41,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:41,113 - DEBUG - Finished Request
2025-06-27 19:23:41,529 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:41,542 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:41,543 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:41,543 - DEBUG - Finished Request
2025-06-27 19:23:42,044 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:42,053 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:42,053 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:42,053 - DEBUG - Finished Request
2025-06-27 19:23:42,115 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:42,123 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:42,124 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:42,124 - DEBUG - Finished Request
2025-06-27 19:23:42,555 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:42,565 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:42,565 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:42,566 - DEBUG - Finished Request
2025-06-27 19:23:43,067 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:43,076 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:43,077 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:43,077 - DEBUG - Finished Request
2025-06-27 19:23:43,125 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:43,133 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:43,133 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:43,134 - DEBUG - Finished Request
2025-06-27 19:23:43,578 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:43,589 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:43,590 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:43,590 - DEBUG - Finished Request
2025-06-27 19:23:44,090 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:44,101 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:44,101 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:44,102 - DEBUG - Finished Request
2025-06-27 19:23:44,135 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:44,141 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:44,142 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:44,142 - DEBUG - Finished Request
2025-06-27 19:23:44,603 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:44,613 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:44,614 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:44,614 - DEBUG - Finished Request
2025-06-27 19:23:45,115 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:45,125 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:45,126 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:45,127 - DEBUG - Finished Request
2025-06-27 19:23:45,143 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:45,150 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:45,150 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:45,151 - DEBUG - Finished Request
2025-06-27 19:23:45,627 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:45,636 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:45,637 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:45,637 - DEBUG - Finished Request
2025-06-27 19:23:46,138 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:46,150 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:46,150 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:46,150 - DEBUG - Finished Request
2025-06-27 19:23:46,151 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:46,158 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:46,159 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:46,159 - DEBUG - Finished Request
2025-06-27 19:23:46,652 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:46,663 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:46,663 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:46,664 - DEBUG - Finished Request
2025-06-27 19:23:47,160 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:47,165 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:47,165 - DEBUG - Starting new HTTP connection (2): localhost:55196
2025-06-27 19:23:47,167 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:47,167 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:47,168 - DEBUG - Finished Request
2025-06-27 19:23:47,185 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:47,185 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:23:47,185 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:47,186 - DEBUG - Finished Request
2025-06-27 19:23:47,687 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:47,696 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:47,696 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:47,697 - DEBUG - Finished Request
2025-06-27 19:23:48,169 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:48,174 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:48,175 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:48,175 - DEBUG - Finished Request
2025-06-27 19:23:48,199 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:48,208 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:48,209 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:48,209 - DEBUG - Finished Request
2025-06-27 19:23:48,711 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:48,720 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:48,720 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:48,721 - DEBUG - Finished Request
2025-06-27 19:23:49,176 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:49,182 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:49,182 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:49,183 - DEBUG - Finished Request
2025-06-27 19:23:49,222 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:49,230 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:49,231 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:49,231 - DEBUG - Finished Request
2025-06-27 19:23:49,733 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:49,743 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:49,743 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:49,744 - DEBUG - Finished Request
2025-06-27 19:23:50,184 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:50,192 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:50,193 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:50,193 - DEBUG - Finished Request
2025-06-27 19:23:50,245 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:50,256 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:50,256 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:50,256 - DEBUG - Finished Request
2025-06-27 19:23:50,758 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:50,766 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:50,766 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:50,767 - DEBUG - Finished Request
2025-06-27 19:23:51,194 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:51,200 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:51,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:51,201 - DEBUG - Finished Request
2025-06-27 19:23:51,268 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:51,279 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:51,280 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:51,280 - DEBUG - Finished Request
2025-06-27 19:23:51,782 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:51,793 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:51,793 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:51,794 - DEBUG - Finished Request
2025-06-27 19:23:52,202 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:52,210 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:52,210 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:52,211 - DEBUG - Finished Request
2025-06-27 19:23:52,295 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:52,305 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:52,305 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:52,306 - DEBUG - Finished Request
2025-06-27 19:23:52,807 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:52,818 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:52,818 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:52,819 - DEBUG - Finished Request
2025-06-27 19:23:53,212 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:53,221 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:53,221 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:53,222 - DEBUG - Finished Request
2025-06-27 19:23:53,320 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:53,329 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:53,330 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:53,330 - DEBUG - Finished Request
2025-06-27 19:23:53,831 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:53,843 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:53,843 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:53,843 - DEBUG - Finished Request
2025-06-27 19:23:54,222 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:54,229 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:54,229 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:54,230 - DEBUG - Finished Request
2025-06-27 19:23:54,344 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:54,354 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:54,354 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:54,355 - DEBUG - Finished Request
2025-06-27 19:23:54,856 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:23:54,866 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:54,866 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611405190953')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:54,866 - DEBUG - Finished Request
2025-06-27 19:23:54,867 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:54,878 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:54,878 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:54,879 - DEBUG - Finished Request
2025-06-27 19:23:55,231 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:55,238 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:55,238 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:55,239 - DEBUG - Finished Request
2025-06-27 19:23:55,380 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:55,390 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:55,390 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:55,391 - DEBUG - Finished Request
2025-06-27 19:23:55,893 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:55,905 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:55,906 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:55,906 - DEBUG - Finished Request
2025-06-27 19:23:56,240 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:56,248 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:56,248 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:56,249 - DEBUG - Finished Request
2025-06-27 19:23:56,407 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:56,416 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:56,417 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:56,417 - DEBUG - Finished Request
2025-06-27 19:23:56,919 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:56,929 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:56,929 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:56,929 - DEBUG - Finished Request
2025-06-27 19:23:57,250 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:57,257 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:57,258 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:57,258 - DEBUG - Finished Request
2025-06-27 19:23:57,431 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:57,440 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:57,441 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:57,441 - DEBUG - Finished Request
2025-06-27 19:23:57,943 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:57,953 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:57,954 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:57,954 - DEBUG - Finished Request
2025-06-27 19:23:58,259 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:58,266 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:58,266 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:58,267 - DEBUG - Finished Request
2025-06-27 19:23:58,455 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:58,465 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:58,465 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:58,466 - DEBUG - Finished Request
2025-06-27 19:23:58,967 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:58,980 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:58,980 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:58,981 - DEBUG - Finished Request
2025-06-27 19:23:59,268 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:23:59,277 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:23:59,277 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:59,277 - DEBUG - Finished Request
2025-06-27 19:23:59,482 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:23:59,494 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:23:59,494 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:23:59,495 - DEBUG - Finished Request
2025-06-27 19:23:59,996 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:00,007 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:00,007 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:00,008 - DEBUG - Finished Request
2025-06-27 19:24:00,279 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:00,287 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:00,287 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:00,288 - DEBUG - Finished Request
2025-06-27 19:24:00,509 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:00,521 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:00,521 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:00,522 - DEBUG - Finished Request
2025-06-27 19:24:01,023 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:01,035 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:01,035 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:01,036 - DEBUG - Finished Request
2025-06-27 19:24:01,289 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:01,296 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:01,296 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:01,296 - DEBUG - Finished Request
2025-06-27 19:24:01,536 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:01,547 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:01,548 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:01,548 - DEBUG - Finished Request
2025-06-27 19:24:02,049 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:02,061 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:02,061 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:02,062 - DEBUG - Finished Request
2025-06-27 19:24:02,298 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:02,305 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:02,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:02,306 - DEBUG - Finished Request
2025-06-27 19:24:02,563 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:02,571 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:02,572 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:02,572 - DEBUG - Finished Request
2025-06-27 19:24:03,073 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:03,081 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:03,082 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:03,082 - DEBUG - Finished Request
2025-06-27 19:24:03,307 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:03,314 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:03,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:03,314 - DEBUG - Finished Request
2025-06-27 19:24:03,583 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:03,593 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:03,593 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:03,593 - DEBUG - Finished Request
2025-06-27 19:24:04,094 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:04,104 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:04,104 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:04,105 - DEBUG - Finished Request
2025-06-27 19:24:04,315 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:04,322 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:04,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:04,322 - DEBUG - Finished Request
2025-06-27 19:24:04,606 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:04,615 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:04,616 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:04,616 - DEBUG - Finished Request
2025-06-27 19:24:05,117 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:05,129 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:05,129 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:05,129 - DEBUG - Finished Request
2025-06-27 19:24:05,323 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:05,328 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:05,328 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:05,329 - DEBUG - Finished Request
2025-06-27 19:24:05,630 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:05,640 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:05,640 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:05,640 - DEBUG - Finished Request
2025-06-27 19:24:06,142 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:06,150 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:06,150 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:06,151 - DEBUG - Finished Request
2025-06-27 19:24:06,330 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:06,338 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:06,339 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:06,339 - DEBUG - Finished Request
2025-06-27 19:24:06,652 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:06,663 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:06,664 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:06,664 - DEBUG - Finished Request
2025-06-27 19:24:07,165 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:07,173 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:07,174 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:07,174 - DEBUG - Finished Request
2025-06-27 19:24:07,339 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:07,345 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:07,346 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:07,346 - DEBUG - Finished Request
2025-06-27 19:24:07,675 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:07,686 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:07,686 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:07,687 - DEBUG - Finished Request
2025-06-27 19:24:08,188 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:08,197 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:08,197 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:08,197 - DEBUG - Finished Request
2025-06-27 19:24:08,347 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:08,354 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:08,354 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:08,354 - DEBUG - Finished Request
2025-06-27 19:24:08,698 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:08,705 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:08,705 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:08,705 - DEBUG - Finished Request
2025-06-27 19:24:09,207 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:09,216 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:09,216 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:09,216 - DEBUG - Finished Request
2025-06-27 19:24:09,355 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:09,362 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:09,363 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:09,363 - DEBUG - Finished Request
2025-06-27 19:24:09,717 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:09,726 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:09,726 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:09,727 - DEBUG - Finished Request
2025-06-27 19:24:10,227 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:10,237 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:10,237 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:10,237 - DEBUG - Finished Request
2025-06-27 19:24:10,364 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:10,371 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:10,372 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:10,372 - DEBUG - Finished Request
2025-06-27 19:24:10,738 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:10,748 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:10,748 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:10,748 - DEBUG - Finished Request
2025-06-27 19:24:11,249 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:11,257 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:11,257 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:11,257 - DEBUG - Finished Request
2025-06-27 19:24:11,373 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:11,381 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:11,381 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:11,381 - DEBUG - Finished Request
2025-06-27 19:24:11,758 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:11,769 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:11,769 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:11,769 - DEBUG - Finished Request
2025-06-27 19:24:12,270 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:12,278 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:12,278 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:12,278 - DEBUG - Finished Request
2025-06-27 19:24:12,382 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:12,389 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:12,389 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:12,389 - DEBUG - Finished Request
2025-06-27 19:24:12,778 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:12,787 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:12,787 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:12,788 - DEBUG - Finished Request
2025-06-27 19:24:13,289 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:13,297 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:13,297 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:13,297 - DEBUG - Finished Request
2025-06-27 19:24:13,390 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:13,396 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:13,396 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:13,396 - DEBUG - Finished Request
2025-06-27 19:24:13,797 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:13,805 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:13,805 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:13,805 - DEBUG - Finished Request
2025-06-27 19:24:14,306 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:14,315 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:14,315 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:14,316 - DEBUG - Finished Request
2025-06-27 19:24:14,397 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:14,402 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:14,403 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:14,403 - DEBUG - Finished Request
2025-06-27 19:24:14,816 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:14,824 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:14,824 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:14,824 - DEBUG - Finished Request
2025-06-27 19:24:15,325 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:24:15,335 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/element HTTP/1.1" 404 0
2025-06-27 19:24:15,336 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d70766]\n\t(No symbol) [0x0x7ff7d7d70a1c]\n\t(No symbol) [0x0x7ff7d7dc4467]\n\t(No symbol) [0x0x7ff7d7d98bcf]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:15,336 - DEBUG - Finished Request
2025-06-27 19:24:15,336 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:24:15,349 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:24:15,349 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:15,350 - DEBUG - Finished Request
2025-06-27 19:24:15,403 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:15,411 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:15,411 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:15,411 - DEBUG - Finished Request
2025-06-27 19:24:15,850 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:24:15,857 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:24:15,858 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:15,858 - DEBUG - Finished Request
2025-06-27 19:24:16,358 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:24:16,366 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:24:16,366 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:16,366 - DEBUG - Finished Request
2025-06-27 19:24:16,412 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:16,418 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 200 0
2025-06-27 19:24:16,418 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:16,418 - DEBUG - Finished Request
2025-06-27 19:24:16,867 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:24:16,876 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 200 0
2025-06-27 19:24:16,876 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:16,876 - DEBUG - Finished Request
2025-06-27 19:24:17,377 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:24:17,378 - DEBUG - http://localhost:55196 "POST /session/75db2ce6f6c55342ac0a1b54777b3744/elements HTTP/1.1" 404 0
2025-06-27 19:24:17,378 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d059b5]\n\t(No symbol) [0x0x7ff7d7d2a9ca]\n\t(No symbol) [0x0x7ff7d7da05e5]\n\t(No symbol) [0x0x7ff7d7dc0b42]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:17,379 - DEBUG - Finished Request
2025-06-27 19:24:17,419 - DEBUG - GET http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/url {}
2025-06-27 19:24:17,420 - DEBUG - http://localhost:55196 "GET /session/75db2ce6f6c55342ac0a1b54777b3744/url HTTP/1.1" 404 0
2025-06-27 19:24:17,421 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d199fc]\n\t(No symbol) [0x0x7ff7d7d607df]\n\t(No symbol) [0x0x7ff7d7d98a52]\n\t(No symbol) [0x0x7ff7d7d93413]\n\t(No symbol) [0x0x7ff7d7d924d9]\n\t(No symbol) [0x0x7ff7d7ce5d55]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\t(No symbol) [0x0x7ff7d7ce4dca]\n\tGetHandleVerifier [0x0x7ff7d83545e8+4238440]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:17,421 - DEBUG - Finished Request
2025-06-27 19:24:17,421 - DEBUG - DELETE http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744 {}
2025-06-27 19:24:17,422 - DEBUG - http://localhost:55196 "DELETE /session/75db2ce6f6c55342ac0a1b54777b3744 HTTP/1.1" 200 0
2025-06-27 19:24:17,422 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:24:17,422 - DEBUG - Finished Request
2025-06-27 19:24:19,380 - DEBUG - POST http://localhost:55196/session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync {'script': '\n            // 檢查頁面最終狀態\n            var bodyText = document.body.innerText || document.body.textCon...', 'args': []}
2025-06-27 19:24:19,381 - DEBUG - Starting new HTTP connection (1): localhost:55196
2025-06-27 19:24:23,484 - DEBUG - Incremented Retry for (url='/session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-06-27 19:24:23,485 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D777410790>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync
2025-06-27 19:24:23,485 - DEBUG - Starting new HTTP connection (2): localhost:55196
2025-06-27 19:24:27,598 - DEBUG - Incremented Retry for (url='/session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-27 19:24:27,598 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D777410E90>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync
2025-06-27 19:24:27,599 - DEBUG - Starting new HTTP connection (3): localhost:55196
2025-06-27 19:24:31,690 - DEBUG - Incremented Retry for (url='/session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-27 19:24:31,691 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D777411910>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/75db2ce6f6c55342ac0a1b54777b3744/execute/sync
2025-06-27 19:24:31,691 - DEBUG - Starting new HTTP connection (4): localhost:55196
2025-06-27 19:24:35,759 - ERROR - 尋找編輯按鈕失敗: 'NoneType' object has no attribute 'current_url'
