# mvp_grabber.py v1.2
# AGES-KH 搶單主程式

import tkinter as tk
from tkinter import simpledialog, messagebox
import csv
from datetime import datetime, date
import os
import sys
import psutil
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
import threading
import time

__VERSION__ = "1.2"

driver = None  # 全域 driver
chrome_pid = None  # 追蹤由程式啟動的 Chrome PID

# ===== 安全離開主程式 =====
def safe_exit():
    global driver, chrome_pid
    print("[INFO] 終止主程式...")
    try:
        if driver:
            try:
                driver.quit()
            except:
                pass
            finally:
                driver = None
    except Exception as e:
        print(f"[ERROR] 關閉 driver 發生錯誤: {str(e)}")

    try:
        for widget in tk._default_root.children.values():
            widget.destroy()
        tk._default_root.quit()
    except:
        pass

    chrome_pid = None
    os._exit(0)

# ===== GUI：詢問使用者輸入觸發時間（支援毫秒） =====
def ask_trigger_time_gui(default="09:30:00.001") -> str:
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    answer = simpledialog.askstring("觸發時間設定 (支援毫秒)", 
                                     "請輸入本次搶單觸發時間 (格式 HH:MM:SS.sss)：",
                                     initialvalue=default)
    if not answer:
        return default
    return answer.strip()

# ===== 載入 orders.csv，回傳今日或萬用單號列表 =====
def normalize_date(date_str: str) -> str:
    try:
        dt = datetime.strptime(date_str.replace("/", "-"), "%Y-%m-%d")
        return dt.strftime("%Y-%m-%d")
    except Exception:
        return date_str

def load_orders_from_csv(path="orders/orders.csv") -> list:
    today_str = date.today().strftime("%Y-%m-%d")
    orders = []
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            row_date = normalize_date(row['date']) if 'date' in row else normalize_date(row['exec_date'])
            if row_date == today_str or row_date == '*-*-*':
                orders.append(row['order_id'])
    return orders

# ===== GUI 等待使用者登入平台後繼續，並支援取消或直接關閉 =====
def wait_for_user_to_login():
    def on_continue():
        window.attributes('-topmost', False)
        window.destroy()

    def on_cancel():
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()

    def on_close():
        if messagebox.askyesno("確認離開", "是否要取消並離開主程式？"):
            window.attributes('-topmost', False)
            window.destroy()
            safe_exit()

    window = tk.Tk()
    window.title(f"登入提示 - PID: {os.getpid()}")
    window.attributes('-topmost', True)

    tk.Label(window, text="請手動登入平台並操作至查詢頁面\n完成後請按下 [繼續]，或取消離開。", padx=20, pady=20).pack()

    chrome_pid_text = f"Python PID: {os.getpid()} | Chrome PID: {chrome_pid if chrome_pid else 'N/A'}"
    tk.Label(window, text=chrome_pid_text, fg="gray").pack()

    btn_frame = tk.Frame(window)
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="繼續", command=on_continue, width=10).pack(side="left", padx=5)
    tk.Button(btn_frame, text="取消", command=on_cancel, width=10).pack(side="left", padx=5)
    window.mainloop()

# ===== 背景監測 Chrome 是否被關閉 =====
def start_driver_monitor():
    def monitor():
        global chrome_pid
        while True:
            try:
                if chrome_pid and not psutil.pid_exists(chrome_pid):
                    print("[WARN] 偵測到 Python 啟動的 Chrome 已關閉")
                    safe_exit()
                    break
                driver.current_url
                time.sleep(1)
            except (WebDriverException, AttributeError):
                print("[WARN] WebDriver 連線異常")
                safe_exit()
                break
            except Exception as e:
                print(f"[ERROR] 監控錯誤: {str(e)}")
                safe_exit()
                break

    threading.Thread(target=monitor, daemon=True).start()

# ===== 主執行區 =====
def main():
    global driver, chrome_pid
    print("[INFO] 啟動 AGES-KH 搶單主程式 v" + __VERSION__)
    print(f"[DEBUG] Python PID: {os.getpid()}")

    # Step 0: GUI 詢問觸發時間（含毫秒）
    trigger_time_str = ask_trigger_time_gui()
    print(f"[INFO] 使用觸發時間：{trigger_time_str}")

    # Step 1: 載入單號
    try:
        order_list = load_orders_from_csv()
        if not order_list:
            messagebox.showwarning("無單可處理", "今天無需處理的單據。請確認 orders.csv。")
            sys.exit(0)
        print(f"[INFO] 今日需處理 {len(order_list)} 筆單據：{order_list}")
    except Exception as e:
        messagebox.showerror("讀取 orders.csv 失敗", str(e))
        sys.exit(1)

    # Step 2: 顯示登入提示並開啟平台網站
    print("[INFO] 開啟瀏覽器並等待使用者登入...")
    driver = webdriver.Chrome()
    chrome_pid = driver.service.process.pid
    print(f"[DEBUG] Chrome PID: {chrome_pid}")
    driver.get("https://wmc.kcg.gov.tw/")
    start_driver_monitor()

    wait_for_user_to_login()

    print("[INFO] 使用者已按下繼續，準備進入搶單流程...（下一步未實作）")

if __name__ == '__main__':
    main()
