2025-06-30 18:42:11,333 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250630_184211.log
2025-06-30 18:42:19,101 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-30 18:42:19,102 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-30 18:42:19,177 - DEBUG - chromedriver not found in PATH
2025-06-30 18:42:19,177 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:42:19,177 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-30 18:42:19,178 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-30 18:42:19,178 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-30 18:42:19,178 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-30 18:42:19,178 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-30 18:42:19,182 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 20380 using 0 to output -3
2025-06-30 18:42:19,692 - DEBUG - POST http://localhost:53620/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-30 18:42:19,692 - DEBUG - Starting new HTTP connection (1): localhost:53620
2025-06-30 18:42:20,225 - DEBUG - http://localhost:53620 "POST /session HTTP/1.1" 200 0
2025-06-30 18:42:20,225 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir20380_946670553"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53623"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"2512b49db4e9a3f5c3d8a19790ffd868"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:20,226 - DEBUG - Finished Request
2025-06-30 18:42:20,226 - DEBUG - POST http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-30 18:42:22,114 - DEBUG - http://localhost:53620 "POST /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:22,114 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:22,115 - DEBUG - Finished Request
2025-06-30 18:42:22,115 - INFO - 🎯 設置瀏覽器事件監控...
2025-06-30 18:42:22,115 - DEBUG - POST http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-06-30 18:42:22,124 - DEBUG - http://localhost:53620 "POST /session/2512b49db4e9a3f5c3d8a19790ffd868/execute/sync HTTP/1.1" 200 0
2025-06-30 18:42:22,124 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:22,124 - DEBUG - Finished Request
2025-06-30 18:42:22,124 - INFO - ✅ 瀏覽器事件監控已啟動
2025-06-30 18:42:22,125 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:22,155 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:22,155 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:22,156 - DEBUG - Finished Request
2025-06-30 18:42:23,157 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:23,165 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:23,166 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:23,166 - DEBUG - Finished Request
2025-06-30 18:42:24,166 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:24,172 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:24,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:24,172 - DEBUG - Finished Request
2025-06-30 18:42:25,173 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:25,179 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:25,179 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:25,179 - DEBUG - Finished Request
2025-06-30 18:42:26,180 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:26,187 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:26,188 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:26,188 - DEBUG - Finished Request
2025-06-30 18:42:27,189 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:27,195 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:27,196 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:27,196 - DEBUG - Finished Request
2025-06-30 18:42:28,197 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:28,206 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:28,207 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:28,207 - DEBUG - Finished Request
2025-06-30 18:42:29,208 - DEBUG - GET http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868/url {}
2025-06-30 18:42:29,217 - DEBUG - http://localhost:53620 "GET /session/2512b49db4e9a3f5c3d8a19790ffd868/url HTTP/1.1" 200 0
2025-06-30 18:42:29,217 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:29,217 - DEBUG - Finished Request
2025-06-30 18:42:29,809 - DEBUG - DELETE http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868 {}
2025-06-30 18:42:29,850 - DEBUG - http://localhost:53620 "DELETE /session/2512b49db4e9a3f5c3d8a19790ffd868 HTTP/1.1" 200 0
2025-06-30 18:42:29,851 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-30 18:42:29,851 - DEBUG - Finished Request
2025-06-30 18:42:30,237 - DEBUG - DELETE http://localhost:53620/session/2512b49db4e9a3f5c3d8a19790ffd868 {}
2025-06-30 18:42:30,238 - DEBUG - Starting new HTTP connection (1): localhost:53620
