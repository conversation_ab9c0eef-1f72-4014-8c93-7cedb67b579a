import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
from rtt_config_manager import RTTConfigManager
from rtt_predictor import get_avg_rtt

class RTTConfigGUI:
    def __init__(self):
        self.config_manager = RTTConfigManager()
        self.window = None
        self.rtt_result = None
        self.is_running = False
        self.start_time = None
        self.total_samples = 0
        self.current_sample = 0
        self.latest_rtt = 0
        self.duration = 0

    def show(self):
        """顯示 RTT 設定 GUI"""
        self.window = tk.Tk()  # 使用 Tk 而不是 Toplevel
        self.window.title("RTT 設定")
        self.window.geometry("500x600")  # 恢復原高度
        self.window.resizable(False, False)

        # 載入當前設定
        config = self.config_manager.load_config()

        # 建立變數
        self.freq_var = tk.StringVar(value=str(config['freq']))
        self.duration_var = tk.StringVar(value=str(config['duration']))
        self.server_url_var = tk.StringVar(value=config['server_url'])

        # 建立 GUI 元件
        self._create_widgets()

        # 啟動主迴圈
        self.window.mainloop()

    def _create_widgets(self):
        """建立 GUI 元件"""
        # 參數來源顯示框架
        source_frame = ttk.LabelFrame(self.window, text="參數來源", padding=10)
        source_frame.pack(fill="x", padx=10, pady=5)
        
        # 檢查是否使用預設值
        config = self.config_manager.load_config()
        is_using_default = config == self.config_manager.default_config
        
        if is_using_default:
            source_text = "目前使用：程式預設值"
            source_color = "blue"
        else:
            source_text = "目前使用：使用者自訂設定"
            source_color = "green"
            
        self.source_label = ttk.Label(source_frame, text=source_text, foreground=source_color, font=("Arial", 10, "bold"))
        self.source_label.pack(fill="x", pady=2)
        
        # 版本資訊
        version_text = f"RTT 模組版本：{config.get('rtt_module_version', 'unknown')}"
        ttk.Label(source_frame, text=version_text, foreground="gray").pack(fill="x", pady=2)

        # RTT 採樣設定框架
        default_frame = ttk.LabelFrame(self.window, text="RTT 採樣設定", padding=10)
        default_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(default_frame, text="採樣頻率 (次/秒):").grid(row=0, column=0, sticky="w", pady=2)
        ttk.Entry(default_frame, textvariable=self.freq_var, width=10).grid(row=0, column=1, sticky="w", pady=2)

        ttk.Label(default_frame, text="採樣時長 (秒):").grid(row=1, column=0, sticky="w", pady=2)
        ttk.Entry(default_frame, textvariable=self.duration_var, width=10).grid(row=1, column=1, sticky="w", pady=2)

        ttk.Label(default_frame, text="目標網址:").grid(row=2, column=0, sticky="w", pady=2)
        ttk.Entry(default_frame, textvariable=self.server_url_var, width=40).grid(row=2, column=1, sticky="w", pady=2)

        # RTT 執行狀態框架
        self.status_frame = ttk.LabelFrame(self.window, text="RTT 執行狀態", padding=10)
        self.status_frame.pack(fill="x", padx=10, pady=5)

        # 狀態標籤
        self.status_label = ttk.Label(self.status_frame, text="尚未執行")
        self.status_label.pack(fill="x", pady=2)

        # 進度條
        self.progress_bar = ttk.Progressbar(self.status_frame, mode='determinate')
        self.progress_bar.pack(fill="x", pady=2)

        # 進度文字
        self.progress_var = tk.StringVar(value="")
        self.progress_label = ttk.Label(self.status_frame, textvariable=self.progress_var)
        self.progress_label.pack(fill="x", pady=2)

        # 即時 RTT 值
        self.latest_rtt_var = tk.StringVar(value="")
        self.latest_rtt_label = ttk.Label(self.status_frame, textvariable=self.latest_rtt_var)
        self.latest_rtt_label.pack(fill="x", pady=2)

        # 剩餘時間
        self.remaining_time_var = tk.StringVar(value="")
        self.remaining_time_label = ttk.Label(self.status_frame, textvariable=self.remaining_time_var)
        self.remaining_time_label.pack(fill="x", pady=2)

        # 結果
        self.result_var = tk.StringVar(value="")
        self.result_label = ttk.Label(self.status_frame, textvariable=self.result_var)
        self.result_label.pack(fill="x", pady=2)

        # 按鈕框架
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(button_frame, text="載入預設值", command=self._load_default).pack(side="left", padx=5)
        ttk.Button(button_frame, text="執行 RTT", command=self._run_rtt).pack(side="left", padx=5)
        ttk.Button(button_frame, text="儲存設定", command=self._save_config).pack(side="left", padx=5)
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side="right", padx=5)

    def _update_progress(self):
        """更新進度顯示"""
        if not self.is_running:
            return

        # 更新進度條
        progress = (self.current_sample / self.total_samples) * 100
        self.progress_bar['value'] = progress

        # 更新進度文字
        self.progress_var.set(f"採樣進度: {self.current_sample}/{self.total_samples} 筆")

        # 更新即時 RTT
        self.latest_rtt_var.set(f"即時 RTT: {self.latest_rtt:.2f} ms")

        # 更新剩餘時間
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            remaining = timedelta(seconds=self.duration) - elapsed
            if remaining.total_seconds() > 0:
                self.remaining_time_var.set(f"剩餘時間: {remaining.seconds} 秒")
            else:
                self.remaining_time_var.set("剩餘時間: 0 秒")

        # 繼續更新
        if self.is_running:
            self.window.after(100, self._update_progress)

    def _run_rtt(self):
        """執行 RTT 測試"""
        if self.is_running:
            messagebox.showwarning("警告", "RTT 測試正在執行中")
            return

        try:
            # 在主執行緒中讀取所有 Tkinter 變數
            freq = int(self.freq_var.get())
            duration = int(self.duration_var.get())
            server_url = self.server_url_var.get()

            if freq <= 0 or duration <= 0:
                raise ValueError("數值必須大於 0")

            # 更新狀態
            self.is_running = True
            self.start_time = datetime.now()
            self.total_samples = freq * duration
            self.current_sample = 0
            self.latest_rtt = 0
            self.duration = duration

            self.status_label.config(text="執行中...")
            self.progress_var.set("")
            self.latest_rtt_var.set("")
            self.remaining_time_var.set("")
            self.result_var.set("")
            self.progress_bar['value'] = 0

            # 開始更新進度
            self._update_progress()

            # 在新執行緒中執行 RTT 測試，傳入所有需要的參數
            thread = threading.Thread(
                target=self._run_rtt_thread,
                args=(freq, duration, server_url)
            )
            thread.daemon = True
            thread.start()

        except ValueError as e:
            messagebox.showerror("錯誤", f"輸入值無效: {str(e)}")
        except Exception as e:
            messagebox.showerror("錯誤", f"執行 RTT 測試失敗: {str(e)}")
            self.is_running = False

    def _run_rtt_thread(self, freq, duration, server_url):
        """在新執行緒中執行 RTT 測試"""
        try:
            # 執行 RTT 測試，使用傳入的參數
            avg_rtt, rtts, timestamps = get_avg_rtt(
                model="A",
                freq=freq,
                duration=duration,
                server_url=server_url
            )

            # 更新結果
            self.rtt_result = {
                'avg_rtt': avg_rtt,
                'rtts': rtts,
                'timestamps': timestamps
            }

            # 使用 after 方法在主執行緒中更新 GUI
            self.window.after(0, self._update_rtt_result)

        except Exception as e:
            print(f"[ERROR] RTT 測試失敗: {str(e)}")  # 加入除錯訊息
            self.window.after(0, lambda: messagebox.showerror("錯誤", f"RTT 測試失敗: {str(e)}"))
        finally:
            self.is_running = False
            self.window.after(0, lambda: self.status_label.config(text="執行完成"))

    def _update_rtt_result(self):
        """更新 RTT 測試結果"""
        if not hasattr(self, 'rtt_result') or not self.rtt_result:
            return

        try:
            # 更新進度
            self.current_sample = len(self.rtt_result['rtts'])
            self.latest_rtt = self.rtt_result['rtts'][-1] if self.rtt_result['rtts'] else 0

            # 更新顯示
            self.progress_var.set(f"採樣完成: {self.current_sample}/{self.total_samples} 筆")
            self.result_var.set(f"平均 RTT: {self.rtt_result['avg_rtt']:.2f} ms")
            self.progress_bar['value'] = 100
            self.remaining_time_var.set("")
        except Exception as e:
            print(f"[ERROR] 更新結果失敗: {str(e)}")  # 加入除錯訊息
            messagebox.showerror("錯誤", f"更新結果失敗: {str(e)}")

    def _load_default(self):
        """載入預設值"""
        config = self.config_manager.default_config
        self.freq_var.set(str(config['freq']))
        self.duration_var.set(str(config['duration']))
        self.server_url_var.set(config['server_url'])
        
        # 更新參數來源顯示
        self.source_label.config(text="目前使用：程式預設值", foreground="blue")

    def _save_config(self):
        """儲存設定"""
        try:
            # 驗證輸入
            freq = int(self.freq_var.get())
            duration = int(self.duration_var.get())

            if freq <= 0 or duration <= 0:
                raise ValueError("數值必須大於 0")

            # 建立新設定（包含版本號）
            new_config = {
                "rtt_module_version": self.config_manager.rtt_module_version,  # 加入版本號
                "freq": freq,
                "duration": duration,
                "server_url": self.server_url_var.get()
            }

            # 儲存設定
            if self.config_manager.save_config(new_config):
                messagebox.showinfo("成功", "設定已儲存")
                # 更新參數來源顯示
                self.source_label.config(text="目前使用：使用者自訂設定", foreground="green")
                self.window.destroy()
            else:
                messagebox.showerror("錯誤", "儲存設定失敗")
        except ValueError as e:
            messagebox.showerror("錯誤", f"輸入值無效: {str(e)}")
        except Exception as e:
            messagebox.showerror("錯誤", f"儲存設定失敗: {str(e)}")

if __name__ == "__main__":
    # 獨立執行時的測試程式碼
    gui = RTTConfigGUI()
    gui.show() 