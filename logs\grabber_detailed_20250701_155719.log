2025-07-01 15:57:19,322 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_155719.log
2025-07-01 15:57:30,281 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 15:57:30,282 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 15:57:30,347 - DEBUG - chromedriver not found in PATH
2025-07-01 15:57:30,347 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 15:57:30,347 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 15:57:30,347 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 15:57:30,347 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 15:57:30,348 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 15:57:30,348 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 15:57:30,352 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 21784 using 0 to output -3
2025-07-01 15:57:30,875 - DEBUG - POST http://localhost:54644/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 15:57:30,877 - DEBUG - Starting new HTTP connection (1): localhost:54644
2025-07-01 15:57:31,419 - DEBUG - http://localhost:54644 "POST /session HTTP/1.1" 200 0
2025-07-01 15:57:31,419 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir21784_1710951365"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:54648"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"a4d4b6ade1916c94fc36430b09aee870"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:57:31,419 - DEBUG - Finished Request
2025-07-01 15:57:31,420 - DEBUG - POST http://localhost:54644/session/a4d4b6ade1916c94fc36430b09aee870/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 15:57:32,525 - DEBUG - http://localhost:54644 "POST /session/a4d4b6ade1916c94fc36430b09aee870/url HTTP/1.1" 200 0
2025-07-01 15:57:32,525 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:57:32,525 - DEBUG - Finished Request
2025-07-01 15:57:32,525 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 15:57:32,526 - DEBUG - POST http://localhost:54644/session/a4d4b6ade1916c94fc36430b09aee870/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 15:57:32,564 - DEBUG - http://localhost:54644 "POST /session/a4d4b6ade1916c94fc36430b09aee870/execute/sync HTTP/1.1" 200 0
2025-07-01 15:57:32,564 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:57:32,564 - DEBUG - Finished Request
2025-07-01 15:57:32,565 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 15:57:32,565 - DEBUG - GET http://localhost:54644/session/a4d4b6ade1916c94fc36430b09aee870/url {}
2025-07-01 15:57:32,594 - DEBUG - http://localhost:54644 "GET /session/a4d4b6ade1916c94fc36430b09aee870/url HTTP/1.1" 200 0
2025-07-01 15:57:32,594 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:57:32,595 - DEBUG - Finished Request
2025-07-01 15:57:33,595 - DEBUG - GET http://localhost:54644/session/a4d4b6ade1916c94fc36430b09aee870/url {}
2025-07-01 15:57:33,597 - DEBUG - http://localhost:54644 "GET /session/a4d4b6ade1916c94fc36430b09aee870/url HTTP/1.1" 404 0
2025-07-01 15:57:33,597 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:57:33,597 - DEBUG - Finished Request
2025-07-01 15:57:33,598 - DEBUG - DELETE http://localhost:54644/session/a4d4b6ade1916c94fc36430b09aee870 {}
2025-07-01 15:57:33,616 - DEBUG - http://localhost:54644 "DELETE /session/a4d4b6ade1916c94fc36430b09aee870 HTTP/1.1" 200 0
2025-07-01 15:57:33,616 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 15:57:33,616 - DEBUG - Finished Request
2025-07-01 15:57:35,267 - DEBUG - DELETE http://localhost:54644/session/a4d4b6ade1916c94fc36430b09aee870 {}
2025-07-01 15:57:35,268 - DEBUG - Starting new HTTP connection (1): localhost:54644
2025-07-01 15:57:39,343 - DEBUG - Incremented Retry for (url='/session/a4d4b6ade1916c94fc36430b09aee870'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-01 15:57:39,343 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001AADB765650>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/a4d4b6ade1916c94fc36430b09aee870
2025-07-01 15:57:39,344 - DEBUG - Starting new HTTP connection (2): localhost:54644
2025-07-01 15:57:43,438 - DEBUG - Incremented Retry for (url='/session/a4d4b6ade1916c94fc36430b09aee870'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-01 15:57:43,439 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001AADB764E50>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/a4d4b6ade1916c94fc36430b09aee870
2025-07-01 15:57:43,439 - DEBUG - Starting new HTTP connection (3): localhost:54644
2025-07-01 15:57:47,550 - DEBUG - Incremented Retry for (url='/session/a4d4b6ade1916c94fc36430b09aee870'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-01 15:57:47,551 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001AADB7807D0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/a4d4b6ade1916c94fc36430b09aee870
2025-07-01 15:57:47,551 - DEBUG - Starting new HTTP connection (4): localhost:54644
