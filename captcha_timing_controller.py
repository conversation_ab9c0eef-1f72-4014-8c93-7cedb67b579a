# Captcha Timing Controller for AGES-KH
# 處理兩階段驗證碼的時機控制
# Version: 1.0
# Author: <PERSON>

import time
from datetime import datetime, timedelta
import threading
from typing import Optional, Callable

class CaptchaTimingController:
    def __init__(self):
        self.login_captcha_completed = False
        self.submit_captcha_completed = False
        self.target_time = None
        self.rtt_start_time = None
        self.captcha_window_start = None  # 搶單驗證碼可輸入的開始時間
        
    def set_target_time(self, target_time_str: str):
        """設定目標搶單時間 (格式: HH:MM:SS.sss)"""
        try:
            today = datetime.now().date()
            time_part = datetime.strptime(target_time_str, "%H:%M:%S.%f").time()
            self.target_time = datetime.combine(today, time_part)
            
            # 計算關鍵時間點
            self.rtt_start_time = self.target_time - timedelta(seconds=90)  # RTT 提前 90 秒開始
            self.captcha_window_start = self.target_time - timedelta(seconds=120)  # 驗證碼視窗提前 2 分鐘
            
            print(f"[INFO] 目標時間: {self.target_time}")
            print(f"[INFO] RTT 開始時間: {self.rtt_start_time}")
            print(f"[INFO] 驗證碼視窗開始: {self.captcha_window_start}")
            
        except ValueError as e:
            raise ValueError(f"時間格式錯誤: {e}")
    
    def wait_for_login_captcha(self, callback: Optional[Callable] = None):
        """等待登入驗證碼完成"""
        print("\n" + "="*50)
        print("階段 1: 登入驗證碼")
        print("請完成登入並輸入登入驗證碼")
        print("完成後請按 Enter 繼續...")
        print("="*50)
        
        input()
        self.login_captcha_completed = True
        
        if callback:
            callback()
            
        print("[INFO] 登入驗證碼階段完成")
        
    def wait_for_captcha_window(self):
        """等待搶單驗證碼視窗開啟"""
        if not self.target_time:
            raise ValueError("請先設定目標時間")
            
        now = datetime.now()
        
        if now < self.captcha_window_start:
            wait_seconds = (self.captcha_window_start - now).total_seconds()
            print(f"\n[INFO] 等待 {wait_seconds:.1f} 秒後開啟搶單驗證碼視窗...")
            time.sleep(wait_seconds)
            
        print("\n" + "="*50)
        print("階段 2: 搶單驗證碼")
        print("⚠️  重要提醒：")
        print("1. 驗證碼必須在最後 2 分鐘內輸入")
        print("2. 過早輸入會導致驗證碼失效")
        print("3. 建議在最後 1 分鐘內輸入")
        print("4. RTT 計算會並行進行，不受影響")
        print("\n請輸入搶單驗證碼，完成後按 Enter...")
        print("="*50)
        
    def wait_for_submit_captcha(self, callback: Optional[Callable] = None):
        """等待搶單驗證碼完成"""
        # 確保在正確時間窗內
        self.wait_for_captcha_window()
        
        input()
        self.submit_captcha_completed = True
        
        if callback:
            callback()
            
        print("[INFO] 搶單驗證碼階段完成")
        
    def is_ready_for_rtt(self) -> bool:
        """檢查是否可以開始 RTT 計算"""
        if not self.target_time:
            return False
            
        now = datetime.now()
        return now >= self.rtt_start_time and self.login_captcha_completed
        
    def is_ready_for_submit(self) -> bool:
        """檢查是否可以開始搶單"""
        return (self.login_captcha_completed and 
                self.submit_captcha_completed and 
                self.target_time is not None)
                
    def get_time_until_target(self) -> float:
        """取得距離目標時間的秒數"""
        if not self.target_time:
            return 0
            
        now = datetime.now()
        return (self.target_time - now).total_seconds()
        
    def get_time_until_rtt_start(self) -> float:
        """取得距離 RTT 開始時間的秒數"""
        if not self.rtt_start_time:
            return 0
            
        now = datetime.now()
        return (self.rtt_start_time - now).total_seconds()
        
    def get_captcha_status_summary(self) -> dict:
        """取得驗證碼狀態摘要"""
        now = datetime.now()
        
        return {
            'login_captcha_completed': self.login_captcha_completed,
            'submit_captcha_completed': self.submit_captcha_completed,
            'target_time': self.target_time.isoformat() if self.target_time else None,
            'time_until_target': self.get_time_until_target(),
            'time_until_rtt_start': self.get_time_until_rtt_start(),
            'ready_for_rtt': self.is_ready_for_rtt(),
            'ready_for_submit': self.is_ready_for_submit(),
            'current_time': now.isoformat()
        }

# 使用範例
if __name__ == "__main__":
    controller = CaptchaTimingController()
    
    try:
        # 設定目標時間
        controller.set_target_time("09:30:00.001")
        
        # 階段 1: 等待登入驗證碼
        controller.wait_for_login_captcha()
        
        # 檢查 RTT 準備狀態
        if controller.is_ready_for_rtt():
            print("[INFO] 可以開始 RTT 計算")
        else:
            print(f"[INFO] 等待 RTT 開始時間，還需 {controller.get_time_until_rtt_start():.1f} 秒")
            
        # 階段 2: 等待搶單驗證碼
        controller.wait_for_submit_captcha()
        
        # 檢查搶單準備狀態
        if controller.is_ready_for_submit():
            print("[INFO] 準備完成，可以開始搶單流程")
            
        # 顯示狀態摘要
        status = controller.get_captcha_status_summary()
        print(f"\n[INFO] 狀態摘要: {status}")
        
    except KeyboardInterrupt:
        print("\n[INFO] 使用者中斷操作")
    except Exception as e:
        print(f"\n[ERROR] 發生錯誤: {e}")
