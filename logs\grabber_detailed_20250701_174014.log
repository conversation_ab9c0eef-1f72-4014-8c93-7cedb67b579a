2025-07-01 17:40:14,096 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_174014.log
2025-07-01 17:40:19,309 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 17:40:19,309 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 17:40:19,382 - DEBUG - chromedriver not found in PATH
2025-07-01 17:40:19,382 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:40:19,383 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 17:40:19,383 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 17:40:19,383 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 17:40:19,383 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 17:40:19,384 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:40:19,388 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 19568 using 0 to output -3
2025-07-01 17:40:19,921 - DEBUG - POST http://localhost:56572/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 17:40:19,922 - DEBUG - Starting new HTTP connection (1): localhost:56572
2025-07-01 17:40:20,467 - DEBUG - http://localhost:56572 "POST /session HTTP/1.1" 200 0
2025-07-01 17:40:20,467 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir19568_1481872833"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:56576"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"49dcd9cb772f72b829653940936d7c2e"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:20,468 - DEBUG - Finished Request
2025-07-01 17:40:20,470 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 17:40:22,384 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:22,384 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:22,384 - DEBUG - Finished Request
2025-07-01 17:40:22,384 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 17:40:22,384 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 17:40:22,390 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/execute/sync HTTP/1.1" 200 0
2025-07-01 17:40:22,391 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:22,391 - DEBUG - Finished Request
2025-07-01 17:40:22,391 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 17:40:22,391 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:22,424 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:22,424 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:22,424 - DEBUG - Finished Request
2025-07-01 17:40:23,425 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:23,433 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:23,434 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:23,434 - DEBUG - Finished Request
2025-07-01 17:40:24,435 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:24,441 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:24,441 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:24,441 - DEBUG - Finished Request
2025-07-01 17:40:25,442 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:25,449 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:25,449 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:25,449 - DEBUG - Finished Request
2025-07-01 17:40:26,449 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:26,455 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:26,455 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:26,455 - DEBUG - Finished Request
2025-07-01 17:40:27,456 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:27,462 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:27,462 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:27,463 - DEBUG - Finished Request
2025-07-01 17:40:28,464 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:28,471 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:28,472 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:28,473 - DEBUG - Finished Request
2025-07-01 17:40:29,474 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:29,555 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:29,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:29,557 - DEBUG - Finished Request
2025-07-01 17:40:30,559 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:30,567 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:30,568 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:30,568 - DEBUG - Finished Request
2025-07-01 17:40:31,569 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:31,576 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:31,576 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:31,577 - DEBUG - Finished Request
2025-07-01 17:40:32,578 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:32,586 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:32,586 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:32,586 - DEBUG - Finished Request
2025-07-01 17:40:33,587 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:33,594 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:33,595 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:33,596 - DEBUG - Finished Request
2025-07-01 17:40:34,597 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:34,603 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:34,603 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:34,604 - DEBUG - Finished Request
2025-07-01 17:40:35,605 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:35,612 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:35,612 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:35,613 - DEBUG - Finished Request
2025-07-01 17:40:36,614 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:36,623 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:36,624 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:36,625 - DEBUG - Finished Request
2025-07-01 17:40:37,626 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:37,633 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:37,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:37,634 - DEBUG - Finished Request
2025-07-01 17:40:38,635 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:38,642 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:38,642 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:38,642 - DEBUG - Finished Request
2025-07-01 17:40:39,643 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:39,650 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:39,650 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:39,651 - DEBUG - Finished Request
2025-07-01 17:40:40,652 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:40,659 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:40,659 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:40,659 - DEBUG - Finished Request
2025-07-01 17:40:41,661 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:41,668 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:41,668 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:41,669 - DEBUG - Finished Request
2025-07-01 17:40:42,669 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:42,677 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:42,677 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:42,677 - DEBUG - Finished Request
2025-07-01 17:40:43,677 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:43,684 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:43,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:43,684 - DEBUG - Finished Request
2025-07-01 17:40:44,686 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:44,694 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:44,694 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:44,694 - DEBUG - Finished Request
2025-07-01 17:40:45,695 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:45,703 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:45,703 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:45,703 - DEBUG - Finished Request
2025-07-01 17:40:46,704 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:46,711 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:46,712 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:46,712 - DEBUG - Finished Request
2025-07-01 17:40:47,713 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:47,718 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:47,718 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:47,718 - DEBUG - Finished Request
2025-07-01 17:40:48,719 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:48,725 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:48,725 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:48,726 - DEBUG - Finished Request
2025-07-01 17:40:49,727 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:49,732 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:49,732 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:49,732 - DEBUG - Finished Request
2025-07-01 17:40:50,733 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:50,739 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:50,739 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:50,739 - DEBUG - Finished Request
2025-07-01 17:40:51,740 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:51,746 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:51,746 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:51,747 - DEBUG - Finished Request
2025-07-01 17:40:52,748 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:52,755 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:52,755 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:52,755 - DEBUG - Finished Request
2025-07-01 17:40:53,756 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:53,764 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:53,764 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:53,764 - DEBUG - Finished Request
2025-07-01 17:40:54,765 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:54,772 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:54,773 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:54,773 - DEBUG - Finished Request
2025-07-01 17:40:55,774 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:55,782 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:55,782 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:55,783 - DEBUG - Finished Request
2025-07-01 17:40:56,783 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:56,790 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:56,790 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:56,790 - DEBUG - Finished Request
2025-07-01 17:40:57,792 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:57,800 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:57,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:57,801 - DEBUG - Finished Request
2025-07-01 17:40:58,802 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:58,809 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:58,809 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:58,809 - DEBUG - Finished Request
2025-07-01 17:40:59,810 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:40:59,818 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:40:59,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:40:59,818 - DEBUG - Finished Request
2025-07-01 17:41:00,819 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:00,826 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:00,826 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:00,826 - DEBUG - Finished Request
2025-07-01 17:41:01,827 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:01,835 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:01,836 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:01,836 - DEBUG - Finished Request
2025-07-01 17:41:02,837 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:02,844 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:02,845 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:02,845 - DEBUG - Finished Request
2025-07-01 17:41:03,846 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:03,854 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:03,854 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:03,854 - DEBUG - Finished Request
2025-07-01 17:41:04,855 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:04,862 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:04,862 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:04,863 - DEBUG - Finished Request
2025-07-01 17:41:05,864 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:05,871 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:05,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:05,871 - DEBUG - Finished Request
2025-07-01 17:41:06,873 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:06,880 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:06,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:06,881 - DEBUG - Finished Request
2025-07-01 17:41:07,882 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:07,889 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:07,889 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:07,889 - DEBUG - Finished Request
2025-07-01 17:41:08,890 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:08,897 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:08,897 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:08,898 - DEBUG - Finished Request
2025-07-01 17:41:09,899 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:09,905 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:09,905 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:09,906 - DEBUG - Finished Request
2025-07-01 17:41:10,907 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:10,913 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:10,913 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:10,914 - DEBUG - Finished Request
2025-07-01 17:41:11,915 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:11,922 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:11,922 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:11,923 - DEBUG - Finished Request
2025-07-01 17:41:12,924 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:12,932 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:12,933 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:12,933 - DEBUG - Finished Request
2025-07-01 17:41:13,429 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,443 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,444 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,444 - DEBUG - Finished Request
2025-07-01 17:41:13,445 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,454 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,454 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,454 - DEBUG - Finished Request
2025-07-01 17:41:13,455 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,461 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,462 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,462 - DEBUG - Finished Request
2025-07-01 17:41:13,463 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 17:41:13,475 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,475 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,476 - DEBUG - Finished Request
2025-07-01 17:41:13,476 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 17:41:13,486 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,486 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,486 - DEBUG - Finished Request
2025-07-01 17:41:13,487 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 17:41:13,499 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,499 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,500 - DEBUG - Finished Request
2025-07-01 17:41:13,500 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-07-01 17:41:13,509 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,509 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,510 - DEBUG - Finished Request
2025-07-01 17:41:13,510 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': '.btn-edit'}
2025-07-01 17:41:13,522 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,522 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,522 - DEBUG - Finished Request
2025-07-01 17:41:13,523 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'a[href*="edit"]'}
2025-07-01 17:41:13,534 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,534 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,535 - DEBUG - Finished Request
2025-07-01 17:41:13,536 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,542 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,542 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,542 - DEBUG - Finished Request
2025-07-01 17:41:13,543 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[name*="captcha"]'}
2025-07-01 17:41:13,552 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,552 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,552 - DEBUG - Finished Request
2025-07-01 17:41:13,552 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[id*="captcha"]'}
2025-07-01 17:41:13,564 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,564 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,564 - DEBUG - Finished Request
2025-07-01 17:41:13,565 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證"]'}
2025-07-01 17:41:13,577 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,577 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,577 - DEBUG - Finished Request
2025-07-01 17:41:13,578 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證碼"]'}
2025-07-01 17:41:13,588 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,589 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,589 - DEBUG - Finished Request
2025-07-01 17:41:13,590 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[name*="code"]'}
2025-07-01 17:41:13,599 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,599 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,599 - DEBUG - Finished Request
2025-07-01 17:41:13,600 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[name*="verify"]'}
2025-07-01 17:41:13,608 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,609 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,609 - DEBUG - Finished Request
2025-07-01 17:41:13,609 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="4"]'}
2025-07-01 17:41:13,617 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,618 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,618 - DEBUG - Finished Request
2025-07-01 17:41:13,618 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="5"]'}
2025-07-01 17:41:13,627 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,627 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,628 - DEBUG - Finished Request
2025-07-01 17:41:13,628 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,633 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,633 - DEBUG - Finished Request
2025-07-01 17:41:13,634 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'img[src*="captcha"]'}
2025-07-01 17:41:13,642 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,642 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,642 - DEBUG - Finished Request
2025-07-01 17:41:13,642 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'img[src*="verify"]'}
2025-07-01 17:41:13,651 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,652 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,652 - DEBUG - Finished Request
2025-07-01 17:41:13,652 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'img[alt*="驗證"]'}
2025-07-01 17:41:13,661 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,661 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,661 - DEBUG - Finished Request
2025-07-01 17:41:13,661 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'img[alt*="驗證碼"]'}
2025-07-01 17:41:13,670 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,670 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,671 - DEBUG - Finished Request
2025-07-01 17:41:13,671 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,675 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,676 - DEBUG - Finished Request
2025-07-01 17:41:13,676 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '確認取得驗證碼')]"}
2025-07-01 17:41:13,684 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,684 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,684 - DEBUG - Finished Request
2025-07-01 17:41:13,685 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-07-01 17:41:13,693 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,693 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,694 - DEBUG - Finished Request
2025-07-01 17:41:13,694 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-07-01 17:41:13,701 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,702 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,702 - DEBUG - Finished Request
2025-07-01 17:41:13,702 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': '.captcha-refresh'}
2025-07-01 17:41:13,710 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,710 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,710 - DEBUG - Finished Request
2025-07-01 17:41:13,711 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,716 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,717 - DEBUG - Finished Request
2025-07-01 17:41:13,717 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '送出')]"}
2025-07-01 17:41:13,728 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,728 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,728 - DEBUG - Finished Request
2025-07-01 17:41:13,729 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[value="送出"]'}
2025-07-01 17:41:13,740 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,740 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,740 - DEBUG - Finished Request
2025-07-01 17:41:13,741 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[type="submit"]'}
2025-07-01 17:41:13,750 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,750 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,750 - DEBUG - Finished Request
2025-07-01 17:41:13,751 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'button[type="submit"]'}
2025-07-01 17:41:13,760 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,761 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,761 - DEBUG - Finished Request
2025-07-01 17:41:13,761 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': '.btn-submit'}
2025-07-01 17:41:13,770 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,770 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,770 - DEBUG - Finished Request
2025-07-01 17:41:13,771 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,776 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,777 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,777 - DEBUG - Finished Request
2025-07-01 17:41:13,777 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': "//*[contains(text(), '取消')]"}
2025-07-01 17:41:13,787 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,787 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,787 - DEBUG - Finished Request
2025-07-01 17:41:13,787 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'input[value="取消"]'}
2025-07-01 17:41:13,796 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,796 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,797 - DEBUG - Finished Request
2025-07-01 17:41:13,797 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': '.btn-cancel'}
2025-07-01 17:41:13,805 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,805 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,805 - DEBUG - Finished Request
2025-07-01 17:41:13,806 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,811 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,811 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,812 - DEBUG - Finished Request
2025-07-01 17:41:13,812 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'table'}
2025-07-01 17:41:13,821 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,821 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,821 - DEBUG - Finished Request
2025-07-01 17:41:13,822 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': '.table'}
2025-07-01 17:41:13,830 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,831 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,831 - DEBUG - Finished Request
2025-07-01 17:41:13,831 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'tbody'}
2025-07-01 17:41:13,840 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,840 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,840 - DEBUG - Finished Request
2025-07-01 17:41:13,840 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'css selector', 'value': 'tr'}
2025-07-01 17:41:13,849 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:41:13,849 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,849 - DEBUG - Finished Request
2025-07-01 17:41:13,850 - INFO - 🔍 自動尋找訂單 E48B201611406191206 的編輯按鈕...
2025-07-01 17:41:13,850 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:13,859 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:13,860 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,860 - DEBUG - Finished Request
2025-07-01 17:41:13,934 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:13,940 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:13,940 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:13,941 - DEBUG - Finished Request
2025-07-01 17:41:14,362 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:14,372 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:14,373 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:14,373 - DEBUG - Finished Request
2025-07-01 17:41:14,874 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:14,903 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:14,904 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:14,904 - DEBUG - Finished Request
2025-07-01 17:41:14,941 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:14,949 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:14,949 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:14,950 - DEBUG - Finished Request
2025-07-01 17:41:15,405 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:15,416 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:15,416 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:15,416 - DEBUG - Finished Request
2025-07-01 17:41:15,917 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:15,926 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:15,927 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:15,927 - DEBUG - Finished Request
2025-07-01 17:41:15,951 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:15,958 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:15,958 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:15,959 - DEBUG - Finished Request
2025-07-01 17:41:16,428 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:16,438 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:16,439 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:16,439 - DEBUG - Finished Request
2025-07-01 17:41:16,940 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:16,959 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:16,960 - DEBUG - Starting new HTTP connection (2): localhost:56572
2025-07-01 17:41:16,963 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:16,963 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:16,964 - DEBUG - Finished Request
2025-07-01 17:41:16,966 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:16,967 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:41:16,967 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:16,967 - DEBUG - Finished Request
2025-07-01 17:41:17,465 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:17,497 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:17,497 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:17,498 - DEBUG - Finished Request
2025-07-01 17:41:17,968 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:17,976 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:17,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:17,977 - DEBUG - Finished Request
2025-07-01 17:41:17,999 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:18,008 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:18,009 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:18,009 - DEBUG - Finished Request
2025-07-01 17:41:18,510 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:18,519 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:18,520 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:18,520 - DEBUG - Finished Request
2025-07-01 17:41:18,978 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:18,984 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:18,985 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:18,985 - DEBUG - Finished Request
2025-07-01 17:41:19,021 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:19,030 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:19,030 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:19,030 - DEBUG - Finished Request
2025-07-01 17:41:19,532 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:19,540 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:19,541 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:19,541 - DEBUG - Finished Request
2025-07-01 17:41:19,986 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:19,993 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:19,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:19,994 - DEBUG - Finished Request
2025-07-01 17:41:20,042 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:20,050 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:20,050 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:20,051 - DEBUG - Finished Request
2025-07-01 17:41:20,552 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:20,561 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:20,561 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:20,561 - DEBUG - Finished Request
2025-07-01 17:41:20,994 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:21,000 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:21,000 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:21,000 - DEBUG - Finished Request
2025-07-01 17:41:21,062 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:21,071 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:21,072 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:21,072 - DEBUG - Finished Request
2025-07-01 17:41:21,573 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:21,582 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:21,582 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:21,582 - DEBUG - Finished Request
2025-07-01 17:41:22,001 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:22,008 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:22,008 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:22,008 - DEBUG - Finished Request
2025-07-01 17:41:22,083 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:22,094 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:22,094 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:22,094 - DEBUG - Finished Request
2025-07-01 17:41:22,595 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:22,603 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:22,603 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:22,603 - DEBUG - Finished Request
2025-07-01 17:41:23,008 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:23,015 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:23,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:23,016 - DEBUG - Finished Request
2025-07-01 17:41:23,104 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:23,115 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:23,115 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:23,116 - DEBUG - Finished Request
2025-07-01 17:41:23,617 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:23,626 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:23,626 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:23,626 - DEBUG - Finished Request
2025-07-01 17:41:24,017 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:24,028 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:24,028 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:24,028 - DEBUG - Finished Request
2025-07-01 17:41:24,127 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:24,135 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:24,135 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:24,136 - DEBUG - Finished Request
2025-07-01 17:41:24,637 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:24,645 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:24,645 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:24,645 - DEBUG - Finished Request
2025-07-01 17:41:25,029 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:25,038 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:25,038 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:25,038 - DEBUG - Finished Request
2025-07-01 17:41:25,146 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:25,159 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:25,159 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:25,159 - DEBUG - Finished Request
2025-07-01 17:41:25,661 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:25,672 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:25,673 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:25,673 - DEBUG - Finished Request
2025-07-01 17:41:26,039 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:26,051 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:26,051 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:26,051 - DEBUG - Finished Request
2025-07-01 17:41:26,174 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:26,185 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:26,185 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:26,185 - DEBUG - Finished Request
2025-07-01 17:41:26,686 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:26,695 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:26,695 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:26,695 - DEBUG - Finished Request
2025-07-01 17:41:27,052 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:27,060 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:27,060 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:27,060 - DEBUG - Finished Request
2025-07-01 17:41:27,196 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:27,205 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:27,205 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:27,205 - DEBUG - Finished Request
2025-07-01 17:41:27,706 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:27,718 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:27,718 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:27,718 - DEBUG - Finished Request
2025-07-01 17:41:28,061 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:28,068 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:28,069 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:28,069 - DEBUG - Finished Request
2025-07-01 17:41:28,220 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:28,232 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:28,232 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:28,232 - DEBUG - Finished Request
2025-07-01 17:41:28,733 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:28,745 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:28,745 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:28,745 - DEBUG - Finished Request
2025-07-01 17:41:29,070 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:29,078 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:29,078 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:29,078 - DEBUG - Finished Request
2025-07-01 17:41:29,246 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:29,257 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:29,257 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:29,257 - DEBUG - Finished Request
2025-07-01 17:41:29,758 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:29,770 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:29,770 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:29,770 - DEBUG - Finished Request
2025-07-01 17:41:30,079 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:30,087 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:30,088 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:30,088 - DEBUG - Finished Request
2025-07-01 17:41:30,271 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:30,281 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:30,281 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:30,281 - DEBUG - Finished Request
2025-07-01 17:41:30,782 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:30,792 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:30,792 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:30,792 - DEBUG - Finished Request
2025-07-01 17:41:31,089 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:31,097 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:31,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:31,098 - DEBUG - Finished Request
2025-07-01 17:41:31,294 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:31,304 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:31,304 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:31,304 - DEBUG - Finished Request
2025-07-01 17:41:31,805 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:31,816 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:31,816 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:31,816 - DEBUG - Finished Request
2025-07-01 17:41:32,099 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:32,108 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:32,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:32,109 - DEBUG - Finished Request
2025-07-01 17:41:32,317 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:32,329 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:32,329 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:32,329 - DEBUG - Finished Request
2025-07-01 17:41:32,830 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:32,839 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:32,839 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:32,839 - DEBUG - Finished Request
2025-07-01 17:41:33,110 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:33,118 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:33,118 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:33,118 - DEBUG - Finished Request
2025-07-01 17:41:33,341 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:33,350 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:33,350 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:33,350 - DEBUG - Finished Request
2025-07-01 17:41:33,852 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:33,861 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:33,862 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:33,862 - DEBUG - Finished Request
2025-07-01 17:41:34,119 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:34,126 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:34,126 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:34,126 - DEBUG - Finished Request
2025-07-01 17:41:34,363 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:34,374 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:34,374 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:34,374 - DEBUG - Finished Request
2025-07-01 17:41:34,875 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:34,883 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:34,884 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:34,884 - DEBUG - Finished Request
2025-07-01 17:41:35,127 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:35,135 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:35,135 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:35,135 - DEBUG - Finished Request
2025-07-01 17:41:35,385 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:35,397 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:35,398 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:35,398 - DEBUG - Finished Request
2025-07-01 17:41:35,898 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:35,909 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:35,909 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:35,910 - DEBUG - Finished Request
2025-07-01 17:41:36,137 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:36,145 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:36,145 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:36,145 - DEBUG - Finished Request
2025-07-01 17:41:36,411 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:36,421 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:36,421 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:36,421 - DEBUG - Finished Request
2025-07-01 17:41:36,922 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:36,933 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:36,933 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:36,933 - DEBUG - Finished Request
2025-07-01 17:41:37,145 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:37,153 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:37,153 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:37,153 - DEBUG - Finished Request
2025-07-01 17:41:37,434 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:37,447 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:37,447 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:37,447 - DEBUG - Finished Request
2025-07-01 17:41:37,948 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:37,960 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:37,960 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:37,961 - DEBUG - Finished Request
2025-07-01 17:41:38,153 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:38,161 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:38,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:38,162 - DEBUG - Finished Request
2025-07-01 17:41:38,462 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:38,472 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:38,472 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:38,472 - DEBUG - Finished Request
2025-07-01 17:41:38,974 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:38,984 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:38,984 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:38,984 - DEBUG - Finished Request
2025-07-01 17:41:39,162 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:39,170 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:39,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:39,171 - DEBUG - Finished Request
2025-07-01 17:41:39,485 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:39,496 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:39,496 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:39,496 - DEBUG - Finished Request
2025-07-01 17:41:39,997 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:40,007 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:40,007 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:40,007 - DEBUG - Finished Request
2025-07-01 17:41:40,172 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:40,179 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:40,179 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:40,179 - DEBUG - Finished Request
2025-07-01 17:41:40,509 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:40,521 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:40,521 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:40,521 - DEBUG - Finished Request
2025-07-01 17:41:41,022 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:41,031 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:41,032 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:41,032 - DEBUG - Finished Request
2025-07-01 17:41:41,181 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:41,189 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:41,189 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:41,190 - DEBUG - Finished Request
2025-07-01 17:41:41,533 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:41,546 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:41,546 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:41,546 - DEBUG - Finished Request
2025-07-01 17:41:42,046 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:42,058 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:42,058 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:42,058 - DEBUG - Finished Request
2025-07-01 17:41:42,190 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:42,197 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:42,197 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:42,198 - DEBUG - Finished Request
2025-07-01 17:41:42,558 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:42,568 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:42,568 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:42,569 - DEBUG - Finished Request
2025-07-01 17:41:43,070 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:43,080 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:43,081 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:43,081 - DEBUG - Finished Request
2025-07-01 17:41:43,199 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:43,206 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:43,206 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:43,206 - DEBUG - Finished Request
2025-07-01 17:41:43,582 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:43,595 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:43,595 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:43,595 - DEBUG - Finished Request
2025-07-01 17:41:44,096 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611406191206')]"}
2025-07-01 17:41:44,107 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:44,108 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B201611406191206')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1099', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:44,108 - DEBUG - Finished Request
2025-07-01 17:41:44,109 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:44,123 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:44,123 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:44,123 - DEBUG - Finished Request
2025-07-01 17:41:44,207 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:44,216 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:44,217 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:44,217 - DEBUG - Finished Request
2025-07-01 17:41:44,624 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:44,634 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:44,634 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:44,634 - DEBUG - Finished Request
2025-07-01 17:41:45,135 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:45,147 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:45,147 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:45,147 - DEBUG - Finished Request
2025-07-01 17:41:45,218 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:45,225 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:45,225 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:45,225 - DEBUG - Finished Request
2025-07-01 17:41:45,648 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:45,657 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:45,658 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:45,658 - DEBUG - Finished Request
2025-07-01 17:41:46,159 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:46,167 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:46,167 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:46,167 - DEBUG - Finished Request
2025-07-01 17:41:46,226 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:46,233 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:46,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:46,234 - DEBUG - Finished Request
2025-07-01 17:41:46,668 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:46,678 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:46,678 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:46,678 - DEBUG - Finished Request
2025-07-01 17:41:47,180 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:47,190 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:47,191 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:47,191 - DEBUG - Finished Request
2025-07-01 17:41:47,235 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:47,241 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:47,241 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:47,242 - DEBUG - Finished Request
2025-07-01 17:41:47,691 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:47,703 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:47,704 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:47,704 - DEBUG - Finished Request
2025-07-01 17:41:48,205 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:48,215 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:48,215 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:48,215 - DEBUG - Finished Request
2025-07-01 17:41:48,243 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:48,249 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:48,250 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:48,250 - DEBUG - Finished Request
2025-07-01 17:41:48,715 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:48,724 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:48,724 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:48,724 - DEBUG - Finished Request
2025-07-01 17:41:49,224 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:49,234 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:49,234 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:49,234 - DEBUG - Finished Request
2025-07-01 17:41:49,251 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:49,257 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:49,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:49,258 - DEBUG - Finished Request
2025-07-01 17:41:49,734 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:49,745 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:49,745 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:49,745 - DEBUG - Finished Request
2025-07-01 17:41:50,246 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:50,256 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:50,256 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:50,257 - DEBUG - Finished Request
2025-07-01 17:41:50,259 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:50,264 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:50,265 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:50,265 - DEBUG - Finished Request
2025-07-01 17:41:50,757 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:50,767 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:50,768 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:50,768 - DEBUG - Finished Request
2025-07-01 17:41:51,266 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:51,269 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:51,269 - DEBUG - Starting new HTTP connection (3): localhost:56572
2025-07-01 17:41:51,272 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:51,272 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:51,273 - DEBUG - Finished Request
2025-07-01 17:41:51,292 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:51,292 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-01 17:41:51,293 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:51,293 - DEBUG - Finished Request
2025-07-01 17:41:51,794 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:51,804 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:51,804 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:51,804 - DEBUG - Finished Request
2025-07-01 17:41:52,274 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:52,282 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:52,282 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:52,283 - DEBUG - Finished Request
2025-07-01 17:41:52,306 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:52,317 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:52,317 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:52,317 - DEBUG - Finished Request
2025-07-01 17:41:52,818 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:52,826 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:52,827 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:52,827 - DEBUG - Finished Request
2025-07-01 17:41:53,283 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:53,290 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:53,291 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:53,291 - DEBUG - Finished Request
2025-07-01 17:41:53,328 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:53,337 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:53,338 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:53,338 - DEBUG - Finished Request
2025-07-01 17:41:53,839 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:53,862 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:53,862 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:53,863 - DEBUG - Finished Request
2025-07-01 17:41:54,291 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:54,299 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:54,300 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:54,300 - DEBUG - Finished Request
2025-07-01 17:41:54,363 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:54,372 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:54,373 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:54,373 - DEBUG - Finished Request
2025-07-01 17:41:54,874 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:54,886 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:54,887 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:54,887 - DEBUG - Finished Request
2025-07-01 17:41:55,301 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:55,309 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:55,309 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:55,309 - DEBUG - Finished Request
2025-07-01 17:41:55,387 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:55,398 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:55,398 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:55,398 - DEBUG - Finished Request
2025-07-01 17:41:55,900 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:55,911 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:55,911 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:55,912 - DEBUG - Finished Request
2025-07-01 17:41:56,310 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:56,319 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:56,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:56,319 - DEBUG - Finished Request
2025-07-01 17:41:56,412 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:56,423 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:56,423 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:56,423 - DEBUG - Finished Request
2025-07-01 17:41:56,924 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:56,934 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:56,934 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:56,934 - DEBUG - Finished Request
2025-07-01 17:41:57,320 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:57,326 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:57,326 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:57,327 - DEBUG - Finished Request
2025-07-01 17:41:57,435 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:57,445 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:57,445 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:57,445 - DEBUG - Finished Request
2025-07-01 17:41:57,947 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:57,957 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:57,957 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:57,957 - DEBUG - Finished Request
2025-07-01 17:41:58,328 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:58,336 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:58,336 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:58,336 - DEBUG - Finished Request
2025-07-01 17:41:58,458 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:58,469 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:58,469 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:58,470 - DEBUG - Finished Request
2025-07-01 17:41:58,970 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:58,980 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:58,980 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:58,980 - DEBUG - Finished Request
2025-07-01 17:41:59,337 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:41:59,344 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:41:59,345 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:59,345 - DEBUG - Finished Request
2025-07-01 17:41:59,481 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:41:59,490 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:41:59,490 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:41:59,491 - DEBUG - Finished Request
2025-07-01 17:41:59,992 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:00,001 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:00,001 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:00,002 - DEBUG - Finished Request
2025-07-01 17:42:00,346 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:00,353 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:00,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:00,354 - DEBUG - Finished Request
2025-07-01 17:42:00,503 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:00,513 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:00,513 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:00,513 - DEBUG - Finished Request
2025-07-01 17:42:01,014 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:01,024 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:01,024 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:01,024 - DEBUG - Finished Request
2025-07-01 17:42:01,354 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:01,362 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:01,362 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:01,362 - DEBUG - Finished Request
2025-07-01 17:42:01,525 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:01,535 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:01,535 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:01,536 - DEBUG - Finished Request
2025-07-01 17:42:02,037 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:02,049 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:02,049 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:02,049 - DEBUG - Finished Request
2025-07-01 17:42:02,363 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:02,371 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:02,371 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:02,372 - DEBUG - Finished Request
2025-07-01 17:42:02,551 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:02,561 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:02,562 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:02,562 - DEBUG - Finished Request
2025-07-01 17:42:03,063 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:03,074 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:03,074 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:03,075 - DEBUG - Finished Request
2025-07-01 17:42:03,372 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:03,379 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:03,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:03,380 - DEBUG - Finished Request
2025-07-01 17:42:03,575 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:03,589 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:03,589 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:03,589 - DEBUG - Finished Request
2025-07-01 17:42:04,090 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:04,100 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:04,100 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:04,100 - DEBUG - Finished Request
2025-07-01 17:42:04,381 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:04,388 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:04,388 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:04,388 - DEBUG - Finished Request
2025-07-01 17:42:04,601 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-07-01 17:42:04,611 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/element HTTP/1.1" 404 0
2025-07-01 17:42:04,611 - DEBUG - Remote response: status=404 | data={"value":{"error":"no such element","message":"no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\"//*[contains(text(), 'E48B')]\"}\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff691810766]\n\t(No symbol) [0x0x7ff691810a1c]\n\t(No symbol) [0x0x7ff691864467]\n\t(No symbol) [0x0x7ff691838bcf]\n\t(No symbol) [0x0x7ff69186122f]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1084', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:04,611 - DEBUG - Finished Request
2025-07-01 17:42:04,611 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:04,624 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:04,624 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:04,624 - DEBUG - Finished Request
2025-07-01 17:42:05,125 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:05,135 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:05,136 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:05,136 - DEBUG - Finished Request
2025-07-01 17:42:05,389 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:05,399 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:05,400 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:05,400 - DEBUG - Finished Request
2025-07-01 17:42:05,637 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:05,648 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:05,649 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:05,649 - DEBUG - Finished Request
2025-07-01 17:42:06,150 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:06,159 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:06,160 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:06,160 - DEBUG - Finished Request
2025-07-01 17:42:06,401 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:06,409 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:06,409 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:06,410 - DEBUG - Finished Request
2025-07-01 17:42:06,662 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:06,675 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:06,676 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:06,676 - DEBUG - Finished Request
2025-07-01 17:42:07,177 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:07,188 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:07,189 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:07,189 - DEBUG - Finished Request
2025-07-01 17:42:07,410 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:07,420 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:07,420 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:07,421 - DEBUG - Finished Request
2025-07-01 17:42:07,690 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:07,702 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:07,703 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:07,703 - DEBUG - Finished Request
2025-07-01 17:42:08,205 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:08,215 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:08,215 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:08,215 - DEBUG - Finished Request
2025-07-01 17:42:08,422 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:08,431 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:08,432 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:08,432 - DEBUG - Finished Request
2025-07-01 17:42:08,716 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:08,729 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:08,729 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:08,730 - DEBUG - Finished Request
2025-07-01 17:42:09,230 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:09,242 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:09,242 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:09,242 - DEBUG - Finished Request
2025-07-01 17:42:09,433 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:09,442 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:09,443 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:09,443 - DEBUG - Finished Request
2025-07-01 17:42:09,743 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:09,756 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:09,757 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:09,757 - DEBUG - Finished Request
2025-07-01 17:42:10,257 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:10,266 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:10,266 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:10,266 - DEBUG - Finished Request
2025-07-01 17:42:10,445 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:10,451 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:10,452 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:10,452 - DEBUG - Finished Request
2025-07-01 17:42:10,767 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:10,776 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:10,776 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:10,776 - DEBUG - Finished Request
2025-07-01 17:42:11,277 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:11,287 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:11,287 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:11,287 - DEBUG - Finished Request
2025-07-01 17:42:11,453 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:11,460 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:11,460 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:11,460 - DEBUG - Finished Request
2025-07-01 17:42:11,788 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:11,797 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:11,797 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:11,798 - DEBUG - Finished Request
2025-07-01 17:42:12,299 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:12,307 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:12,308 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:12,308 - DEBUG - Finished Request
2025-07-01 17:42:12,462 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:12,469 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:12,469 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:12,469 - DEBUG - Finished Request
2025-07-01 17:42:12,808 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:12,816 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:12,816 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:12,816 - DEBUG - Finished Request
2025-07-01 17:42:13,317 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:13,328 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:13,328 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:13,328 - DEBUG - Finished Request
2025-07-01 17:42:13,470 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:13,477 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:13,477 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:13,478 - DEBUG - Finished Request
2025-07-01 17:42:13,829 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:13,840 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:13,840 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:13,841 - DEBUG - Finished Request
2025-07-01 17:42:14,342 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:14,352 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:14,353 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:14,353 - DEBUG - Finished Request
2025-07-01 17:42:14,479 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:14,487 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:14,487 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:14,488 - DEBUG - Finished Request
2025-07-01 17:42:14,853 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:14,862 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:14,862 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:14,862 - DEBUG - Finished Request
2025-07-01 17:42:15,363 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:15,372 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:15,372 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:15,372 - DEBUG - Finished Request
2025-07-01 17:42:15,488 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:15,494 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:15,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:15,494 - DEBUG - Finished Request
2025-07-01 17:42:15,873 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:15,881 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:15,881 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:15,881 - DEBUG - Finished Request
2025-07-01 17:42:16,382 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:16,389 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:16,390 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:16,390 - DEBUG - Finished Request
2025-07-01 17:42:16,495 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:16,500 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:16,501 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:16,501 - DEBUG - Finished Request
2025-07-01 17:42:16,891 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:16,900 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:16,900 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:16,900 - DEBUG - Finished Request
2025-07-01 17:42:17,401 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:17,409 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:17,409 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:17,409 - DEBUG - Finished Request
2025-07-01 17:42:17,502 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:17,508 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:17,508 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:17,509 - DEBUG - Finished Request
2025-07-01 17:42:17,910 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:17,920 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:17,920 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:17,921 - DEBUG - Finished Request
2025-07-01 17:42:18,421 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:18,429 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:18,429 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:18,429 - DEBUG - Finished Request
2025-07-01 17:42:18,510 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:18,518 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 200 0
2025-07-01 17:42:18,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:18,519 - DEBUG - Finished Request
2025-07-01 17:42:18,930 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:18,941 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 200 0
2025-07-01 17:42:18,942 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:18,942 - DEBUG - Finished Request
2025-07-01 17:42:19,443 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/elements {'using': 'xpath', 'value': '//tr'}
2025-07-01 17:42:19,444 - DEBUG - http://localhost:56572 "POST /session/49dcd9cb772f72b829653940936d7c2e/elements HTTP/1.1" 404 0
2025-07-01 17:42:19,445 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:19,445 - DEBUG - Finished Request
2025-07-01 17:42:19,519 - DEBUG - GET http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/url {}
2025-07-01 17:42:19,521 - DEBUG - http://localhost:56572 "GET /session/49dcd9cb772f72b829653940936d7c2e/url HTTP/1.1" 404 0
2025-07-01 17:42:19,521 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b99fc]\n\t(No symbol) [0x0x7ff6918007df]\n\t(No symbol) [0x0x7ff691838a52]\n\t(No symbol) [0x0x7ff691833413]\n\t(No symbol) [0x0x7ff6918324d9]\n\t(No symbol) [0x0x7ff691785d55]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\t(No symbol) [0x0x7ff691784dca]\n\tGetHandleVerifier [0x0x7ff691df45e8+4238440]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '792', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:19,521 - DEBUG - Finished Request
2025-07-01 17:42:19,522 - DEBUG - DELETE http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e {}
2025-07-01 17:42:19,523 - DEBUG - http://localhost:56572 "DELETE /session/49dcd9cb772f72b829653940936d7c2e HTTP/1.1" 200 0
2025-07-01 17:42:19,523 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:42:19,523 - DEBUG - Finished Request
2025-07-01 17:42:21,446 - DEBUG - POST http://localhost:56572/session/49dcd9cb772f72b829653940936d7c2e/execute/sync {'script': '\n            // 檢查頁面最終狀態\n            var bodyText = document.body.innerText || document.body.textCon...', 'args': []}
2025-07-01 17:42:21,447 - DEBUG - Starting new HTTP connection (1): localhost:56572
2025-07-01 17:42:25,509 - DEBUG - Incremented Retry for (url='/session/49dcd9cb772f72b829653940936d7c2e/execute/sync'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-01 17:42:25,510 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000019D6ADF4590>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/49dcd9cb772f72b829653940936d7c2e/execute/sync
2025-07-01 17:42:25,510 - DEBUG - Starting new HTTP connection (2): localhost:56572
2025-07-01 17:42:29,579 - DEBUG - Incremented Retry for (url='/session/49dcd9cb772f72b829653940936d7c2e/execute/sync'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-01 17:42:29,579 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000019D6ADF5050>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/49dcd9cb772f72b829653940936d7c2e/execute/sync
2025-07-01 17:42:29,580 - DEBUG - Starting new HTTP connection (3): localhost:56572
2025-07-01 17:42:33,622 - DEBUG - Incremented Retry for (url='/session/49dcd9cb772f72b829653940936d7c2e/execute/sync'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-01 17:42:33,622 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000019D6ADF5AD0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。')': /session/49dcd9cb772f72b829653940936d7c2e/execute/sync
2025-07-01 17:42:33,622 - DEBUG - Starting new HTTP connection (4): localhost:56572
2025-07-01 17:42:37,718 - ERROR - 尋找編輯按鈕失敗: 'NoneType' object has no attribute 'current_url'
