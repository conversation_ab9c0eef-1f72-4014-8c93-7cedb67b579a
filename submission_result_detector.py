"""
送出結果檢測器 - 專門檢測搶單送出後的結果
只檢測、記錄、截圖，不做任何自動處理
"""

import time
import os
import json
import logging
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from typing import Dict, Optional, Tuple

class SubmissionResultDetector:
    """送出結果檢測器 - 搶單生命週期終止檢測"""
    
    def __init__(self, driver, logger=None, screenshot_dir="screenshots"):
        self.driver = driver
        self.logger = logger or logging.getLogger(__name__)
        self.screenshot_dir = screenshot_dir
        
        # 確保截圖目錄存在
        os.makedirs(screenshot_dir, exist_ok=True)
        
        # 失敗模式定義（基於用戶反饋）
        self.failure_patterns = {
            "time_not_open": {
                "keywords": ["尚未開放", "請於", "9:30"],
                "description": "預約時間未開放"
            },
            "factory_full": {
                "keywords": ["已滿", "請選擇其他廠"],
                "description": "工廠名額已滿"
            },
            "general_failure": {
                "keywords": ["送出失敗"],
                "description": "一般送出失敗"
            }
        }
    
    def detect_submission_result(self, timeout: int = 10) -> Dict:
        """
        檢測送出結果 - 搶單生命週期的終點
        
        Args:
            timeout: 等待結果出現的超時時間
            
        Returns:
            Dict: 包含檢測結果的完整信息
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        result = {
            "timestamp": timestamp,
            "lifecycle_ended": False,  # 搶單生命週期是否結束
            "is_success": None,        # None=未知, True=成功, False=失敗
            "result_type": "unknown",  # 結果類型
            "message": "",             # 結果訊息
            "screenshot_path": "",     # 截圖路徑
            "raw_data": {}            # 原始數據
        }
        
        try:
            self.logger.info("開始檢測送出結果...")
            
            # 1. 檢測是否有彈窗出現
            popup_info = self._detect_popup(timeout)
            
            if popup_info:
                # 有彈窗 = 生命週期結束
                result["lifecycle_ended"] = True
                result["raw_data"]["popup"] = popup_info
                
                # 分析彈窗內容
                analysis = self._analyze_popup_content(popup_info["text"])
                result.update(analysis)
                
                self.logger.info(f"檢測到彈窗結果: {result['result_type']}")
                
            else:
                # 沒有彈窗，檢查頁面狀態
                page_info = self._check_page_status()
                result["raw_data"]["page"] = page_info
                
                if page_info["has_success_indicator"]:
                    # 可能成功
                    result["lifecycle_ended"] = True
                    result["is_success"] = True
                    result["result_type"] = "possible_success"
                    result["message"] = "未檢測到失敗彈窗，可能成功"
                    self.logger.info("未檢測到失敗彈窗，推測可能成功")
                else:
                    # 狀態不明
                    result["result_type"] = "status_unclear"
                    result["message"] = "無法確定送出結果"
                    self.logger.warning("無法確定送出結果")
            
            # 2. 截圖保存（不管成功失敗都截圖）
            screenshot_path = self._take_screenshot(timestamp, result["result_type"])
            result["screenshot_path"] = screenshot_path
            
            # 3. 記錄結果到文件
            self._save_result_log(result)
            
            # 4. 輸出摘要
            self._log_result_summary(result)
            
        except Exception as e:
            result["result_type"] = "detection_error"
            result["message"] = f"檢測過程發生異常: {e}"
            result["lifecycle_ended"] = True  # 異常也算生命週期結束
            self.logger.error(f"檢測送出結果時發生異常: {e}")
        
        return result
    
    def _detect_popup(self, timeout: int) -> Optional[Dict]:
        """檢測是否有彈窗出現"""
        try:
            # SweetAlert2 彈窗選擇器
            popup_selectors = [
                "div.swal2-popup.swal2-modal.swal2-show",
                "div.swal2-popup",
                "div#swal2-html-container"
            ]
            
            for selector in popup_selectors:
                try:
                    popup_element = WebDriverWait(self.driver, timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    
                    # 提取彈窗文字
                    popup_text = self._extract_popup_text(popup_element)
                    
                    return {
                        "found": True,
                        "selector": selector,
                        "text": popup_text,
                        "element": popup_element
                    }
                    
                except TimeoutException:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"檢測彈窗時發生異常: {e}")
            return None
    
    def _extract_popup_text(self, popup_element) -> str:
        """提取彈窗文字內容"""
        try:
            # 嘗試多種方式獲取文字
            text_selectors = [
                "div#swal2-html-container",
                ".swal2-html-container", 
                ".swal2-content",
                "div.swal2-content"
            ]
            
            for selector in text_selectors:
                try:
                    text_element = popup_element.find_element(By.CSS_SELECTOR, selector)
                    text = text_element.text.strip()
                    if text:
                        return text
                except NoSuchElementException:
                    continue
            
            # 直接獲取彈窗文字
            return popup_element.text.strip()
            
        except Exception as e:
            self.logger.error(f"提取彈窗文字時發生異常: {e}")
            return ""
    
    def _analyze_popup_content(self, popup_text: str) -> Dict:
        """分析彈窗內容，判斷結果類型"""
        if not popup_text:
            return {
                "is_success": None,
                "result_type": "empty_popup",
                "message": "彈窗內容為空"
            }
        
        # 檢查是否包含失敗關鍵字
        for failure_type, pattern in self.failure_patterns.items():
            if all(keyword in popup_text for keyword in pattern["keywords"]):
                return {
                    "is_success": False,
                    "result_type": failure_type,
                    "message": f"檢測到失敗: {pattern['description']} - {popup_text}"
                }
        
        # 檢查是否包含 "送出失敗" 通用關鍵字
        if "送出失敗" in popup_text:
            return {
                "is_success": False,
                "result_type": "unknown_failure",
                "message": f"檢測到未知失敗類型 - {popup_text}"
            }
        
        # 沒有失敗關鍵字，可能是成功或其他狀態
        return {
            "is_success": None,  # 不確定
            "result_type": "non_failure_popup",
            "message": f"檢測到非失敗彈窗，需人工判讀 - {popup_text}"
        }
    
    def _check_page_status(self) -> Dict:
        """檢查頁面狀態（當沒有彈窗時）"""
        try:
            page_source = self.driver.page_source
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            # 檢查可能的成功指標
            success_indicators = [
                "提交成功", "預約完成", "申請已送出", 
                "預約成功", "送出成功", "已完成"
            ]
            
            has_success_indicator = any(
                indicator in page_source for indicator in success_indicators
            )
            
            return {
                "url": current_url,
                "title": page_title,
                "has_success_indicator": has_success_indicator,
                "page_length": len(page_source)
            }
            
        except Exception as e:
            self.logger.error(f"檢查頁面狀態時發生異常: {e}")
            return {"error": str(e)}
    
    def _take_screenshot(self, timestamp: str, result_type: str) -> str:
        """截圖保存"""
        try:
            filename = f"submission_result_{timestamp}_{result_type}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            
            self.driver.save_screenshot(filepath)
            self.logger.info(f"截圖已保存: {filepath}")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"截圖保存失敗: {e}")
            return ""
    
    def _save_result_log(self, result: Dict):
        """保存結果到日誌文件"""
        try:
            log_filename = f"submission_results_{datetime.now().strftime('%Y%m%d')}.json"
            log_filepath = os.path.join(self.screenshot_dir, log_filename)
            
            # 讀取現有日誌
            if os.path.exists(log_filepath):
                with open(log_filepath, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            # 添加新記錄（移除不能序列化的元素）
            log_entry = result.copy()
            if "raw_data" in log_entry and "popup" in log_entry["raw_data"]:
                if "element" in log_entry["raw_data"]["popup"]:
                    del log_entry["raw_data"]["popup"]["element"]
            
            logs.append(log_entry)
            
            # 保存日誌
            with open(log_filepath, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"結果已記錄到: {log_filepath}")
            
        except Exception as e:
            self.logger.error(f"保存結果日誌失敗: {e}")
    
    def _log_result_summary(self, result: Dict):
        """輸出結果摘要"""
        summary = f"""
        ╔══════════════════════════════════════╗
        ║           搶單結果檢測摘要           ║
        ╠══════════════════════════════════════╣
        ║ 時間戳: {result['timestamp']}        ║
        ║ 生命週期結束: {'是' if result['lifecycle_ended'] else '否'}           ║
        ║ 結果類型: {result['result_type']}    ║
        ║ 是否成功: {result['is_success']}     ║
        ║ 訊息: {result['message'][:30]}...    ║
        ║ 截圖: {os.path.basename(result['screenshot_path'])} ║
        ╚══════════════════════════════════════╝
        """
        
        self.logger.info(summary)
        print(summary)  # 同時輸出到控制台
    
    def should_terminate_process(self, result: Dict) -> bool:
        """判斷是否應該終止主程序"""
        return result.get("lifecycle_ended", False)
