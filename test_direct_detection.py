#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接檢測：不管載入問題，直接找所有可能的送出按鈕
Direct Detection: Find all possible submit buttons regardless of loading
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox, scrolledtext
from datetime import datetime
import threading

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DirectDetectionTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 直接檢測：所有可能的送出按鈕")
        self.root.geometry("1200x800")
        
        self.setup_ui()
        
    def setup_ui(self):
        """設置 UI"""
        # 標題
        title_label = tk.Label(self.root, text="🔍 直接檢測：所有可能的送出按鈕", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 說明
        info_text = """
💡 您說得對！不是載入問題，是檢測邏輯問題
🎯 現在直接檢測所有可能的元素，不管它叫什麼名字

請確保編輯彈窗已打開，然後點擊檢測按鈕
        """
        
        info_label = tk.Label(self.root, text=info_text, 
                             font=("Arial", 10), justify="center")
        info_label.pack(pady=5)
        
        # 按鈕區域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 檢測所有元素
        self.detect_all_btn = tk.Button(button_frame, text="🔍 檢測所有元素", 
                                       command=self.detect_all_elements,
                                       font=("Arial", 12), bg="lightblue")
        self.detect_all_btn.pack(side="left", padx=10)
        
        # 檢測可點擊元素
        self.detect_clickable_btn = tk.Button(button_frame, text="👆 檢測可點擊元素", 
                                             command=self.detect_clickable_elements,
                                             font=("Arial", 12), bg="lightgreen")
        self.detect_clickable_btn.pack(side="left", padx=10)
        
        # 檢測表單元素
        self.detect_form_btn = tk.Button(button_frame, text="📝 檢測表單元素", 
                                        command=self.detect_form_elements,
                                        font=("Arial", 12), bg="lightyellow")
        self.detect_form_btn.pack(side="left", padx=10)
        
        # 檢測 DOM 結構
        self.detect_dom_btn = tk.Button(button_frame, text="🌳 檢測 DOM 結構", 
                                       command=self.detect_dom_structure,
                                       font=("Arial", 12), bg="lightcyan")
        self.detect_dom_btn.pack(side="left", padx=10)
        
        # 關閉按鈕
        self.close_btn = tk.Button(button_frame, text="❌ 關閉", 
                                 command=self.close_app,
                                 font=("Arial", 12), bg="lightcoral")
        self.close_btn.pack(side="right", padx=10)
        
        # 日誌顯示區域
        log_frame = tk.Frame(self.root)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(log_frame, text="檢測日誌:", font=("Arial", 12, "bold")).pack(anchor="w")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, width=140)
        self.log_text.pack(fill="both", expand=True)
        
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def detect_all_elements(self):
        """檢測所有元素"""
        try:
            self.log("🔍 開始檢測所有元素...")
            self.detect_all_btn.config(state="disabled")
            
            def detect_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 檢測所有標籤類型
                    tag_types = ['button', 'input', 'a', 'div', 'span', 'td', 'th', 'li']
                    
                    for tag in tag_types:
                        try:
                            elements = driver.find_elements(By.TAG_NAME, tag)
                            self.root.after(0, lambda tag=tag, count=len(elements): 
                                           self.log(f"📊 {tag} 標籤: {count} 個"))
                            
                            # 分析前10個元素
                            for i, element in enumerate(elements[:10]):
                                try:
                                    text = element.text.strip()[:50]
                                    value = (element.get_attribute('value') or '')[:50]
                                    onclick = (element.get_attribute('onclick') or '')[:100]
                                    class_name = (element.get_attribute('class') or '')[:50]
                                    is_visible = element.is_displayed()
                                    
                                    if text or value or onclick:
                                        self.root.after(0, lambda tag=tag, i=i, txt=text, val=value, 
                                                       click=onclick, cls=class_name, vis=is_visible:
                                                       self.log(f"  {tag} {i+1}: text='{txt}' value='{val}' onclick='{click}' class='{cls}' visible={vis}"))
                                        
                                except Exception as e:
                                    pass
                                    
                        except Exception as e:
                            self.root.after(0, lambda tag=tag, e=str(e): self.log(f"❌ 檢測 {tag} 失敗: {e}"))
                    
                    self.root.after(0, lambda: self.log("✅ 所有元素檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_all_btn.config(state="normal"))
            
            threading.Thread(target=detect_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_all_btn.config(state="normal")
    
    def detect_clickable_elements(self):
        """檢測可點擊元素"""
        try:
            self.log("👆 開始檢測可點擊元素...")
            self.detect_clickable_btn.config(state="disabled")
            
            def detect_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 檢測所有可能可點擊的元素
                    clickable_selectors = [
                        "[onclick]",
                        "[role='button']",
                        "button",
                        "input[type='submit']",
                        "input[type='button']",
                        "a[href]",
                        ".btn",
                        ".button",
                        ".ui-button",
                        "[data-action]",
                        "[data-click]"
                    ]
                    
                    all_clickable = []
                    
                    for selector in clickable_selectors:
                        try:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            if elements:
                                all_clickable.extend(elements)
                                self.root.after(0, lambda sel=selector, count=len(elements): 
                                               self.log(f"📊 {sel}: {count} 個"))
                        except Exception as e:
                            pass
                    
                    # 去重
                    unique_elements = []
                    seen_elements = set()
                    for element in all_clickable:
                        try:
                            element_id = id(element)
                            if element_id not in seen_elements:
                                unique_elements.append(element)
                                seen_elements.add(element_id)
                        except:
                            unique_elements.append(element)
                    
                    self.root.after(0, lambda count=len(unique_elements): 
                                   self.log(f"📊 去重後總計: {count} 個可點擊元素"))
                    
                    # 詳細分析每個可點擊元素
                    for i, element in enumerate(unique_elements):
                        try:
                            tag_name = element.tag_name
                            text = element.text.strip()[:50]
                            value = (element.get_attribute('value') or '')[:50]
                            onclick = (element.get_attribute('onclick') or '')[:100]
                            href = (element.get_attribute('href') or '')[:50]
                            class_name = (element.get_attribute('class') or '')[:50]
                            data_action = (element.get_attribute('data-action') or '')[:50]
                            is_visible = element.is_displayed()
                            is_enabled = element.is_enabled()
                            
                            # 檢查是否可能是送出按鈕
                            all_text = f"{text} {value} {onclick} {class_name} {data_action}".lower()
                            submit_indicators = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok', '確定']
                            found_indicators = [ind for ind in submit_indicators if ind in all_text]
                            
                            if found_indicators or is_visible:
                                self.root.after(0, lambda i=i, tag=tag_name, txt=text, val=value, 
                                               click=onclick, href=href, cls=class_name, action=data_action,
                                               vis=is_visible, en=is_enabled, inds=found_indicators:
                                               self.log(f"  元素 {i+1}: <{tag}> text='{txt}' value='{val}' onclick='{click}' href='{href}' class='{cls}' data-action='{action}' visible={vis} enabled={en} 送出指標={inds}"))
                                
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"  元素 {i+1} 分析失敗: {e}"))
                    
                    self.root.after(0, lambda: self.log("✅ 可點擊元素檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_clickable_btn.config(state="normal"))
            
            threading.Thread(target=detect_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_clickable_btn.config(state="normal")
    
    def detect_form_elements(self):
        """檢測表單元素"""
        try:
            self.log("📝 開始檢測表單元素...")
            self.detect_form_btn.config(state="disabled")
            
            def detect_thread():
                try:
                    from mvp_grabber import driver
                    from selenium.webdriver.common.by import By
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 檢測表單
                    forms = driver.find_elements(By.TAG_NAME, "form")
                    self.root.after(0, lambda count=len(forms): self.log(f"📊 檢測到 {count} 個表單"))
                    
                    for i, form in enumerate(forms):
                        try:
                            form_action = form.get_attribute('action') or ''
                            form_method = form.get_attribute('method') or ''
                            form_id = form.get_attribute('id') or ''
                            form_class = form.get_attribute('class') or ''
                            
                            self.root.after(0, lambda i=i, action=form_action, method=form_method, 
                                           fid=form_id, cls=form_class:
                                           self.log(f"  表單 {i+1}: action='{action}' method='{method}' id='{fid}' class='{cls}'"))
                            
                            # 檢測表單內的按鈕
                            form_buttons = form.find_elements(By.TAG_NAME, "button")
                            form_inputs = form.find_elements(By.CSS_SELECTOR, "input[type='submit'], input[type='button']")
                            
                            self.root.after(0, lambda i=i, btn_count=len(form_buttons), inp_count=len(form_inputs):
                                           self.log(f"    表單 {i+1} 內按鈕: button={btn_count}, input={inp_count}"))
                            
                            for j, btn in enumerate(form_buttons + form_inputs):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_value = btn.get_attribute('value') or ''
                                    btn_type = btn.get_attribute('type') or ''
                                    btn_name = btn.get_attribute('name') or ''
                                    
                                    self.root.after(0, lambda i=i, j=j, txt=btn_text, val=btn_value, 
                                                   typ=btn_type, name=btn_name:
                                                   self.log(f"      按鈕 {j+1}: text='{txt}' value='{val}' type='{typ}' name='{name}'"))
                                except:
                                    pass
                                    
                        except Exception as e:
                            self.root.after(0, lambda i=i, e=str(e): self.log(f"  表單 {i+1} 分析失敗: {e}"))
                    
                    self.root.after(0, lambda: self.log("✅ 表單元素檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_form_btn.config(state="normal"))
            
            threading.Thread(target=detect_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_form_btn.config(state="normal")
    
    def detect_dom_structure(self):
        """檢測 DOM 結構"""
        try:
            self.log("🌳 開始檢測 DOM 結構...")
            self.detect_dom_btn.config(state="disabled")
            
            def detect_thread():
                try:
                    from mvp_grabber import driver
                    
                    if not driver:
                        self.root.after(0, lambda: self.log("❌ 瀏覽器未啟動"))
                        return
                    
                    # 獲取完整的 HTML 結構
                    html_content = driver.execute_script("return document.documentElement.outerHTML;")
                    
                    # 搜尋送出相關的內容
                    submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save']
                    
                    self.root.after(0, lambda: self.log(f"📊 HTML 內容長度: {len(html_content)}"))
                    
                    for keyword in submit_keywords:
                        if keyword in html_content:
                            # 找到關鍵字的位置
                            positions = []
                            start = 0
                            while True:
                                pos = html_content.find(keyword, start)
                                if pos == -1:
                                    break
                                positions.append(pos)
                                start = pos + 1
                                if len(positions) >= 10:  # 最多顯示10個位置
                                    break
                            
                            self.root.after(0, lambda kw=keyword, count=len(positions): 
                                           self.log(f"🎯 找到關鍵字 '{kw}': {count} 次"))
                            
                            # 顯示關鍵字周圍的 HTML
                            for i, pos in enumerate(positions[:3]):  # 只顯示前3個
                                start_pos = max(0, pos - 100)
                                end_pos = min(len(html_content), pos + 100)
                                context = html_content[start_pos:end_pos]
                                
                                self.root.after(0, lambda i=i, ctx=context: 
                                               self.log(f"  位置 {i+1} 上下文: ...{ctx}..."))
                    
                    self.root.after(0, lambda: self.log("✅ DOM 結構檢測完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda e=str(e): self.log(f"❌ 檢測過程發生錯誤: {e}"))
                finally:
                    self.root.after(0, lambda: self.detect_dom_btn.config(state="normal"))
            
            threading.Thread(target=detect_thread, daemon=True).start()
            
        except Exception as e:
            self.log(f"❌ 啟動檢測失敗: {e}")
            self.detect_dom_btn.config(state="normal")
    
    def close_app(self):
        """關閉應用"""
        self.root.destroy()
        
    def run(self):
        """運行 GUI"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🔍 啟動直接檢測工具")
    
    try:
        app = DirectDetectionTest()
        app.run()
    except Exception as e:
        print(f"❌ GUI 啟動失敗: {e}")

if __name__ == "__main__":
    main()
