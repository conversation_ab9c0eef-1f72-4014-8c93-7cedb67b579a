2025-07-01 18:45:39,929 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_184539.log
2025-07-01 18:45:42,460 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 18:45:42,461 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 18:45:42,517 - DEBUG - chromedriver not found in PATH
2025-07-01 18:45:42,518 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:45:42,518 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 18:45:42,518 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 18:45:42,518 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 18:45:42,518 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 18:45:42,519 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:45:42,522 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 10520 using 0 to output -3
2025-07-01 18:45:43,056 - DEBUG - POST http://localhost:57667/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 18:45:43,057 - DEBUG - Starting new HTTP connection (1): localhost:57667
2025-07-01 18:45:43,603 - DEBUG - http://localhost:57667 "POST /session HTTP/1.1" 200 0
2025-07-01 18:45:43,603 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir10520_1028203047"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:57670"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"bc4173cdd361c68ae76bf52b5b5544b4"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:43,604 - DEBUG - Finished Request
2025-07-01 18:45:43,604 - DEBUG - POST http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 18:45:44,524 - DEBUG - http://localhost:57667 "POST /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:44,525 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:44,525 - DEBUG - Finished Request
2025-07-01 18:45:44,525 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 18:45:44,526 - DEBUG - POST http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 18:45:44,535 - DEBUG - http://localhost:57667 "POST /session/bc4173cdd361c68ae76bf52b5b5544b4/execute/sync HTTP/1.1" 200 0
2025-07-01 18:45:44,536 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:44,536 - DEBUG - Finished Request
2025-07-01 18:45:44,536 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 18:45:44,537 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:44,585 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:44,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:44,585 - DEBUG - Finished Request
2025-07-01 18:45:45,587 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:45,596 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:45,597 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:45,597 - DEBUG - Finished Request
2025-07-01 18:45:46,597 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:46,604 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:46,604 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:46,604 - DEBUG - Finished Request
2025-07-01 18:45:47,606 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:47,614 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:47,614 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:47,614 - DEBUG - Finished Request
2025-07-01 18:45:48,615 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:48,622 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:48,622 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:48,623 - DEBUG - Finished Request
2025-07-01 18:45:49,624 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:49,630 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:49,630 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:49,630 - DEBUG - Finished Request
2025-07-01 18:45:50,630 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:50,637 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:50,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:50,638 - DEBUG - Finished Request
2025-07-01 18:45:51,639 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:51,647 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:51,648 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:51,648 - DEBUG - Finished Request
2025-07-01 18:45:52,649 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:52,657 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:52,658 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:52,658 - DEBUG - Finished Request
2025-07-01 18:45:53,659 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:53,666 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:53,666 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:53,667 - DEBUG - Finished Request
2025-07-01 18:45:54,668 - DEBUG - GET http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4/url {}
2025-07-01 18:45:54,676 - DEBUG - http://localhost:57667 "GET /session/bc4173cdd361c68ae76bf52b5b5544b4/url HTTP/1.1" 200 0
2025-07-01 18:45:54,676 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:54,676 - DEBUG - Finished Request
2025-07-01 18:45:54,811 - DEBUG - DELETE http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4 {}
2025-07-01 18:45:54,853 - DEBUG - http://localhost:57667 "DELETE /session/bc4173cdd361c68ae76bf52b5b5544b4 HTTP/1.1" 200 0
2025-07-01 18:45:54,853 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:45:54,854 - DEBUG - Finished Request
2025-07-01 18:45:55,688 - DEBUG - DELETE http://localhost:57667/session/bc4173cdd361c68ae76bf52b5b5544b4 {}
2025-07-01 18:45:55,689 - DEBUG - Starting new HTTP connection (1): localhost:57667
