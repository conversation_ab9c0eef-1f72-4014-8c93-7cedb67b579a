"""
主程序整合示例 - 展示如何使用送出結果檢測器
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from submission_result_detector import SubmissionResultDetector

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ages_kh_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AGESKHBot:
    """AGES-KH-Bot 主程序（整合結果檢測）"""
    
    def __init__(self):
        self.driver = None
        self.logger = logging.getLogger(__name__)
        self.result_detector = None
    
    def setup_browser(self):
        """設置瀏覽器"""
        try:
            options = webdriver.ChromeOptions()
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 初始化結果檢測器
            self.result_detector = SubmissionResultDetector(
                self.driver, 
                self.logger,
                screenshot_dir="submission_screenshots"
            )
            
            self.logger.info("瀏覽器和結果檢測器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"瀏覽器設置失敗: {e}")
            return False
    
    def submit_order_and_detect_result(self, captcha_code: str):
        """
        送出訂單並檢測結果 - 搶單生命週期的核心
        
        Args:
            captcha_code: 驗證碼
            
        Returns:
            dict: 檢測結果
        """
        try:
            self.logger.info("開始送出訂單...")
            
            # 1. 填入驗證碼
            captcha_input = self.driver.find_element(By.CSS_SELECTOR, "input[name*='captcha']")
            captcha_input.clear()
            captcha_input.send_keys(captcha_code)
            self.logger.info(f"已填入驗證碼: {captcha_code}")
            
            # 2. 點擊送出按鈕
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('送出'), input[type='submit']")
            submit_button.click()
            self.logger.info("已點擊送出按鈕")
            
            # 3. 等待頁面反應
            time.sleep(2)
            
            # 4. 檢測送出結果 - 關鍵步驟
            self.logger.info("開始檢測送出結果...")
            result = self.result_detector.detect_submission_result(timeout=10)
            
            # 5. 記錄結果
            self._log_submission_result(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"送出訂單過程發生異常: {e}")
            return {
                "lifecycle_ended": True,
                "is_success": False,
                "result_type": "submission_error",
                "message": f"送出過程異常: {e}",
                "timestamp": time.strftime("%Y%m%d_%H%M%S")
            }
    
    def _log_submission_result(self, result: dict):
        """記錄送出結果"""
        if result["is_success"] is True:
            self.logger.info("🎉 搶單可能成功！")
        elif result["is_success"] is False:
            self.logger.warning(f"❌ 搶單失敗: {result['message']}")
        else:
            self.logger.info(f"❓ 搶單結果不明確: {result['message']}")
        
        self.logger.info(f"截圖已保存: {result.get('screenshot_path', 'N/A')}")
    
    def run_order_submission_cycle(self):
        """運行一次完整的搶單週期"""
        try:
            # 這裡應該包含導航到訂單頁面的代碼
            # self.driver.get("https://your-order-page-url")
            
            # 等待用戶輸入驗證碼
            captcha_code = input("請輸入驗證碼: ")
            
            # 送出訂單並檢測結果
            result = self.submit_order_and_detect_result(captcha_code)
            
            # 判斷是否應該終止程序
            if self.result_detector.should_terminate_process(result):
                self.logger.info("🔚 搶單生命週期結束，程序終止")
                return True  # 表示應該終止
            else:
                self.logger.info("🔄 搶單生命週期未結束，可以繼續")
                return False  # 表示可以繼續
                
        except Exception as e:
            self.logger.error(f"搶單週期執行異常: {e}")
            return True  # 異常情況下也終止程序
    
    def cleanup(self):
        """清理資源"""
        if self.driver:
            self.driver.quit()
            self.logger.info("瀏覽器已關閉")

def main():
    """主程序入口"""
    bot = AGESKHBot()
    
    try:
        # 1. 設置瀏覽器
        if not bot.setup_browser():
            print("❌ 瀏覽器設置失敗，程序退出")
            return
        
        print("✅ AGES-KH-Bot 已啟動")
        print("📋 程序將在檢測到送出結果後自動終止")
        
        # 2. 運行搶單週期
        while True:
            print("\n" + "="*50)
            print("🚀 開始新的搶單週期")
            
            should_terminate = bot.run_order_submission_cycle()
            
            if should_terminate:
                print("🏁 程序終止")
                break
            else:
                # 如果沒有終止，詢問是否繼續
                continue_choice = input("\n是否繼續下一次搶單？(y/n): ")
                if continue_choice.lower() != 'y':
                    print("👋 用戶選擇退出")
                    break
    
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷程序")
    except Exception as e:
        print(f"❌ 程序執行異常: {e}")
    finally:
        bot.cleanup()
        print("🔚 程序已結束")

if __name__ == "__main__":
    main()
