2025-07-01 16:41:42,064 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_164142.log
2025-07-01 16:41:49,930 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 16:41:49,930 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 16:41:50,001 - DEBUG - chromedriver not found in PATH
2025-07-01 16:41:50,001 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:41:50,002 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 16:41:50,002 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 16:41:50,002 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 16:41:50,002 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 16:41:50,002 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 16:41:50,006 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 20876 using 0 to output -3
2025-07-01 16:41:50,537 - DEBUG - POST http://localhost:55367/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 16:41:50,538 - DEBUG - Starting new HTTP connection (1): localhost:55367
2025-07-01 16:41:51,071 - DEBUG - http://localhost:55367 "POST /session HTTP/1.1" 200 0
2025-07-01 16:41:51,071 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir20876_1966698555"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:55370"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"a7282eff51b1eb8b38eb2858a4c263fd"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:51,071 - DEBUG - Finished Request
2025-07-01 16:41:51,072 - DEBUG - POST http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 16:41:52,486 - DEBUG - http://localhost:55367 "POST /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:52,486 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:52,486 - DEBUG - Finished Request
2025-07-01 16:41:52,487 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 16:41:52,487 - DEBUG - POST http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 16:41:52,494 - DEBUG - http://localhost:55367 "POST /session/a7282eff51b1eb8b38eb2858a4c263fd/execute/sync HTTP/1.1" 200 0
2025-07-01 16:41:52,494 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:52,494 - DEBUG - Finished Request
2025-07-01 16:41:52,494 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 16:41:52,495 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:52,528 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:52,528 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:52,528 - DEBUG - Finished Request
2025-07-01 16:41:53,529 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:53,538 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:53,538 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:53,539 - DEBUG - Finished Request
2025-07-01 16:41:54,539 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:54,546 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:54,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:54,546 - DEBUG - Finished Request
2025-07-01 16:41:55,547 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:55,552 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:55,552 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:55,552 - DEBUG - Finished Request
2025-07-01 16:41:56,553 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:56,558 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:56,559 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:56,559 - DEBUG - Finished Request
2025-07-01 16:41:57,560 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:57,567 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:57,567 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:57,568 - DEBUG - Finished Request
2025-07-01 16:41:58,569 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:58,576 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:58,576 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:58,577 - DEBUG - Finished Request
2025-07-01 16:41:59,577 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:41:59,584 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 200 0
2025-07-01 16:41:59,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:41:59,585 - DEBUG - Finished Request
2025-07-01 16:42:00,585 - DEBUG - GET http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd/url {}
2025-07-01 16:42:00,586 - DEBUG - http://localhost:55367 "GET /session/a7282eff51b1eb8b38eb2858a4c263fd/url HTTP/1.1" 404 0
2025-07-01 16:42:00,587 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:00,587 - DEBUG - Finished Request
2025-07-01 16:42:00,587 - DEBUG - DELETE http://localhost:55367/session/a7282eff51b1eb8b38eb2858a4c263fd {}
2025-07-01 16:42:00,644 - DEBUG - http://localhost:55367 "DELETE /session/a7282eff51b1eb8b38eb2858a4c263fd HTTP/1.1" 200 0
2025-07-01 16:42:00,645 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 16:42:00,645 - DEBUG - Finished Request
