# DOM Change Analyzer for AGES-KH
# 分析 DOM 元素配置檔的變化，追蹤平台更新
# Version: 1.0
# Author: <PERSON>

import json
import glob
import os
from datetime import datetime
from typing import List, Dict, Any

class DOMChangeAnalyzer:
    def __init__(self):
        self.config_files = []
        self.scan_files = []
        
    def load_all_configs(self):
        """載入所有配置檔"""
        # DOM Inspector 的配置檔
        self.config_files = glob.glob('dom_elements_config_*.json')
        self.config_files.sort()
        
        # Simple Scanner 的掃描檔
        self.scan_files = glob.glob('simple_dom_scan_*.json')
        self.scan_files.sort()
        
        print(f"[INFO] 找到 {len(self.config_files)} 個 DOM 配置檔")
        print(f"[INFO] 找到 {len(self.scan_files)} 個簡單掃描檔")
        
    def analyze_dom_configs(self):
        """分析 DOM 配置檔的變化"""
        if len(self.config_files) < 2:
            print("[WARNING] 需要至少 2 個配置檔才能分析變化")
            return
            
        print("\n" + "="*60)
        print("DOM 配置檔變化分析")
        print("="*60)
        
        for i in range(1, len(self.config_files)):
            previous_file = self.config_files[i-1]
            current_file = self.config_files[i]
            
            print(f"\n📊 比較: {os.path.basename(previous_file)} → {os.path.basename(current_file)}")
            
            try:
                with open(previous_file, 'r', encoding='utf-8') as f:
                    previous_config = json.load(f)
                with open(current_file, 'r', encoding='utf-8') as f:
                    current_config = json.load(f)
                    
                self._compare_configs(previous_config, current_config)
                
            except Exception as e:
                print(f"[ERROR] 比較失敗: {e}")
                
    def analyze_scan_results(self):
        """分析簡單掃描結果的變化"""
        if len(self.scan_files) < 2:
            print("[WARNING] 需要至少 2 個掃描檔才能分析變化")
            return
            
        print("\n" + "="*60)
        print("頁面掃描結果變化分析")
        print("="*60)
        
        for i in range(1, len(self.scan_files)):
            previous_file = self.scan_files[i-1]
            current_file = self.scan_files[i]
            
            print(f"\n📊 比較: {os.path.basename(previous_file)} → {os.path.basename(current_file)}")
            
            try:
                with open(previous_file, 'r', encoding='utf-8') as f:
                    previous_scan = json.load(f)
                with open(current_file, 'r', encoding='utf-8') as f:
                    current_scan = json.load(f)
                    
                self._compare_scans(previous_scan, current_scan)
                
            except Exception as e:
                print(f"[ERROR] 比較失敗: {e}")
                
    def _compare_configs(self, previous: Dict, current: Dict):
        """比較兩個 DOM 配置"""
        # 基本資訊比較
        prev_info = previous.get('scan_info', {})
        curr_info = current.get('scan_info', {})
        
        print(f"  時間: {prev_info.get('timestamp', 'N/A')} → {curr_info.get('timestamp', 'N/A')}")
        print(f"  瀏覽器: {prev_info.get('browser_type', 'N/A')} → {curr_info.get('browser_type', 'N/A')}")
        
        # 元素比較
        prev_elements = set(previous.get('elements', {}).keys())
        curr_elements = set(current.get('elements', {}).keys())
        
        new_elements = curr_elements - prev_elements
        missing_elements = prev_elements - curr_elements
        common_elements = prev_elements & curr_elements
        
        print(f"  元素數量: {len(prev_elements)} → {len(curr_elements)} ({len(curr_elements) - len(prev_elements):+d})")
        
        if new_elements:
            print(f"  ✅ 新增元素: {list(new_elements)}")
        if missing_elements:
            print(f"  ❌ 消失元素: {list(missing_elements)}")
            
        # 檢查共同元素的選擇器變化
        changed_selectors = []
        for element_name in common_elements:
            prev_selector = previous['elements'][element_name].get('selector', '')
            curr_selector = current['elements'][element_name].get('selector', '')
            
            if prev_selector != curr_selector:
                changed_selectors.append({
                    'element': element_name,
                    'old': prev_selector,
                    'new': curr_selector
                })
                
        if changed_selectors:
            print(f"  🔄 選擇器變化:")
            for change in changed_selectors:
                print(f"    - {change['element']}: {change['old']} → {change['new']}")
        else:
            print(f"  ✅ 選擇器無變化")
            
    def _compare_scans(self, previous: Dict, current: Dict):
        """比較兩個掃描結果"""
        prev_info = previous.get('scan_info', {})
        curr_info = current.get('scan_info', {})
        
        print(f"  時間: {prev_info.get('timestamp', 'N/A')} → {curr_info.get('timestamp', 'N/A')}")
        
        prev_count = prev_info.get('total_elements', 0)
        curr_count = curr_info.get('total_elements', 0)
        
        print(f"  總元素數: {prev_count} → {curr_count} ({curr_count - prev_count:+d})")
        
        # 分析元素類型變化
        prev_elements = previous.get('elements', {})
        curr_elements = current.get('elements', {})
        
        prev_types = self._count_element_types(prev_elements)
        curr_types = self._count_element_types(curr_elements)
        
        print(f"  元素類型變化:")
        all_types = set(prev_types.keys()) | set(curr_types.keys())
        
        for element_type in sorted(all_types):
            prev_count = prev_types.get(element_type, 0)
            curr_count = curr_types.get(element_type, 0)
            if prev_count != curr_count:
                print(f"    - {element_type}: {prev_count} → {curr_count} ({curr_count - prev_count:+d})")
                
    def _count_element_types(self, elements: Dict) -> Dict[str, int]:
        """統計元素類型數量"""
        type_counts = {}
        
        for element_info in elements.values():
            if isinstance(element_info, dict):
                element_type = element_info.get('tag', 'unknown')
                type_counts[element_type] = type_counts.get(element_type, 0) + 1
                
        return type_counts
        
    def generate_stability_report(self):
        """生成穩定性報告"""
        print("\n" + "="*60)
        print("DOM 穩定性報告")
        print("="*60)
        
        if self.config_files:
            print(f"\n📋 DOM 配置檔分析:")
            print(f"  - 總掃描次數: {len(self.config_files)}")
            
            if len(self.config_files) >= 2:
                # 分析第一次和最後一次的差異
                try:
                    with open(self.config_files[0], 'r', encoding='utf-8') as f:
                        first_config = json.load(f)
                    with open(self.config_files[-1], 'r', encoding='utf-8') as f:
                        last_config = json.load(f)
                        
                    first_elements = set(first_config.get('elements', {}).keys())
                    last_elements = set(last_config.get('elements', {}).keys())
                    
                    stability = len(first_elements & last_elements) / len(first_elements | last_elements) * 100
                    
                    print(f"  - 元素穩定性: {stability:.1f}%")
                    print(f"  - 首次掃描: {first_config.get('scan_info', {}).get('timestamp', 'N/A')}")
                    print(f"  - 最新掃描: {last_config.get('scan_info', {}).get('timestamp', 'N/A')}")
                    
                except Exception as e:
                    print(f"  - 穩定性分析失敗: {e}")
                    
        if self.scan_files:
            print(f"\n📋 頁面掃描分析:")
            print(f"  - 總掃描次數: {len(self.scan_files)}")
            
        # 建議
        print(f"\n💡 建議:")
        if len(self.config_files) < 3:
            print("  - 建議進行更多次掃描以建立穩定性基準")
        else:
            print("  - 已有足夠的掃描資料，可以開始開發")
            
        print("  - 定期執行掃描以監控平台變化")
        print("  - 如發現重大變化，需要更新主程式的元素定位邏輯")

def main():
    print("="*60)
    print("           DOM 變化分析工具")
    print("="*60)
    print("此工具分析 DOM 配置檔的變化，追蹤平台更新")
    print("="*60)
    
    analyzer = DOMChangeAnalyzer()
    
    try:
        # 載入所有配置檔
        analyzer.load_all_configs()
        
        if not analyzer.config_files and not analyzer.scan_files:
            print("\n[WARNING] 未找到任何配置檔或掃描檔")
            print("請先執行 dom_inspector.py 或 simple_dom_scanner.py")
            return
            
        # 分析變化
        if analyzer.config_files:
            analyzer.analyze_dom_configs()
            
        if analyzer.scan_files:
            analyzer.analyze_scan_results()
            
        # 生成穩定性報告
        analyzer.generate_stability_report()
        
    except Exception as e:
        print(f"[ERROR] 分析過程發生錯誤: {e}")

if __name__ == "__main__":
    main()
