2025-07-01 17:23:02,196 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_172302.log
2025-07-01 17:23:19,017 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 17:23:19,017 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 17:23:19,635 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-01 17:23:19,636 - DEBUG - chromedriver not found in PATH
2025-07-01 17:23:19,636 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:23:19,636 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 17:23:19,636 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-01 17:23:19,636 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 17:23:19,637 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 17:23:19,637 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 17:23:19,637 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 17:23:19,640 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 18364 using 0 to output -3
2025-07-01 17:23:20,161 - DEBUG - POST http://localhost:56190/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 17:23:20,162 - DEBUG - Starting new HTTP connection (1): localhost:56190
2025-07-01 17:23:20,690 - DEBUG - http://localhost:56190 "POST /session HTTP/1.1" 200 0
2025-07-01 17:23:20,691 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir18364_1593427522"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:56196"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"599986c233863706fff173d182f268fa"}} | headers=HTTPHeaderDict({'Content-Length': '884', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:20,691 - DEBUG - Finished Request
2025-07-01 17:23:20,691 - DEBUG - POST http://localhost:56190/session/599986c233863706fff173d182f268fa/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 17:23:21,785 - DEBUG - http://localhost:56190 "POST /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:21,785 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:21,786 - DEBUG - Finished Request
2025-07-01 17:23:21,786 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 17:23:21,787 - DEBUG - POST http://localhost:56190/session/599986c233863706fff173d182f268fa/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 17:23:21,802 - DEBUG - http://localhost:56190 "POST /session/599986c233863706fff173d182f268fa/execute/sync HTTP/1.1" 200 0
2025-07-01 17:23:21,802 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:21,803 - DEBUG - Finished Request
2025-07-01 17:23:21,803 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 17:23:21,803 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:21,843 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:21,843 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:21,844 - DEBUG - Finished Request
2025-07-01 17:23:22,844 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:22,850 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:22,850 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:22,851 - DEBUG - Finished Request
2025-07-01 17:23:23,852 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:23,858 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:23,859 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:23,859 - DEBUG - Finished Request
2025-07-01 17:23:24,860 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:24,865 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:24,866 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:24,866 - DEBUG - Finished Request
2025-07-01 17:23:25,867 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:25,874 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:25,875 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:25,875 - DEBUG - Finished Request
2025-07-01 17:23:26,876 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:26,880 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:26,881 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:26,881 - DEBUG - Finished Request
2025-07-01 17:23:27,882 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:27,887 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:27,887 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:27,888 - DEBUG - Finished Request
2025-07-01 17:23:28,889 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:28,896 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:28,896 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:28,897 - DEBUG - Finished Request
2025-07-01 17:23:29,897 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:29,903 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:29,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:29,904 - DEBUG - Finished Request
2025-07-01 17:23:30,905 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:30,912 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:30,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:30,913 - DEBUG - Finished Request
2025-07-01 17:23:31,913 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:31,919 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:31,920 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:31,920 - DEBUG - Finished Request
2025-07-01 17:23:32,921 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:32,928 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:32,928 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:32,929 - DEBUG - Finished Request
2025-07-01 17:23:33,929 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:33,943 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:33,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:33,944 - DEBUG - Finished Request
2025-07-01 17:23:34,944 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:34,950 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:34,950 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:34,951 - DEBUG - Finished Request
2025-07-01 17:23:35,952 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:35,959 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:35,959 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:35,959 - DEBUG - Finished Request
2025-07-01 17:23:36,960 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:36,967 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:36,968 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:36,969 - DEBUG - Finished Request
2025-07-01 17:23:37,970 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:37,976 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:37,976 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:37,977 - DEBUG - Finished Request
2025-07-01 17:23:38,978 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:38,984 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:38,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:38,984 - DEBUG - Finished Request
2025-07-01 17:23:39,985 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:39,991 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:39,992 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:39,992 - DEBUG - Finished Request
2025-07-01 17:23:40,993 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:40,998 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:40,999 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:40,999 - DEBUG - Finished Request
2025-07-01 17:23:42,000 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:42,008 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:42,008 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:42,009 - DEBUG - Finished Request
2025-07-01 17:23:43,010 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:43,016 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:43,016 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:43,016 - DEBUG - Finished Request
2025-07-01 17:23:44,017 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:44,023 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:44,024 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:44,024 - DEBUG - Finished Request
2025-07-01 17:23:45,024 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:45,030 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:45,031 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:45,031 - DEBUG - Finished Request
2025-07-01 17:23:46,032 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:46,038 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:46,038 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:46,038 - DEBUG - Finished Request
2025-07-01 17:23:47,039 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:47,046 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:47,047 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:47,047 - DEBUG - Finished Request
2025-07-01 17:23:48,047 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:48,053 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:48,054 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:48,054 - DEBUG - Finished Request
2025-07-01 17:23:49,055 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:49,062 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:49,062 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:49,063 - DEBUG - Finished Request
2025-07-01 17:23:50,063 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:50,072 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:50,072 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:50,072 - DEBUG - Finished Request
2025-07-01 17:23:51,073 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:51,080 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:51,081 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:51,081 - DEBUG - Finished Request
2025-07-01 17:23:52,082 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:52,088 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:52,088 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:52,089 - DEBUG - Finished Request
2025-07-01 17:23:53,090 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:53,096 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:53,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:53,097 - DEBUG - Finished Request
2025-07-01 17:23:54,099 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:54,106 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:54,107 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:54,107 - DEBUG - Finished Request
2025-07-01 17:23:55,109 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:55,116 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:55,116 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:55,117 - DEBUG - Finished Request
2025-07-01 17:23:56,117 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:56,124 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:56,124 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:56,125 - DEBUG - Finished Request
2025-07-01 17:23:57,126 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:57,132 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:57,132 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:57,133 - DEBUG - Finished Request
2025-07-01 17:23:58,134 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:58,140 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:58,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:58,141 - DEBUG - Finished Request
2025-07-01 17:23:59,142 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:23:59,148 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:23:59,149 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:23:59,149 - DEBUG - Finished Request
2025-07-01 17:24:00,150 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:00,156 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:00,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:00,156 - DEBUG - Finished Request
2025-07-01 17:24:01,158 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:01,164 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:01,164 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:01,165 - DEBUG - Finished Request
2025-07-01 17:24:02,165 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:02,172 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:02,172 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:02,172 - DEBUG - Finished Request
2025-07-01 17:24:03,174 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:03,183 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:03,183 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:03,183 - DEBUG - Finished Request
2025-07-01 17:24:04,184 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:04,190 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:04,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:04,190 - DEBUG - Finished Request
2025-07-01 17:24:05,191 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:05,198 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:05,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:05,199 - DEBUG - Finished Request
2025-07-01 17:24:06,200 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:06,206 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:06,206 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:06,207 - DEBUG - Finished Request
2025-07-01 17:24:07,208 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:07,600 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:07,601 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:07,601 - DEBUG - Finished Request
2025-07-01 17:24:08,603 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:08,608 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:08,608 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:08,609 - DEBUG - Finished Request
2025-07-01 17:24:09,610 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:09,618 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:09,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:09,618 - DEBUG - Finished Request
2025-07-01 17:24:10,620 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:10,627 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:10,628 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:10,629 - DEBUG - Finished Request
2025-07-01 17:24:11,629 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:11,635 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:11,636 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:11,636 - DEBUG - Finished Request
2025-07-01 17:24:12,638 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:12,655 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:12,655 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:12,656 - DEBUG - Finished Request
2025-07-01 17:24:13,656 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:13,663 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:13,664 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:13,664 - DEBUG - Finished Request
2025-07-01 17:24:14,665 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:14,671 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:14,671 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:14,672 - DEBUG - Finished Request
2025-07-01 17:24:15,674 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:15,680 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:15,680 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:15,681 - DEBUG - Finished Request
2025-07-01 17:24:16,681 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:16,686 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:16,687 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:16,687 - DEBUG - Finished Request
2025-07-01 17:24:17,689 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:17,694 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:17,695 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:17,695 - DEBUG - Finished Request
2025-07-01 17:24:18,697 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:18,704 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:18,704 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:18,704 - DEBUG - Finished Request
2025-07-01 17:24:19,705 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:19,710 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:19,711 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:19,711 - DEBUG - Finished Request
2025-07-01 17:24:20,712 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:20,720 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:20,720 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:20,720 - DEBUG - Finished Request
2025-07-01 17:24:21,721 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:21,727 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:21,727 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:21,728 - DEBUG - Finished Request
2025-07-01 17:24:22,728 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:22,735 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:22,735 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:22,735 - DEBUG - Finished Request
2025-07-01 17:24:23,737 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:23,745 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:23,745 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:23,746 - DEBUG - Finished Request
2025-07-01 17:24:24,747 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:24,755 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:24,755 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:24,755 - DEBUG - Finished Request
2025-07-01 17:24:25,756 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:25,766 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:25,767 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:25,767 - DEBUG - Finished Request
2025-07-01 17:24:26,768 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:26,775 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:26,776 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:26,776 - DEBUG - Finished Request
2025-07-01 17:24:27,777 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:27,783 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:27,783 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:27,783 - DEBUG - Finished Request
2025-07-01 17:24:28,784 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:28,792 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:28,792 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:28,793 - DEBUG - Finished Request
2025-07-01 17:24:29,793 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:29,801 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:29,801 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:29,802 - DEBUG - Finished Request
2025-07-01 17:24:30,803 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:30,812 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:30,812 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:30,812 - DEBUG - Finished Request
2025-07-01 17:24:31,813 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:31,821 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:31,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:31,822 - DEBUG - Finished Request
2025-07-01 17:24:32,822 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:32,829 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:32,830 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:32,830 - DEBUG - Finished Request
2025-07-01 17:24:33,831 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:33,838 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:33,838 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:33,838 - DEBUG - Finished Request
2025-07-01 17:24:34,840 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:34,848 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:34,849 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:34,849 - DEBUG - Finished Request
2025-07-01 17:24:35,850 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:35,856 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:35,856 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:35,857 - DEBUG - Finished Request
2025-07-01 17:24:36,858 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:36,864 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:36,865 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:36,865 - DEBUG - Finished Request
2025-07-01 17:24:37,867 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:37,874 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:37,874 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:37,875 - DEBUG - Finished Request
2025-07-01 17:24:38,876 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:38,882 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:38,883 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:38,883 - DEBUG - Finished Request
2025-07-01 17:24:39,883 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:39,889 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:39,889 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:39,890 - DEBUG - Finished Request
2025-07-01 17:24:40,891 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:40,896 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:40,896 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:40,896 - DEBUG - Finished Request
2025-07-01 17:24:41,897 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:41,904 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:41,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:41,904 - DEBUG - Finished Request
2025-07-01 17:24:42,905 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:42,912 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:42,912 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:42,912 - DEBUG - Finished Request
2025-07-01 17:24:43,913 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:43,920 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:43,920 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:43,920 - DEBUG - Finished Request
2025-07-01 17:24:44,922 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:44,929 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:44,929 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:44,929 - DEBUG - Finished Request
2025-07-01 17:24:45,930 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:45,936 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:45,936 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:45,937 - DEBUG - Finished Request
2025-07-01 17:24:46,938 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:46,946 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:46,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:46,946 - DEBUG - Finished Request
2025-07-01 17:24:47,947 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:47,953 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:47,954 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:47,954 - DEBUG - Finished Request
2025-07-01 17:24:48,954 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:48,961 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:48,961 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:48,963 - DEBUG - Finished Request
2025-07-01 17:24:49,964 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:49,970 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:49,970 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:49,970 - DEBUG - Finished Request
2025-07-01 17:24:50,972 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:50,981 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:50,981 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:50,981 - DEBUG - Finished Request
2025-07-01 17:24:51,982 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:51,989 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:51,990 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:51,990 - DEBUG - Finished Request
2025-07-01 17:24:52,990 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:52,997 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:52,998 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:52,998 - DEBUG - Finished Request
2025-07-01 17:24:53,999 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:54,006 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:54,006 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:54,007 - DEBUG - Finished Request
2025-07-01 17:24:55,007 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:55,015 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:55,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:55,015 - DEBUG - Finished Request
2025-07-01 17:24:56,015 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:56,023 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:56,023 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:56,023 - DEBUG - Finished Request
2025-07-01 17:24:57,024 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:57,030 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:57,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:57,030 - DEBUG - Finished Request
2025-07-01 17:24:58,031 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:58,038 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:58,038 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:58,038 - DEBUG - Finished Request
2025-07-01 17:24:59,039 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:24:59,046 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:24:59,046 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:24:59,046 - DEBUG - Finished Request
2025-07-01 17:25:00,047 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:00,054 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:00,055 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:00,055 - DEBUG - Finished Request
2025-07-01 17:25:01,056 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:01,064 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:01,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:01,064 - DEBUG - Finished Request
2025-07-01 17:25:02,065 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:02,072 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:02,072 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:02,072 - DEBUG - Finished Request
2025-07-01 17:25:03,074 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:03,081 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:03,081 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:03,081 - DEBUG - Finished Request
2025-07-01 17:25:04,083 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:04,089 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:04,089 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:04,089 - DEBUG - Finished Request
2025-07-01 17:25:05,090 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:05,096 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:05,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:05,097 - DEBUG - Finished Request
2025-07-01 17:25:06,098 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:06,104 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:06,104 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:06,105 - DEBUG - Finished Request
2025-07-01 17:25:07,105 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:07,113 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:07,113 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:07,113 - DEBUG - Finished Request
2025-07-01 17:25:08,114 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:08,122 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:08,122 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:08,122 - DEBUG - Finished Request
2025-07-01 17:25:09,123 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:09,130 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:09,130 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:09,130 - DEBUG - Finished Request
2025-07-01 17:25:10,131 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:10,139 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:10,139 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:10,139 - DEBUG - Finished Request
2025-07-01 17:25:11,140 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:11,147 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:11,148 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:11,148 - DEBUG - Finished Request
2025-07-01 17:25:12,149 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:12,155 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:12,155 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:12,156 - DEBUG - Finished Request
2025-07-01 17:25:13,156 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:13,161 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:13,161 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:13,162 - DEBUG - Finished Request
2025-07-01 17:25:14,163 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:14,172 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:14,173 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:14,173 - DEBUG - Finished Request
2025-07-01 17:25:15,173 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:15,180 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:15,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:15,181 - DEBUG - Finished Request
2025-07-01 17:25:16,181 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:16,190 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:16,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:16,190 - DEBUG - Finished Request
2025-07-01 17:25:17,192 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:17,200 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:17,200 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:17,200 - DEBUG - Finished Request
2025-07-01 17:25:18,202 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:18,209 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:18,209 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:18,209 - DEBUG - Finished Request
2025-07-01 17:25:19,210 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:19,215 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:19,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:19,216 - DEBUG - Finished Request
2025-07-01 17:25:20,217 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:20,224 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:20,224 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:20,224 - DEBUG - Finished Request
2025-07-01 17:25:21,225 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:21,230 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:21,230 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:21,231 - DEBUG - Finished Request
2025-07-01 17:25:22,232 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:22,237 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:22,237 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:22,238 - DEBUG - Finished Request
2025-07-01 17:25:23,238 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:23,247 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:23,247 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:23,247 - DEBUG - Finished Request
2025-07-01 17:25:24,248 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:24,253 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:24,254 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:24,254 - DEBUG - Finished Request
2025-07-01 17:25:25,254 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:25,261 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:25,262 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:25,262 - DEBUG - Finished Request
2025-07-01 17:25:26,263 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:26,270 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:26,271 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:26,271 - DEBUG - Finished Request
2025-07-01 17:25:27,271 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:27,278 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:27,279 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:27,279 - DEBUG - Finished Request
2025-07-01 17:25:28,280 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:28,288 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:28,288 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:28,289 - DEBUG - Finished Request
2025-07-01 17:25:29,297 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:29,485 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:29,490 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:29,492 - DEBUG - Finished Request
2025-07-01 17:25:30,495 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:30,503 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:30,503 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:30,503 - DEBUG - Finished Request
2025-07-01 17:25:31,504 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:31,510 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:31,511 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:31,511 - DEBUG - Finished Request
2025-07-01 17:25:32,512 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:32,519 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:32,519 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:32,520 - DEBUG - Finished Request
2025-07-01 17:25:33,520 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:33,528 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:33,528 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:33,529 - DEBUG - Finished Request
2025-07-01 17:25:34,530 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:34,539 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:34,540 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:34,540 - DEBUG - Finished Request
2025-07-01 17:25:35,541 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:35,548 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:35,548 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:35,548 - DEBUG - Finished Request
2025-07-01 17:25:36,550 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:36,557 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:36,557 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:36,557 - DEBUG - Finished Request
2025-07-01 17:25:37,558 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:37,567 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:37,567 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:37,567 - DEBUG - Finished Request
2025-07-01 17:25:38,569 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:38,577 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:38,578 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:38,578 - DEBUG - Finished Request
2025-07-01 17:25:39,580 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:39,587 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:39,587 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:39,587 - DEBUG - Finished Request
2025-07-01 17:25:40,588 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:40,599 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:40,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:40,600 - DEBUG - Finished Request
2025-07-01 17:25:41,601 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:41,609 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:41,609 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:41,609 - DEBUG - Finished Request
2025-07-01 17:25:42,610 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:42,618 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:42,618 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:42,619 - DEBUG - Finished Request
2025-07-01 17:25:43,620 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:43,629 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:43,629 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:43,630 - DEBUG - Finished Request
2025-07-01 17:25:44,630 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:44,641 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:44,641 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:44,641 - DEBUG - Finished Request
2025-07-01 17:25:45,643 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:45,653 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:45,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:45,654 - DEBUG - Finished Request
2025-07-01 17:25:46,654 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:46,663 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:46,663 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:46,663 - DEBUG - Finished Request
2025-07-01 17:25:47,665 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:47,672 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:47,673 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:47,673 - DEBUG - Finished Request
2025-07-01 17:25:48,674 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:48,681 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:48,681 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:48,682 - DEBUG - Finished Request
2025-07-01 17:25:49,683 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:49,692 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:49,693 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:49,693 - DEBUG - Finished Request
2025-07-01 17:25:50,693 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:50,700 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:50,700 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:50,700 - DEBUG - Finished Request
2025-07-01 17:25:51,702 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:51,709 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:51,709 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:51,709 - DEBUG - Finished Request
2025-07-01 17:25:52,710 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:52,719 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:52,719 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:52,719 - DEBUG - Finished Request
2025-07-01 17:25:53,720 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:53,730 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:53,730 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:53,730 - DEBUG - Finished Request
2025-07-01 17:25:54,731 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:54,738 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:54,738 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:54,739 - DEBUG - Finished Request
2025-07-01 17:25:55,740 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:55,747 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:55,748 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:55,748 - DEBUG - Finished Request
2025-07-01 17:25:56,748 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:56,757 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:56,757 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:56,757 - DEBUG - Finished Request
2025-07-01 17:25:57,758 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:57,767 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:57,767 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:57,768 - DEBUG - Finished Request
2025-07-01 17:25:58,769 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:58,780 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:58,780 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:58,781 - DEBUG - Finished Request
2025-07-01 17:25:59,781 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:25:59,790 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:25:59,791 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:25:59,791 - DEBUG - Finished Request
2025-07-01 17:26:00,792 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:00,799 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:00,799 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:00,799 - DEBUG - Finished Request
2025-07-01 17:26:01,800 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:01,808 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:01,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:01,808 - DEBUG - Finished Request
2025-07-01 17:26:02,810 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:02,818 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:02,818 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:02,818 - DEBUG - Finished Request
2025-07-01 17:26:03,819 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:03,828 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:03,828 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:03,829 - DEBUG - Finished Request
2025-07-01 17:26:04,829 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:04,838 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:04,838 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:04,838 - DEBUG - Finished Request
2025-07-01 17:26:05,839 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:05,846 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:05,847 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:05,847 - DEBUG - Finished Request
2025-07-01 17:26:06,848 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:06,856 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:06,856 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:06,857 - DEBUG - Finished Request
2025-07-01 17:26:07,858 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:07,867 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:07,868 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:07,868 - DEBUG - Finished Request
2025-07-01 17:26:08,869 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:08,877 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:08,878 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:08,878 - DEBUG - Finished Request
2025-07-01 17:26:09,879 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:09,886 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:09,886 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:09,886 - DEBUG - Finished Request
2025-07-01 17:26:10,887 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:10,895 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:10,895 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:10,895 - DEBUG - Finished Request
2025-07-01 17:26:11,896 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:11,904 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:11,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:11,905 - DEBUG - Finished Request
2025-07-01 17:26:12,905 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:12,913 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:12,913 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:12,913 - DEBUG - Finished Request
2025-07-01 17:26:13,914 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:13,921 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:13,921 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:13,922 - DEBUG - Finished Request
2025-07-01 17:26:14,923 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:14,931 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:14,932 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:14,932 - DEBUG - Finished Request
2025-07-01 17:26:15,932 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:15,940 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:15,940 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:15,940 - DEBUG - Finished Request
2025-07-01 17:26:16,941 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:16,948 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:16,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:16,949 - DEBUG - Finished Request
2025-07-01 17:26:17,949 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:17,955 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:17,955 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:17,955 - DEBUG - Finished Request
2025-07-01 17:26:18,956 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:18,964 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:18,964 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:18,964 - DEBUG - Finished Request
2025-07-01 17:26:19,966 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:19,973 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:19,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:19,974 - DEBUG - Finished Request
2025-07-01 17:26:20,975 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:20,982 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:20,983 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:20,983 - DEBUG - Finished Request
2025-07-01 17:26:21,985 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:21,995 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:21,996 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:21,996 - DEBUG - Finished Request
2025-07-01 17:26:22,997 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:23,006 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:23,007 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:23,007 - DEBUG - Finished Request
2025-07-01 17:26:24,007 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:24,014 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:24,014 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:24,014 - DEBUG - Finished Request
2025-07-01 17:26:25,015 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:25,022 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:25,023 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:25,023 - DEBUG - Finished Request
2025-07-01 17:26:26,024 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:26,030 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:26,030 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:26,030 - DEBUG - Finished Request
2025-07-01 17:26:27,031 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:27,108 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:27,109 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:27,109 - DEBUG - Finished Request
2025-07-01 17:26:28,110 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:28,117 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:28,117 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:28,117 - DEBUG - Finished Request
2025-07-01 17:26:29,118 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:29,129 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:29,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:29,129 - DEBUG - Finished Request
2025-07-01 17:26:30,129 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:30,136 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:30,136 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:30,136 - DEBUG - Finished Request
2025-07-01 17:26:31,138 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:31,146 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:31,146 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:31,146 - DEBUG - Finished Request
2025-07-01 17:26:32,147 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:32,154 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:32,154 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:32,154 - DEBUG - Finished Request
2025-07-01 17:26:33,155 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:33,162 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:33,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:33,163 - DEBUG - Finished Request
2025-07-01 17:26:34,163 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:34,170 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:34,170 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:34,171 - DEBUG - Finished Request
2025-07-01 17:26:35,171 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:35,180 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:35,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:35,181 - DEBUG - Finished Request
2025-07-01 17:26:36,182 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:36,190 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:36,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:36,190 - DEBUG - Finished Request
2025-07-01 17:26:37,191 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:37,200 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:37,201 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:37,201 - DEBUG - Finished Request
2025-07-01 17:26:38,202 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:38,211 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:38,211 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:38,212 - DEBUG - Finished Request
2025-07-01 17:26:39,213 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:39,220 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:39,220 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:39,220 - DEBUG - Finished Request
2025-07-01 17:26:40,221 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:40,231 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:40,232 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:40,232 - DEBUG - Finished Request
2025-07-01 17:26:41,233 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:41,245 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:41,245 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:41,246 - DEBUG - Finished Request
2025-07-01 17:26:42,247 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:42,254 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:42,254 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:42,255 - DEBUG - Finished Request
2025-07-01 17:26:43,255 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:43,265 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:43,265 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:43,265 - DEBUG - Finished Request
2025-07-01 17:26:44,267 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:44,275 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:44,276 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:44,276 - DEBUG - Finished Request
2025-07-01 17:26:45,277 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:45,285 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:45,285 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:45,285 - DEBUG - Finished Request
2025-07-01 17:26:46,286 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:46,294 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:46,294 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:46,295 - DEBUG - Finished Request
2025-07-01 17:26:47,296 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:47,304 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:47,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:47,305 - DEBUG - Finished Request
2025-07-01 17:26:48,306 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:48,313 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:48,314 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:48,314 - DEBUG - Finished Request
2025-07-01 17:26:49,315 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:49,323 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:49,323 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:49,324 - DEBUG - Finished Request
2025-07-01 17:26:50,324 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:50,332 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:50,332 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:50,332 - DEBUG - Finished Request
2025-07-01 17:26:51,334 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:51,342 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:51,343 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:51,343 - DEBUG - Finished Request
2025-07-01 17:26:52,343 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:52,351 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:52,351 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:52,352 - DEBUG - Finished Request
2025-07-01 17:26:53,352 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:53,360 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:53,360 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:53,361 - DEBUG - Finished Request
2025-07-01 17:26:54,362 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:54,369 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:54,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:54,370 - DEBUG - Finished Request
2025-07-01 17:26:55,371 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:55,380 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:55,380 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:55,380 - DEBUG - Finished Request
2025-07-01 17:26:56,382 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:56,391 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:56,391 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:56,391 - DEBUG - Finished Request
2025-07-01 17:26:57,392 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:57,403 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:57,404 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:57,404 - DEBUG - Finished Request
2025-07-01 17:26:58,405 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:58,413 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:58,414 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:58,414 - DEBUG - Finished Request
2025-07-01 17:26:59,415 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:26:59,423 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:26:59,423 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:26:59,424 - DEBUG - Finished Request
2025-07-01 17:27:00,425 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:00,432 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:00,433 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:00,433 - DEBUG - Finished Request
2025-07-01 17:27:01,434 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:01,442 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:01,442 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:01,442 - DEBUG - Finished Request
2025-07-01 17:27:02,443 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:02,451 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:02,451 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:02,451 - DEBUG - Finished Request
2025-07-01 17:27:03,453 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:03,461 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:03,461 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:03,463 - DEBUG - Finished Request
2025-07-01 17:27:04,463 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:04,473 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:04,473 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:04,474 - DEBUG - Finished Request
2025-07-01 17:27:05,475 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:05,482 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:05,483 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:05,483 - DEBUG - Finished Request
2025-07-01 17:27:06,484 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:06,493 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:06,493 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:06,494 - DEBUG - Finished Request
2025-07-01 17:27:07,495 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:07,503 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:07,504 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:07,504 - DEBUG - Finished Request
2025-07-01 17:27:08,505 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:08,513 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:08,513 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:08,513 - DEBUG - Finished Request
2025-07-01 17:27:09,514 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:09,524 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:09,524 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:09,524 - DEBUG - Finished Request
2025-07-01 17:27:10,525 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:10,532 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:10,533 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:10,534 - DEBUG - Finished Request
2025-07-01 17:27:11,536 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:11,544 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:11,544 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:11,545 - DEBUG - Finished Request
2025-07-01 17:27:12,545 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:12,554 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:12,554 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:12,555 - DEBUG - Finished Request
2025-07-01 17:27:13,556 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:13,563 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:13,563 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:13,563 - DEBUG - Finished Request
2025-07-01 17:27:14,565 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:14,573 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:14,574 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:14,574 - DEBUG - Finished Request
2025-07-01 17:27:15,575 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:15,585 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:15,585 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:15,585 - DEBUG - Finished Request
2025-07-01 17:27:16,586 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:16,595 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:16,596 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:16,596 - DEBUG - Finished Request
2025-07-01 17:27:17,597 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:17,604 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:17,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:17,605 - DEBUG - Finished Request
2025-07-01 17:27:18,606 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:18,615 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:18,615 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:18,615 - DEBUG - Finished Request
2025-07-01 17:27:19,617 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:19,624 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:19,625 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:19,625 - DEBUG - Finished Request
2025-07-01 17:27:20,625 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:20,635 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:20,636 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:20,636 - DEBUG - Finished Request
2025-07-01 17:27:21,637 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:21,647 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:21,648 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:21,648 - DEBUG - Finished Request
2025-07-01 17:27:22,649 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:22,658 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:22,659 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:22,659 - DEBUG - Finished Request
2025-07-01 17:27:23,660 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:23,670 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:23,670 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:23,670 - DEBUG - Finished Request
2025-07-01 17:27:24,672 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:24,681 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:24,681 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:24,682 - DEBUG - Finished Request
2025-07-01 17:27:25,682 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:25,690 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:25,691 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:25,691 - DEBUG - Finished Request
2025-07-01 17:27:26,692 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:26,701 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:26,701 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:26,701 - DEBUG - Finished Request
2025-07-01 17:27:27,702 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:27,712 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:27,712 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:27,712 - DEBUG - Finished Request
2025-07-01 17:27:28,713 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:28,723 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:28,723 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:28,724 - DEBUG - Finished Request
2025-07-01 17:27:29,725 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:29,735 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:29,735 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:29,736 - DEBUG - Finished Request
2025-07-01 17:27:30,737 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:30,745 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:30,746 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:30,746 - DEBUG - Finished Request
2025-07-01 17:27:31,748 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:31,758 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:31,758 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:31,759 - DEBUG - Finished Request
2025-07-01 17:27:32,760 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:32,768 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:32,768 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:32,769 - DEBUG - Finished Request
2025-07-01 17:27:33,769 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:33,777 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:33,778 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:33,778 - DEBUG - Finished Request
2025-07-01 17:27:34,779 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:34,788 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:34,788 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:34,789 - DEBUG - Finished Request
2025-07-01 17:27:35,790 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:35,798 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:35,799 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:35,799 - DEBUG - Finished Request
2025-07-01 17:27:36,799 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:36,808 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:36,808 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:36,809 - DEBUG - Finished Request
2025-07-01 17:27:37,810 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:37,819 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:37,820 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:37,820 - DEBUG - Finished Request
2025-07-01 17:27:38,821 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:38,829 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:38,829 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:38,829 - DEBUG - Finished Request
2025-07-01 17:27:39,831 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:39,839 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:39,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:39,839 - DEBUG - Finished Request
2025-07-01 17:27:40,841 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:40,850 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:40,850 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:40,850 - DEBUG - Finished Request
2025-07-01 17:27:41,851 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:41,859 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:41,859 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:41,859 - DEBUG - Finished Request
2025-07-01 17:27:42,862 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:42,871 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:42,872 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:42,872 - DEBUG - Finished Request
2025-07-01 17:27:43,873 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:43,881 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:43,882 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:43,882 - DEBUG - Finished Request
2025-07-01 17:27:44,882 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:44,889 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:44,889 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:44,890 - DEBUG - Finished Request
2025-07-01 17:27:45,891 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:45,899 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:45,899 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:45,900 - DEBUG - Finished Request
2025-07-01 17:27:46,901 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:46,911 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:46,911 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:46,912 - DEBUG - Finished Request
2025-07-01 17:27:47,912 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:47,921 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:47,921 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:47,922 - DEBUG - Finished Request
2025-07-01 17:27:48,924 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:48,933 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:48,933 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:48,933 - DEBUG - Finished Request
2025-07-01 17:27:49,934 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:49,946 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:49,946 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:49,947 - DEBUG - Finished Request
2025-07-01 17:27:50,947 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:50,955 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:50,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:50,956 - DEBUG - Finished Request
2025-07-01 17:27:51,957 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:51,964 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:51,965 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:51,965 - DEBUG - Finished Request
2025-07-01 17:27:52,966 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:52,974 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:52,974 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:52,975 - DEBUG - Finished Request
2025-07-01 17:27:53,975 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:53,982 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:53,982 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:53,983 - DEBUG - Finished Request
2025-07-01 17:27:54,984 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:54,993 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:54,993 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:54,993 - DEBUG - Finished Request
2025-07-01 17:27:55,994 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:56,003 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:56,003 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:56,004 - DEBUG - Finished Request
2025-07-01 17:27:57,005 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:57,015 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:57,015 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:57,016 - DEBUG - Finished Request
2025-07-01 17:27:58,017 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:58,026 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:58,026 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:58,027 - DEBUG - Finished Request
2025-07-01 17:27:59,028 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:27:59,036 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:27:59,036 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:27:59,036 - DEBUG - Finished Request
2025-07-01 17:28:00,037 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:00,045 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:00,045 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:00,046 - DEBUG - Finished Request
2025-07-01 17:28:01,046 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:01,056 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:01,056 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:01,056 - DEBUG - Finished Request
2025-07-01 17:28:02,058 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:02,066 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:02,066 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:02,066 - DEBUG - Finished Request
2025-07-01 17:28:03,067 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:03,076 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:03,076 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:03,076 - DEBUG - Finished Request
2025-07-01 17:28:04,077 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:04,085 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:04,086 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:04,086 - DEBUG - Finished Request
2025-07-01 17:28:05,088 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:05,096 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:05,097 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:05,097 - DEBUG - Finished Request
2025-07-01 17:28:06,098 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:06,107 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:06,107 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:06,107 - DEBUG - Finished Request
2025-07-01 17:28:07,109 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:07,119 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:07,119 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:07,119 - DEBUG - Finished Request
2025-07-01 17:28:08,121 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:08,129 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:08,129 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:08,129 - DEBUG - Finished Request
2025-07-01 17:28:09,130 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:09,141 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:09,141 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:09,142 - DEBUG - Finished Request
2025-07-01 17:28:10,143 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:10,151 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:10,152 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:10,152 - DEBUG - Finished Request
2025-07-01 17:28:11,153 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:11,160 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:11,161 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:11,161 - DEBUG - Finished Request
2025-07-01 17:28:12,162 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:12,171 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:12,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:12,172 - DEBUG - Finished Request
2025-07-01 17:28:13,173 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:13,182 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:13,183 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:13,183 - DEBUG - Finished Request
2025-07-01 17:28:14,184 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:14,193 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:14,193 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:14,193 - DEBUG - Finished Request
2025-07-01 17:28:15,195 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:15,205 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:15,206 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:15,206 - DEBUG - Finished Request
2025-07-01 17:28:16,208 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:16,215 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:16,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:16,216 - DEBUG - Finished Request
2025-07-01 17:28:17,217 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:17,225 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:17,225 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:17,225 - DEBUG - Finished Request
2025-07-01 17:28:18,226 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:18,233 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:18,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:18,235 - DEBUG - Finished Request
2025-07-01 17:28:19,236 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:19,247 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:19,247 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:19,247 - DEBUG - Finished Request
2025-07-01 17:28:20,248 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:20,256 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:20,256 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:20,256 - DEBUG - Finished Request
2025-07-01 17:28:21,257 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:21,267 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:21,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:21,268 - DEBUG - Finished Request
2025-07-01 17:28:22,269 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:22,278 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:22,278 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:22,278 - DEBUG - Finished Request
2025-07-01 17:28:23,279 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:23,287 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:23,287 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:23,287 - DEBUG - Finished Request
2025-07-01 17:28:24,289 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:24,296 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:24,297 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:24,297 - DEBUG - Finished Request
2025-07-01 17:28:25,299 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:25,308 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:25,309 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:25,309 - DEBUG - Finished Request
2025-07-01 17:28:26,310 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:26,319 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:26,320 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:26,320 - DEBUG - Finished Request
2025-07-01 17:28:27,321 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:27,331 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:27,331 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:27,332 - DEBUG - Finished Request
2025-07-01 17:28:28,332 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:28,343 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:28,343 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:28,344 - DEBUG - Finished Request
2025-07-01 17:28:29,345 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:29,353 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:29,353 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:29,354 - DEBUG - Finished Request
2025-07-01 17:28:30,355 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:30,363 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:30,363 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:30,363 - DEBUG - Finished Request
2025-07-01 17:28:31,365 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:31,374 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:31,374 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:31,374 - DEBUG - Finished Request
2025-07-01 17:28:32,375 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:32,382 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:32,383 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:32,383 - DEBUG - Finished Request
2025-07-01 17:28:33,385 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:33,394 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:33,394 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:33,394 - DEBUG - Finished Request
2025-07-01 17:28:34,395 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:34,405 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:34,406 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:34,406 - DEBUG - Finished Request
2025-07-01 17:28:35,407 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:35,415 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:35,415 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:35,416 - DEBUG - Finished Request
2025-07-01 17:28:36,417 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:36,425 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:36,425 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:36,425 - DEBUG - Finished Request
2025-07-01 17:28:37,426 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:37,437 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:37,437 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:37,437 - DEBUG - Finished Request
2025-07-01 17:28:38,439 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:38,446 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:38,446 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:38,447 - DEBUG - Finished Request
2025-07-01 17:28:39,448 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:39,455 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:39,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:39,456 - DEBUG - Finished Request
2025-07-01 17:28:40,456 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:40,464 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:40,465 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:40,465 - DEBUG - Finished Request
2025-07-01 17:28:41,466 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:41,473 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:41,473 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:41,473 - DEBUG - Finished Request
2025-07-01 17:28:42,475 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:42,483 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:42,483 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:42,483 - DEBUG - Finished Request
2025-07-01 17:28:43,484 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:43,493 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:43,494 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:43,494 - DEBUG - Finished Request
2025-07-01 17:28:44,495 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:44,503 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:44,503 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:44,504 - DEBUG - Finished Request
2025-07-01 17:28:45,505 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:45,513 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:45,514 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:45,514 - DEBUG - Finished Request
2025-07-01 17:28:46,515 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:46,522 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:46,523 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:46,523 - DEBUG - Finished Request
2025-07-01 17:28:47,524 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:47,531 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:47,533 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:47,533 - DEBUG - Finished Request
2025-07-01 17:28:48,534 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:48,542 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:48,543 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:48,543 - DEBUG - Finished Request
2025-07-01 17:28:49,544 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:49,553 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:49,553 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:49,554 - DEBUG - Finished Request
2025-07-01 17:28:50,555 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:50,563 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:50,563 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:50,563 - DEBUG - Finished Request
2025-07-01 17:28:51,565 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:51,577 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:51,578 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:51,579 - DEBUG - Finished Request
2025-07-01 17:28:52,580 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:52,588 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:52,589 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:52,589 - DEBUG - Finished Request
2025-07-01 17:28:53,590 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:53,597 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:53,598 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:53,598 - DEBUG - Finished Request
2025-07-01 17:28:54,599 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:54,606 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:54,606 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:54,606 - DEBUG - Finished Request
2025-07-01 17:28:55,607 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:55,616 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:55,616 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:55,616 - DEBUG - Finished Request
2025-07-01 17:28:56,618 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:56,625 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:56,626 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:56,626 - DEBUG - Finished Request
2025-07-01 17:28:57,627 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:57,635 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:57,635 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:57,635 - DEBUG - Finished Request
2025-07-01 17:28:58,637 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:58,647 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:58,648 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:58,648 - DEBUG - Finished Request
2025-07-01 17:28:59,649 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:28:59,657 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:28:59,657 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:28:59,658 - DEBUG - Finished Request
2025-07-01 17:29:00,659 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:29:00,665 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:29:00,665 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:29:00,665 - DEBUG - Finished Request
2025-07-01 17:29:01,667 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:29:01,675 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 200 0
2025-07-01 17:29:01,675 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:29:01,675 - DEBUG - Finished Request
2025-07-01 17:29:02,677 - DEBUG - GET http://localhost:56190/session/599986c233863706fff173d182f268fa/url {}
2025-07-01 17:29:02,679 - DEBUG - http://localhost:56190 "GET /session/599986c233863706fff173d182f268fa/url HTTP/1.1" 404 0
2025-07-01 17:29:02,680 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff6919fcda5+78885]\n\tGetHandleVerifier [0x0x7ff6919fce00+78976]\n\t(No symbol) [0x0x7ff6917b9bca]\n\t(No symbol) [0x0x7ff6917a59b5]\n\t(No symbol) [0x0x7ff6917ca9ca]\n\t(No symbol) [0x0x7ff6918405e5]\n\t(No symbol) [0x0x7ff691860b42]\n\t(No symbol) [0x0x7ff691838963]\n\t(No symbol) [0x0x7ff6918016b1]\n\t(No symbol) [0x0x7ff691802443]\n\tGetHandleVerifier [0x0x7ff691cd4eed+3061101]\n\tGetHandleVerifier [0x0x7ff691ccf33d+3037629]\n\tGetHandleVerifier [0x0x7ff691cee592+3165202]\n\tGetHandleVerifier [0x0x7ff691a1730e+186766]\n\tGetHandleVerifier [0x0x7ff691a1eb3f+217535]\n\tGetHandleVerifier [0x0x7ff691a059b4+114740]\n\tGetHandleVerifier [0x0x7ff691a05b69+115177]\n\tGetHandleVerifier [0x0x7ff6919ec368+10728]\n\tBaseThreadInitThunk [0x0x7fff7d26e8d7+23]\n\tRtlUserThreadStart [0x0x7fff7df1c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:29:02,680 - DEBUG - Finished Request
2025-07-01 17:29:02,681 - DEBUG - DELETE http://localhost:56190/session/599986c233863706fff173d182f268fa {}
2025-07-01 17:29:02,774 - DEBUG - http://localhost:56190 "DELETE /session/599986c233863706fff173d182f268fa HTTP/1.1" 200 0
2025-07-01 17:29:02,774 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 17:29:02,775 - DEBUG - Finished Request
