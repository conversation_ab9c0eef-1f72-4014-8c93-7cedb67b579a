#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AGES-KH-Bot 修復驗證測試腳本
用於驗證 v1.4 版本的修復是否正常工作
"""

import os
import sys
import json
import time
from datetime import datetime

def test_dom_inspector_import():
    """測試 DOM 檢查器導入"""
    try:
        from dom_inspector import DOMInspector
        print("✅ DOM 檢查器導入成功")
        
        # 測試創建實例
        inspector = DOMInspector()
        print("✅ DOM 檢查器實例創建成功")
        
        # 測試 find_order_elements 方法是否接受 driver 參數
        import inspect
        sig = inspect.signature(inspector.find_order_elements)
        params = list(sig.parameters.keys())
        if 'driver' in params:
            print("✅ find_order_elements 方法支援 driver 參數")
        else:
            print("❌ find_order_elements 方法不支援 driver 參數")
            
        return True
    except Exception as e:
        print(f"❌ DOM 檢查器測試失敗: {e}")
        return False

def test_submission_detector_import():
    """測試送出結果檢測器導入"""
    try:
        from submission_result_detector import SubmissionResultDetector
        print("✅ 送出結果檢測器導入成功")
        return True
    except Exception as e:
        print(f"❌ 送出結果檢測器測試失敗: {e}")
        return False

def test_rtt_integration():
    """測試 RTT 整合"""
    try:
        from rtt_predictor import get_avg_rtt
        print("✅ RTT 預測器導入成功")

        # 測試獲取 RTT - 使用正確的模型名稱
        avg_rtt, rtts, timestamps = get_avg_rtt('A')
        print(f"✅ RTT 預測測試: avg_rtt={avg_rtt:.2f}ms, samples={len(rtts)}")

        # 檢查 logs 目錄
        import os
        logs_dir = "logs"
        if os.path.exists(logs_dir):
            print(f"✅ logs 目錄存在")
            log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log') or f.endswith('.csv')]
            print(f"✅ 找到 {len(log_files)} 個日誌文件")
        else:
            print(f"❌ logs 目錄不存在")

        return True
    except Exception as e:
        print(f"❌ RTT 整合測試失敗: {e}")
        return False

def test_dom_config_file():
    """測試 DOM 配置文件"""
    config_path = "dom_elements_config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ DOM 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ DOM 配置文件讀取成功")
        
        # 檢查必要的元素
        required_elements = ['edit_button', 'captcha_input', 'submit_button']
        elements = config.get('elements', {})
        
        for element in required_elements:
            if element in elements:
                print(f"✅ 找到必要元素: {element}")
            else:
                print(f"❌ 缺少必要元素: {element}")
        
        return True
    except Exception as e:
        print(f"❌ DOM 配置文件測試失敗: {e}")
        return False

def test_mvp_grabber_functions():
    """測試 mvp_grabber.py 中的關鍵函數"""
    try:
        # 測試導入
        import mvp_grabber
        print("✅ mvp_grabber 模組導入成功")
        
        # 檢查關鍵函數是否存在
        functions_to_check = [
            'scan_current_page_dom',
            'find_and_click_edit_button', 
            'handle_captcha_input',
            'wait_for_trigger_time',
            'execute_order_grabbing'
        ]
        
        for func_name in functions_to_check:
            if hasattr(mvp_grabber, func_name):
                print(f"✅ 函數存在: {func_name}")
            else:
                print(f"❌ 函數不存在: {func_name}")
        
        return True
    except Exception as e:
        print(f"❌ mvp_grabber 函數測試失敗: {e}")
        return False

def test_gui_class():
    """測試 GUI 類"""
    try:
        from mvp_grabber import GrabberGUI
        print("✅ GrabberGUI 類導入成功")

        # 檢查關鍵方法
        methods_to_check = [
            '_start_execution',
            '_start_browser_and_wait_for_user',
            '_show_simplified_user_guide',
            '_verify_correct_page'
        ]

        for method_name in methods_to_check:
            if hasattr(GrabberGUI, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法不存在: {method_name}")

        return True
    except Exception as e:
        print(f"❌ GUI 類測試失敗: {e}")
        return False

def test_order_search_functionality():
    """測試訂單搜尋功能"""
    try:
        import mvp_grabber

        # 檢查是否有改進的編輯按鈕查找功能
        if hasattr(mvp_grabber, 'find_and_click_edit_button'):
            print("✅ find_and_click_edit_button 函數存在")

            # 檢查函數是否支援高亮元素搜尋
            import inspect
            source = inspect.getsource(mvp_grabber.find_and_click_edit_button)
            if 'highlight' in source.lower() and 'ctrl+f' in source.lower():
                print("✅ 支援 Ctrl+F 高亮搜尋功能")
            else:
                print("❌ 不支援 Ctrl+F 高亮搜尋功能")

            if 'mark[contains' in source:
                print("✅ 支援瀏覽器高亮標記檢測")
            else:
                print("❌ 不支援瀏覽器高亮標記檢測")

        else:
            print("❌ find_and_click_edit_button 函數不存在")
            return False

        return True
    except Exception as e:
        print(f"❌ 訂單搜尋功能測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("="*60)
    print("AGES-KH-Bot v1.4 修復驗證測試")
    print("="*60)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("DOM 檢查器", test_dom_inspector_import),
        ("送出結果檢測器", test_submission_detector_import),
        ("RTT 整合", test_rtt_integration),
        ("DOM 配置文件", test_dom_config_file),
        ("mvp_grabber 函數", test_mvp_grabber_functions),
        ("GUI 類", test_gui_class),
        ("訂單搜尋功能", test_order_search_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 測試: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print("\n" + "="*60)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！修復成功！")
        return True
    else:
        print("⚠️ 部分測試失敗，需要進一步檢查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
