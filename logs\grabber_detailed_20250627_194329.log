2025-06-27 19:43:29,015 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250627_194329.log
2025-06-27 19:44:13,038 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-06-27 19:44:13,038 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-06-27 19:44:13,868 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-06-27 19:44:13,868 - DEBUG - chromedriver not found in PATH
2025-06-27 19:44:13,869 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:44:13,869 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-06-27 19:44:13,869 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-06-27 19:44:13,869 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-06-27 19:44:13,869 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-06-27 19:44:13,869 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-06-27 19:44:13,869 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-06-27 19:44:13,873 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 28004 using 0 to output -3
2025-06-27 19:44:14,408 - DEBUG - POST http://localhost:56271/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-06-27 19:44:14,409 - DEBUG - Starting new HTTP connection (1): localhost:56271
2025-06-27 19:44:14,941 - DEBUG - http://localhost:56271 "POST /session HTTP/1.1" 200 0
2025-06-27 19:44:14,941 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir28004_818044886"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:56278"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"1c10e8c63cff957bc9ac906eb0e51689"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:14,941 - DEBUG - Finished Request
2025-06-27 19:44:14,942 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-06-27 19:44:16,069 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:16,070 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:16,070 - DEBUG - Finished Request
2025-06-27 19:44:16,071 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:16,156 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:16,156 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:16,157 - DEBUG - Finished Request
2025-06-27 19:44:17,158 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:17,179 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:17,179 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:17,180 - DEBUG - Finished Request
2025-06-27 19:44:18,180 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:18,187 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:18,188 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:18,188 - DEBUG - Finished Request
2025-06-27 19:44:19,189 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:19,195 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:19,195 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:19,196 - DEBUG - Finished Request
2025-06-27 19:44:20,197 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:20,203 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:20,203 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:20,203 - DEBUG - Finished Request
2025-06-27 19:44:21,204 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:21,212 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:21,212 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:21,212 - DEBUG - Finished Request
2025-06-27 19:44:22,213 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:22,219 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:22,219 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:22,220 - DEBUG - Finished Request
2025-06-27 19:44:23,220 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:23,228 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:23,228 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:23,229 - DEBUG - Finished Request
2025-06-27 19:44:24,230 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:24,237 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:24,237 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:24,237 - DEBUG - Finished Request
2025-06-27 19:44:25,238 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:25,247 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:25,248 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:25,248 - DEBUG - Finished Request
2025-06-27 19:44:26,249 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:26,257 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:26,257 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:26,257 - DEBUG - Finished Request
2025-06-27 19:44:27,258 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:27,266 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:27,267 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:27,267 - DEBUG - Finished Request
2025-06-27 19:44:28,268 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:28,275 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:28,275 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:28,275 - DEBUG - Finished Request
2025-06-27 19:44:29,276 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:29,284 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:29,285 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:29,285 - DEBUG - Finished Request
2025-06-27 19:44:30,286 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:30,295 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:30,295 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:30,295 - DEBUG - Finished Request
2025-06-27 19:44:31,297 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:31,305 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:31,305 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:31,306 - DEBUG - Finished Request
2025-06-27 19:44:32,307 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:32,314 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:32,315 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:32,315 - DEBUG - Finished Request
2025-06-27 19:44:33,316 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:33,322 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:33,322 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:33,323 - DEBUG - Finished Request
2025-06-27 19:44:34,324 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:34,330 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:34,330 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:34,330 - DEBUG - Finished Request
2025-06-27 19:44:35,331 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:35,338 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:35,338 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:35,339 - DEBUG - Finished Request
2025-06-27 19:44:36,339 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:36,348 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:36,348 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:36,349 - DEBUG - Finished Request
2025-06-27 19:44:37,350 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:37,356 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:37,357 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:37,357 - DEBUG - Finished Request
2025-06-27 19:44:38,358 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:38,366 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:38,366 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:38,366 - DEBUG - Finished Request
2025-06-27 19:44:39,367 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:39,374 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:39,374 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:39,376 - DEBUG - Finished Request
2025-06-27 19:44:40,377 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:40,384 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:40,384 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:40,384 - DEBUG - Finished Request
2025-06-27 19:44:41,385 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:42,135 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:42,136 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:42,136 - DEBUG - Finished Request
2025-06-27 19:44:43,137 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:43,143 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:43,143 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:43,144 - DEBUG - Finished Request
2025-06-27 19:44:44,145 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:44,153 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:44,153 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:44,154 - DEBUG - Finished Request
2025-06-27 19:44:45,155 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:45,162 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:45,162 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:45,162 - DEBUG - Finished Request
2025-06-27 19:44:46,163 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:46,171 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:46,171 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:46,172 - DEBUG - Finished Request
2025-06-27 19:44:47,173 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:47,181 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:47,181 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:47,181 - DEBUG - Finished Request
2025-06-27 19:44:48,182 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:48,190 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:48,190 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:48,190 - DEBUG - Finished Request
2025-06-27 19:44:49,191 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:49,199 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:49,199 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:49,199 - DEBUG - Finished Request
2025-06-27 19:44:50,200 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:50,207 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:50,207 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:50,207 - DEBUG - Finished Request
2025-06-27 19:44:51,208 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:51,215 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:51,216 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:51,216 - DEBUG - Finished Request
2025-06-27 19:44:52,217 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:52,225 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:52,227 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:52,227 - DEBUG - Finished Request
2025-06-27 19:44:53,228 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:53,234 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:53,234 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:53,235 - DEBUG - Finished Request
2025-06-27 19:44:54,236 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:54,243 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:54,243 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:54,244 - DEBUG - Finished Request
2025-06-27 19:44:55,245 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:55,250 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:55,251 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:55,251 - DEBUG - Finished Request
2025-06-27 19:44:56,252 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:56,260 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:56,260 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:56,261 - DEBUG - Finished Request
2025-06-27 19:44:57,261 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:57,268 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:57,268 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:57,268 - DEBUG - Finished Request
2025-06-27 19:44:58,269 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:58,277 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:58,277 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:58,277 - DEBUG - Finished Request
2025-06-27 19:44:59,278 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:44:59,284 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:44:59,284 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:44:59,284 - DEBUG - Finished Request
2025-06-27 19:45:00,286 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:00,293 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:00,293 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:00,293 - DEBUG - Finished Request
2025-06-27 19:45:01,294 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:01,300 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:01,300 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:01,301 - DEBUG - Finished Request
2025-06-27 19:45:02,302 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:02,311 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:02,311 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:02,311 - DEBUG - Finished Request
2025-06-27 19:45:03,312 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:03,319 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:03,319 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:03,319 - DEBUG - Finished Request
2025-06-27 19:45:04,320 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:04,327 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:04,327 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:04,327 - DEBUG - Finished Request
2025-06-27 19:45:05,329 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:05,334 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:05,334 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:05,334 - DEBUG - Finished Request
2025-06-27 19:45:06,162 - INFO - 🎯 用戶點擊準備完成按鈕，開始詳細檢測...
2025-06-27 19:45:06,163 - INFO - 🔍 [用戶點擊準備完成] 開始記錄頁面內容...
2025-06-27 19:45:06,163 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:06,187 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:06,187 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,188 - DEBUG - Finished Request
2025-06-27 19:45:06,188 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/title {}
2025-06-27 19:45:06,195 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/title HTTP/1.1" 200 0
2025-06-27 19:45:06,196 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,196 - DEBUG - Finished Request
2025-06-27 19:45:06,197 - INFO - 🔍 [用戶點擊準備完成] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:45:06,197 - INFO - 🔍 [用戶點擊準備完成] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:45:06,197 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/source {}
2025-06-27 19:45:06,202 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/source HTTP/1.1" 200 0
2025-06-27 19:45:06,203 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 17.330000000000002px;\">\u003Chead>\n    \u003Cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    \u003Cmeta charset=\"utf-8\">\n    \u003Cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \u003Ctitle>\n            高雄市廢棄物調度中心\n        \u003C/title>\n\n\n    \u003Clink href=\"/Content/customCommon?v=pEq9m2jIrcWf8XyuUkJQaoqBG2IvIvSBSHkyR7alE2I1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n    \u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n    \u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n    \u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'R_-RgV4UgYrol_cP1O7Zb2j7Omsx6fMBIShloJqvrLkvnn03ezHZdWMTQLPgdpmXRhKkIft1MkEpUaeXISZQMiNmLj-fQ5GS3YO7azkc2dg1:JnAKH4xVBGiMMB_zyEAtVhb547Wr1UAPdII37nOcmXZeQvLdbFqDdW8FkoiaS0FWpjuca15vbe48ev0P7JvAFTDF5AqAmeHphFT-0TjdOhY1' };\n\n        function openNaviToIframe(newUrl, targetIframeId) {\n            if (targetIframeId === '' || targetIframeId === undefined) {\n                targetIframeId = 'frameid';\n            }\n            $('#' + targetIframeId).attr(\"scrolling\", \"yes\");\n            $('#' + targetIframeId).attr(\"src\", newUrl);\n        }\n\n         $(document).ready(function () {\n            function ilSessionTimeout_PST() {\n               // window.location = '/Home/SessionExpired';\n            };\n            function ilSessionTimeoutWarning_PST() {\n              // MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\");\n                MessageShowDialog(\"閒置過久將自動登出!!\", \"訊息\", null, 400, 300, function () {  window.location = '/Home/SessionExpired'});\n            };\n            //Time out 時間\n            var g_pageTimer_PST = window.setTimeout(ilSessionTimeout_PST, 144000000);\n            //Time out 跳出訊息框\n            var g_pageTimerWarning_PST = window.setTimeout(ilSessionTimeoutWarning_PST, Math.round(144000000* 0.99999));\n            window.onbeforeunload = function(e) {\n\n                window.clearTimeout(g_pageTimer_PST);\n                window.clearTimeout(g_pageTimerWarning_PST);\n            };\n        });\n\u003C/script>\n    \n\n\n\n\n\u003C/head>\n\n\u003Cbody>\n\n    \u003Cheader>\n\n        \u003Cdiv class=\"mainHead container-fluid\">\n            \u003Ch1>\n                \u003Ca href=\"/Home/Index\" title=\"高雄市政府環境保護局南區資源回收廠\">\n                    \u003Cimg src=\"/img/logo.png\" alt=\"logo\">\n                \u003C/a>\n            \u003C/h1>\n\n        \u003C/div>\n\n        \u003Cnav class=\"mainNav clearfix\">\n\n            \u003Cinput type=\"checkbox\" name=\"\" id=\"hb_control\">\n\n            \u003Clabel class=\"hb\" tabindex=\"0\" for=\"hb_control\">\n                \u003Cspan class=\"bar bar1\">\u003C/span>\n                \u003Cspan class=\"bar bar2\">\u003C/span>\n                \u003Cspan class=\"bar bar3\">\u003C/span>\n            \u003C/label>\n\n            \u003Cdiv class=\"mainNavMask\">\u003C/div>\n\n            \u003Cdiv class=\"containNav container-fluid\">\n                \u003Cdiv class=\"signInWrap\">\n                    \n    \u003Cp class=\"userName\">\n\n        環碩環保工程股份有限公司|郭炯宏 \u003Cspan class=\"loginTime\">\n            2025/06/27 19:44:41\n        \u003C/span>\n        \n    \u003C/p>\n   \n\n\n                    \u003Ca href=\"/Home/LoginOut\" class=\"signInOut btn btn-warning\">登出\u003C/a>\n                \u003C/div>\n\n\n\n\u003Cul class=\"dropDownMenu_horizontal clearfix\">\n\n    \n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  公告訊息/檔案下載\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/Index','frameid'); return false;\"> 公告訊息\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Bulletin/GetDownloadInfo','frameid'); return false;\"> 檔案下載\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  基本資料\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1010R00','frameid'); return false;\"> 資料管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1021Q00','frameid'); return false;\"> 車輛管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1070Q00','frameid'); return false;\"> 帳戶管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1090Q00','frameid'); return false;\"> 設備數管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  事業單位\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1010Q00','frameid'); return false;\"> 基本資料\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/FAC/FAC1020Q00','frameid'); return false;\"> 進廠同意函管理\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  清運路線管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1030Q00','frameid'); return false;\"> 路線清單\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  進廠確認單\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1040Q00','frameid'); return false;\"> 進廠確認單清單\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/CLE/CLE1042Q00','frameid'); return false;\"> 噸數調配\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"openNaviToIframe('/Frontend/RPT/RPT1020Q00','frameid'); return false;  \">  月營運紀錄XML下載\u003C/a>\n\n                \u003Cul>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n            \u003Cli>\n                \u003Ca href=\"#\" onclick=\"; return false;  \">  系統管理\u003C/a>\n\n                \u003Cul>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 帳號管理\u003C/a>\u003C/li>\n                            \u003Cli>\u003Ca href=\"#\" onclick=\"openNaviToIframe('/Mgr/Users/<USER>','frameid'); return false;\"> 修改密碼\u003C/a>\u003C/li>\n                \u003C/ul>\n\n\n\n            \u003C/li>\n\u003C/ul>\n\n            \u003C/div>\n\n        \u003C/nav>\n\n    \u003C/header>\n\n\n\n\n    \u003Cdiv class=\"container-fluid flex-fill\">\n\n\n        \n\n\n\n\n\u003Ciframe id=\"frameid\" src=\"/Frontend/CLE/CLE1040Q00\" scrolling=\"yes\">\n\n\n\u003C/iframe>\n\n\n\n\n    \u003C/div>\n    \u003Cfooter>\n        \u003Cdiv class=\"toggleFooter text-white\">\n            \u003Cspan>聯絡我們\u003C/span>\n            \u003Ci class=\"fas fa-chevron-up\">\u003C/i>\n        \u003C/div>\n\n        \n\n        \u003Cdiv class=\"mainFooter\">\n           \u003Cp>電話：(07)735-1500 分機：2232、2234 (08：00~17：30，中午休息12：00~13：30)\u003C/p>\n            \u003Cp>Email：<EMAIL>\u003C/p>\n        \u003C/div>\n    \u003C/footer>\n\n\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;9564b1a0ef9a4a33&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '10228', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,204 - DEBUG - Finished Request
2025-06-27 19:45:06,204 - INFO - 🔍 [用戶點擊準備完成] page_source 長度: 8479
2025-06-27 19:45:06,204 - INFO - 🔍 [用戶點擊準備完成] page_source 包含 E48B: False
2025-06-27 19:45:06,204 - INFO - 🔍 [用戶點擊準備完成] page_source 包含目標訂單: False
2025-06-27 19:45:06,204 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:45:06,211 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:06,212 - DEBUG - Remote response: status=200 | data={"value":" 環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:44:41\n\n 登出\n公告訊息/檔案下載\n基本資料\n事業單位\n清運路線管理\n進廠確認單\n月營運紀錄XML下載\n系統管理\n聯絡我們 "} | headers=HTTPHeaderDict({'Content-Length': '237', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,212 - DEBUG - Finished Request
2025-06-27 19:45:06,212 - INFO - 🔍 [用戶點擊準備完成] innerText 長度: 97
2025-06-27 19:45:06,212 - INFO - 🔍 [用戶點擊準備完成] innerText 包含 E48B: False
2025-06-27 19:45:06,212 - INFO - 🔍 [用戶點擊準備完成] innerText 包含目標訂單: False
2025-06-27 19:45:06,213 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:45:06,222 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,223 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,223 - DEBUG - Finished Request
2025-06-27 19:45:06,224 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:45:06,235 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,236 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,236 - DEBUG - Finished Request
2025-06-27 19:45:06,236 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個表格，0 個表格行
2025-06-27 19:45:06,236 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:45:06,246 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,246 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,247 - DEBUG - Finished Request
2025-06-27 19:45:06,247 - INFO - 🔍 [用戶點擊準備完成] 包含 'E48B' 的元素數量: 0
2025-06-27 19:45:06,247 - INFO - 🔍 [用戶點擊準備完成] innerText 前300字符:
2025-06-27 19:45:06,248 - INFO - 🔍 [用戶點擊準備完成]  環碩環保工程股份有限公司|郭炯宏 2025/06/27 19:44:41

 登出
公告訊息/檔案下載
基本資料
事業單位
清運路線管理
進廠確認單
月營運紀錄XML下載
系統管理
聯絡我們 ...
2025-06-27 19:45:06,248 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:45:06,257 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,257 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,257 - DEBUG - Finished Request
2025-06-27 19:45:06,258 - INFO - 🔍 [用戶點擊準備完成] 檢測到 0 個編輯按鈕
2025-06-27 19:45:06,258 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:45:06,268 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,269 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.787F044CFDED3BB7D01222511160B8C2.d.E4ADE1ED3B4AEE7494C40EF591A5E2C6.e.38"}]} | headers=HTTPHeaderDict({'Content-Length': '128', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,269 - DEBUG - Finished Request
2025-06-27 19:45:06,269 - INFO - 🔍 [用戶點擊準備完成] 檢測到 1 個 iframe
2025-06-27 19:45:06,270 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.787F044CFDED3BB7D01222511160B8C2.d.E4ADE1ED3B4AEE7494C40EF591A5E2C6.e.38'}, 'id']}
2025-06-27 19:45:06,279 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:06,279 - DEBUG - Remote response: status=200 | data={"value":"frameid"} | headers=HTTPHeaderDict({'Content-Length': '19', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,279 - DEBUG - Finished Request
2025-06-27 19:45:06,280 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.787F044CFDED3BB7D01222511160B8C2.d.E4ADE1ED3B4AEE7494C40EF591A5E2C6.e.38'}, 'src']}
2025-06-27 19:45:06,286 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:06,286 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00"} | headers=HTTPHeaderDict({'Content-Length': '58', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,287 - DEBUG - Finished Request
2025-06-27 19:45:06,287 - INFO - 🔍 [用戶點擊準備完成] iframe 1: id='frameid', src='https://wmc.kcg.gov.tw/Frontend/CLE/CLE1040Q00'
2025-06-27 19:45:06,287 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/frame {'id': {'element-6066-11e4-a52e-4f735466cecf': 'f.787F044CFDED3BB7D01222511160B8C2.d.E4ADE1ED3B4AEE7494C40EF591A5E2C6.e.38'}}
2025-06-27 19:45:06,308 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/frame HTTP/1.1" 200 0
2025-06-27 19:45:06,308 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,308 - DEBUG - Finished Request
2025-06-27 19:45:06,308 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:45:06,314 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:06,314 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t815.691\t76.89\t0\t453.919\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406230886\t(NEW)H4 2808 星期五(30%)\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406200782\tH4 2808義大遊樂\tKEP-2808\t專車清運\t1.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181141\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181137\t仁\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181136\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181135\t岡\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406181133\t(NEW)H9 5580星期五 只有一車(25%)\tKEJ-5580\t一般清運\t6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110721\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110719\t南\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110718\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110717\t南\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110715\t岡\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110714\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261022\t岡\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261021\t南\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210775\t岡\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210774\t仁\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210773\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190957\t(NEW)119星期五(50%)\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190956\t南\tKED-9671\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190955\tH2 119 義大\t119-BR\t專車清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190954\t南\t117-BR\t一般清運\t3.5\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190953\t仁\t119-BR\t一般清運\t3.6\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190949\t南\t119-BR\t一般清運\t3.6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190948\t仁\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190947\t岡\t120-BR\t一般清運\t3.2\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190945\t仁\t121-BR\t一般清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190944\t岡\t121-BR\t一般清運\t3.5\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190943\t仁\t129-BR\t一般清運\t4.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190940\t岡\t129-BR\t一般清運\t4.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190938\t(NEW)H72560星期五(有美生) (20%)\tKEP-2560\t一般清運\t4.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190936\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190934\t岡\t937-N6\t一般清運\t4.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190932\t南\t937-N6\t一般清運\t4.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190931\t南\tKEH-9278\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190929\t仁\tKEH-9278\t一般清運\t8.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190928\t岡\tKER-2807\t一般清運\t7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190698\t仁\tKER-2807\t一般清運\t7\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n顯示第 1 至 38 項結果，共 38 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '7514', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,316 - DEBUG - Finished Request
2025-06-27 19:45:06,317 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:45:06,323 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,324 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.131"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,324 - DEBUG - Finished Request
2025-06-27 19:45:06,325 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:45:06,332 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,333 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.170"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.172"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,333 - DEBUG - Finished Request
2025-06-27 19:45:06,334 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:45:06,336 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:06,336 - DEBUG - Starting new HTTP connection (2): localhost:56271
2025-06-27 19:45:06,344 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,344 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.79"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.239"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.241"}]} | headers=HTTPHeaderDict({'Content-Length': '8270', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,346 - DEBUG - Finished Request
2025-06-27 19:45:06,346 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:45:06,354 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:06,354 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.242"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.243"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.277"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.278"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.279"}]} | headers=HTTPHeaderDict({'Content-Length': '4495', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,355 - DEBUG - Finished Request
2025-06-27 19:45:06,355 - INFO - 🔍 [用戶點擊準備完成] iframe 1 內容:
2025-06-27 19:45:06,356 - INFO - 🔍 [用戶點擊準備完成]   - 文字長度: 3955
2025-06-27 19:45:06,356 - INFO - 🔍 [用戶點擊準備完成]   - 包含目標訂單: True
2025-06-27 19:45:06,356 - INFO - 🔍 [用戶點擊準備完成]   - 包含 E48B: True
2025-06-27 19:45:06,356 - INFO - 🔍 [用戶點擊準備完成]   - 表格數量: 2
2025-06-27 19:45:06,357 - INFO - 🔍 [用戶點擊準備完成]   - 表格行數: 41
2025-06-27 19:45:06,357 - INFO - 🔍 [用戶點擊準備完成]   - 編輯按鈕數量: 70
2025-06-27 19:45:06,357 - INFO - 🔍 [用戶點擊準備完成]   - E48B 元素數量: 38
2025-06-27 19:45:06,358 - INFO - 🔍 [用戶點擊準備完成]   - 內容前300字符: 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

6月
7月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	815.691	76.89	0	453.919
每日開放查詢時日1...
2025-06-27 19:45:06,359 - INFO - 🔍 [用戶點擊準備完成]   - 內容後300字符: ...		高南廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190929	仁	KEH-9278	一般清運	8.5	2025-07-04		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190928	岡	KER-2807	一般清運	7	2025-07-04		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190698	仁	KER-2807	一般清運	7	2025-07-04		仁武廠	0	一般	取消(刪除)
顯示第 1 至 38 項結果，共 38 項
上一頁
1
下一頁
2025-06-27 19:45:06,360 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:06,361 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:45:06,361 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:06,361 - DEBUG - Finished Request
2025-06-27 19:45:07,362 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:07,369 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:07,370 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:07,370 - DEBUG - Finished Request
2025-06-27 19:45:08,361 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,369 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,369 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,369 - DEBUG - Finished Request
2025-06-27 19:45:08,369 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/title {}
2025-06-27 19:45:08,371 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,371 - DEBUG - Starting new HTTP connection (3): localhost:56271
2025-06-27 19:45:08,376 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/title HTTP/1.1" 200 0
2025-06-27 19:45:08,376 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,377 - DEBUG - Finished Request
2025-06-27 19:45:08,377 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': '//table'}
2025-06-27 19:45:08,386 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,387 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.131"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,387 - DEBUG - Finished Request
2025-06-27 19:45:08,387 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': '//tr'}
2025-06-27 19:45:08,398 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,398 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.170"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.172"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,399 - DEBUG - Finished Request
2025-06-27 19:45:08,400 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/source {}
2025-06-27 19:45:08,401 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,402 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,402 - DEBUG - Finished Request
2025-06-27 19:45:08,407 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/source HTTP/1.1" 200 0
2025-06-27 19:45:08,407 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-06-27 19:45:08,407 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 16.14px;\">\u003Chead>\u003Clink href=\"/Content/MainCss?v=zPHWubngoBLlh81X9bw_8aWFtUJfWgM1VR7izfwxpoQ1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DataTableCss?v=Z2hod0l-s4nUkCqEwOiqsErJ5wq6PmToPF0R4Rizr9g1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n\u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DataTableJs?v=PrL5mJgVEL8_qJD7wVZM5xxp6rEVZ9g1TBfDlvdUlWg1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'dwQmRPQpfyn8F0oM5b2Y34Jj8Kqg2ixTpEqE3zoxDhEmpEg6SriMRGFtK1CMRKuNyrHVrxHL_ZYEBDymqi4H3PouxCLFES4gSAVlpueyt-I1:uEIlvK6Wj2ktDB7sC5IDt-rJCQPqB59nGLSUS4iqPVib-MzSNOM3xGJYzZzepvy7O2rMBgZ8jV2Yj-FKPax7oGi--So91r-aXrzX588n95w1' };\n    \n    let lengthMenuList = [[10, 50, 100, 300], [10, 50, 100, 300]];\n\u003C/script>\n\n\n    \u003Cscript>\n        var UrlDeleteCleEntryForm = '/Frontend/CLE/DeleteCleEntryForm';\n        var Token = '92JVXw5oPZ9VRwGSSOL5a1zLTVCwaF8_lfPSh2JBblSY9W3lyMR6-N6qE8pNzEHNswVaH1OQw377mgSklPO_o3KmIL133l7Yg0MoFqntP0I1:x9YT6T5xjXpXdVvX-UKYc6tNRPxTi7BMQsuO3SvxnSgt6LlN7M-GbMyqb59ApIulKABDENwIcn56w0YCpkZmwIU6S-w7DJTXpJcMlM0LoEY1';\n        var UrlExport1 = '/Frontend/CLE/DownloadCLE104Data';\n        var UrlExport = '/Frontend/CLE/PrintEntryForm';\n        var UrlEFDetailExport = '/Frontend/CLE/DownloadCLEDetailData';\n        var UrlLog = '/Frontend/CLE/LogFP';\n   \n        let actualWeightDays = 10;\n        console.log(UrlLog);\n        let addRouteOption = {\n            Title: '新增路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030A01',\n            BeforeClose: function () {\n\n            }\n        };\n\n        let editRouteOption = {\n            Title: '編輯路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030E00',\n            BeforeClose: function () {\n\n            }\n        };\n\n      $(function () {\n\n        //  var FPoptions = {\n        //      fonts: { extendedJsFonts: true },\n        //      excludes: {\n        //          cpuClass: true,\n        //          fonts: true,\n        //          fontsFlash: true,\n        //          doNotTrack: true,\n        //          webgl: true,\n        //          webglVendorAndRenderer: true,\n              \n                \n        //      }\n        //  }\n        //    var FP_token;\n\n        //    setTimeout(function () {\n        //        Fingerprint2.get(FPoptions, function (components) {\n        //            var values = components.map(function (component) { return component.value })\n        //            var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //            FP_token = murmur\n        //            LogData2(FP_token);\n        //        })\n        //    }, 500)\n\n\n        //    Fingerprint2.get(FPoptions, function (components) {\n        //        var values = components.map(function (component) { return component.value })\n        //        var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //        FP_token = murmur\n        //        LogData(FP_token);\n        //    });\n\n\n        });\n          var FP_token;\n\n          function getOrCreateLocalID() {\n              const key = 'local_fingerprint_id';\n              let id = localStorage.getItem(key);\n              if (!id) {\n                  id = 'id-' + Math.random().toString(36).substr(2, 9);\n                  localStorage.setItem(key, id);\n              }\n              return id;\n          }\n\n          window.addEventListener('load', function () {\n              setTimeout(function () {\n                  Fingerprint2.get(function (components) {\n                      // 過濾與排序\n                      const baseComponents = components\n                          .filter(c => !['userAgent', 'language', 'webdriver'].includes(c.key))\n                          .sort((a, b) => a.key.localeCompare(b.key));\n\n                      // 額外客製化參數\n                      const customComponents = [\n                          { key: 'timezoneOffset', value: new Date().getTimezoneOffset() },\n                          { key: 'localID', value: getOrCreateLocalID() },\n                          { key: 'cpuCores', value: navigator.hardwareConcurrency || 'unknown' },\n                          { key: 'pixelRatio', value: window.devicePixelRatio },\n                          { key: 'connectionType', value: (navigator.connection && navigator.connection.effectiveType) || 'unknown' }\n                      ];\n\n                      const allComponents = [...baseComponents, ...customComponents];\n\n\n                      const values = allComponents\n                          .sort((a, b) => a.key.localeCompare(b.key))\n                          .map(component => component.value);\n\n                      const rawString = values.join('###');\n\n\n                      const hash = Fingerprint2.x64hash128(rawString, 31);\n\n\n                      LogData(hash);\n\n                      console.log('Extended fingerprint hash:', hash);\n                      console.log('All components:', allComponents);\n                  });\n              }, 500);\n\n          });\n\n\n\n        function LogData(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n        function LogData2(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog2,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n\n\n    \u003C/script>\n    \u003Cscript src=\"/Scripts/fingerprint2.js\" type=\"text/javascript\">\u003C/script>\n    \u003Cscript src=\"/Scripts/View/Frontend/CLE/CLE1040Q00.js?2025062719444338\">\u003C/script>\n\n\n\n\n\n\n\u003C/head>\u003Cbody>\u003Cdiv class=\"container-fluid\">\n    \n\n\n\n\n\u003Cdiv class=\"mainTitle\">\n    \u003Ch2>進廠確認單管理\u003C/h2>\n\u003C/div>\n\u003Cdiv class=\"row\">\n    \u003Cdiv class=\"col-md-7 text-left\">\n        \n\n\n\u003Cform action=\"/Frontend/CLE/QueryEntryForm\" autocomplete=\"off\" id=\"QueryForm\" method=\"post\" novalidate=\"\">\u003Cinput name=\"__RequestVerificationToken\" type=\"hidden\" value=\"HC_EaCsCGu7yuPT3WqL8unFHVV2umqSPyj5JPbJPD7NkX7e56QNca4qWwBueT37zSsS2DdBrS4JPQZtXMeofuJFdcWVgXOSAIivTaDUesFU1\">    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryIncineratorId\">進廠別\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control\" id=\"EntryIncineratorId\" name=\"EntryIncineratorId\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"0\">調度中心\u003C/option>\n\u003Coption value=\"1\">高南廠\u003C/option>\n\u003Coption value=\"2\">岡山廠\u003C/option>\n\u003Coption value=\"3\">仁武廠\u003C/option>\n\u003Coption value=\"5\">路竹掩埋場\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"EntryNo\">進廠確認單號\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cinput class=\"form-control\" id=\"EntryNo\" name=\"EntryNo\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"FormStatus\">狀態\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control p-2\" id=\"FormStatus\" name=\"FormStatus\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"00\">暫存\u003C/option>\n\u003Coption value=\"01\">待審查\u003C/option>\n\u003Coption value=\"02\">未載運\u003C/option>\n\u003Coption value=\"03\">已載運-待清除確認\u003C/option>\n\u003Coption value=\"41\">已載運-檢核未通過\u003C/option>\n\u003Coption value=\"91\">取消\u003C/option>\n\u003Coption value=\"97\">審查退回\u003C/option>\n\u003Coption value=\"98\">退運\u003C/option>\n\u003Coption value=\"99\">已完成\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"CheckWeighFail\">檢核結果\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cselect class=\"form-control\" id=\"CheckWeighFail\" name=\"CheckWeighFail\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"1\">通過\u003C/option>\n\u003Coption value=\"2\">未通過\u003C/option>\n\u003Coption value=\"3\">未檢核\u003C/option>\n\u003C/select>\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"ReserveEntryDate_Start\">預計進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_Start\" name=\"ReserveEntryDate_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_End\" name=\"ReserveEntryDate_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryDateTime_Start\">實際進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_Start\" name=\"EntryDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_End\" name=\"EntryDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"\">報表日期起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_Start\" name=\"RptDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_End\" name=\"RptDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"text-center\">\n        \n\n        \u003Cbutton type=\"button\" id=\"btnQuery\" onclick=\"tableDraw(queryTable, 'QueryForm');tableDraw(queryQueryCleEntryWeight, '',_queryQueryCleEntryWeightConfig );\" class=\"btn btn-primary\">查詢\u003C/button>\n        \u003Cspan style=\"color:red\">請按查詢以顯示清單\u003C/span>\n        \n\n        \u003Cbr>\n                                \u003Cbutton type=\"button\" class=\"btn btn-warning\" onclick=\"addA1Option.Data = {entryFormType:'A1'};EditShowDialog(addA1Option)\">新增A1本市事廢\u003C/button>\n                        \u003Cbr>\n\n        \n                        \u003Cbutton type=\"button\" class=\"btn btn-dark\" onclick=\"addA3bOption.Data = {entryFormType:'A3b'};EditShowDialog(addA3bOption)\">新增A3b2050專案\u003C/button>\n                \n                    \u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"DownloadCLEDetailData();\">下載明細報表\u003C/button>\n            \u003Cbr>\n\n    \u003C/div>\n\u003C/form>\n    \u003C/div>\n    \u003Cdiv class=\"col-md-5 text-left\">\n\u003Cform action=\"/Frontend/CLE/QueryCleEntryWeight\" autocomplete=\"off\" id=\"QueryDocForm\" method=\"post\" novalidate=\"\" onsubmit=\"return false;\">            \u003Cdiv>\n                \u003Cselect id=\"QueryDate\" name=\"QueryDate\" onchange=\"getCleEntryWeight()\">\u003Coption value=\"2025/06/27\">6月\u003C/option>\n\u003Coption value=\"2025/07/27\">7月\u003C/option>\n\u003C/select>\u003Cspan>進廠量統計\u003C/span>\n                \n            \u003C/div>\n\u003C/form>        \u003Cdiv id=\"queryQueryCleEntryWeight_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryQueryCleEntryWeight\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">月核定量(A)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">日控量\u003Cbr>(七天後)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠量(B)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量(C)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">上月超量(D)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">剩餘進廠量\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>A1\u003C/td>\u003Ctd>1346.5\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>815.691\u003C/td>\u003Ctd>76.89\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>453.919\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryQueryCleEntryWeight_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryQueryCleEntryWeightConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": false,\n  \"lengthChange\": false,\n  \"ordering\": false,\n  \"paging\": false,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"get\",\n    \"url\": \"/Frontend/CLE/QueryCleEntryWeight\",\n    \"data\": ajaxParam\n  },\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 0,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"ConsentType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"月核定量(A)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ControlOpenWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"日控量\u003Cbr/>(七天後)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthEntryWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"實際進廠量(B)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量(C)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthOverWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"上月超量(D)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthRemainingWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"剩餘進廠量\",\n      \"visible\": true\n    }\n  ]\n};\nlet queryQueryCleEntryWeight = $('#queryQueryCleEntryWeight').DataTable(_queryQueryCleEntryWeightConfig);\n\u003C/script>\n        \u003Cdiv>\n            \u003Cul>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">每日開放查詢時日10:30~次日09:00\u003C/span>\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">欄位說明：\u003C/span>(單位：噸)\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">月核定量(A)：\u003C/span>因進廠管控措施機制，故月核可量為浮動數值\u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月實際進廠量(B)：\u003C/span>進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月預計進廠量(C)：\u003C/span>進廠確認單狀態為「未載運」、「已逾期」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">上月超量(D)：\u003C/span>上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月剩餘進廠量：\u003C/span>A-B-C-D\n                \u003C/li>\n                \n            \u003C/ul>\n        \u003C/div>\n    \u003C/div>\n\n\u003C/div>\n\n\n\n\n\u003Cscript>\n\n    function operateFormatter(data, type, row, meta) {\n        let result = \"\";\n        let todayDate = new Date(); //Today Date\n\n        if (row.IsLocked === true) {\n\n        }\n\n\n\n        //預計進廠日前一天可修改、刪除\n        //if (moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString())) {\n        if (\n            (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1\n            && row.FormStatus != '03'\n            && (row.FormStatus === '00' || row.FormStatus === '02' || row.FormStatus == '97')\n        ) {\n            //console.log((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")));\n            if (row.EntryFormType == \"A4a\") {\n                //02=審查完成\n                if (row.FormStatus !== '02') {\n                    result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"Edit41EOption.Data = {entryId:${data}};Edit41EOption.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(Edit41EOption)\">編輯\u003C/button>`;\n                }\n            }\n            else {\n                if (row.FormStatus === '00') {\n                    if (row.ReserveType != \"特殊\") {\n                        if (row.ReserveType != \"A3a\") {\n                            result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"EditE20Option.Data = {entryId:${data}};EditE20Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE20Option)\">編輯\u003C/button>`;\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n\n                    }\n                }\n                else {\n                    if (row.ReserveType != \"特殊\" && row.IsLocked === false) {\n                        if (row.ReserveType == \"優先\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE30Option.Data = {entryId:${data}};EditE30Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE30Option)\">編輯\u003C/button>`;\n                        }\n                        else if (row.EntryFormType == \"A3a\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                    }\n\n                }\n\n            }\n\n\n        }\n        else {\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02')) {\n\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('確定取消單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormCancel', { entryId: ${data}}, 'post', _Headers);}\">未進廠取消\u003C/button>`;\n            }\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\") \u003C= actualWeightDays)) {\n                if ((row.FormStatus == '03' || row.FormStatus == '41')) { ////20250220 關閉隔日可填寫實際重量\n                    // if ((row.FormStatus == '03' || row.FormStatus == '41') || (row.FormStatus == '02' && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02'))) {\n                    if (row.EntryFormType == \"A4a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"Edit41E10Option.Data = {entryId:${data}};Edit41E10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(Edit41E10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else if (row.EntryFormType == \"A3a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                }\n            }\n            \n            if (row.EntryFormType == \"A4a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1041R00')\">查看\u003C/button>`;\n            }\n            else if (row.EntryFormType == \"A3a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R10')\">查看\u003C/button>`;\n            }\n            else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R00')\">查看\u003C/button>`;\n            }\n\n\n\n        }\n\n        //if (row.CargoRoute) {\n        //    result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"editRouteOption.Data = {RouteId:${row.RouteId}};window.parent.EditShowDialog(editRouteOption)\">編輯路線\u003C/button>`;\n        //}\n        //else {\n        result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"addRouteOption.Data = {entryId:${row.EntryId}, ClearType:'${row.ClearType}',EntryFormType:'${row.EntryFormType}'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>`;\n        //}\n\n        if ((moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString()) || moment(row.ReserveEntryDate).isSame(todayDate.toLocaleDateString()))\n            && row.FormStatus === '02') {\n            //  && row.IsLocked === false) {\n            result += `\u003Cbutton type=\"button\" class='btn btn-secondary' onclick=\"ShowEntryForm(${data})\">列印\u003C/button>`;\n\n        }\n        if (row.EntryFormType == \"A1\")\n        {\n         }\n\n             return result;\n         }\n\n    function cancelFormatter(data, type, row, meta) {\n        let todayDate = new Date(); //Today Date\n        let result = \"\";\n        //刪單須在預約進廠日前一天中午前刪除\n        let hours = moment(row.ReserveEntryDate).diff(todayDate, 'hours');\n\n        if ((hours >= 12 && data === false) || row.FormStatus === '00') {\n            if (row.ReserveType == \"一般\" && row.IsLocked === true) {\n                result = \"\";\n            } else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n            }\n         }\n        else if (row.ReserveType == \"特殊\" && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1 && row.FormStatus === '02') {\n\n            result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n\n        }\n\n        return result;\n    }\n\n    function entryFormTypeRender(data, type, row, meta) {\n        let result = \"\";\n        result = `\u003Cspan>${data}${row.EntryFormTypeText}\u003C/span>`\n        return result;\n    }\n\n    function CarNoReserveType(data, type, row, meta) {\n        let result = \"\";\n        if (row.ReserveType == '特殊') {\n            result = `\u003Cspan>${data}(${row.ReserveType})\u003C/span>`\n        }\n        else {\n            result = `\u003Cspan>${data}\u003C/span>`\n        }\n        return result;\n    }\n\n\n    function checkStatus(row, data, dataIndex) {\n        if (data[\"IsLocked\"] == true) {\n          /*  $(row).css(\"background-color\", \"red\");*/\n        }\n    }\n\n\u003C/script>\n\n\u003Cdiv class=\"pt-2\">\n\n    \u003Cdiv id=\"queryTable_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003Cdiv class=\"dataTables_length\" id=\"queryTable_length\">\u003Clabel>顯示 \u003Cselect name=\"queryTable_length\" aria-controls=\"queryTable\" class=\"custom-select custom-select-sm form-control form-control-sm\">\u003Coption value=\"10\">10\u003C/option>\u003Coption value=\"50\">50\u003C/option>\u003Coption value=\"100\">100\u003C/option>\u003Coption value=\"300\">300\u003C/option>\u003C/select> 項結果\u003C/label>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryTable\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\" aria-describedby=\"queryTable_info\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\" style=\"width: 15%;\">操作\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">狀態\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠單號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運路線\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">車號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運種類\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠日\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠時間\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">業者過磅淨重(噸)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預約類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">取消\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3146082\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3146082};EditE00Option.Title='編輯進廠確認單(E48B201611406230886)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3146082, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3146082)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406230886\u003C/td>\u003Ctd>(NEW)H4 2808 星期五(30%)\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406230886？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3146082}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3144221\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3144221};EditE00Option.Title='編輯進廠確認單(E48B201611406200782)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3144221, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3144221)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406200782\u003C/td>\u003Ctd>H4 2808義大遊樂\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>1.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406200782？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3144221}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142204};EditE20Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142204\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142204};EditE00Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142204, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181141\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181141？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142204}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142201};EditE20Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142201\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142201};EditE00Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142201, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181137\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181137？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142201}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142200};EditE20Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142200\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142200};EditE00Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142200, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181136\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181136？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142200}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142199};EditE20Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142199\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142199};EditE00Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142199, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181135\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181135？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142199}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3142197\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3142197};EditE00Option.Title='編輯進廠確認單(E48B201611406181133)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142197, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3142197)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406181133\u003C/td>\u003Ctd>(NEW)H9 5580星期五 只有一車(25%)\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181133？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142197}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134987};EditE20Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134987\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134987};EditE00Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134987, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110721\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110721？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134987}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134985};EditE20Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134985\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134985};EditE00Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134985, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110719\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110719？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134985}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134984};EditE20Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134984\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134984};EditE00Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134984, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110718\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110718？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134984}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134983};EditE20Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134983\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134983};EditE00Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134983, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110717\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110717？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134983}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134981};EditE20Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134981\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134981};EditE00Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134981, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110715\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110715？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134981}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134980};EditE20Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134980\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134980};EditE00Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134980, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110714\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110714？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134980}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121411};EditE20Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121411\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121411};EditE00Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121411, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261022\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261022？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121411}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121410};EditE20Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121410\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121410};EditE00Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121410, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261021\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261021？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121410}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117615};EditE20Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117615};EditE00Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210775\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210775？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117614};EditE20Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117614\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117614};EditE00Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117614, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210774\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210774？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117614}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117613};EditE20Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117613\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117613};EditE00Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117613, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210773\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210773？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117613}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115644\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115644};EditE00Option.Title='編輯進廠確認單(E48B201611405190957)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115644, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115644)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190957\u003C/td>\u003Ctd>(NEW)119星期五(50%)\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190957？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115644}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115643};EditE20Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115643\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115643};EditE00Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115643, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190956\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9671\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190956？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115643}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115642\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115642};EditE00Option.Title='編輯進廠確認單(E48B201611405190955)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115642, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115642)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190955\u003C/td>\u003Ctd>H2 119 義大\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190955？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115642}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115641};EditE20Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115641\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115641};EditE00Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115641, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190954\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>117-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190954？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115641}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115640};EditE20Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115640\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115640};EditE00Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115640, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190953\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190953？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115640}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115636};EditE20Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115636\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115636};EditE00Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115636, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190949\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190949？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115636}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115635};EditE20Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115635\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115635};EditE00Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115635, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190948\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190948？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115635}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115634};EditE20Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115634\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115634};EditE00Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115634, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190947\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190947？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115634}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115632};EditE20Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115632\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115632};EditE00Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115632, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190945\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190945？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115632}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115631};EditE20Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115631\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115631};EditE00Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115631, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190944\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190944？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115631}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115630};EditE20Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115630\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115630};EditE00Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115630, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190943\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190943？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115630}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115627};EditE20Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115627\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115627};EditE00Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115627, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190940\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190940？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115627}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115625\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115625};EditE00Option.Title='編輯進廠確認單(E48B201611405190938)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115625, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115625)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190938\u003C/td>\u003Ctd>(NEW)H72560星期五(有美生) (20%)\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190938？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115625}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115623};EditE20Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115623\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115623};EditE00Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115623, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190936\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190936？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115623}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115621};EditE20Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115621\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115621};EditE00Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115621, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190934\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190934？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115621}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115619};EditE20Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115619\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115619};EditE00Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115619, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190932\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190932？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115619}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115618};EditE20Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115618\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115618};EditE00Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115618, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190931\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190931？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115618}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115616};EditE20Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115616\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115616};EditE00Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115616, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190929\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190929？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115616}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115615};EditE20Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115615};EditE00Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190928\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190928？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115399};EditE20Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115399\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115399};EditE00Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115399, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190698\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190698？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115399}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryTable_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003Cdiv class=\"dataTables_info\" id=\"queryTable_info\" role=\"status\" aria-live=\"polite\">\u003Cbig>顯示第 1 至 38 項結果，共\u003Cfont color=\"red\"> 38 \u003C/font>項\u003C/big>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003Cdiv class=\"dataTables_paginate paging_simple_numbers\" id=\"queryTable_paginate\">\u003Cul class=\"pagination\">\u003Cli class=\"paginate_button page-item previous disabled\" id=\"queryTable_previous\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"0\" tabindex=\"0\" class=\"page-link\">上一頁\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item active\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"1\" tabindex=\"0\" class=\"page-link\">1\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item next disabled\" id=\"queryTable_next\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"2\" tabindex=\"0\" class=\"page-link\">下一頁\u003C/a>\u003C/li>\u003C/ul>\u003C/div>\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryTableConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": true,\n  \"lengthChange\": true,\n  \"ordering\": false,\n  \"paging\": true,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"post\",\n    \"url\": \"/Frontend/CLE/QueryEntryForm\",\n    \"data\": ajaxParam\n  },\n  \"createdRow\": checkStatus,\n  \"deferLoading\": \"0\",\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"lengthMenu\": lengthMenuList,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 50,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"EntryId\",\n      \"orderable\": true,\n      \"render\": operateFormatter,\n      \"searchable\": true,\n      \"title\": \"操作\",\n      \"visible\": true,\n      \"width\": \"15%\"\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"render\": entryFormTypeRender,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatusText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠單號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CargoRoute\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CarNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"車號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ClearTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveEntryDate\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"預計進廠日\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryDateTime\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"實際進廠時間\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"Incinerator_ShortName\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"NetWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"業者過磅淨重(噸)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預約類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"IsDelete\",\n      \"orderable\": true,\n      \"render\": cancelFormatter,\n      \"searchable\": true,\n      \"title\": \"取消\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatus\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"IsLocked\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"鎖定\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"RouteId\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"ClearType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    }\n  ]\n};\nlet queryTable = $('#queryTable').DataTable(_queryTableConfig);\n\u003C/script>\n\u003C/div>\n\n\n\n\n\n\n\u003C/div>\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;9564b1af0fe54a33&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '92489', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,418 - DEBUG - Finished Request
2025-06-27 19:45:08,419 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,425 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,425 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,425 - DEBUG - Finished Request
2025-06-27 19:45:08,425 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,431 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,431 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,431 - DEBUG - Finished Request
2025-06-27 19:45:08,432 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,435 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,436 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,436 - DEBUG - Finished Request
2025-06-27 19:45:08,436 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), '編輯')]"}
2025-06-27 19:45:08,447 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,447 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.281"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.79"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.239"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.241"}]} | headers=HTTPHeaderDict({'Content-Length': '8506', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,449 - DEBUG - Finished Request
2025-06-27 19:45:08,449 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280/name {}
2025-06-27 19:45:08,456 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280/name HTTP/1.1" 200 0
2025-06-27 19:45:08,456 - DEBUG - Remote response: status=200 | data={"value":"script"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,456 - DEBUG - Finished Request
2025-06-27 19:45:08,456 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280/text {}
2025-06-27 19:45:08,467 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280/text HTTP/1.1" 200 0
2025-06-27 19:45:08,467 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,467 - DEBUG - Finished Request
2025-06-27 19:45:08,468 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280'}, 'id']}
2025-06-27 19:45:08,473 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,473 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,473 - DEBUG - Finished Request
2025-06-27 19:45:08,473 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280'}, 'class']}
2025-06-27 19:45:08,480 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,480 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,480 - DEBUG - Finished Request
2025-06-27 19:45:08,480 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.280'}, 'name']}
2025-06-27 19:45:08,486 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,486 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,487 - DEBUG - Finished Request
2025-06-27 19:45:08,487 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,493 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,493 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,493 - DEBUG - Finished Request
2025-06-27 19:45:08,493 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[name*="captcha"]'}
2025-06-27 19:45:08,501 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,501 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,502 - DEBUG - Finished Request
2025-06-27 19:45:08,502 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[id*="captcha"]'}
2025-06-27 19:45:08,510 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,510 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,510 - DEBUG - Finished Request
2025-06-27 19:45:08,511 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證"]'}
2025-06-27 19:45:08,520 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,520 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,521 - DEBUG - Finished Request
2025-06-27 19:45:08,521 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[placeholder*="驗證碼"]'}
2025-06-27 19:45:08,529 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,529 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,530 - DEBUG - Finished Request
2025-06-27 19:45:08,530 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[name*="code"]'}
2025-06-27 19:45:08,538 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,538 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,538 - DEBUG - Finished Request
2025-06-27 19:45:08,538 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[name*="verify"]'}
2025-06-27 19:45:08,546 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,546 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,546 - DEBUG - Finished Request
2025-06-27 19:45:08,547 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="4"]'}
2025-06-27 19:45:08,555 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,555 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,555 - DEBUG - Finished Request
2025-06-27 19:45:08,555 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[type="text"][maxlength="5"]'}
2025-06-27 19:45:08,563 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,563 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,564 - DEBUG - Finished Request
2025-06-27 19:45:08,564 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,568 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,568 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,569 - DEBUG - Finished Request
2025-06-27 19:45:08,569 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'img[src*="captcha"]'}
2025-06-27 19:45:08,578 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,578 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,578 - DEBUG - Finished Request
2025-06-27 19:45:08,578 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'img[src*="verify"]'}
2025-06-27 19:45:08,586 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,586 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,586 - DEBUG - Finished Request
2025-06-27 19:45:08,587 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'img[alt*="驗證"]'}
2025-06-27 19:45:08,594 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,594 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,595 - DEBUG - Finished Request
2025-06-27 19:45:08,595 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'img[alt*="驗證碼"]'}
2025-06-27 19:45:08,603 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,603 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,604 - DEBUG - Finished Request
2025-06-27 19:45:08,604 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,610 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,610 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,610 - DEBUG - Finished Request
2025-06-27 19:45:08,610 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), '確認取得驗證碼')]"}
2025-06-27 19:45:08,621 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,621 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,622 - DEBUG - Finished Request
2025-06-27 19:45:08,622 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:45:08,631 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,632 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,632 - DEBUG - Finished Request
2025-06-27 19:45:08,632 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), '重新產生')]"}
2025-06-27 19:45:08,642 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,642 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,643 - DEBUG - Finished Request
2025-06-27 19:45:08,643 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': '.captcha-refresh'}
2025-06-27 19:45:08,652 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,653 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,653 - DEBUG - Finished Request
2025-06-27 19:45:08,653 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,658 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,659 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,659 - DEBUG - Finished Request
2025-06-27 19:45:08,659 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), '送出')]"}
2025-06-27 19:45:08,669 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,669 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,669 - DEBUG - Finished Request
2025-06-27 19:45:08,669 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[value="送出"]'}
2025-06-27 19:45:08,678 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,678 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,679 - DEBUG - Finished Request
2025-06-27 19:45:08,679 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'input[type="submit"]'}
2025-06-27 19:45:08,685 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,685 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,686 - DEBUG - Finished Request
2025-06-27 19:45:08,687 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'button[type="submit"]'}
2025-06-27 19:45:08,696 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,696 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,696 - DEBUG - Finished Request
2025-06-27 19:45:08,697 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': '.btn-submit'}
2025-06-27 19:45:08,704 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,705 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,705 - DEBUG - Finished Request
2025-06-27 19:45:08,705 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,709 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,709 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,709 - DEBUG - Finished Request
2025-06-27 19:45:08,710 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), '取消')]"}
2025-06-27 19:45:08,720 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,720 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.281"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.126"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.284"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.285"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.286"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.287"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.288"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.289"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.290"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.291"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.292"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.293"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.294"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.295"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.296"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.297"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.298"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.299"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.300"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.301"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.302"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.303"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.304"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.305"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.306"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.307"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.308"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.309"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.310"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.311"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.312"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.313"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.314"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.315"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.316"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.317"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.318"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.319"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.320"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.321"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.322"}]} | headers=HTTPHeaderDict({'Content-Length': '4967', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,721 - DEBUG - Finished Request
2025-06-27 19:45:08,721 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282/name {}
2025-06-27 19:45:08,727 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282/name HTTP/1.1" 200 0
2025-06-27 19:45:08,727 - DEBUG - Remote response: status=200 | data={"value":"option"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,727 - DEBUG - Finished Request
2025-06-27 19:45:08,727 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282/text {}
2025-06-27 19:45:08,736 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282/text HTTP/1.1" 200 0
2025-06-27 19:45:08,736 - DEBUG - Remote response: status=200 | data={"value":"取消"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,736 - DEBUG - Finished Request
2025-06-27 19:45:08,736 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282/text {}
2025-06-27 19:45:08,743 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282/text HTTP/1.1" 200 0
2025-06-27 19:45:08,744 - DEBUG - Remote response: status=200 | data={"value":"取消"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,744 - DEBUG - Finished Request
2025-06-27 19:45:08,745 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282'}, 'id']}
2025-06-27 19:45:08,751 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,751 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,752 - DEBUG - Finished Request
2025-06-27 19:45:08,752 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282'}, 'class']}
2025-06-27 19:45:08,758 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,759 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,759 - DEBUG - Finished Request
2025-06-27 19:45:08,759 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.282'}, 'name']}
2025-06-27 19:45:08,765 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,765 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,765 - DEBUG - Finished Request
2025-06-27 19:45:08,766 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:08,770 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:08,770 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,771 - DEBUG - Finished Request
2025-06-27 19:45:08,771 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': 'table'}
2025-06-27 19:45:08,778 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:08,778 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.131"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,778 - DEBUG - Finished Request
2025-06-27 19:45:08,779 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130/name {}
2025-06-27 19:45:08,784 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130/name HTTP/1.1" 200 0
2025-06-27 19:45:08,784 - DEBUG - Remote response: status=200 | data={"value":"table"} | headers=HTTPHeaderDict({'Content-Length': '17', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,784 - DEBUG - Finished Request
2025-06-27 19:45:08,786 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130/text {}
2025-06-27 19:45:08,795 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130/text HTTP/1.1" 200 0
2025-06-27 19:45:08,795 - DEBUG - Remote response: status=200 | data={"value":"進廠類別 月核定量(A) 日控量\n(七天後) 實際進廠量(B) 預計進廠量(C) 上月超量(D) 剩餘進廠量\nA1 1346.5 0 815.691 76.89 0 453.919"} | headers=HTTPHeaderDict({'Content-Length': '170', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,796 - DEBUG - Finished Request
2025-06-27 19:45:08,796 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130/text {}
2025-06-27 19:45:08,804 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130/text HTTP/1.1" 200 0
2025-06-27 19:45:08,804 - DEBUG - Remote response: status=200 | data={"value":"進廠類別 月核定量(A) 日控量\n(七天後) 實際進廠量(B) 預計進廠量(C) 上月超量(D) 剩餘進廠量\nA1 1346.5 0 815.691 76.89 0 453.919"} | headers=HTTPHeaderDict({'Content-Length': '170', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,806 - DEBUG - Finished Request
2025-06-27 19:45:08,806 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130'}, 'id']}
2025-06-27 19:45:08,813 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,813 - DEBUG - Remote response: status=200 | data={"value":"queryQueryCleEntryWeight"} | headers=HTTPHeaderDict({'Content-Length': '36', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,813 - DEBUG - Finished Request
2025-06-27 19:45:08,814 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130'}, 'class']}
2025-06-27 19:45:08,819 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,820 - DEBUG - Remote response: status=200 | data={"value":"table table-striped table-bordered dataTable no-footer"} | headers=HTTPHeaderDict({'Content-Length': '66', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,820 - DEBUG - Finished Request
2025-06-27 19:45:08,821 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130'}, 'name']}
2025-06-27 19:45:08,826 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:08,826 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,826 - DEBUG - Finished Request
2025-06-27 19:45:08,842 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element {'using': 'xpath', 'value': "//*[contains(text(), 'E48B201611405190953')]"}
2025-06-27 19:45:08,852 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/element HTTP/1.1" 200 0
2025-06-27 19:45:08,852 - DEBUG - Remote response: status=200 | data={"value":{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.264"}} | headers=HTTPHeaderDict({'Content-Length': '127', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:08,852 - DEBUG - Finished Request
2025-06-27 19:45:09,403 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:09,409 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:09,410 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:09,411 - DEBUG - Finished Request
2025-06-27 19:45:10,412 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:10,420 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:10,420 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,420 - DEBUG - Finished Request
2025-06-27 19:45:10,854 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '\n            // 檢查頁面最終狀態\n            var bodyText = document.body.innerText || document.body.textCon...', 'args': []}
2025-06-27 19:45:10,863 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:10,864 - DEBUG - Remote response: status=200 | data={"value":{"bodyTextLength":3955,"editButtonCount":70,"hasTargetOrder":true,"rowCount":41,"sampleText":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n"}} | headers=HTTPHeaderDict({'Content-Length': '632', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,864 - DEBUG - Finished Request
2025-06-27 19:45:10,866 - INFO - 🔍 [WebDriverWait完成後] 開始記錄頁面內容...
2025-06-27 19:45:10,866 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:10,871 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:10,871 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,871 - DEBUG - Finished Request
2025-06-27 19:45:10,872 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/title {}
2025-06-27 19:45:10,877 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/title HTTP/1.1" 200 0
2025-06-27 19:45:10,877 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,877 - DEBUG - Finished Request
2025-06-27 19:45:10,878 - INFO - 🔍 [WebDriverWait完成後] 當前 URL: https://wmc.kcg.gov.tw/
2025-06-27 19:45:10,878 - INFO - 🔍 [WebDriverWait完成後] 頁面標題: 高雄市廢棄物調度中心
2025-06-27 19:45:10,878 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/source {}
2025-06-27 19:45:10,885 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/source HTTP/1.1" 200 0
2025-06-27 19:45:10,886 - DEBUG - Remote response: status=200 | data={"value":"\u003Chtml class=\" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths\" style=\"--vh: 16.14px;\">\u003Chead>\u003Clink href=\"/Content/MainCss?v=zPHWubngoBLlh81X9bw_8aWFtUJfWgM1VR7izfwxpoQ1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DatePickerCss?v=_eIV2YVNY3NGm3qI_2PZGbvVnvApZ-2xCi6B-LE3eog1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/DataTableCss?v=Z2hod0l-s4nUkCqEwOiqsErJ5wq6PmToPF0R4Rizr9g1\" rel=\"stylesheet\">\n\n\u003Clink href=\"/Content/Select2?v=4ztEzS1BFK9SJXpwvk8t7BHUmbadVq3PcyTLX5zs8R41\" rel=\"stylesheet\">\n\n\u003Cscript src=\"/bundles/modernizr?v=inCVuEFe6J4Q07A0AcRsbJic_UE5MwpRMNGcOtk94TE1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/jquery?v=9ktsOtIo0upvJP7-7FiXuOoOJe58RLFJ__wIRPL2vGo1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/bootstrap?v=Rc0g6GDBha0U2Ue5txisObdTUK1i4R1sOhOsbTe7GyM1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Common?v=9LzV-tERxVFhuqR--vFDWHVOODtQJjJxZWXucFKfyMc1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DatePickerJs?v=GYo0dK9-Sa66qOqr4Y_drvtWRpDslr-z_Ak-YVkqpSU1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/DataTableJs?v=PrL5mJgVEL8_qJD7wVZM5xxp6rEVZ9g1TBfDlvdUlWg1\">\u003C/script>\n\n\u003Cscript src=\"/bundles/Select2?v=EVnzBeaY0QRwE1sNLFSjOSAbSsKlryXF7hBwa0aFFLc1\">\u003C/script>\n\n\n\u003Cscript>\n    var _Headers = { 'RequestVerificationToken': 'dwQmRPQpfyn8F0oM5b2Y34Jj8Kqg2ixTpEqE3zoxDhEmpEg6SriMRGFtK1CMRKuNyrHVrxHL_ZYEBDymqi4H3PouxCLFES4gSAVlpueyt-I1:uEIlvK6Wj2ktDB7sC5IDt-rJCQPqB59nGLSUS4iqPVib-MzSNOM3xGJYzZzepvy7O2rMBgZ8jV2Yj-FKPax7oGi--So91r-aXrzX588n95w1' };\n    \n    let lengthMenuList = [[10, 50, 100, 300], [10, 50, 100, 300]];\n\u003C/script>\n\n\n    \u003Cscript>\n        var UrlDeleteCleEntryForm = '/Frontend/CLE/DeleteCleEntryForm';\n        var Token = '92JVXw5oPZ9VRwGSSOL5a1zLTVCwaF8_lfPSh2JBblSY9W3lyMR6-N6qE8pNzEHNswVaH1OQw377mgSklPO_o3KmIL133l7Yg0MoFqntP0I1:x9YT6T5xjXpXdVvX-UKYc6tNRPxTi7BMQsuO3SvxnSgt6LlN7M-GbMyqb59ApIulKABDENwIcn56w0YCpkZmwIU6S-w7DJTXpJcMlM0LoEY1';\n        var UrlExport1 = '/Frontend/CLE/DownloadCLE104Data';\n        var UrlExport = '/Frontend/CLE/PrintEntryForm';\n        var UrlEFDetailExport = '/Frontend/CLE/DownloadCLEDetailData';\n        var UrlLog = '/Frontend/CLE/LogFP';\n   \n        let actualWeightDays = 10;\n        console.log(UrlLog);\n        let addRouteOption = {\n            Title: '新增路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030A01',\n            BeforeClose: function () {\n\n            }\n        };\n\n        let editRouteOption = {\n            Title: '編輯路線',\n            Buttons: null,\n            DialogId: 'divAdd',\n            Width: 1200,\n            Height: 700,\n            Url: '/Frontend/CLE/CLE1030E00',\n            BeforeClose: function () {\n\n            }\n        };\n\n      $(function () {\n\n        //  var FPoptions = {\n        //      fonts: { extendedJsFonts: true },\n        //      excludes: {\n        //          cpuClass: true,\n        //          fonts: true,\n        //          fontsFlash: true,\n        //          doNotTrack: true,\n        //          webgl: true,\n        //          webglVendorAndRenderer: true,\n              \n                \n        //      }\n        //  }\n        //    var FP_token;\n\n        //    setTimeout(function () {\n        //        Fingerprint2.get(FPoptions, function (components) {\n        //            var values = components.map(function (component) { return component.value })\n        //            var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //            FP_token = murmur\n        //            LogData2(FP_token);\n        //        })\n        //    }, 500)\n\n\n        //    Fingerprint2.get(FPoptions, function (components) {\n        //        var values = components.map(function (component) { return component.value })\n        //        var murmur = Fingerprint2.x64hash128(values.join(''), 31)\n\n        //        FP_token = murmur\n        //        LogData(FP_token);\n        //    });\n\n\n        });\n          var FP_token;\n\n          function getOrCreateLocalID() {\n              const key = 'local_fingerprint_id';\n              let id = localStorage.getItem(key);\n              if (!id) {\n                  id = 'id-' + Math.random().toString(36).substr(2, 9);\n                  localStorage.setItem(key, id);\n              }\n              return id;\n          }\n\n          window.addEventListener('load', function () {\n              setTimeout(function () {\n                  Fingerprint2.get(function (components) {\n                      // 過濾與排序\n                      const baseComponents = components\n                          .filter(c => !['userAgent', 'language', 'webdriver'].includes(c.key))\n                          .sort((a, b) => a.key.localeCompare(b.key));\n\n                      // 額外客製化參數\n                      const customComponents = [\n                          { key: 'timezoneOffset', value: new Date().getTimezoneOffset() },\n                          { key: 'localID', value: getOrCreateLocalID() },\n                          { key: 'cpuCores', value: navigator.hardwareConcurrency || 'unknown' },\n                          { key: 'pixelRatio', value: window.devicePixelRatio },\n                          { key: 'connectionType', value: (navigator.connection && navigator.connection.effectiveType) || 'unknown' }\n                      ];\n\n                      const allComponents = [...baseComponents, ...customComponents];\n\n\n                      const values = allComponents\n                          .sort((a, b) => a.key.localeCompare(b.key))\n                          .map(component => component.value);\n\n                      const rawString = values.join('###');\n\n\n                      const hash = Fingerprint2.x64hash128(rawString, 31);\n\n\n                      LogData(hash);\n\n                      console.log('Extended fingerprint hash:', hash);\n                      console.log('All components:', allComponents);\n                  });\n              }, 500);\n\n          });\n\n\n\n        function LogData(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n        function LogData2(data) {\n\n\n\n\n            $.ajax({\n                url: UrlLog2,\n                data: { LoginToken: data },\n                type: 'post',\n                cache: false,\n                async: false,\n                dataType: 'json'\n\n            });\n\n        }\n\n\n\n    \u003C/script>\n    \u003Cscript src=\"/Scripts/fingerprint2.js\" type=\"text/javascript\">\u003C/script>\n    \u003Cscript src=\"/Scripts/View/Frontend/CLE/CLE1040Q00.js?2025062719444338\">\u003C/script>\n\n\n\n\n\n\n\u003C/head>\u003Cbody>\u003Cdiv class=\"container-fluid\">\n    \n\n\n\n\n\u003Cdiv class=\"mainTitle\">\n    \u003Ch2>進廠確認單管理\u003C/h2>\n\u003C/div>\n\u003Cdiv class=\"row\">\n    \u003Cdiv class=\"col-md-7 text-left\">\n        \n\n\n\u003Cform action=\"/Frontend/CLE/QueryEntryForm\" autocomplete=\"off\" id=\"QueryForm\" method=\"post\" novalidate=\"\">\u003Cinput name=\"__RequestVerificationToken\" type=\"hidden\" value=\"HC_EaCsCGu7yuPT3WqL8unFHVV2umqSPyj5JPbJPD7NkX7e56QNca4qWwBueT37zSsS2DdBrS4JPQZtXMeofuJFdcWVgXOSAIivTaDUesFU1\">    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryIncineratorId\">進廠別\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control\" id=\"EntryIncineratorId\" name=\"EntryIncineratorId\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"0\">調度中心\u003C/option>\n\u003Coption value=\"1\">高南廠\u003C/option>\n\u003Coption value=\"2\">岡山廠\u003C/option>\n\u003Coption value=\"3\">仁武廠\u003C/option>\n\u003Coption value=\"5\">路竹掩埋場\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"EntryNo\">進廠確認單號\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cinput class=\"form-control\" id=\"EntryNo\" name=\"EntryNo\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"FormStatus\">狀態\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-3\">\n            \u003Cselect class=\"form-control p-2\" id=\"FormStatus\" name=\"FormStatus\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"00\">暫存\u003C/option>\n\u003Coption value=\"01\">待審查\u003C/option>\n\u003Coption value=\"02\">未載運\u003C/option>\n\u003Coption value=\"03\">已載運-待清除確認\u003C/option>\n\u003Coption value=\"41\">已載運-檢核未通過\u003C/option>\n\u003Coption value=\"91\">取消\u003C/option>\n\u003Coption value=\"97\">審查退回\u003C/option>\n\u003Coption value=\"98\">退運\u003C/option>\n\u003Coption value=\"99\">已完成\u003C/option>\n\u003C/select>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2 text-right\">\n            \u003Clabel for=\"CheckWeighFail\">檢核結果\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-4\">\n            \u003Cselect class=\"form-control\" id=\"CheckWeighFail\" name=\"CheckWeighFail\">\u003Coption value=\"\">全部\u003C/option>\n\u003Coption value=\"1\">通過\u003C/option>\n\u003Coption value=\"2\">未通過\u003C/option>\n\u003Coption value=\"3\">未檢核\u003C/option>\n\u003C/select>\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"ReserveEntryDate_Start\">預計進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_Start\" name=\"ReserveEntryDate_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"ReserveEntryDate_End\" name=\"ReserveEntryDate_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"EntryDateTime_Start\">實際進廠起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_Start\" name=\"EntryDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control datePicker\" id=\"EntryDateTime_End\" name=\"EntryDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"form-group row align-items-center\">\n        \u003Cdiv class=\"col-sm-3 text-right\">\n            \u003Clabel for=\"\">報表日期起迄日\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_Start\" name=\"RptDateTime_Start\" type=\"text\" value=\"\">\n        \u003C/div>\n        \u003Cdiv class=\"text-center\">\n            \u003Clabel for=\"\">~\u003C/label>\n        \u003C/div>\n        \u003Cdiv class=\"col-sm-2\">\n            \u003Cinput class=\"form-control\" id=\"RptDateTime_End\" name=\"RptDateTime_End\" type=\"text\" value=\"\">\n        \u003C/div>\n    \u003C/div>\n    \u003Cdiv class=\"text-center\">\n        \n\n        \u003Cbutton type=\"button\" id=\"btnQuery\" onclick=\"tableDraw(queryTable, 'QueryForm');tableDraw(queryQueryCleEntryWeight, '',_queryQueryCleEntryWeightConfig );\" class=\"btn btn-primary\">查詢\u003C/button>\n        \u003Cspan style=\"color:red\">請按查詢以顯示清單\u003C/span>\n        \n\n        \u003Cbr>\n                                \u003Cbutton type=\"button\" class=\"btn btn-warning\" onclick=\"addA1Option.Data = {entryFormType:'A1'};EditShowDialog(addA1Option)\">新增A1本市事廢\u003C/button>\n                        \u003Cbr>\n\n        \n                        \u003Cbutton type=\"button\" class=\"btn btn-dark\" onclick=\"addA3bOption.Data = {entryFormType:'A3b'};EditShowDialog(addA3bOption)\">新增A3b2050專案\u003C/button>\n                \n                    \u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"DownloadCLEDetailData();\">下載明細報表\u003C/button>\n            \u003Cbr>\n\n    \u003C/div>\n\u003C/form>\n    \u003C/div>\n    \u003Cdiv class=\"col-md-5 text-left\">\n\u003Cform action=\"/Frontend/CLE/QueryCleEntryWeight\" autocomplete=\"off\" id=\"QueryDocForm\" method=\"post\" novalidate=\"\" onsubmit=\"return false;\">            \u003Cdiv>\n                \u003Cselect id=\"QueryDate\" name=\"QueryDate\" onchange=\"getCleEntryWeight()\">\u003Coption value=\"2025/06/27\">6月\u003C/option>\n\u003Coption value=\"2025/07/27\">7月\u003C/option>\n\u003C/select>\u003Cspan>進廠量統計\u003C/span>\n                \n            \u003C/div>\n\u003C/form>        \u003Cdiv id=\"queryQueryCleEntryWeight_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryQueryCleEntryWeight\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">月核定量(A)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">日控量\u003Cbr>(七天後)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠量(B)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量(C)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">上月超量(D)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">剩餘進廠量\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>A1\u003C/td>\u003Ctd>1346.5\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>815.691\u003C/td>\u003Ctd>76.89\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>453.919\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryQueryCleEntryWeight_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryQueryCleEntryWeightConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": false,\n  \"lengthChange\": false,\n  \"ordering\": false,\n  \"paging\": false,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"get\",\n    \"url\": \"/Frontend/CLE/QueryCleEntryWeight\",\n    \"data\": ajaxParam\n  },\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 0,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"ConsentType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"月核定量(A)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ControlOpenWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"日控量\u003Cbr/>(七天後)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthEntryWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"實際進廠量(B)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量(C)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthOverWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"上月超量(D)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"MonthRemainingWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"剩餘進廠量\",\n      \"visible\": true\n    }\n  ]\n};\nlet queryQueryCleEntryWeight = $('#queryQueryCleEntryWeight').DataTable(_queryQueryCleEntryWeightConfig);\n\u003C/script>\n        \u003Cdiv>\n            \u003Cul>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">每日開放查詢時日10:30~次日09:00\u003C/span>\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">欄位說明：\u003C/span>(單位：噸)\u003C/li>\n                \u003Cli>\u003Cspan style=\"font-weight:bold\">月核定量(A)：\u003C/span>因進廠管控措施機制，故月核可量為浮動數值\u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月實際進廠量(B)：\u003C/span>進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月預計進廠量(C)：\u003C/span>進廠確認單狀態為「未載運」、「已逾期」的累積量。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">上月超量(D)：\u003C/span>上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n                \u003C/li>\n                \u003Cli>\n                    \u003Cspan style=\"font-weight: bold\">本月剩餘進廠量：\u003C/span>A-B-C-D\n                \u003C/li>\n                \n            \u003C/ul>\n        \u003C/div>\n    \u003C/div>\n\n\u003C/div>\n\n\n\n\n\u003Cscript>\n\n    function operateFormatter(data, type, row, meta) {\n        let result = \"\";\n        let todayDate = new Date(); //Today Date\n\n        if (row.IsLocked === true) {\n\n        }\n\n\n\n        //預計進廠日前一天可修改、刪除\n        //if (moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString())) {\n        if (\n            (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1\n            && row.FormStatus != '03'\n            && (row.FormStatus === '00' || row.FormStatus === '02' || row.FormStatus == '97')\n        ) {\n            //console.log((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")));\n            if (row.EntryFormType == \"A4a\") {\n                //02=審查完成\n                if (row.FormStatus !== '02') {\n                    result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"Edit41EOption.Data = {entryId:${data}};Edit41EOption.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(Edit41EOption)\">編輯\u003C/button>`;\n                }\n            }\n            else {\n                if (row.FormStatus === '00') {\n                    if (row.ReserveType != \"特殊\") {\n                        if (row.ReserveType != \"A3a\") {\n                            result += `\u003Cbutton type=\"button\"  class='btn btn-success' onclick=\"EditE20Option.Data = {entryId:${data}};EditE20Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE20Option)\">編輯\u003C/button>`;\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' style='display:none;'  onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n\n                    }\n                }\n                else {\n                    if (row.ReserveType != \"特殊\" && row.IsLocked === false) {\n                        if (row.ReserveType == \"優先\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE30Option.Data = {entryId:${data}};EditE30Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE30Option)\">編輯\u003C/button>`;\n                        }\n                        else if (row.EntryFormType == \"A3a\") {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE40Option.Data = {entryId:${data}};EditE40Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE40Option)\">編輯\u003C/button>`;\n                        }\n                        else {\n                            result += `\u003Cbutton type=\"button\" id='E00_${data}' class='btn btn-success' onclick=\"EditE00Option.Data = {entryId:${data}};EditE00Option.Title='編輯進廠確認單(${row.EntryNo})';EditShowDialog(EditE00Option)\">編輯\u003C/button>`;\n                        }\n                    }\n\n                }\n\n            }\n\n\n        }\n        else {\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02')) {\n\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('確定取消單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormCancel', { entryId: ${data}}, 'post', _Headers);}\">未進廠取消\u003C/button>`;\n            }\n            if ((moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\") \u003C= actualWeightDays)) {\n                if ((row.FormStatus == '03' || row.FormStatus == '41')) { ////20250220 關閉隔日可填寫實際重量\n                    // if ((row.FormStatus == '03' || row.FormStatus == '41') || (row.FormStatus == '02' && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) > 0 && (row.FormStatus == '02'))) {\n                    if (row.EntryFormType == \"A4a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"Edit41E10Option.Data = {entryId:${data}};Edit41E10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(Edit41E10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else if (row.EntryFormType == \"A3a\") {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                    else {\n                        result += `\u003Cbutton type=\"button\" class='btn btn-success' onclick=\"EditE10Option.Data = {entryId:${data}};EditE10Option.Title='填寫實際重量(${row.EntryNo})';EditShowDialog(EditE10Option)\">填寫實際重量\u003C/button>`;\n                    }\n                }\n            }\n            \n            if (row.EntryFormType == \"A4a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1041R00')\">查看\u003C/button>`;\n            }\n            else if (row.EntryFormType == \"A3a\") {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R10')\">查看\u003C/button>`;\n            }\n            else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-primary' onclick=\"window.parent.EditShowDialog({entryId:${data}},'查看(${row.EntryNo})',null,'divAdd','/Frontend/CLE/CLE1040R00')\">查看\u003C/button>`;\n            }\n\n\n\n        }\n\n        //if (row.CargoRoute) {\n        //    result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"editRouteOption.Data = {RouteId:${row.RouteId}};window.parent.EditShowDialog(editRouteOption)\">編輯路線\u003C/button>`;\n        //}\n        //else {\n        result += `\u003Cbutton type=\"button\" class='btn btn-info' onclick=\"addRouteOption.Data = {entryId:${row.EntryId}, ClearType:'${row.ClearType}',EntryFormType:'${row.EntryFormType}'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>`;\n        //}\n\n        if ((moment(row.ReserveEntryDate).isAfter(todayDate.toLocaleDateString()) || moment(row.ReserveEntryDate).isSame(todayDate.toLocaleDateString()))\n            && row.FormStatus === '02') {\n            //  && row.IsLocked === false) {\n            result += `\u003Cbutton type=\"button\" class='btn btn-secondary' onclick=\"ShowEntryForm(${data})\">列印\u003C/button>`;\n\n        }\n        if (row.EntryFormType == \"A1\")\n        {\n         }\n\n             return result;\n         }\n\n    function cancelFormatter(data, type, row, meta) {\n        let todayDate = new Date(); //Today Date\n        let result = \"\";\n        //刪單須在預約進廠日前一天中午前刪除\n        let hours = moment(row.ReserveEntryDate).diff(todayDate, 'hours');\n\n        if ((hours >= 12 && data === false) || row.FormStatus === '00') {\n            if (row.ReserveType == \"一般\" && row.IsLocked === true) {\n                result = \"\";\n            } else {\n                result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n            }\n         }\n        else if (row.ReserveType == \"特殊\" && (moment(todayDate.toLocaleDateString()).diff(row.ReserveEntryDate, \"days\")) \u003C 1 && row.FormStatus === '02') {\n\n            result += `\u003Cbutton type=\"button\" class='btn btn-danger' onclick=\"if(confirm('【取消(刪除)警語警告】\\\\r\\\\n取消(刪除)將導致清運車輛無法進廠 \\\\r\\\\n確定取消(刪除)單號：${row.EntryNo}？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: ${row.EntryId}}, 'post', _Headers);}\">取消(刪除)\u003C/button>`;\n\n        }\n\n        return result;\n    }\n\n    function entryFormTypeRender(data, type, row, meta) {\n        let result = \"\";\n        result = `\u003Cspan>${data}${row.EntryFormTypeText}\u003C/span>`\n        return result;\n    }\n\n    function CarNoReserveType(data, type, row, meta) {\n        let result = \"\";\n        if (row.ReserveType == '特殊') {\n            result = `\u003Cspan>${data}(${row.ReserveType})\u003C/span>`\n        }\n        else {\n            result = `\u003Cspan>${data}\u003C/span>`\n        }\n        return result;\n    }\n\n\n    function checkStatus(row, data, dataIndex) {\n        if (data[\"IsLocked\"] == true) {\n          /*  $(row).css(\"background-color\", \"red\");*/\n        }\n    }\n\n\u003C/script>\n\n\u003Cdiv class=\"pt-2\">\n\n    \u003Cdiv id=\"queryTable_wrapper\" class=\"dataTables_wrapper dt-bootstrap4 no-footer\">\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-6\">\u003Cdiv class=\"dataTables_length\" id=\"queryTable_length\">\u003Clabel>顯示 \u003Cselect name=\"queryTable_length\" aria-controls=\"queryTable\" class=\"custom-select custom-select-sm form-control form-control-sm\">\u003Coption value=\"10\">10\u003C/option>\u003Coption value=\"50\">50\u003C/option>\u003Coption value=\"100\">100\u003C/option>\u003Coption value=\"300\">300\u003C/option>\u003C/select> 項結果\u003C/label>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-6\">\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12\">\u003Ctable id=\"queryTable\" class=\"table table-striped table-bordered dataTable no-footer\" role=\"grid\" aria-describedby=\"queryTable_info\">\u003Cthead>\u003Ctr role=\"row\">\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\" style=\"width: 15%;\">操作\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">狀態\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠單號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運路線\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">車號\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">清運種類\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠量\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預計進廠日\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">實際進廠時間\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">進廠別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">業者過磅淨重(噸)\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">預約類別\u003C/th>\u003Cth class=\"sorting_disabled\" rowspan=\"1\" colspan=\"1\">取消\u003C/th>\u003C/tr>\u003C/thead>\u003Ctbody>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3146082\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3146082};EditE00Option.Title='編輯進廠確認單(E48B201611406230886)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3146082, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3146082)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406230886\u003C/td>\u003Ctd>(NEW)H4 2808 星期五(30%)\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406230886？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3146082}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3144221\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3144221};EditE00Option.Title='編輯進廠確認單(E48B201611406200782)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3144221, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3144221)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406200782\u003C/td>\u003Ctd>H4 2808義大遊樂\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>1.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406200782？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3144221}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142204};EditE20Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142204\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142204};EditE00Option.Title='編輯進廠確認單(E48B201611406181141)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142204, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181141\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181141？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142204}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142201};EditE20Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142201\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142201};EditE00Option.Title='編輯進廠確認單(E48B201611406181137)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142201, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181137\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181137？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142201}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142200};EditE20Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142200\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142200};EditE00Option.Title='編輯進廠確認單(E48B201611406181136)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142200, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181136\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181136？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142200}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3142199};EditE20Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3142199\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3142199};EditE00Option.Title='編輯進廠確認單(E48B201611406181135)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142199, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406181135\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181135？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142199}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3142197\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3142197};EditE00Option.Title='編輯進廠確認單(E48B201611406181133)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3142197, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3142197)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611406181133\u003C/td>\u003Ctd>(NEW)H9 5580星期五 只有一車(25%)\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406181133？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3142197}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134987};EditE20Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134987\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134987};EditE00Option.Title='編輯進廠確認單(E48B201611406110721)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134987, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110721\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110721？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134987}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134985};EditE20Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134985\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134985};EditE00Option.Title='編輯進廠確認單(E48B201611406110719)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134985, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110719\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110719？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134985}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134984};EditE20Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134984\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134984};EditE00Option.Title='編輯進廠確認單(E48B201611406110718)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134984, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110718\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2808\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>2.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110718？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134984}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134983};EditE20Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134983\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134983};EditE00Option.Title='編輯進廠確認單(E48B201611406110717)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134983, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110717\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110717？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134983}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134981};EditE20Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134981\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134981};EditE00Option.Title='編輯進廠確認單(E48B201611406110715)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134981, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110715\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEH-9230\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110715？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134981}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3134980};EditE20Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3134980\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3134980};EditE00Option.Title='編輯進廠確認單(E48B201611406110714)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3134980, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611406110714\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611406110714？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3134980}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121411};EditE20Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121411\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121411};EditE00Option.Title='編輯進廠確認單(E48B201611405261022)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121411, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261022\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEB-6030\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261022？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121411}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3121410};EditE20Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3121410\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3121410};EditE00Option.Title='編輯進廠確認單(E48B201611405261021)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3121410, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405261021\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405261021？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3121410}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117615};EditE20Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117615};EditE00Option.Title='編輯進廠確認單(E48B201611405210775)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210775\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEJ-5580\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210775？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117614};EditE20Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117614\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117614};EditE00Option.Title='編輯進廠確認單(E48B201611405210774)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117614, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210774\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210774？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117614}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3117613};EditE20Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3117613\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3117613};EditE00Option.Title='編輯進廠確認單(E48B201611405210773)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3117613, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405210773\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9670\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405210773？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3117613}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115644\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115644};EditE00Option.Title='編輯進廠確認單(E48B201611405190957)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115644, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115644)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190957\u003C/td>\u003Ctd>(NEW)119星期五(50%)\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190957？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115644}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115643};EditE20Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115643\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115643};EditE00Option.Title='編輯進廠確認單(E48B201611405190956)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115643, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190956\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KED-9671\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>5.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190956？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115643}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115642\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115642};EditE00Option.Title='編輯進廠確認單(E48B201611405190955)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115642, ClearType:'3',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115642)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190955\u003C/td>\u003Ctd>H2 119 義大\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>專車清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190955？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115642}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115641};EditE20Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115641\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115641};EditE00Option.Title='編輯進廠確認單(E48B201611405190954)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115641, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190954\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>117-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190954？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115641}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115640};EditE20Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115640\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115640};EditE00Option.Title='編輯進廠確認單(E48B201611405190953)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115640, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190953\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190953？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115640}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115636};EditE20Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115636\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115636};EditE00Option.Title='編輯進廠確認單(E48B201611405190949)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115636, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190949\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>119-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190949？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115636}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115635};EditE20Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115635\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115635};EditE00Option.Title='編輯進廠確認單(E48B201611405190948)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115635, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190948\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190948？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115635}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115634};EditE20Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115634\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115634};EditE00Option.Title='編輯進廠確認單(E48B201611405190947)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115634, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190947\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>120-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.2\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190947？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115634}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115632};EditE20Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115632\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115632};EditE00Option.Title='編輯進廠確認單(E48B201611405190945)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115632, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190945\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190945？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115632}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115631};EditE20Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115631\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115631};EditE00Option.Title='編輯進廠確認單(E48B201611405190944)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115631, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190944\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>121-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>3.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190944？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115631}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115630};EditE20Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115630\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115630};EditE00Option.Title='編輯進廠確認單(E48B201611405190943)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115630, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190943\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190943？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115630}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115627};EditE20Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115627\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115627};EditE00Option.Title='編輯進廠確認單(E48B201611405190940)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115627, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190940\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>129-BR\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.3\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190940？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115627}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" id=\"E00_3115625\" class=\"btn btn-success\" onclick=\"EditE00Option.Data = {entryId:3115625};EditE00Option.Title='編輯進廠確認單(E48B201611405190938)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115625, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-secondary\" onclick=\"ShowEntryForm(3115625)\">列印\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>未載運\u003C/td>\u003Ctd>E48B201611405190938\u003C/td>\u003Ctd>(NEW)H72560星期五(有美生) (20%)\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190938？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115625}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115623};EditE20Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115623\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115623};EditE00Option.Title='編輯進廠確認單(E48B201611405190936)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115623, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190936\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KEP-2560\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>6\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190936？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115623}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115621};EditE20Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115621\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115621};EditE00Option.Title='編輯進廠確認單(E48B201611405190934)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115621, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190934\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190934？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115621}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115619};EditE20Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115619\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115619};EditE00Option.Title='編輯進廠確認單(E48B201611405190932)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115619, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190932\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>937-N6\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>4.9\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190932？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115619}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115618};EditE20Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115618\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115618};EditE00Option.Title='編輯進廠確認單(E48B201611405190931)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115618, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190931\u003C/td>\u003Ctd>南\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>高南廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190931？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115618}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115616};EditE20Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115616\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115616};EditE00Option.Title='編輯進廠確認單(E48B201611405190929)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115616, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190929\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KEH-9278\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>8.5\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190929？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115616}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"odd\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115615};EditE20Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115615\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115615};EditE00Option.Title='編輯進廠確認單(E48B201611405190928)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115615, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190928\u003C/td>\u003Ctd>岡\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>岡山廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190928？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115615}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003Ctr class=\"even\">\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-success\" onclick=\"EditE20Option.Data = {entryId:3115399};EditE20Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE20Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" id=\"E00_3115399\" class=\"btn btn-success\" style=\"display:none;\" onclick=\"EditE00Option.Data = {entryId:3115399};EditE00Option.Title='編輯進廠確認單(E48B201611405190698)';EditShowDialog(EditE00Option)\">編輯\u003C/button>\u003Cbutton type=\"button\" class=\"btn btn-info\" onclick=\"addRouteOption.Data = {entryId:3115399, ClearType:'1',EntryFormType:'A1'};window.parent.EditShowDialog(addRouteOption)\">新增路線\u003C/button>\u003C/td>\u003Ctd>\u003Cspan>A1本市事廢\u003C/span>\u003C/td>\u003Ctd>暫存\u003C/td>\u003Ctd>E48B201611405190698\u003C/td>\u003Ctd>仁\u003C/td>\u003Ctd>KER-2807\u003C/td>\u003Ctd>一般清運\u003C/td>\u003Ctd>7\u003C/td>\u003Ctd>2025-07-04\u003C/td>\u003Ctd>\u003C/td>\u003Ctd>仁武廠\u003C/td>\u003Ctd>0\u003C/td>\u003Ctd>一般\u003C/td>\u003Ctd>\u003Cbutton type=\"button\" class=\"btn btn-danger\" onclick=\"if(confirm('【取消(刪除)警語警告】\\r\\n取消(刪除)將導致清運車輛無法進廠 \\r\\n確定取消(刪除)單號：E48B201611405190698？')){AjaxDialog('/Frontend/CLE/CleEntryFormDelete', { entryId: 3115399}, 'post', _Headers);}\">取消(刪除)\u003C/button>\u003C/td>\u003C/tr>\u003C/tbody>\u003C/table>\u003Cdiv id=\"queryTable_processing\" class=\"dataTables_processing card\" style=\"display: none;\">處理中...\u003C/div>\u003C/div>\u003C/div>\u003Cdiv class=\"row\">\u003Cdiv class=\"col-sm-12 col-md-5\">\u003Cdiv class=\"dataTables_info\" id=\"queryTable_info\" role=\"status\" aria-live=\"polite\">\u003Cbig>顯示第 1 至 38 項結果，共\u003Cfont color=\"red\"> 38 \u003C/font>項\u003C/big>\u003C/div>\u003C/div>\u003Cdiv class=\"col-sm-12 col-md-7\">\u003Cdiv class=\"dataTables_paginate paging_simple_numbers\" id=\"queryTable_paginate\">\u003Cul class=\"pagination\">\u003Cli class=\"paginate_button page-item previous disabled\" id=\"queryTable_previous\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"0\" tabindex=\"0\" class=\"page-link\">上一頁\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item active\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"1\" tabindex=\"0\" class=\"page-link\">1\u003C/a>\u003C/li>\u003Cli class=\"paginate_button page-item next disabled\" id=\"queryTable_next\">\u003Ca href=\"#\" aria-controls=\"queryTable\" data-dt-idx=\"2\" tabindex=\"0\" class=\"page-link\">下一頁\u003C/a>\u003C/li>\u003C/ul>\u003C/div>\u003C/div>\u003C/div>\u003C/div>\n\u003Cscript>let _queryTableConfig ={\n  \"autoWidth\": false,\n  \"deferRender\": false,\n  \"info\": true,\n  \"lengthChange\": true,\n  \"ordering\": false,\n  \"paging\": true,\n  \"processing\": true,\n  \"scrollX\": false,\n  \"searching\": false,\n  \"serverSide\": true,\n  \"stateSave\": false,\n  \"ajax\": {\n    \"method\": \"post\",\n    \"url\": \"/Frontend/CLE/QueryEntryForm\",\n    \"data\": ajaxParam\n  },\n  \"createdRow\": checkStatus,\n  \"deferLoading\": \"0\",\n  \"destroy\": true,\n  \"displayStart\": 0,\n  \"lengthMenu\": lengthMenuList,\n  \"orderCellsTop\": false,\n  \"orderClasses\": false,\n  \"orderMulti\": false,\n  \"pageLength\": 50,\n  \"retrieve\": false,\n  \"scrollCollapse\": false,\n  \"searchDelay\": 0,\n  \"stateDuration\": 0,\n  \"tabIndex\": 0,\n  \"language\": {\n    \"zeroRecords\": \"沒有符合的結果\",\n    \"url\": \"/Scripts/dataTables/datatables-zh-TW.js\"\n  },\n  \"columns\": [\n    {\n      \"data\": \"EntryId\",\n      \"orderable\": true,\n      \"render\": operateFormatter,\n      \"searchable\": true,\n      \"title\": \"操作\",\n      \"visible\": true,\n      \"width\": \"15%\"\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"render\": entryFormTypeRender,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatusText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠單號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CargoRoute\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"CarNo\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"車號\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ClearTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EstimateWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預計進廠量\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveEntryDate\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"預計進廠日\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"EntryDateTime\",\n      \"orderable\": true,\n      \"render\": dateTypeToyyyyMMdd,\n      \"searchable\": true,\n      \"title\": \"實際進廠時間\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"Incinerator_ShortName\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"NetWeight\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"業者過磅淨重(噸)\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"ReserveType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"預約類別\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"IsDelete\",\n      \"orderable\": true,\n      \"render\": cancelFormatter,\n      \"searchable\": true,\n      \"title\": \"取消\",\n      \"visible\": true\n    },\n    {\n      \"data\": \"FormStatus\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"狀態\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"IsLocked\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"鎖定\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormTypeText\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"RouteId\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運路線\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"ClearType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"清運種類\",\n      \"visible\": false\n    },\n    {\n      \"data\": \"EntryFormType\",\n      \"orderable\": true,\n      \"searchable\": true,\n      \"title\": \"進廠類別\",\n      \"visible\": false\n    }\n  ]\n};\nlet queryTable = $('#queryTable').DataTable(_queryTableConfig);\n\u003C/script>\n\u003C/div>\n\n\n\n\n\n\n\u003C/div>\n\n\u003Cscript defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;9564b1af0fe54a33&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;9d258267e10b4b358daa337da96d1082&quot;}\" crossorigin=\"anonymous\">\u003C/script>\n\u003C/body>\u003C/html>"} | headers=HTTPHeaderDict({'Content-Length': '92489', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,893 - DEBUG - Finished Request
2025-06-27 19:45:10,894 - INFO - 🔍 [WebDriverWait完成後] page_source 長度: 71771
2025-06-27 19:45:10,894 - INFO - 🔍 [WebDriverWait完成後] page_source 包含 E48B: True
2025-06-27 19:45:10,894 - INFO - 🔍 [WebDriverWait完成後] page_source 包含目標訂單: True
2025-06-27 19:45:10,894 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': "return document.body.innerText || document.body.textContent || '';", 'args': []}
2025-06-27 19:45:10,901 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:10,901 - DEBUG - Remote response: status=200 | data={"value":"進廠確認單管理\n進廠別\n全部\n調度中心\n高南廠\n岡山廠\n仁武廠\n路竹掩埋場\n進廠確認單號\n狀態\n全部\n暫存\n待審查\n未載運\n已載運-待清除確認\n已載運-檢核未通過\n取消\n審查退回\n退運\n已完成\n檢核結果\n全部\n通過\n未通過\n未檢核\n預計進廠起迄日\n~\n實際進廠起迄日\n~\n報表日期起迄日\n~\n查詢 請按查詢以顯示清單\n新增A1本市事廢\n新增A3b2050專案 下載明細報表\n\n6月\n7月\n進廠量統計\n進廠類別\t月核定量(A)\t日控量\n(七天後)\t實際進廠量(B)\t預計進廠量(C)\t上月超量(D)\t剩餘進廠量\nA1\t1346.5\t0\t815.691\t76.89\t0\t453.919\n每日開放查詢時日10:30~次日09:00\n欄位說明：(單位：噸)\n月核定量(A)：因進廠管控措施機制，故月核可量為浮動數值\n本月實際進廠量(B)：進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量。\n本月預計進廠量(C)：進廠確認單狀態為「未載運」、「已逾期」的累積量。\n上月超量(D)：上月進廠確認單狀態為「已完成」、「已載運-待清除確認」的累積量超過核定量的差值，每月月底、每月8日、每月16日，重新計算一次超量部份。\n本月剩餘進廠量：A-B-C-D\n顯示 \n10\n50\n100\n300\n 項結果\n操作\t進廠類別\t狀態\t進廠單號\t清運路線\t車號\t清運種類\t預計進廠量\t預計進廠日\t實際進廠時間\t進廠別\t業者過磅淨重(噸)\t預約類別\t取消\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406230886\t(NEW)H4 2808 星期五(30%)\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406200782\tH4 2808義大遊樂\tKEP-2808\t專車清運\t1.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181141\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181137\t仁\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181136\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406181135\t岡\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611406181133\t(NEW)H9 5580星期五 只有一車(25%)\tKEJ-5580\t一般清運\t6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110721\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110719\t南\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110718\t岡\tKEP-2808\t一般清運\t2.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110717\t南\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110715\t岡\tKEH-9230\t一般清運\t3.7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611406110714\t南\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261022\t岡\tKEB-6030\t一般清運\t6.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405261021\t南\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210775\t岡\tKEJ-5580\t一般清運\t8\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210774\t仁\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405210773\t南\tKED-9670\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190957\t(NEW)119星期五(50%)\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190956\t南\tKED-9671\t一般清運\t5.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190955\tH2 119 義大\t119-BR\t專車清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190954\t南\t117-BR\t一般清運\t3.5\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190953\t仁\t119-BR\t一般清運\t3.6\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190949\t南\t119-BR\t一般清運\t3.6\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190948\t仁\t120-BR\t一般清運\t3.2\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190947\t岡\t120-BR\t一般清運\t3.2\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190945\t仁\t121-BR\t一般清運\t3.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190944\t岡\t121-BR\t一般清運\t3.5\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190943\t仁\t129-BR\t一般清運\t4.3\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190940\t岡\t129-BR\t一般清運\t4.3\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線列印\tA1本市事廢\t未載運\tE48B201611405190938\t(NEW)H72560星期五(有美生) (20%)\tKEP-2560\t一般清運\t4.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190936\t岡\tKEP-2560\t一般清運\t6\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190934\t岡\t937-N6\t一般清運\t4.9\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190932\t南\t937-N6\t一般清運\t4.9\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190931\t南\tKEH-9278\t一般清運\t8\t2025-07-04\t\t高南廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190929\t仁\tKEH-9278\t一般清運\t8.5\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190928\t岡\tKER-2807\t一般清運\t7\t2025-07-04\t\t岡山廠\t0\t一般\t取消(刪除)\n編輯新增路線\tA1本市事廢\t暫存\tE48B201611405190698\t仁\tKER-2807\t一般清運\t7\t2025-07-04\t\t仁武廠\t0\t一般\t取消(刪除)\n顯示第 1 至 38 項結果，共 38 項\n上一頁\n1\n下一頁"} | headers=HTTPHeaderDict({'Content-Length': '7514', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,903 - DEBUG - Finished Request
2025-06-27 19:45:10,903 - INFO - 🔍 [WebDriverWait完成後] innerText 長度: 3955
2025-06-27 19:45:10,903 - INFO - 🔍 [WebDriverWait完成後] innerText 包含 E48B: True
2025-06-27 19:45:10,903 - INFO - 🔍 [WebDriverWait完成後] innerText 包含目標訂單: True
2025-06-27 19:45:10,904 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'table'}
2025-06-27 19:45:10,910 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:10,911 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.130"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.131"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,911 - DEBUG - Finished Request
2025-06-27 19:45:10,911 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'tr'}
2025-06-27 19:45:10,919 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:10,919 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.132"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.133"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.134"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.135"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.136"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.137"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.138"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.139"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.140"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.141"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.142"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.143"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.144"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.145"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.146"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.147"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.148"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.149"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.150"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.151"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.152"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.153"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.154"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.155"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.156"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.158"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.159"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.160"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.161"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.162"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.163"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.164"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.165"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.166"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.167"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.168"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.169"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.170"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.171"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.172"}]} | headers=HTTPHeaderDict({'Content-Length': '4849', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,921 - DEBUG - Finished Request
2025-06-27 19:45:10,921 - INFO - 🔍 [WebDriverWait完成後] 檢測到 2 個表格，41 個表格行
2025-06-27 19:45:10,921 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//*[contains(text(), 'E48B')]"}
2025-06-27 19:45:10,931 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:10,932 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.242"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.243"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.244"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.245"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.246"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.247"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.248"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.249"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.250"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.251"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.252"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.253"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.254"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.255"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.256"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.257"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.258"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.259"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.260"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.261"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.262"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.263"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.264"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.265"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.266"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.267"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.268"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.269"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.270"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.271"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.272"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.273"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.274"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.275"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.276"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.277"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.278"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.279"}]} | headers=HTTPHeaderDict({'Content-Length': '4495', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,932 - DEBUG - Finished Request
2025-06-27 19:45:10,933 - INFO - 🔍 [WebDriverWait完成後] 包含 'E48B' 的元素數量: 38
2025-06-27 19:45:10,933 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.242/text {}
2025-06-27 19:45:10,941 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.242/text HTTP/1.1" 200 0
2025-06-27 19:45:10,941 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406230886"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,941 - DEBUG - Finished Request
2025-06-27 19:45:10,942 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.242/name {}
2025-06-27 19:45:10,947 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.242/name HTTP/1.1" 200 0
2025-06-27 19:45:10,947 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,948 - DEBUG - Finished Request
2025-06-27 19:45:10,948 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 1: <td> E48B201611406230886...
2025-06-27 19:45:10,948 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.243/text {}
2025-06-27 19:45:10,957 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.243/text HTTP/1.1" 200 0
2025-06-27 19:45:10,957 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406200782"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,957 - DEBUG - Finished Request
2025-06-27 19:45:10,957 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.243/name {}
2025-06-27 19:45:10,963 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.243/name HTTP/1.1" 200 0
2025-06-27 19:45:10,963 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,964 - DEBUG - Finished Request
2025-06-27 19:45:10,964 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 2: <td> E48B201611406200782...
2025-06-27 19:45:10,964 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.244/text {}
2025-06-27 19:45:10,972 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.244/text HTTP/1.1" 200 0
2025-06-27 19:45:10,972 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406181141"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,972 - DEBUG - Finished Request
2025-06-27 19:45:10,973 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.244/name {}
2025-06-27 19:45:10,979 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.244/name HTTP/1.1" 200 0
2025-06-27 19:45:10,979 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,979 - DEBUG - Finished Request
2025-06-27 19:45:10,979 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 3: <td> E48B201611406181141...
2025-06-27 19:45:10,980 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.245/text {}
2025-06-27 19:45:10,987 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.245/text HTTP/1.1" 200 0
2025-06-27 19:45:10,987 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406181137"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,987 - DEBUG - Finished Request
2025-06-27 19:45:10,988 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.245/name {}
2025-06-27 19:45:10,994 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.245/name HTTP/1.1" 200 0
2025-06-27 19:45:10,994 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:10,995 - DEBUG - Finished Request
2025-06-27 19:45:10,995 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 4: <td> E48B201611406181137...
2025-06-27 19:45:10,995 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.246/text {}
2025-06-27 19:45:11,003 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.246/text HTTP/1.1" 200 0
2025-06-27 19:45:11,003 - DEBUG - Remote response: status=200 | data={"value":"E48B201611406181136"} | headers=HTTPHeaderDict({'Content-Length': '31', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,003 - DEBUG - Finished Request
2025-06-27 19:45:11,004 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.246/name {}
2025-06-27 19:45:11,011 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.246/name HTTP/1.1" 200 0
2025-06-27 19:45:11,011 - DEBUG - Remote response: status=200 | data={"value":"td"} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,011 - DEBUG - Finished Request
2025-06-27 19:45:11,011 - INFO - 🔍 [WebDriverWait完成後] E48B 元素 5: <td> E48B201611406181136...
2025-06-27 19:45:11,012 - INFO - 🔍 [WebDriverWait完成後] innerText 前300字符:
2025-06-27 19:45:11,012 - INFO - 🔍 [WebDriverWait完成後] 進廠確認單管理
進廠別
全部
調度中心
高南廠
岡山廠
仁武廠
路竹掩埋場
進廠確認單號
狀態
全部
暫存
待審查
未載運
已載運-待清除確認
已載運-檢核未通過
取消
審查退回
退運
已完成
檢核結果
全部
通過
未通過
未檢核
預計進廠起迄日
~
實際進廠起迄日
~
報表日期起迄日
~
查詢 請按查詢以顯示清單
新增A1本市事廢
新增A3b2050專案 下載明細報表

6月
7月
進廠量統計
進廠類別	月核定量(A)	日控量
(七天後)	實際進廠量(B)	預計進廠量(C)	上月超量(D)	剩餘進廠量
A1	1346.5	0	815.691	76.89	0	453.919
每日開放查詢時日1...
2025-06-27 19:45:11,012 - INFO - 🔍 [WebDriverWait完成後] innerText 後300字符:
2025-06-27 19:45:11,012 - INFO - 🔍 [WebDriverWait完成後] ...		高南廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190929	仁	KEH-9278	一般清運	8.5	2025-07-04		仁武廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190928	岡	KER-2807	一般清運	7	2025-07-04		岡山廠	0	一般	取消(刪除)
編輯新增路線	A1本市事廢	暫存	E48B201611405190698	仁	KER-2807	一般清運	7	2025-07-04		仁武廠	0	一般	取消(刪除)
顯示第 1 至 38 項結果，共 38 項
上一頁
1
下一頁
2025-06-27 19:45:11,013 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']"}
2025-06-27 19:45:11,024 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:11,024 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.79"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.173"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.174"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.175"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.176"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.177"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.178"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.179"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.180"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.181"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.182"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.183"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.184"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.185"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.186"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.187"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.188"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.189"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.190"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.191"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.192"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.193"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.194"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.195"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.196"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.197"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.198"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.199"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.200"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.201"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.202"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.203"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.204"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.205"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.206"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.207"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.208"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.209"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.210"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.212"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.213"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.214"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.215"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.216"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.217"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.218"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.219"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.220"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.221"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.222"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.223"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.224"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.225"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.226"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.227"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.228"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.229"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.230"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.231"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.232"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.233"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.234"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.235"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.236"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.237"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.238"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.239"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.240"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.241"}]} | headers=HTTPHeaderDict({'Content-Length': '8270', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,026 - DEBUG - Finished Request
2025-06-27 19:45:11,026 - INFO - 🔍 [WebDriverWait完成後] 檢測到 70 個編輯按鈕
2025-06-27 19:45:11,026 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:45:11,034 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:11,034 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,034 - DEBUG - Finished Request
2025-06-27 19:45:11,034 - INFO - 🔍 [WebDriverWait完成後] 檢測到 0 個 iframe
2025-06-27 19:45:11,037 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'tag name', 'value': 'iframe'}
2025-06-27 19:45:11,042 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:11,042 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,043 - DEBUG - Finished Request
2025-06-27 19:45:11,043 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:11,048 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:11,048 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,049 - DEBUG - Finished Request
2025-06-27 19:45:11,049 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/title {}
2025-06-27 19:45:11,055 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/title HTTP/1.1" 200 0
2025-06-27 19:45:11,055 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,056 - DEBUG - Finished Request
2025-06-27 19:45:11,056 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//tr[contains(., 'E48B201611405190953')]"}
2025-06-27 19:45:11,066 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:11,066 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157"}]} | headers=HTTPHeaderDict({'Content-Length': '129', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,066 - DEBUG - Finished Request
2025-06-27 19:45:11,067 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157/text {}
2025-06-27 19:45:11,076 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157/text HTTP/1.1" 200 0
2025-06-27 19:45:11,077 - DEBUG - Remote response: status=200 | data={"value":"編輯新增路線 A1本市事廢 暫存 E48B201611405190953 仁 119-BR 一般清運 3.6 2025-07-04 仁武廠 0 一般 取消(刪除)"} | headers=HTTPHeaderDict({'Content-Length': '145', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,077 - DEBUG - Finished Request
2025-06-27 19:45:11,078 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157/elements {'using': 'xpath', 'value': ".//a[contains(text(), '編輯')]"}
2025-06-27 19:45:11,088 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157/elements HTTP/1.1" 200 0
2025-06-27 19:45:11,088 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,089 - DEBUG - Finished Request
2025-06-27 19:45:11,089 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157/elements {'using': 'xpath', 'value': ".//button[contains(text(), '編輯')]"}
2025-06-27 19:45:11,099 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.157/elements HTTP/1.1" 200 0
2025-06-27 19:45:11,099 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.212"}]} | headers=HTTPHeaderDict({'Content-Length': '247', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,100 - DEBUG - Finished Request
2025-06-27 19:45:11,100 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/text {}
2025-06-27 19:45:11,109 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/text HTTP/1.1" 200 0
2025-06-27 19:45:11,109 - DEBUG - Remote response: status=200 | data={"value":"編輯"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,109 - DEBUG - Finished Request
2025-06-27 19:45:11,109 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211'}, 'value']}
2025-06-27 19:45:11,116 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:11,116 - DEBUG - Remote response: status=200 | data={"value":""} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,116 - DEBUG - Finished Request
2025-06-27 19:45:11,117 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* getAttribute */return (function(){return (function(){var d=this||self;function f(a,b){function c(...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211'}, 'class']}
2025-06-27 19:45:11,123 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:11,123 - DEBUG - Remote response: status=200 | data={"value":"btn btn-success"} | headers=HTTPHeaderDict({'Content-Length': '27', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,124 - DEBUG - Finished Request
2025-06-27 19:45:11,124 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/name {}
2025-06-27 19:45:11,130 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/name HTTP/1.1" 200 0
2025-06-27 19:45:11,131 - DEBUG - Remote response: status=200 | data={"value":"button"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,131 - DEBUG - Finished Request
2025-06-27 19:45:11,131 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* isDisplayed */return (function(){return (function(){var g=this||self;\nfunction aa(a){var b=typeof...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211'}]}
2025-06-27 19:45:11,140 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:11,140 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,140 - DEBUG - Finished Request
2025-06-27 19:45:11,141 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/enabled {}
2025-06-27 19:45:11,150 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/enabled HTTP/1.1" 200 0
2025-06-27 19:45:11,150 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,151 - DEBUG - Finished Request
2025-06-27 19:45:11,151 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/click {}
2025-06-27 19:45:11,288 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.211/click HTTP/1.1" 200 0
2025-06-27 19:45:11,288 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,288 - DEBUG - Finished Request
2025-06-27 19:45:11,421 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:11,445 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:11,445 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:11,446 - DEBUG - Finished Request
2025-06-27 19:45:12,447 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:12,456 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:12,456 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:12,457 - DEBUG - Finished Request
2025-06-27 19:45:13,458 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:13,468 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:13,469 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:13,470 - DEBUG - Finished Request
2025-06-27 19:45:14,471 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:14,479 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:14,479 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:14,480 - DEBUG - Finished Request
2025-06-27 19:45:15,480 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:15,488 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:15,489 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:15,489 - DEBUG - Finished Request
2025-06-27 19:45:16,290 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:16,297 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:16,298 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:16,298 - DEBUG - Finished Request
2025-06-27 19:45:16,299 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/title {}
2025-06-27 19:45:16,304 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/title HTTP/1.1" 200 0
2025-06-27 19:45:16,305 - DEBUG - Remote response: status=200 | data={"value":"高雄市廢棄物調度中心"} | headers=HTTPHeaderDict({'Content-Length': '42', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:16,305 - DEBUG - Finished Request
2025-06-27 19:45:16,491 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:16,497 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:16,497 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:16,498 - DEBUG - Finished Request
2025-06-27 19:45:17,499 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:17,508 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:17,508 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:17,508 - DEBUG - Finished Request
2025-06-27 19:45:18,307 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "img[src*='captcha']"}
2025-06-27 19:45:18,320 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,320 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,320 - DEBUG - Finished Request
2025-06-27 19:45:18,321 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "img[src*='verify']"}
2025-06-27 19:45:18,330 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,330 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,331 - DEBUG - Finished Request
2025-06-27 19:45:18,331 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "img[alt*='驗證']"}
2025-06-27 19:45:18,341 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,341 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,341 - DEBUG - Finished Request
2025-06-27 19:45:18,341 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "img[alt*='驗證碼']"}
2025-06-27 19:45:18,350 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,350 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,351 - DEBUG - Finished Request
2025-06-27 19:45:18,351 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//button[contains(text(), '確認取得驗證碼')]"}
2025-06-27 19:45:18,362 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,363 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,363 - DEBUG - Finished Request
2025-06-27 19:45:18,363 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//button[contains(text(), '重新產生')]"}
2025-06-27 19:45:18,372 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,372 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,372 - DEBUG - Finished Request
2025-06-27 19:45:18,373 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'xpath', 'value': "//button[contains(text(), '重新產生')]"}
2025-06-27 19:45:18,379 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:18,380 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,380 - DEBUG - Finished Request
2025-06-27 19:45:18,510 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:18,529 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:18,529 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:18,529 - DEBUG - Finished Request
2025-06-27 19:45:19,530 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:19,538 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:19,538 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:19,538 - DEBUG - Finished Request
2025-06-27 19:45:20,539 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:20,545 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:20,546 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:20,546 - DEBUG - Finished Request
2025-06-27 19:45:21,547 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:21,554 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:21,554 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:21,555 - DEBUG - Finished Request
2025-06-27 19:45:22,555 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:22,563 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:22,563 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:22,564 - DEBUG - Finished Request
2025-06-27 19:45:23,564 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:23,573 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:23,573 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:23,574 - DEBUG - Finished Request
2025-06-27 19:45:24,574 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:24,584 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:24,584 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:24,584 - DEBUG - Finished Request
2025-06-27 19:45:25,584 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:25,592 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:25,592 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:25,592 - DEBUG - Finished Request
2025-06-27 19:45:26,233 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[name*='captcha']"}
2025-06-27 19:45:26,254 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,254 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,254 - DEBUG - Finished Request
2025-06-27 19:45:26,255 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[name*='verify']"}
2025-06-27 19:45:26,264 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,265 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,265 - DEBUG - Finished Request
2025-06-27 19:45:26,265 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[id*='captcha']"}
2025-06-27 19:45:26,277 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,277 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,277 - DEBUG - Finished Request
2025-06-27 19:45:26,277 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[id*='verify']"}
2025-06-27 19:45:26,287 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,287 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,287 - DEBUG - Finished Request
2025-06-27 19:45:26,287 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[placeholder*='驗證']"}
2025-06-27 19:45:26,301 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,301 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,301 - DEBUG - Finished Request
2025-06-27 19:45:26,301 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[placeholder*='驗證碼']"}
2025-06-27 19:45:26,314 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,315 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,315 - DEBUG - Finished Request
2025-06-27 19:45:26,315 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[type='text'][maxlength='4']"}
2025-06-27 19:45:26,326 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,327 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,327 - DEBUG - Finished Request
2025-06-27 19:45:26,327 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[type='text'][maxlength='5']"}
2025-06-27 19:45:26,336 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,337 - DEBUG - Remote response: status=200 | data={"value":[]} | headers=HTTPHeaderDict({'Content-Length': '12', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,337 - DEBUG - Finished Request
2025-06-27 19:45:26,337 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/elements {'using': 'css selector', 'value': "input[type='text']"}
2025-06-27 19:45:26,346 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/elements HTTP/1.1" 200 0
2025-06-27 19:45:26,346 - DEBUG - Remote response: status=200 | data={"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.48"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.49"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.50"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.51"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.52"},{"element-6066-11e4-a52e-4f735466cecf":"f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.53"}]} | headers=HTTPHeaderDict({'Content-Length': '830', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,346 - DEBUG - Finished Request
2025-06-27 19:45:26,346 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync {'script': '/* isDisplayed */return (function(){return (function(){var g=this||self;\nfunction aa(a){var b=typeof...', 'args': [{'element-6066-11e4-a52e-4f735466cecf': 'f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45'}]}
2025-06-27 19:45:26,353 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/execute/sync HTTP/1.1" 200 0
2025-06-27 19:45:26,354 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,354 - DEBUG - Finished Request
2025-06-27 19:45:26,354 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45/enabled {}
2025-06-27 19:45:26,361 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45/enabled HTTP/1.1" 200 0
2025-06-27 19:45:26,362 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,362 - DEBUG - Finished Request
2025-06-27 19:45:26,362 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45/clear {}
2025-06-27 19:45:26,388 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45/clear HTTP/1.1" 200 0
2025-06-27 19:45:26,388 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,389 - DEBUG - Finished Request
2025-06-27 19:45:26,389 - DEBUG - POST http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45/value {'text': '0733', 'value': ['0', '7', '3', '3']}
2025-06-27 19:45:26,427 - DEBUG - http://localhost:56271 "POST /session/1c10e8c63cff957bc9ac906eb0e51689/element/f.E845319C5B0E246F4FB3BBE899F28EC0.d.9C2B8ED2A7EFC90974AADDDB54001283.e.45/value HTTP/1.1" 400 0
2025-06-27 19:45:26,427 - DEBUG - Remote response: status=400 | data={"value":{"error":"element not interactable","message":"element not interactable\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d199fc]\n\t(No symbol) [0x0x7ff7d7d69db2]\n\t(No symbol) [0x0x7ff7d7d67b29]\n\t(No symbol) [0x0x7ff7d7d98b8a]\n\t(No symbol) [0x0x7ff7d7d62f06]\n\t(No symbol) [0x0x7ff7d7d98da0]\n\t(No symbol) [0x0x7ff7d7dc122f]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1039', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,427 - DEBUG - Finished Request
2025-06-27 19:45:26,593 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:26,600 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:26,600 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:26,601 - DEBUG - Finished Request
2025-06-27 19:45:27,602 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:27,607 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:27,608 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:27,608 - DEBUG - Finished Request
2025-06-27 19:45:28,609 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:28,615 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:28,615 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:28,615 - DEBUG - Finished Request
2025-06-27 19:45:29,616 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:29,623 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:29,623 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:29,624 - DEBUG - Finished Request
2025-06-27 19:45:30,625 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:30,633 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:30,633 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:30,633 - DEBUG - Finished Request
2025-06-27 19:45:31,634 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:31,641 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:31,641 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:31,642 - DEBUG - Finished Request
2025-06-27 19:45:32,642 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:32,650 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:32,650 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:32,651 - DEBUG - Finished Request
2025-06-27 19:45:33,651 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:33,658 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:33,658 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:33,659 - DEBUG - Finished Request
2025-06-27 19:45:34,661 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:34,668 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:34,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:34,669 - DEBUG - Finished Request
2025-06-27 19:45:35,670 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:35,678 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:35,678 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:35,678 - DEBUG - Finished Request
2025-06-27 19:45:36,679 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:36,685 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:36,686 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:36,686 - DEBUG - Finished Request
2025-06-27 19:45:37,687 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:37,693 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 200 0
2025-06-27 19:45:37,693 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:37,693 - DEBUG - Finished Request
2025-06-27 19:45:38,694 - DEBUG - GET http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689/url {}
2025-06-27 19:45:38,697 - DEBUG - http://localhost:56271 "GET /session/1c10e8c63cff957bc9ac906eb0e51689/url HTTP/1.1" 404 0
2025-06-27 19:45:38,697 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=137.0.7151.120)","stacktrace":"\tGetHandleVerifier [0x0x7ff7d7f5cda5+78885]\n\tGetHandleVerifier [0x0x7ff7d7f5ce00+78976]\n\t(No symbol) [0x0x7ff7d7d19bca]\n\t(No symbol) [0x0x7ff7d7d059b5]\n\t(No symbol) [0x0x7ff7d7d2a9ca]\n\t(No symbol) [0x0x7ff7d7da05e5]\n\t(No symbol) [0x0x7ff7d7dc0b42]\n\t(No symbol) [0x0x7ff7d7d98963]\n\t(No symbol) [0x0x7ff7d7d616b1]\n\t(No symbol) [0x0x7ff7d7d62443]\n\tGetHandleVerifier [0x0x7ff7d8234eed+3061101]\n\tGetHandleVerifier [0x0x7ff7d822f33d+3037629]\n\tGetHandleVerifier [0x0x7ff7d824e592+3165202]\n\tGetHandleVerifier [0x0x7ff7d7f7730e+186766]\n\tGetHandleVerifier [0x0x7ff7d7f7eb3f+217535]\n\tGetHandleVerifier [0x0x7ff7d7f659b4+114740]\n\tGetHandleVerifier [0x0x7ff7d7f65b69+115177]\n\tGetHandleVerifier [0x0x7ff7d7f4c368+10728]\n\tBaseThreadInitThunk [0x0x7fff8519e8d7+23]\n\tRtlUserThreadStart [0x0x7fff86c7c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1063', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:38,697 - DEBUG - Finished Request
2025-06-27 19:45:38,698 - DEBUG - DELETE http://localhost:56271/session/1c10e8c63cff957bc9ac906eb0e51689 {}
2025-06-27 19:45:38,760 - DEBUG - http://localhost:56271 "DELETE /session/1c10e8c63cff957bc9ac906eb0e51689 HTTP/1.1" 200 0
2025-06-27 19:45:38,760 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-27 19:45:38,760 - DEBUG - Finished Request
