#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強的按鈕檢測功能
Test Enhanced Button Detection Features
"""

import os
import sys
import time
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_features():
    """測試增強的檢測功能"""
    print("🚀 測試增強的按鈕檢測功能")
    print("=" * 60)
    
    try:
        # 導入主程式模組
        from mvp_grabber import (
            setup_browser_event_monitoring,
            get_browser_event_log,
            enhanced_dialog_button_detection,
            monitor_dialog_opening,
            start_browser
        )
        
        print("✅ 成功導入所有增強功能模組")
        
        # 測試任務配置
        test_task = {
            'browser': 'chrome',
            'order_id': 'E48B201611406250521',
            'trigger_time': '09:30:00.001',
            'model': 'A'
        }
        
        print(f"📋 測試任務: {test_task}")
        
        # 啟動瀏覽器
        print("\n🌐 啟動瀏覽器...")
        if start_browser(test_task):
            print("✅ 瀏覽器啟動成功")
            
            # 等待用戶手動導航到編輯頁面
            print("\n📋 請手動執行以下步驟:")
            print("1. 登入系統")
            print("2. 導航到進廠確認單列表")
            print("3. 點擊任一編輯按鈕打開編輯彈窗")
            print("4. 完成後在此控制台按 Enter 繼續測試...")
            print("\n⚠️ 重要：請不要關閉瀏覽器，只需按 Enter 繼續測試")

            try:
                input("按 Enter 繼續...")

                # 檢查瀏覽器是否還在運行
                from mvp_grabber import driver
                if driver is None:
                    print("❌ 瀏覽器已關閉，無法繼續測試")
                    return False

                # 測試增強的按鈕檢測
                print("\n🔍 測試增強的按鈕檢測...")
                detection_result = enhanced_dialog_button_detection()

            except Exception as e:
                print(f"❌ 測試過程中發生錯誤: {e}")
                print("可能的原因：瀏覽器已關閉或網路連接問題")
                return False
            
            if detection_result:
                print("✅ 按鈕檢測成功")
                print(f"📊 檢測結果:")
                print(f"  - 送出按鈕: {len(detection_result.get('submit_buttons', []))} 個")
                print(f"  - 取消按鈕: {len(detection_result.get('cancel_buttons', []))} 個")
                print(f"  - 其他按鈕: {len(detection_result.get('other_buttons', []))} 個")
                print(f"  - 彈窗容器: {len(detection_result.get('dialog_containers', []))} 個")
                print(f"  - 按鈕容器: {len(detection_result.get('button_containers', []))} 個")
                print(f"  - 找到標題: {detection_result.get('title_found', False)}")
                
                if detection_result.get('screenshot_path'):
                    print(f"  - 截圖路徑: {detection_result['screenshot_path']}")
            else:
                print("❌ 按鈕檢測失敗")
            
            # 測試事件監控
            print("\n📊 測試事件監控...")
            event_log = get_browser_event_log()
            
            if event_log:
                print(f"✅ 獲取到 {len(event_log)} 個事件")
                
                # 分析事件類型
                event_types = {}
                for event in event_log:
                    event_type = event.get('type', 'unknown')
                    event_types[event_type] = event_types.get(event_type, 0) + 1
                
                print("📋 事件類型統計:")
                for event_type, count in event_types.items():
                    print(f"  - {event_type}: {count} 個")
                
                # 顯示最近的事件
                print("\n📋 最近的事件:")
                recent_events = event_log[-5:]  # 最近5個事件
                for i, event in enumerate(recent_events):
                    timestamp = event.get('timestamp', 'N/A')
                    event_type = event.get('type', 'N/A')
                    target = event.get('target', {})
                    target_info = f"{target.get('tagName', 'N/A')}"
                    if target.get('textContent'):
                        target_info += f" - '{target['textContent'][:30]}...'"
                    print(f"  {len(event_log)-len(recent_events)+i+1}. [{timestamp}] {event_type}: {target_info}")
            else:
                print("❌ 未獲取到事件日誌")
            
            print("\n🎯 測試完成！")
            print("瀏覽器將保持開啟，您可以繼續手動測試...")
            
        else:
            print("❌ 瀏覽器啟動失敗")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        print("請確保所有必要的模組都已正確安裝")
        return False
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        return False
    
    return True

def main():
    """主函數"""
    print("🧪 AGES-KH-Bot 增強功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        success = test_enhanced_features()
        
        if success:
            print("\n✅ 所有測試完成")
        else:
            print("\n❌ 測試失敗")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷測試")
    except Exception as e:
        print(f"\n❌ 測試過程發生未預期錯誤: {e}")
    
    print("\n測試結束")

if __name__ == "__main__":
    main()
