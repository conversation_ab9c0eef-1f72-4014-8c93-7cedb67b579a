2025-07-02 09:03:26,339 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250702_090326.log
2025-07-02 09:03:34,358 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-02 09:03:34,358 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-02 09:03:35,922 - DEBUG - Sending stats to Plausible: Props { browser: "chrome", browser_version: "", os: "windows", arch: "amd64", lang: "python", selenium_version: "4.33" }
2025-07-02 09:03:35,922 - DEBUG - chromedriver not found in PATH
2025-07-02 09:03:35,922 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:03:35,922 - DEBUG - Detected browser: chrome 138.0.7204.96
2025-07-02 09:03:35,922 - DEBUG - Discovering versions from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
2025-07-02 09:03:35,922 - DEBUG - Required driver: chromedriver 138.0.7204.92
2025-07-02 09:03:35,923 - DEBUG - Acquiring lock: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\sm.lock
2025-07-02 09:03:35,923 - DEBUG - Downloading chromedriver 138.0.7204.92 from https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.92/win64/chromedriver-win64.zip
2025-07-02 09:03:35,923 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe
2025-07-02 09:03:35,923 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-02 09:03:37,149 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\138.0.7204.92\chromedriver.exe` in a child process with pid: 23792 using 0 to output -3
2025-07-02 09:03:37,659 - DEBUG - POST http://localhost:53419/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-02 09:03:37,659 - DEBUG - Starting new HTTP connection (1): localhost:53419
2025-07-02 09:03:38,190 - DEBUG - http://localhost:53419 "POST /session HTTP/1.1" 200 0
2025-07-02 09:03:38,190 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"138.0.7204.96","chrome":{"chromedriverVersion":"138.0.7204.92 (f079b9bc781e3c2adb1496ea1d72812deb0ddb3d-refs/branch-heads/7204_50@{#8})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir23792_90815883"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53426"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"6151dfd199479e6782539c73693e3c3e"}} | headers=HTTPHeaderDict({'Content-Length': '880', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:38,191 - DEBUG - Finished Request
2025-07-02 09:03:38,192 - DEBUG - POST http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-02 09:03:39,560 - DEBUG - http://localhost:53419 "POST /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:39,560 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:39,560 - DEBUG - Finished Request
2025-07-02 09:03:39,561 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-02 09:03:39,561 - DEBUG - POST http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-02 09:03:39,567 - DEBUG - http://localhost:53419 "POST /session/6151dfd199479e6782539c73693e3c3e/execute/sync HTTP/1.1" 200 0
2025-07-02 09:03:39,567 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:39,567 - DEBUG - Finished Request
2025-07-02 09:03:39,568 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-02 09:03:39,568 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:39,597 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:39,597 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:39,597 - DEBUG - Finished Request
2025-07-02 09:03:40,598 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:40,605 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:40,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:40,605 - DEBUG - Finished Request
2025-07-02 09:03:41,606 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:41,613 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:41,613 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:41,613 - DEBUG - Finished Request
2025-07-02 09:03:42,614 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:42,622 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:42,622 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:42,622 - DEBUG - Finished Request
2025-07-02 09:03:43,623 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:43,628 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:43,628 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:43,629 - DEBUG - Finished Request
2025-07-02 09:03:44,630 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:44,637 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:44,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:44,637 - DEBUG - Finished Request
2025-07-02 09:03:45,638 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:45,645 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:45,645 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:45,645 - DEBUG - Finished Request
2025-07-02 09:03:46,646 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:46,653 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:46,653 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:46,653 - DEBUG - Finished Request
2025-07-02 09:03:47,655 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:47,664 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:47,664 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:47,664 - DEBUG - Finished Request
2025-07-02 09:03:48,666 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:48,673 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:48,673 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:48,673 - DEBUG - Finished Request
2025-07-02 09:03:49,674 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:49,683 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:49,683 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:49,684 - DEBUG - Finished Request
2025-07-02 09:03:50,684 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:50,693 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:50,693 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:50,694 - DEBUG - Finished Request
2025-07-02 09:03:51,695 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:51,703 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:51,704 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:51,704 - DEBUG - Finished Request
2025-07-02 09:03:52,705 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:52,714 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:52,714 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:52,714 - DEBUG - Finished Request
2025-07-02 09:03:53,715 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:53,723 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:53,724 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:53,724 - DEBUG - Finished Request
2025-07-02 09:03:54,725 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:54,733 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:54,733 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:54,734 - DEBUG - Finished Request
2025-07-02 09:03:55,734 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:55,742 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:55,743 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:55,743 - DEBUG - Finished Request
2025-07-02 09:03:56,745 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:56,754 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:56,754 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:56,755 - DEBUG - Finished Request
2025-07-02 09:03:57,756 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:57,764 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:57,764 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:57,764 - DEBUG - Finished Request
2025-07-02 09:03:58,765 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:58,774 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:58,775 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:58,775 - DEBUG - Finished Request
2025-07-02 09:03:59,776 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:03:59,783 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:03:59,784 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:03:59,784 - DEBUG - Finished Request
2025-07-02 09:04:00,785 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:00,804 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:00,805 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:00,805 - DEBUG - Finished Request
2025-07-02 09:04:01,807 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:01,813 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:01,813 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:01,814 - DEBUG - Finished Request
2025-07-02 09:04:02,815 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:02,822 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:02,822 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:02,823 - DEBUG - Finished Request
2025-07-02 09:04:03,824 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:03,830 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:03,831 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:03,831 - DEBUG - Finished Request
2025-07-02 09:04:04,833 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:04,839 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:04,839 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:04,839 - DEBUG - Finished Request
2025-07-02 09:04:05,840 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:05,848 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:05,848 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:05,848 - DEBUG - Finished Request
2025-07-02 09:04:06,849 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:06,855 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:06,855 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:06,856 - DEBUG - Finished Request
2025-07-02 09:04:07,856 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:07,863 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:07,863 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:07,864 - DEBUG - Finished Request
2025-07-02 09:04:08,865 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:08,873 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:08,873 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:08,873 - DEBUG - Finished Request
2025-07-02 09:04:09,875 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:09,881 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:09,882 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:09,882 - DEBUG - Finished Request
2025-07-02 09:04:10,883 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:10,889 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:10,889 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:10,890 - DEBUG - Finished Request
2025-07-02 09:04:11,890 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:11,896 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:11,896 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:11,896 - DEBUG - Finished Request
2025-07-02 09:04:12,897 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:12,905 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:12,905 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:12,905 - DEBUG - Finished Request
2025-07-02 09:04:13,906 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:13,913 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:13,913 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:13,913 - DEBUG - Finished Request
2025-07-02 09:04:14,915 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:14,922 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:14,923 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:14,923 - DEBUG - Finished Request
2025-07-02 09:04:15,924 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:15,930 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:15,931 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:15,932 - DEBUG - Finished Request
2025-07-02 09:04:16,933 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:16,939 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:16,939 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:16,940 - DEBUG - Finished Request
2025-07-02 09:04:17,941 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:17,947 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:17,948 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:17,948 - DEBUG - Finished Request
2025-07-02 09:04:18,949 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:18,956 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:18,956 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:18,956 - DEBUG - Finished Request
2025-07-02 09:04:19,957 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:19,964 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:19,964 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:19,964 - DEBUG - Finished Request
2025-07-02 09:04:20,965 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:20,970 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:20,971 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:20,971 - DEBUG - Finished Request
2025-07-02 09:04:21,972 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:21,978 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:21,978 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:21,979 - DEBUG - Finished Request
2025-07-02 09:04:22,980 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:22,986 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:22,987 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:22,987 - DEBUG - Finished Request
2025-07-02 09:04:23,987 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:23,995 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:23,995 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:23,995 - DEBUG - Finished Request
2025-07-02 09:04:24,996 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:25,003 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:25,003 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:25,004 - DEBUG - Finished Request
2025-07-02 09:04:26,004 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:26,012 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:26,012 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:26,013 - DEBUG - Finished Request
2025-07-02 09:04:27,014 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:27,022 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:27,022 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:27,022 - DEBUG - Finished Request
2025-07-02 09:04:28,023 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:28,029 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:28,029 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:28,030 - DEBUG - Finished Request
2025-07-02 09:04:29,030 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:29,554 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:29,554 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:29,555 - DEBUG - Finished Request
2025-07-02 09:04:30,556 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:30,562 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:30,563 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:30,563 - DEBUG - Finished Request
2025-07-02 09:04:31,564 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:31,573 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:31,573 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:31,573 - DEBUG - Finished Request
2025-07-02 09:04:32,575 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:32,581 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:32,582 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:32,582 - DEBUG - Finished Request
2025-07-02 09:04:33,583 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:33,589 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:33,590 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:33,590 - DEBUG - Finished Request
2025-07-02 09:04:34,591 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:34,597 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:34,597 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:34,598 - DEBUG - Finished Request
2025-07-02 09:04:35,598 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:35,605 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:35,605 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:35,606 - DEBUG - Finished Request
2025-07-02 09:04:36,606 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:36,613 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:36,613 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:36,614 - DEBUG - Finished Request
2025-07-02 09:04:37,615 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:37,620 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:37,621 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:37,622 - DEBUG - Finished Request
2025-07-02 09:04:38,623 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:38,630 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:38,630 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:38,630 - DEBUG - Finished Request
2025-07-02 09:04:39,631 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:39,637 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:39,637 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:39,637 - DEBUG - Finished Request
2025-07-02 09:04:40,638 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:40,644 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:40,645 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:40,646 - DEBUG - Finished Request
2025-07-02 09:04:41,646 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:41,653 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:41,654 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:41,654 - DEBUG - Finished Request
2025-07-02 09:04:42,656 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:42,662 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:42,662 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:42,662 - DEBUG - Finished Request
2025-07-02 09:04:43,664 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:43,669 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:43,669 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:43,669 - DEBUG - Finished Request
2025-07-02 09:04:44,670 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:44,676 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:44,676 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:44,677 - DEBUG - Finished Request
2025-07-02 09:04:45,678 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:45,684 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:45,684 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:45,685 - DEBUG - Finished Request
2025-07-02 09:04:46,686 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:46,692 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:46,693 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:46,693 - DEBUG - Finished Request
2025-07-02 09:04:47,693 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:47,700 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:47,700 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:47,700 - DEBUG - Finished Request
2025-07-02 09:04:48,702 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:48,708 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:48,709 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:48,709 - DEBUG - Finished Request
2025-07-02 09:04:49,710 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:49,716 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:49,717 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:49,717 - DEBUG - Finished Request
2025-07-02 09:04:50,718 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:50,726 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:50,727 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:50,727 - DEBUG - Finished Request
2025-07-02 09:04:51,728 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:51,734 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:51,734 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:51,734 - DEBUG - Finished Request
2025-07-02 09:04:52,735 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:52,741 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:52,741 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:52,742 - DEBUG - Finished Request
2025-07-02 09:04:53,743 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:53,748 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:53,748 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:53,749 - DEBUG - Finished Request
2025-07-02 09:04:54,751 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:54,757 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:54,757 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:54,758 - DEBUG - Finished Request
2025-07-02 09:04:55,759 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:55,765 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:55,766 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:55,766 - DEBUG - Finished Request
2025-07-02 09:04:56,767 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:56,774 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:56,774 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:56,774 - DEBUG - Finished Request
2025-07-02 09:04:57,775 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:57,781 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:57,781 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:57,782 - DEBUG - Finished Request
2025-07-02 09:04:58,783 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:58,789 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:58,789 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:58,789 - DEBUG - Finished Request
2025-07-02 09:04:59,790 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:04:59,797 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:04:59,798 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:04:59,798 - DEBUG - Finished Request
2025-07-02 09:05:00,799 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:00,807 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:00,807 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:00,807 - DEBUG - Finished Request
2025-07-02 09:05:01,808 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:01,814 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:01,815 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:01,815 - DEBUG - Finished Request
2025-07-02 09:05:02,816 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:02,824 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:02,825 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:02,825 - DEBUG - Finished Request
2025-07-02 09:05:03,825 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:03,832 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:03,832 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:03,832 - DEBUG - Finished Request
2025-07-02 09:05:04,833 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:04,841 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:04,841 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:04,841 - DEBUG - Finished Request
2025-07-02 09:05:05,842 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:05,850 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:05,850 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:05,850 - DEBUG - Finished Request
2025-07-02 09:05:06,850 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:06,857 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:06,857 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:06,857 - DEBUG - Finished Request
2025-07-02 09:05:07,858 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:07,866 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:07,866 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:07,867 - DEBUG - Finished Request
2025-07-02 09:05:08,867 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:08,876 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:08,876 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:08,877 - DEBUG - Finished Request
2025-07-02 09:05:09,877 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:09,885 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:09,885 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:09,885 - DEBUG - Finished Request
2025-07-02 09:05:10,887 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:10,894 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:10,894 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:10,895 - DEBUG - Finished Request
2025-07-02 09:05:11,895 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:11,903 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:11,904 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:11,904 - DEBUG - Finished Request
2025-07-02 09:05:12,905 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:12,911 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:12,911 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:12,911 - DEBUG - Finished Request
2025-07-02 09:05:13,912 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:13,918 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:13,918 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:13,918 - DEBUG - Finished Request
2025-07-02 09:05:14,919 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:14,925 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:14,925 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:14,925 - DEBUG - Finished Request
2025-07-02 09:05:15,926 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:15,935 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:15,935 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:15,935 - DEBUG - Finished Request
2025-07-02 09:05:16,936 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:16,943 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:16,943 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:16,943 - DEBUG - Finished Request
2025-07-02 09:05:17,944 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:17,953 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:17,953 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:17,953 - DEBUG - Finished Request
2025-07-02 09:05:18,955 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:18,963 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:18,963 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:18,963 - DEBUG - Finished Request
2025-07-02 09:05:19,965 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:19,974 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:19,975 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:19,975 - DEBUG - Finished Request
2025-07-02 09:05:20,975 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:20,984 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:20,984 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:20,984 - DEBUG - Finished Request
2025-07-02 09:05:21,986 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:21,994 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:21,994 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:21,995 - DEBUG - Finished Request
2025-07-02 09:05:22,996 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:23,003 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:23,004 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:23,004 - DEBUG - Finished Request
2025-07-02 09:05:24,004 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:24,012 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:24,013 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:24,013 - DEBUG - Finished Request
2025-07-02 09:05:25,014 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:25,025 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:25,025 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:25,026 - DEBUG - Finished Request
2025-07-02 09:05:26,028 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:26,037 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:26,037 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:26,038 - DEBUG - Finished Request
2025-07-02 09:05:27,038 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:27,046 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:27,046 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:27,046 - DEBUG - Finished Request
2025-07-02 09:05:28,047 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:28,054 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:28,055 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:28,055 - DEBUG - Finished Request
2025-07-02 09:05:29,055 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:29,063 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:29,064 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:29,064 - DEBUG - Finished Request
2025-07-02 09:05:30,065 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:30,074 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:30,075 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:30,075 - DEBUG - Finished Request
2025-07-02 09:05:31,076 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:31,083 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 200 0
2025-07-02 09:05:31,084 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/"} | headers=HTTPHeaderDict({'Content-Length': '35', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:31,084 - DEBUG - Finished Request
2025-07-02 09:05:32,086 - DEBUG - GET http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e/url {}
2025-07-02 09:05:32,087 - DEBUG - http://localhost:53419 "GET /session/6151dfd199479e6782539c73693e3c3e/url HTTP/1.1" 404 0
2025-07-02 09:05:32,088 - DEBUG - Remote response: status=404 | data={"value":{"error":"invalid session id","message":"invalid session id: session deleted as the browser has closed the connection\nfrom disconnected: not connected to DevTools\n  (Session info: chrome=138.0.7204.96)","stacktrace":"\tGetHandleVerifier [0x0x7ff668b86f95+76917]\n\tGetHandleVerifier [0x0x7ff668b86ff0+77008]\n\t(No symbol) [0x0x7ff668939dea]\n\t(No symbol) [0x0x7ff668925f15]\n\t(No symbol) [0x0x7ff66894abf4]\n\t(No symbol) [0x0x7ff6689bfa85]\n\t(No symbol) [0x0x7ff6689dff72]\n\t(No symbol) [0x0x7ff6689b8243]\n\t(No symbol) [0x0x7ff668981431]\n\t(No symbol) [0x0x7ff6689821c3]\n\tGetHandleVerifier [0x0x7ff668e5d2cd+3051437]\n\tGetHandleVerifier [0x0x7ff668e57923+3028483]\n\tGetHandleVerifier [0x0x7ff668e758bd+3151261]\n\tGetHandleVerifier [0x0x7ff668ba185e+185662]\n\tGetHandleVerifier [0x0x7ff668ba971f+218111]\n\tGetHandleVerifier [0x0x7ff668b8fb14+112628]\n\tGetHandleVerifier [0x0x7ff668b8fcc9+113065]\n\tGetHandleVerifier [0x0x7ff668b76c98+10616]\n\tBaseThreadInitThunk [0x0x7ffcd5f9e8d7+23]\n\tRtlUserThreadStart [0x0x7ffcd727c34c+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1062', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:32,088 - DEBUG - Finished Request
2025-07-02 09:05:32,089 - DEBUG - DELETE http://localhost:53419/session/6151dfd199479e6782539c73693e3c3e {}
2025-07-02 09:05:32,162 - DEBUG - http://localhost:53419 "DELETE /session/6151dfd199479e6782539c73693e3c3e HTTP/1.1" 200 0
2025-07-02 09:05:32,162 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-02 09:05:32,162 - DEBUG - Finished Request
