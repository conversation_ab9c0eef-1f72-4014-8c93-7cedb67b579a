# DOM Inspector Tool for AGES-KH
# 用於安全地探測和記錄網頁元素，不會觸發實際搶單動作
# Version: 1.1 - 增加錯誤處理和環境檢查
# Author: <PERSON> <PERSON>

import time
import os
import sys
from datetime import datetime
import json

# 檢查並安裝必要套件
def check_dependencies():
    """檢查必要的套件是否已安裝"""
    missing_packages = []

    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.common.exceptions import TimeoutException, NoSuchElementException
    except ImportError:
        missing_packages.append("selenium")

    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from webdriver_manager.firefox import GeckoDriverManager
        from webdriver_manager.microsoft import EdgeChromiumDriverManager
    except ImportError:
        missing_packages.append("webdriver-manager")

    if missing_packages:
        print(f"[ERROR] 缺少必要套件: {', '.join(missing_packages)}")
        print("[INFO] 請執行以下命令安裝：")
        print("pip install selenium webdriver-manager")
        return False

    return True

if not check_dependencies():
    sys.exit(1)

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService

# webdriver-manager imports
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

class DOMInspector:
    def __init__(self):
        self.driver = None
        self.elements_found = {}
        self.browser_type = None

    def setup_browser(self, browser_type="chrome", debug_mode=True):
        """設定瀏覽器，支援 chrome、firefox、edge"""
        self.browser_type = browser_type.lower()

        try:
            if self.browser_type == "chrome":
                return self._setup_chrome(debug_mode)
            elif self.browser_type == "firefox":
                return self._setup_firefox(debug_mode)
            elif self.browser_type == "edge":
                return self._setup_edge(debug_mode)
            else:
                print(f"[ERROR] 不支援的瀏覽器類型: {browser_type}")
                print("[INFO] 支援的瀏覽器: chrome, firefox, edge")
                return False

        except Exception as e:
            print(f"[ERROR] 設定瀏覽器時發生錯誤: {e}")
            return False

    def _setup_chrome(self, debug_mode):
        """設定 Chrome 瀏覽器"""
        try:
            chrome_options = webdriver.ChromeOptions()

            if debug_mode:
                chrome_options.add_argument('--start-maximized')
            else:
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')

            # 使用 webdriver-manager 自動管理 ChromeDriver
            service = ChromeService(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            print(f"[INFO] Chrome 瀏覽器已啟動 (Debug模式: {debug_mode})")
            return True

        except Exception as e:
            print(f"[ERROR] Chrome 啟動失敗: {e}")
            return False

    def _setup_firefox(self, debug_mode):
        """設定 Firefox 瀏覽器"""
        try:
            firefox_options = webdriver.FirefoxOptions()

            if debug_mode:
                firefox_options.add_argument('--width=1200')
                firefox_options.add_argument('--height=800')
            else:
                # Firefox 防偵測設定
                firefox_options.set_preference("dom.webdriver.enabled", False)
                firefox_options.set_preference('useAutomationExtension', False)

            # 使用 webdriver-manager 自動管理 GeckoDriver
            service = FirefoxService(GeckoDriverManager().install())
            self.driver = webdriver.Firefox(service=service, options=firefox_options)

            print(f"[INFO] Firefox 瀏覽器已啟動 (Debug模式: {debug_mode})")
            return True

        except Exception as e:
            print(f"[ERROR] Firefox 啟動失敗: {e}")
            return False

    def _setup_edge(self, debug_mode):
        """設定 Edge 瀏覽器"""
        try:
            edge_options = webdriver.EdgeOptions()

            if debug_mode:
                edge_options.add_argument('--start-maximized')
            else:
                edge_options.add_argument('--disable-blink-features=AutomationControlled')
                edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')

            # 使用 webdriver-manager 自動管理 EdgeDriver
            service = EdgeService(EdgeChromiumDriverManager().install())
            self.driver = webdriver.Edge(service=service, options=edge_options)

            print(f"[INFO] Edge 瀏覽器已啟動 (Debug模式: {debug_mode})")
            return True

        except Exception as e:
            print(f"[ERROR] Edge 啟動失敗: {e}")
            return False
        
    def navigate_to_platform(self):
        """導航到平台首頁"""
        try:
            self.driver.get("https://wmc.kcg.gov.tw/")
            print("[INFO] 已導航到平台首頁")
            return True
        except Exception as e:
            print(f"[ERROR] 導航失敗: {e}")
            return False
            
    def wait_for_manual_login(self):
        """等待使用者手動登入"""
        print("\n" + "="*50)
        print("請手動完成以下操作：")
        print("1. 登入您的帳號")
        print("2. 輸入登入驗證碼")
        print("3. 導航到搶單頁面")
        print("4. 完成所有操作後，可以關閉瀏覽器")
        print("5. 然後回到終端機按 Enter 繼續...")
        print("="*50)

        input()

        # 檢查瀏覽器狀態
        try:
            current_url = self.driver.current_url
            print(f"[INFO] 當前頁面: {current_url}")
            print(f"[INFO] 瀏覽器仍在運行，將進行元素掃描")
            return True
        except Exception as e:
            print(f"[INFO] 偵測到瀏覽器已關閉")
            print(f"[INFO] 假設您已完成所有操作")
            return False
        
    def find_order_elements(self, driver=None, order_id=""):
        """尋找訂單相關的元素"""
        elements = {}

        # 使用傳入的 driver 或者類的 driver
        active_driver = driver if driver else self.driver

        if not active_driver:
            print("[ERROR] 沒有可用的瀏覽器 driver")
            return elements

        # 檢查瀏覽器連線
        try:
            current_url = active_driver.current_url
            print(f"[INFO] 正在掃描頁面: {current_url}")
        except Exception as e:
            print(f"[ERROR] 瀏覽器連線已中斷: {e}")
            return elements

        # 根據用戶反饋更新的元素選擇器
        selectors_to_try = {
            'edit_button': [
                # 基於用戶截圖，編輯按鈕在表格行中
                'a:contains("編輯")',
                'button:contains("編輯")',
                'td a:contains("編輯")',
                'tr a:contains("編輯")',
                '.btn-edit',
                'a[href*="edit"]'
            ],
            'captcha_input': [
                # 驗證碼輸入框
                'input[name*="captcha"]',
                'input[id*="captcha"]',
                'input[placeholder*="驗證"]',
                'input[placeholder*="驗證碼"]',
                'input[name*="code"]',
                'input[name*="verify"]',
                'input[type="text"][maxlength="4"]',
                'input[type="text"][maxlength="5"]'
            ],
            'captcha_image': [
                # 驗證碼圖片
                'img[src*="captcha"]',
                'img[src*="verify"]',
                'img[alt*="驗證"]',
                'img[alt*="驗證碼"]'
            ],
            'captcha_refresh': [
                # 重新產生驗證碼按鈕
                'button:contains("確認取得驗證碼")',
                'button:contains("重新產生")',
                'a:contains("重新產生")',
                '.captcha-refresh'
            ],
            'submit_button': [
                # 送出按鈕 - 基於用戶截圖
                'button:contains("送出")',
                'input[value="送出"]',
                'input[type="submit"]',
                'button[type="submit"]',
                '.btn-submit'
            ],
            'cancel_button': [
                # 取消按鈕
                'button:contains("取消")',
                'input[value="取消"]',
                '.btn-cancel'
            ],
            'order_table': [
                # 訂單表格
                'table',
                '.table',
                'tbody',
                'tr'
            ]
        }

        for element_name, selectors in selectors_to_try.items():
            print(f"[INFO] 尋找 {element_name}...")

            # 檢查瀏覽器是否還在運行
            try:
                active_driver.current_url
            except Exception as e:
                print(f"[ERROR] 瀏覽器在掃描過程中斷開: {e}")
                break

            for selector in selectors:
                try:
                    if ':contains(' in selector:
                        # 使用 XPath 處理 contains 文字
                        text_content = selector.split('"')[1]
                        xpath = f"//*[contains(text(), '{text_content}')]"
                        elements_found = active_driver.find_elements(By.XPATH, xpath)
                        if elements_found:
                            element = elements_found[0]  # 取第一個找到的元素
                        else:
                            continue
                    else:
                        elements_found = active_driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements_found:
                            element = elements_found[0]  # 取第一個找到的元素
                        else:
                            continue

                    elements[element_name] = {
                        'selector': selector,
                        'tag': element.tag_name,
                        'text': element.text[:50] if element.text else '',
                        'id': element.get_attribute('id') or '',
                        'class': element.get_attribute('class') or '',
                        'name': element.get_attribute('name') or '',
                        'count': len(elements_found)  # 記錄找到的元素數量
                    }
                    print(f"  ✅ 找到 {len(elements_found)} 個 {element_name}: {selector}")
                    break
                except Exception as e:
                    print(f"  ⚠️  掃描 {selector} 時發生錯誤: {e}")
                    continue

            if element_name not in elements:
                print(f"  ❌ 未找到 {element_name}")

        return elements
        
    def save_elements_config(self, elements):
        """儲存找到的元素配置，包含時間標籤"""
        timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")

        config = {
            'scan_info': {
                'timestamp': timestamp.isoformat(),
                'date': timestamp.strftime("%Y-%m-%d"),
                'time': timestamp.strftime("%H:%M:%S"),
                'browser_type': getattr(self, 'browser_type', 'unknown'),
                'url': 'browser_closed_before_scan',
                'total_elements_found': len(elements)
            },
            'elements': elements
        }

        # 主配置檔（最新版本）
        main_filename = 'dom_elements_config.json'

        # 帶時間標籤的備份檔
        backup_filename = f'dom_elements_config_{timestamp_str}.json'

        try:
            # 儲存主配置檔
            with open(main_filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 儲存備份檔
            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"\n[INFO] 元素配置已儲存:")
            print(f"  - 主檔案: {main_filename}")
            print(f"  - 備份檔: {backup_filename}")

            # 檢查是否有舊版本可以比較
            self._compare_with_previous_scan(config)

        except Exception as e:
            print(f"[ERROR] 儲存配置失敗: {e}")

    def _compare_with_previous_scan(self, current_config):
        """比較與之前掃描的差異"""
        try:
            import glob

            # 尋找之前的掃描檔案
            previous_files = glob.glob('dom_elements_config_*.json')
            if len(previous_files) <= 1:  # 只有當前檔案
                print(f"[INFO] 這是第一次掃描，無法比較差異")
                return

            # 取得最新的前一個檔案
            previous_files.sort()
            previous_file = previous_files[-2]  # 倒數第二個（最新的是剛儲存的）

            with open(previous_file, 'r', encoding='utf-8') as f:
                previous_config = json.load(f)

            # 比較元素數量
            current_count = current_config['scan_info']['total_elements_found']
            previous_count = previous_config['scan_info']['total_elements_found']

            print(f"\n[INFO] DOM 變化分析:")
            print(f"  - 上次掃描: {previous_config['scan_info']['timestamp']}")
            print(f"  - 上次元素數: {previous_count}")
            print(f"  - 本次元素數: {current_count}")
            print(f"  - 差異: {current_count - previous_count:+d}")

            # 比較具體元素
            current_elements = set(current_config['elements'].keys())
            previous_elements = set(previous_config['elements'].keys())

            new_elements = current_elements - previous_elements
            missing_elements = previous_elements - current_elements

            if new_elements:
                print(f"  - 新增元素: {list(new_elements)}")
            if missing_elements:
                print(f"  - 消失元素: {list(missing_elements)}")
            if not new_elements and not missing_elements:
                print(f"  - ✅ 元素結構無變化")

        except Exception as e:
            print(f"[WARNING] 比較差異時發生錯誤: {e}")
        
    def interactive_element_finder(self):
        """互動式元素尋找器，支援瀏覽器關閉檢測"""
        print("\n" + "="*50)
        print("互動式元素尋找器")
        print("請按照提示操作，我們將一步步找到所需的元素")
        print("="*50)

        # 等待手動登入
        browser_still_running = self.wait_for_manual_login()

        if browser_still_running:
            # 瀏覽器還在運行，進行正常掃描
            print("[INFO] 開始掃描頁面元素...")
            elements = self.find_order_elements()

            # 顯示結果
            self._display_scan_results(elements)

            # 儲存配置
            self.save_elements_config(elements)

            return elements
        else:
            # 瀏覽器已關閉，詢問使用者並建立備用配置
            return self._handle_browser_closed_after_completion()

    def _handle_browser_closed_after_completion(self):
        """處理使用者完成操作後關閉瀏覽器的情況"""
        print("\n" + "="*60)
        print("✅ 偵測到瀏覽器已關閉")
        print("="*60)

        while True:
            response = input("請確認您是否已完成所有手動操作？(y/n): ").strip().lower()

            if response in ['y', 'yes', '是', 'Y']:
                print("\n[INFO] 確認已完成操作，建立基本配置檔...")

                # 建立基本配置檔
                elements = self._create_fallback_config()

                print("[INFO] 已建立基本配置檔")
                self._display_scan_results(elements)
                self.save_elements_config(elements)

                return elements

            elif response in ['n', 'no', '否', 'N']:
                print("\n[INFO] 請重新執行程式並完成所有操作")
                print("建議：下次請在瀏覽器中完成所有操作後再關閉")
                return {}
            else:
                print("請輸入 y (是) 或 n (否)")

    def _handle_browser_closed(self):
        """處理瀏覽器意外關閉的情況（保留原方法以防其他地方調用）"""
        return self._handle_browser_closed_after_completion()

    def _create_fallback_config(self):
        """建立備用配置（當瀏覽器關閉時）"""
        print("[INFO] 建立基本元素配置...")

        # 詢問使用者在操作過程中觀察到的元素
        print("\n請協助提供您在操作過程中觀察到的資訊：")

        # 基本的元素配置模板
        fallback_elements = {
            'order_input': {
                'selector': 'input[name*="order"]',
                'tag': 'input',
                'text': '',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'search_button': {
                'selector': 'button[type="submit"]',
                'tag': 'button',
                'text': '查詢',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'edit_button': {
                'selector': 'a:contains("編輯")',
                'tag': 'a',
                'text': '編輯',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'captcha_input': {
                'selector': 'input[name*="captcha"]',
                'tag': 'input',
                'text': '',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            },
            'submit_button': {
                'selector': 'input[type="submit"]',
                'tag': 'input',
                'text': '送出',
                'id': '',
                'class': '',
                'name': '',
                'note': '基於常見模式的推測，需要實際測試驗證',
                'confidence': 'low'
            }
        }

        print(f"[INFO] 已建立 {len(fallback_elements)} 個基本元素配置")
        print("[WARNING] 這些配置基於常見模式推測，實際使用前需要驗證")

        return fallback_elements

    def _display_scan_results(self, elements):
        """顯示掃描結果"""
        print("\n" + "="*30 + " 發現的元素 " + "="*30)

        if not elements:
            print("❌ 未找到任何元素")
            return

        for name, info in elements.items():
            print(f"\n✅ {name}:")
            print(f"  選擇器: {info['selector']}")
            print(f"  標籤: {info['tag']}")
            print(f"  文字: {info['text']}")
            print(f"  ID: {info['id']}")
            print(f"  Class: {info['class']}")
            if 'note' in info:
                print(f"  備註: {info['note']}")

        print(f"\n📊 總計找到 {len(elements)} 個元素")
        
    def cleanup(self):
        """清理資源"""
        if self.driver:
            print("\n[INFO] 關閉瀏覽器...")
            self.driver.quit()

def choose_browser():
    """讓使用者選擇瀏覽器"""
    print("\n請選擇要使用的瀏覽器：")
    print("1. Chrome (推薦)")
    print("2. Firefox")
    print("3. Edge")

    while True:
        choice = input("\n請輸入選項 (1-3): ").strip()
        if choice == "1":
            return "chrome"
        elif choice == "2":
            return "firefox"
        elif choice == "3":
            return "edge"
        else:
            print("無效選項，請重新輸入")

if __name__ == "__main__":
    print("="*60)
    print("           AGES-KH DOM 元素探測工具")
    print("="*60)
    print("此工具用於安全地探測網頁元素，不會執行實際搶單動作")
    print("請確保您已安裝必要套件：")
    print("pip install selenium webdriver-manager")
    print("="*60)

    inspector = DOMInspector()

    try:
        # 選擇瀏覽器
        browser_type = choose_browser()

        # 設定瀏覽器 (Debug 模式)
        print(f"\n[步驟 1] 啟動 {browser_type.title()} 瀏覽器...")
        print("[INFO] 首次使用會自動下載對應的 WebDriver，請稍候...")

        if not inspector.setup_browser(browser_type, debug_mode=True):
            print(f"\n[錯誤] {browser_type.title()} 瀏覽器啟動失敗，程式結束")
            sys.exit(1)

        # 導航到平台
        print("\n[步驟 2] 導航到平台...")
        if inspector.navigate_to_platform():
            # 開始互動式元素尋找
            print("\n[步驟 3] 開始元素探測...")
            elements = inspector.interactive_element_finder()

            print("\n" + "="*50)
            print("DOM 元素探測完成！")
            print(f"使用瀏覽器: {browser_type.title()}")
            print("請將 dom_elements_config.json 檔案內容提供給開發團隊")
            print("="*50)
        else:
            print("\n[錯誤] 無法導航到平台")

    except KeyboardInterrupt:
        print("\n[INFO] 使用者中斷操作")
    except Exception as e:
        print(f"\n[ERROR] 發生錯誤: {e}")
        print("\n[除錯資訊] 請檢查：")
        print("1. 是否已安裝套件: pip install selenium webdriver-manager")
        print("2. 對應瀏覽器是否已安裝 (Chrome/Firefox/Edge)")
        print("3. 網路連線是否正常 (首次使用需下載 WebDriver)")
    finally:
        inspector.cleanup()
