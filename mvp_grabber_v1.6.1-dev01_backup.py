# mvp_grabber.py v1.3.32
# AGES-KH 搶單主程式
# Last Modified: 2024-06-17
# Maintainer: Will Wang
# 調整內容：v1.3.32 - 固定任務區域高度，優化版型與說明文字

import tkinter as tk
from tkinter import simpledialog, messagebox, ttk
import csv
from datetime import datetime, date, timedelta
import os
import sys
import psutil
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import threading
import time
import logging
from rtt_config_gui import RTTConfigGUI
from rtt_config_manager import RTTConfigManager
from rtt_predictor import get_avg_rtt
import pandas as pd
import re

# 導入新的模組
from submission_result_detector import SubmissionResultDetector
from dom_inspector import DOMInspector

__VERSION__ = "1.6.1-dev01"  # 重新命名GUI表頭，統一編號格式

# ===== v1.6.0 開發說明 =====
"""
🎯 v1.6.0 開發基礎：

📋 基於版本：v1.4.33 (mvp_grabber_v1.4.33_backup.py)
📅 創建日期：2025-07-02
🎯 開發目標：在穩定基礎上實現核心功能

✅ v1.4.33 已驗證功能（🔒 不可修改）：
1. 穩定的瀏覽器啟動和管理
2. 可靠的編輯按鈕檢測和點擊
3. 成功的"修改進廠確認單"彈窗處理
4. 有效的訂單掃描和識別
5. 基本的GUI操作流程
6. 時間觸發機制
7. 驗證碼處理邏輯

🎯 v1.6.X 核心目標：
1. 【核心功能1】程式能自動按下"送出"動作（正式送單）
2. 【核心功能2】程式能自動按下"送出"動作（取消=模擬送單）
3. 【GUI升級】整合v1.5.X中成功的GUI改進

⚠️ 開發原則：
1. 絕不修改已驗證的核心搶單邏輯
2. 漸進式添加新功能，每步都要測試
3. 嚴格版本控制，每次修改都要更新版本號
4. 保持v1.4.33的穩定性和可靠性

🚨 重要提醒：
- 本版本基於v1.4.33，不包含v1.5.X的任何修改
- 所有標記為🔒的功能都是已驗證的，不得隨意修改
- 新功能添加必須基於穩定的基礎，不能破壞現有功能

📋 v1.6.0-dev01 功能標記總結：

🔒 已驗證功能（絕對不可修改）：
- GUI-04 操作指南 → 用戶點擊"準備完成"
- execute_order_grabbing() → 搶單主流程
- find_and_click_edit_button() → 編輯按鈕檢測和點擊
- handle_captcha_input() → GUI-09 驗證碼輸入
- 步驟1-2：編輯按鈕點擊 → 彈窗出現

⚠️ 需要修改的功能：
- wait_for_user_ready_confirmation() → 替換為GUI#09
- execute_precise_submit() → 支援送出/取消選擇
- 步驟3-4：驗證碼輸入後的流程處理

🎯 核心開發目標：
- 實現GUI#09：提供"正式送單"和"模擬送單"選項
- 核心功能1：程式能自動按下"送出"動作（正式送單）
- 核心功能2：程式能自動按下"取消"動作（模擬送單）
"""

driver = None  # 全域 driver
chrome_pid = None  # 追蹤由程式啟動的 Chrome PID
result_detector = None  # 全域結果檢測器
dom_inspector = None  # 全域 DOM 檢查器
current_mode = 'normal'  # 當前運行模式 ('normal', 'verification_ready', 'test')

ORDERS_PATH = os.path.join('orders', 'orders.csv')

# 🔍 設置詳細日誌記錄
def setup_detailed_logging():
    """設置詳細的日誌記錄系統"""
    # 創建 logs 目錄
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 設置日誌格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'

    # 創建文件處理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/grabber_detailed_{timestamp}.log'

    # 配置日誌
    logging.basicConfig(
        level=logging.DEBUG,
        format=log_format,
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.FileHandler('ages_kh_bot.log', encoding='utf-8'),  # 保留原有日誌
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"🔍 詳細日誌記錄已啟動，日誌文件: {log_filename}")
    return logger, log_filename

# 初始化詳細日誌
logger, current_log_file = setup_detailed_logging()

# 全域變數
last_dialog_detection = None  # 儲存最後一次彈窗檢測結果

def log_page_content(driver, context=""):
    """詳細記錄頁面內容"""
    try:
        logger.info(f"🔍 [{context}] 開始記錄頁面內容...")

        # 基本頁面信息
        current_url = driver.current_url
        page_title = driver.title
        logger.info(f"🔍 [{context}] 當前 URL: {current_url}")
        logger.info(f"🔍 [{context}] 頁面標題: {page_title}")

        # 方法1: driver.page_source
        page_source = driver.page_source
        logger.info(f"🔍 [{context}] page_source 長度: {len(page_source)}")
        logger.info(f"🔍 [{context}] page_source 包含 E48B: {'E48B' in page_source}")
        logger.info(f"🔍 [{context}] page_source 包含目標訂單: {'E48B201611405190953' in page_source}")

        # 方法2: JavaScript innerText
        js_inner_text = driver.execute_script("return document.body.innerText || document.body.textContent || '';")
        logger.info(f"🔍 [{context}] innerText 長度: {len(js_inner_text)}")
        logger.info(f"🔍 [{context}] innerText 包含 E48B: {'E48B' in js_inner_text}")
        logger.info(f"🔍 [{context}] innerText 包含目標訂單: {'E48B201611405190953' in js_inner_text}")

        # 方法3: 表格檢測
        tables = driver.find_elements(By.TAG_NAME, "table")
        rows = driver.find_elements(By.TAG_NAME, "tr")
        logger.info(f"🔍 [{context}] 檢測到 {len(tables)} 個表格，{len(rows)} 個表格行")

        # 方法4: 檢查所有可能包含訂單號的元素
        possible_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'E48B')]")
        logger.info(f"🔍 [{context}] 包含 'E48B' 的元素數量: {len(possible_elements)}")

        for i, elem in enumerate(possible_elements[:5]):  # 只記錄前5個
            try:
                elem_text = elem.text.strip()
                elem_tag = elem.tag_name
                logger.info(f"🔍 [{context}] E48B 元素 {i+1}: <{elem_tag}> {elem_text[:100]}...")
            except:
                logger.warning(f"🔍 [{context}] 無法讀取 E48B 元素 {i+1}")

        # 方法5: 記錄頁面內容樣本
        logger.info(f"🔍 [{context}] innerText 前300字符:")
        logger.info(f"🔍 [{context}] {js_inner_text[:300]}...")

        if len(js_inner_text) > 300:
            logger.info(f"🔍 [{context}] innerText 後300字符:")
            logger.info(f"🔍 [{context}] ...{js_inner_text[-300:]}")

        # 方法6: 檢查是否有編輯按鈕
        edit_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']")
        logger.info(f"🔍 [{context}] 檢測到 {len(edit_buttons)} 個編輯按鈕")

        # 🎯 方法7: 檢查是否有 iframe，並檢測 iframe 內容
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        logger.info(f"🔍 [{context}] 檢測到 {len(iframes)} 個 iframe")

        iframe_info = {}
        if iframes:
            for i, iframe in enumerate(iframes):
                try:
                    iframe_id = iframe.get_attribute('id') or f'iframe_{i}'
                    iframe_src = iframe.get_attribute('src') or 'no_src'
                    logger.info(f"🔍 [{context}] iframe {i+1}: id='{iframe_id}', src='{iframe_src}'")

                    # 切換到 iframe
                    driver.switch_to.frame(iframe)

                    # 檢測 iframe 內容
                    iframe_inner_text = driver.execute_script("return document.body.innerText || document.body.textContent || '';")
                    iframe_tables = driver.find_elements(By.TAG_NAME, "table")
                    iframe_rows = driver.find_elements(By.TAG_NAME, "tr")
                    iframe_edit_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), '編輯')] | //button[contains(text(), '編輯')] | //input[@value='編輯']")
                    iframe_e48b_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'E48B')]")

                    has_target_in_iframe = 'E48B201611405190953' in iframe_inner_text
                    has_e48b_in_iframe = 'E48B' in iframe_inner_text

                    logger.info(f"🔍 [{context}] iframe {i+1} 內容:")
                    logger.info(f"🔍 [{context}]   - 文字長度: {len(iframe_inner_text)}")
                    logger.info(f"🔍 [{context}]   - 包含目標訂單: {has_target_in_iframe}")
                    logger.info(f"🔍 [{context}]   - 包含 E48B: {has_e48b_in_iframe}")
                    logger.info(f"🔍 [{context}]   - 表格數量: {len(iframe_tables)}")
                    logger.info(f"🔍 [{context}]   - 表格行數: {len(iframe_rows)}")
                    logger.info(f"🔍 [{context}]   - 編輯按鈕數量: {len(iframe_edit_buttons)}")
                    logger.info(f"🔍 [{context}]   - E48B 元素數量: {len(iframe_e48b_elements)}")

                    if len(iframe_inner_text) > 300:
                        logger.info(f"🔍 [{context}]   - 內容前300字符: {iframe_inner_text[:300]}...")
                        logger.info(f"🔍 [{context}]   - 內容後300字符: ...{iframe_inner_text[-300:]}")
                    else:
                        logger.info(f"🔍 [{context}]   - 完整內容: {iframe_inner_text}")

                    iframe_info[iframe_id] = {
                        'src': iframe_src,
                        'inner_text_length': len(iframe_inner_text),
                        'has_target_order': has_target_in_iframe,
                        'has_e48b': has_e48b_in_iframe,
                        'table_count': len(iframe_tables),
                        'row_count': len(iframe_rows),
                        'edit_button_count': len(iframe_edit_buttons),
                        'e48b_element_count': len(iframe_e48b_elements)
                    }

                    # 🎯 不要切換回主頁面，保持在 iframe 內進行後續搜尋
                    # driver.switch_to.default_content()  # 註釋掉，保持在 iframe 內

                except Exception as e:
                    logger.error(f"🔍 [{context}] 檢測 iframe {i+1} 時發生錯誤: {e}")
                    # 🎯 發生錯誤時也不要切換回主頁面，保持在 iframe 內
                    # try:
                    #     driver.switch_to.default_content()
                    # except:
                    #     pass

        return {
            'url': current_url,
            'title': page_title,
            'page_source_length': len(page_source),
            'inner_text_length': len(js_inner_text),
            'has_target_order': 'E48B201611405190953' in js_inner_text,
            'has_e48b': 'E48B' in js_inner_text,
            'table_count': len(tables),
            'row_count': len(rows),
            'edit_button_count': len(edit_buttons),
            'e48b_element_count': len(possible_elements),
            'iframe_count': len(iframes),
            'iframe_info': iframe_info
        }

    except Exception as e:
        logger.error(f"🔍 [{context}] 記錄頁面內容時發生錯誤: {e}")
        return None

# 支援多種日期格式
DATE_FORMATS = [
    "%Y/%m/%d", "%Y-%m-%d", "%Y/%-m/%-d", "%Y/%m/%-d", "%Y/%-m/%d", "%Y/%d/%m"
]

# ===== 安全離開主程式 =====
def safe_exit():
    global driver, chrome_pid, result_detector, dom_inspector
    print("[INFO] 終止主程式...")
    try:
        if driver:
            try:
                driver.quit()
            except:
                pass
            finally:
                driver = None
    except Exception as e:
        print(f"[ERROR] 關閉 driver 發生錯誤: {str(e)}")

    try:
        for widget in tk._default_root.children.values():
            widget.destroy()
        tk._default_root.quit()
    except:
        pass

    # 清理全域變數
    chrome_pid = None
    result_detector = None
    dom_inspector = None
    os._exit(0)

# ===== 瀏覽器支援檢查 =====
def get_supported_browsers():
    """回傳目前支援的瀏覽器列表"""
    return ['chrome']  # 未來可以加入 'firefox', 'edge'

def has_supported_browser(tasks):
    """檢查是否有支援的瀏覽器任務"""
    supported = get_supported_browsers()
    return any(task.get('browser', '').strip().lower() in supported for task in tasks)

def start_browser(task):
    """根據任務啟動對應的瀏覽器"""
    global driver, chrome_pid, result_detector, dom_inspector
    print(f"[DEBUG] 準備啟動瀏覽器，任務內容: {task}")

    if not task or not isinstance(task, dict):
        print("[ERROR] 無效的任務格式")
        return False

    try:
        browser = task.get('browser', '').strip().lower()
        if not browser:
            print("[ERROR] 任務中未指定瀏覽器")
            return False

        if browser == 'chrome':
            # 直接啟動新的 Chrome 實例，不嘗試連接現有瀏覽器
            print("[INFO] 啟動新的 Chrome 瀏覽器實例...")

            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_argument('--start-maximized')  # 最大化視窗
            chrome_options.add_argument('--disable-gpu')  # 禁用 GPU 加速
            chrome_options.add_argument('--no-sandbox')  # 禁用沙盒模式
            chrome_options.add_argument('--disable-dev-shm-usage')  # 禁用 /dev/shm 使用

            try:
                driver = webdriver.Chrome(options=chrome_options)
                chrome_pid = driver.service.process.pid
                print(f"[DEBUG] Chrome PID: {chrome_pid}")

                # 初始化檢測器
                result_detector = SubmissionResultDetector(driver, logger)
                dom_inspector = DOMInspector()
                print("[INFO] 結果檢測器和 DOM 檢查器已初始化")

                # 導航到平台首頁
                print("[INFO] 導航到平台首頁")
                driver.get("https://wmc.kcg.gov.tw/")

                # 🚀 立即設置瀏覽器事件監控
                print("[INFO] 🚀 設置瀏覽器事件監控...")
                setup_browser_event_monitoring()

                start_driver_monitor()
                print("[DEBUG] 瀏覽器啟動完成，返回 True")
                return True

            except Exception as e:
                print(f"[ERROR] 啟動 Chrome 失敗: {str(e)}")
                return False

        print(f"[ERROR] 不支援的瀏覽器: {browser}")
        return False

    except Exception as e:
        print(f"[ERROR] 啟動瀏覽器失敗: {str(e)}")
        if driver:
            try:
                driver.quit()
            except:
                pass
            driver = None
        chrome_pid = None
        result_detector = None
        dom_inspector = None
        return False

# ===== GUI：詢問使用者輸入觸發時間（支援毫秒） =====
def ask_trigger_time_gui(default="09:30:00.001") -> str:
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    answer = simpledialog.askstring("GUI-05 觸發時間設定 (支援毫秒)",
                                     "請輸入本次搶單觸發時間 (格式 HH:MM:SS.sss)：",
                                     initialvalue=default)
    if not answer:
        return default
    return answer.strip()

def normalize_date(date_str: str) -> str:
    """
    將日期字串正規化為 YYYY-MM-DD 格式
    
    Args:
        date_str (str): 原始日期字串，支援 YYYY/M/D、YYYY/MM/DD、YYYY-MM-DD 等格式
        
    Returns:
        str: 正規化後的日期字串 (YYYY-MM-DD)，若為萬用日期則返回 '*-*-*'
    """
    s = date_str.strip()
    if s == '*-*-*':
        return '*-*-*'
    
    # 替換可能的日期分隔符號為統一格式
    s = s.replace("-", "/")
    
    # 嘗試解析日期 (支援 YYYY/M/D 和 YYYY/MM/DD)
    parts = s.split("/")
    if len(parts) == 3:
        try:
            year = int(parts[0])
            month = int(parts[1])
            day = int(parts[2])
            return f"{year:04d}-{month:02d}-{day:02d}"
        except (ValueError, IndexError):
            pass
            
    return s  # 無法解析則返回原始字串

def fix_row_keys(row: dict) -> dict:
    # 自動修正欄位名稱 user_profil -> user_profile
    if 'user_profil' in row and 'user_profile' not in row:
        row['user_profile'] = row['user_profil']
    return row

def load_and_filter_orders(path=ORDERS_PATH):
    """
    載入並過濾 orders.csv 中的任務
    
    Args:
        path (str): orders.csv 的檔案路徑
        
    Returns:
        tuple: (filtered_tasks, ignored_tasks, all_valid_tasks)
            - filtered_tasks: 過濾後要執行的任務（每個瀏覽器只保留第一筆）
            - ignored_tasks: 因瀏覽器重複而被忽略的任務
            - all_valid_tasks: 所有今日有效任務（包含萬用任務）
    """
    today_str = date.today().strftime("%Y-%m-%d")
    print(f"[DEBUG] 今天日期: {today_str}")
    print(f"[DEBUG] CSV 路徑: {path}")
    
    valid_tasks = []
    ignored_tasks = []
    filtered_tasks = []
    seen_browsers = set()
    
    if not os.path.exists(path):
        print(f"[ERROR] 找不到訂單檔案: {path}")
        return filtered_tasks, ignored_tasks, valid_tasks
        
    try:
        with open(path, newline='', encoding='utf-8') as f:
            # 先讀取第一行確認分隔符
            first_line = f.readline().strip()
            delimiter = '\t' if '\t' in first_line else ','
            f.seek(0)  # 重置檔案指標
            
            reader = csv.DictReader(f, delimiter=delimiter)
            print(f"[DEBUG] 使用分隔符: {delimiter}")
            
            for row in reader:
                row = fix_row_keys(row)
                raw_date = row.get('date', '')
                norm_date = normalize_date(raw_date)
                print(f"[DEBUG] 原始日期: {raw_date!r}, 正規化後: {norm_date!r}")
                
                if norm_date == today_str or norm_date == '*-*-*':
                    print(f"[DEBUG] 找到有效任務: {row}")
                    valid_tasks.append(row)
                    
                    browser = row.get('browser', '').strip().lower()
                    if browser and browser not in seen_browsers:
                        filtered_tasks.append(row)
                        seen_browsers.add(browser)
                    else:
                        ignored_tasks.append(row)
                        
    except Exception as e:
        print(f"[ERROR] 讀取訂單檔案失敗: {str(e)}")
        
    print(f"[DEBUG] 有效任務數量: {len(valid_tasks)}")
    print(f"[DEBUG] 過濾後任務數量: {len(filtered_tasks)}")
    return filtered_tasks, ignored_tasks, valid_tasks

# ===== 顯示準備提示 GUI =====
def show_preparation_gui(version, valid_summary, task_summary, ignored_tasks, filtered_tasks):
    def on_continue():
        window.attributes('-topmost', False)
        window.destroy()
        # 1. 啟動瀏覽器
        print(f"[DEBUG] filtered_tasks: {filtered_tasks}")
        for task in filtered_tasks:
            print(f"[DEBUG] 嘗試啟動: {task.get('browser')}")
            if start_browser(task):
                break  # 只啟動第一個支援的瀏覽器任務
        # 2. 等待使用者操作並開始搶單流程
        wait_for_user_operation_and_start_grabbing(filtered_tasks)

    def on_cancel():
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()
        return False

    def show_rtt_config():
        """顯示 RTT 設定 GUI"""
        def run_rtt_gui():
            rtt_gui = RTTConfigGUI()
            rtt_gui.show()

        # 在新執行緒中啟動 RTT 設定視窗
        thread = threading.Thread(target=run_rtt_gui)
        thread.daemon = True
        thread.start()

    window = tk.Tk()
    window.title(f"GUI-02 AGES-KH 準備提示 v{version} - PID: {os.getpid()}")
    window.geometry("700x600")
    window.attributes('-topmost', True)

    # 版本與操作提醒
    tk.Label(window, text=f"GUI-01 AGES-KH 搶單主程式 v{version}", font=("Arial", 12, "bold"), fg="blue").pack(pady=(10, 0))
    tk.Label(window, text="【重要提醒】\n1. 請確認已準備好登入資訊\n2. 點選「啟動瀏覽器」後，請手動登入並操作至清單畫面", 
             fg="red", wraplength=660, justify="left").pack(pady=(0, 10))

    # 今日有效任務（可滾動）
    frame1 = tk.Frame(window)
    frame1.pack(fill="both", expand=True, padx=10, pady=5)
    tk.Label(frame1, text="今日有效任務（符合今日或萬用條件）：", anchor="w", fg="black").pack(fill="x")
    valid_text = tk.Text(frame1, height=6, wrap="none", font=("Consolas", 10))
    valid_text.pack(fill="both", expand=True)
    valid_text.insert("end", valid_summary)
    valid_text.config(state="disabled")

    # 今日將執行的任務（可滾動）
    frame2 = tk.Frame(window)
    frame2.pack(fill="both", expand=True, padx=10, pady=5)
    tk.Label(frame2, text="今日將執行的任務（每個瀏覽器僅執行一筆）：", anchor="w", fg="green").pack(fill="x")
    text = tk.Text(frame2, height=6, wrap="none", font=("Consolas", 10))
    text.pack(fill="both", expand=True)
    text.insert("end", task_summary)
    text.config(state="disabled")

    # 被忽略的 browser 任務警告
    if ignored_tasks:
        tk.Label(window, text="【警告】以下任務雖符合今日條件，但因同一瀏覽器僅執行一筆，已被忽略：", 
                fg="orange", wraplength=660, justify="left").pack(pady=(5, 0))
        ignore_text = tk.Text(window, height=5, wrap="none", font=("Consolas", 10))
        ignore_text.pack(fill="x", padx=10)
        for task in ignored_tasks:
            ignore_text.insert("end", str(task) + "  （同 browser 已有任務，已忽略）\n")
        ignore_text.config(state="disabled")

    # 按鈕框架
    button_frame = tk.Frame(window)
    button_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Button(button_frame, text="啟動瀏覽器", command=on_continue).pack(side="left", padx=5)
    tk.Button(button_frame, text="RTT 設定", command=show_rtt_config).pack(side="left", padx=5)
    tk.Button(button_frame, text="取消", command=on_cancel).pack(side="right", padx=5)

    window.protocol("WM_DELETE_WINDOW", on_cancel)
    window.mainloop()

# ===== 等待使用者操作完成並開始搶單 =====
def wait_for_user_operation_and_start_grabbing(filtered_tasks):
    def on_ready():
        window.attributes('-topmost', False)
        window.destroy()
        print("[INFO] 使用者已按下繼續，準備進入搶單流程...")

        # 開始搶單流程
        execute_order_grabbing(filtered_tasks)
        return True

    def on_cancel():
        window.attributes('-topmost', False)
        window.destroy()
        safe_exit()
        return False

    window = tk.Tk()
    window.title(f"GUI-03 AGES-KH 登入提示 v{__VERSION__} - PID: {os.getpid()}")
    window.geometry("400x200")
    window.attributes('-topmost', True)

    tk.Label(window, text="請手動登入平台並操作至清單畫面\n完成後請按下「準備完成」",
             padx=20, pady=20).pack()

    btn_frame = tk.Frame(window)
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="準備完成", command=on_ready, width=10).pack(side="left", padx=5)
    tk.Button(btn_frame, text="取消", command=on_cancel, width=10).pack(side="left", padx=5)
    window.protocol("WM_DELETE_WINDOW", on_cancel)
    window.mainloop()

# ===== 背景監測 Chrome 是否被關閉 =====
def start_driver_monitor():
    def monitor():
        global chrome_pid
        while True:
            try:
                if chrome_pid and not psutil.pid_exists(chrome_pid):
                    print("[WARN] 偵測到 Python 啟動的 Chrome 已關閉")
                    safe_exit()
                    break
                driver.current_url
                time.sleep(1)
            except (WebDriverException, AttributeError):
                print("[WARN] WebDriver 連線異常")
                safe_exit()
                break
            except Exception as e:
                print(f"[ERROR] 監控錯誤: {str(e)}")
                safe_exit()
                break

    threading.Thread(target=monitor, daemon=True).start()

class GrabberGUI:
    def __init__(self):
        print("[DEBUG] 初始化 GrabberGUI...")
        self.window = tk.Tk()
        self.window.title(f"GUI-01 AGES-KH 搶單主程式 v{__VERSION__}")
        self.window.geometry("500x600")
        self.window.resizable(False, False)

        # 新增：儲存觸發時間
        self.trigger_time = "09:30:00.001"  # 預設值

        # 建立 GUI 元件
        print("[DEBUG] 創建 GUI 元件...")
        self._create_widgets()

        # 載入今日任務
        print("[DEBUG] 載入今日任務...")
        self._load_today_tasks()
        print("[DEBUG] GrabberGUI 初始化完成")

    def _create_widgets(self):
        """建立 GUI 元件"""
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill="both", expand=True)

        # 標題
        title_label = ttk.Label(
            main_frame,
            text=f"GUI-01 AGES-KH 搶單主程式 v{__VERSION__}",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10, fill="x")

        # 觸發時間區域
        time_frame = ttk.Frame(main_frame)
        time_frame.pack(fill="x", pady=5)
        ttk.Label(time_frame, text="當前搶單時間:").pack(side="left")
        self.time_label = ttk.Label(time_frame, text=self.trigger_time, font=("Arial", 10, "bold"))
        self.time_label.pack(side="left", padx=5)
        ttk.Button(
            time_frame,
            text="修改時間",
            command=self._ask_trigger_time
        ).pack(side="left", padx=5)

        # 任務顯示區域
        self.task_frame = ttk.Frame(main_frame)
        self.task_frame.pack(fill="both", expand=True, pady=5)

        # 按鈕區域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=10, side="bottom")
        ttk.Button(
            button_frame,
            text="RTT 設定",
            command=self._show_rtt_config
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame,
            text="開始執行",
            command=self._start_execution
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame,
            text="結束程式",
            command=self.window.destroy
        ).pack(side="right", padx=5)

    def _display_tasks(self, filtered_tasks, ignored_tasks, valid_tasks):
        """顯示任務列表"""
        for widget in self.task_frame.winfo_children():
            widget.destroy()

        # 今日任務區域（固定高度 200）
        filtered_frame = ttk.LabelFrame(self.task_frame, text="今日任務（待執行）")
        filtered_frame.pack(side="top", fill="x", padx=5, pady=2)
        filtered_frame.config(height=200)
        filtered_frame.pack_propagate(False)
        filtered_canvas = tk.Canvas(filtered_frame, height=200)
        filtered_scrollbar = ttk.Scrollbar(filtered_frame, orient="vertical", command=filtered_canvas.yview)
        filtered_content = ttk.Frame(filtered_canvas)
        filtered_content.bind(
            "<Configure>",
            lambda e: filtered_canvas.configure(scrollregion=filtered_canvas.bbox("all"))
        )
        filtered_canvas.create_window((0, 0), window=filtered_content, anchor="nw")
        filtered_canvas.configure(yscrollcommand=filtered_scrollbar.set)
        filtered_canvas.pack(side="left", fill="both", expand=True)
        filtered_scrollbar.pack(side="right", fill="y")
        ttk.Label(
            filtered_content,
            text=f"總數：{len(filtered_tasks)} 筆",
            font=("Arial", 10, "bold")
        ).pack(anchor="w")
        if filtered_tasks:
            for task in filtered_tasks:
                task_text = (
                    f"任務日期： {task.get('date', 'N/A')}\n"
                    f"進廠單日： {task.get('order_date', 'N/A')}\n"
                    f"進廠單號： {task.get('order_id', 'N/A')}\n"
                    f"　瀏覽器： {task.get('browser', 'N/A')}\n"
                    f"　使用者： {task.get('user_profile', 'N/A')}\n"
                    f"預測模型： {task.get('model', 'N/A')}\n"
                    f"觸發時間： {task.get('trigger_time', 'N/A')}\n"
                    f"任務備註： {task.get('note', 'N/A')}\n"
                )
                ttk.Label(
                    filtered_content,
                    text=task_text,
                    font=("Arial", 10)
                ).pack(pady=5, anchor="w")
        else:
            ttk.Label(
                filtered_content,
                text="無今日任務",
                font=("Arial", 10)
            ).pack(pady=5)

        # 有效任務區域（固定高度 200）
        valid_frame = ttk.LabelFrame(self.task_frame, text="有效任務（全部有效，含待執行與不執行）")
        valid_frame.pack(side="top", fill="x", padx=5, pady=2)
        valid_frame.config(height=200)
        valid_frame.pack_propagate(False)
        valid_canvas = tk.Canvas(valid_frame, height=200)
        valid_scrollbar = ttk.Scrollbar(valid_frame, orient="vertical", command=valid_canvas.yview)
        valid_content = ttk.Frame(valid_canvas)
        valid_content.bind(
            "<Configure>",
            lambda e: valid_canvas.configure(scrollregion=valid_canvas.bbox("all"))
        )
        valid_canvas.create_window((0, 0), window=valid_content, anchor="nw")
        valid_canvas.configure(yscrollcommand=valid_scrollbar.set)
        valid_canvas.pack(side="left", fill="both", expand=True)
        valid_scrollbar.pack(side="right", fill="y")
        ttk.Label(
            valid_content,
            text=f"總數：{len(valid_tasks)} 筆",
            font=("Arial", 10, "bold")
        ).pack(anchor="w")
        if valid_tasks:
            for task in valid_tasks:
                task_text = (
                    f"任務日期： {task.get('date', 'N/A')}\n"
                    f"進廠單日： {task.get('order_date', 'N/A')}\n"
                    f"進廠單號： {task.get('order_id', 'N/A')}\n"
                    f"　瀏覽器： {task.get('browser', 'N/A')}\n"
                    f"　使用者： {task.get('user_profile', 'N/A')}\n"
                    f"預測模型： {task.get('model', 'N/A')}\n"
                    f"觸發時間： {task.get('trigger_time', 'N/A')}\n"
                    f"任務備註： {task.get('note', 'N/A')}\n"
                )
                ttk.Label(
                    valid_content,
                    text=task_text,
                    font=("Arial", 10)
                ).pack(pady=5, anchor="w")
        else:
            ttk.Label(
                valid_content,
                text="無有效任務",
                font=("Arial", 10)
            ).pack(pady=5)

    def _load_today_tasks(self):
        """載入今日任務"""
        try:
            filtered_tasks, ignored_tasks, valid_tasks = load_and_filter_orders()
            self._display_tasks(filtered_tasks, ignored_tasks, valid_tasks)
        except Exception as e:
            messagebox.showerror("GUI-E1 錯誤", f"載入任務失敗：{str(e)}")

    def _show_rtt_config(self):
        """顯示 RTT 設定視窗"""
        RTTConfigGUI().show()

    def _ask_trigger_time(self):
        """詢問使用者輸入觸發時間"""
        new_time = ask_trigger_time_gui(self.trigger_time)
        if new_time:
            self.trigger_time = new_time
            self.time_label.config(text=self.trigger_time)
            
    def _start_execution(self):
        """開始執行搶單 - 簡化流程，避免重複確認"""
        # 確認觸發時間格式
        if not re.match(r"^\d{2}:\d{2}:\d{2}\.\d{3}$", self.trigger_time):
            messagebox.showerror("GUI-E1 錯誤", "觸發時間格式不正確，請使用 HH:MM:SS.sss 格式")
            return

        # 載入並過濾訂單
        filtered_tasks, ignored_tasks, valid_tasks = load_and_filter_orders()

        if not filtered_tasks:
            messagebox.showwarning("GUI-E2 警告", "無有效任務可執行")
            return

        # 最終確認 - 合併觸發時間和任務確認
        task_count = len(filtered_tasks)
        confirm_message = (
            f"即將開始搶單執行：\n\n"
            f"觸發時間：{self.trigger_time}\n"
            f"任務數量：{task_count} 筆\n"
            f"任務詳情：{filtered_tasks[0].get('order_id', 'N/A')} 等\n\n"
            f"確認開始執行嗎？"
        )

        confirm = messagebox.askyesno("GUI-06 確認開始搶單", confirm_message)
        if not confirm:
            return

        # 準備任務摘要
        valid_summary = "\n".join(str(t) for t in valid_tasks)
        task_summary = "\n".join(str(t) for t in filtered_tasks)

        # 關閉主 GUI 並顯示準備提示
        self.window.withdraw()  # 隱藏主視窗

        # 直接啟動瀏覽器並進入操作流程
        self._start_browser_and_wait_for_user(filtered_tasks, valid_summary, task_summary, ignored_tasks)

    def _start_browser_and_wait_for_user(self, filtered_tasks, valid_summary, task_summary, ignored_tasks):
        """啟動瀏覽器並等待用戶操作 - 簡化版本"""
        print("[INFO] 啟動瀏覽器...")
        print(f"[DEBUG] 任務摘要: {len(filtered_tasks)} 筆任務")
        # valid_summary, task_summary, ignored_tasks 保留供未來使用

        # 啟動瀏覽器
        browser_started = False
        for task in filtered_tasks:
            print(f"[DEBUG] 嘗試啟動: {task.get('browser')}")
            if start_browser(task):
                browser_started = True
                break

        if not browser_started:
            messagebox.showerror("GUI-E1 錯誤", "無法啟動瀏覽器")
            safe_exit()
            return

        # 顯示簡化的操作提示
        print("[DEBUG] 準備顯示用戶操作指南...")
        self._show_simplified_user_guide(filtered_tasks)

    def _show_simplified_user_guide(self, filtered_tasks):
        """🔒 [v1.6.0-CORE] GUI-04 操作指南 - 已驗證功能，不可修改

        功能：顯示操作指南，用戶點擊"準備完成"後開始搶單流程
        按鈕：✅ 準備完成、🧪 測試模式、❌ 取消
        狀態：🔒 已驗證正常，絕對不可修改
        """
        print("[DEBUG] 進入 _show_simplified_user_guide 函數")
        def on_ready():
            # 🔒 [v1.6.0-CORE] 準備完成按鈕處理 - 不可修改
            window.attributes('-topmost', False)
            window.destroy()
            print("[INFO] 用戶確認準備完成，開始搶單流程...")
            logger.info("🎯 用戶點擊準備完成按鈕，開始詳細檢測...")

            # 🔍 詳細記錄當前頁面狀態
            if driver:
                page_info = log_page_content(driver, "用戶點擊準備完成")
                if page_info:
                    print(f"[INFO] 📋 頁面檢測摘要:")
                    print(f"  - URL: {page_info['url']}")
                    print(f"  - 標題: {page_info['title']}")
                    print(f"  - 包含目標訂單: {page_info['has_target_order']}")
                    print(f"  - 包含 E48B: {page_info['has_e48b']}")
                    print(f"  - 表格數量: {page_info['table_count']}")
                    print(f"  - 表格行數: {page_info['row_count']}")
                    print(f"  - 編輯按鈕數量: {page_info['edit_button_count']}")
                    print(f"  - E48B 元素數量: {page_info['e48b_element_count']}")
                    print(f"[INFO] 📄 詳細日誌已記錄到: {current_log_file}")

            # 檢查用戶是否在正確的頁面
            if not self._verify_correct_page():
                return

            # 🔒 [v1.6.0-CORE] 開始搶單流程 - 不可修改
            execute_order_grabbing(filtered_tasks)

        def on_cancel():
            window.attributes('-topmost', False)
            window.destroy()
            safe_exit()

        window = tk.Tk()
        window.title(f"GUI-04 AGES-KH 操作指南 v{__VERSION__}")
        window.geometry("600x500")  # 增大視窗
        window.resizable(False, False)  # 固定大小
        window.attributes('-topmost', True)

        # 創建主框架和滾動條
        main_frame = tk.Frame(window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 創建滾動框架
        canvas = tk.Canvas(main_frame)
        scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        title_label = tk.Label(
            scrollable_frame,
            text="🚀 搶單準備階段",
            font=("Arial", 14, "bold"),
            fg="blue"
        )
        title_label.pack(pady=(0, 10))

        instructions = [
            "1. 瀏覽器已啟動，請手動完成以下操作：",
            "   • 登入您的帳號",
            "   • 輸入登入驗證碼",
            "   • 導航到搶單頁面（進廠確認單列表）",
            "   • 確保能看到您要搶的訂單",
            "",
            "2. 完成上述操作後，點擊「準備完成」",
            "",
            "3. 程式將自動：",
            "   • 掃描頁面元素",
            "   • 等待觸發時間",
            "   • 執行搶單動作"
        ]

        for instruction in instructions:
            label = tk.Label(
                scrollable_frame,
                text=instruction,
                font=("Arial", 10),
                anchor="w",
                justify="left"
            )
            label.pack(fill="x", pady=1)

        # 時間顯示區域
        time_frame = tk.Frame(scrollable_frame)
        time_frame.pack(pady=(20, 10), fill="x")

        current_time_label = tk.Label(
            time_frame,
            text="⏰ 當前時間: 載入中...",
            font=("Arial", 10, "bold"),
            fg="blue"
        )
        current_time_label.pack()

        trigger_time_label = tk.Label(
            time_frame,
            text="🎯 觸發時間: 載入中...",
            font=("Arial", 10, "bold"),
            fg="red"
        )
        trigger_time_label.pack()

        countdown_label = tk.Label(
            time_frame,
            text="⏳ 倒數計時: 載入中...",
            font=("Arial", 12, "bold"),
            fg="green"
        )
        countdown_label.pack()

        # 時間更新函數
        def get_ntp_time():
            """獲取 NTP 精準時間"""
            from datetime import datetime

            # 嘗試導入 ntplib
            try:
                import ntplib
                print("[INFO] ntplib 模組載入成功")
            except ImportError:
                print("[WARN] ❌ ntplib 未安裝，請執行: pip install ntplib")
                return datetime.now(), "本地系統時間 (缺少 ntplib)"

            # 使用台灣 NTP 服務器
            ntp_servers = [
                'tock.stdtime.gov.tw',  # 台灣標準時間
                'watch.stdtime.gov.tw', # 台灣標準時間備用
                'time.stdtime.gov.tw',  # 台灣標準時間第三選擇
                'time.nist.gov',        # 美國 NIST
                'pool.ntp.org',         # 全球 NTP 池
                '1.tw.pool.ntp.org',    # 台灣 NTP 池
                '0.pool.ntp.org'        # 全球 NTP 池備用
            ]

            print(f"[INFO] 🌐 開始嘗試連接 NTP 服務器...")

            for i, server in enumerate(ntp_servers):
                try:
                    print(f"[INFO] 嘗試連接 NTP 服務器 {i+1}/{len(ntp_servers)}: {server}")

                    client = ntplib.NTPClient()
                    response = client.request(server, version=3, timeout=5)  # 增加超時時間
                    ntp_time = datetime.fromtimestamp(response.tx_time)

                    print(f"[INFO] ✅ NTP 連接成功: {server}")
                    print(f"[INFO] 🕐 NTP 時間: {ntp_time.strftime('%Y-%m-%d %H:%M:%S')}")

                    return ntp_time, server

                except Exception as e:
                    print(f"[WARN] ❌ NTP 服務器 {server} 連接失敗: {e}")
                    continue

            # 如果所有 NTP 服務器都失敗，使用本地時間
            print("[WARN] ⚠️ 所有 NTP 服務器連接失敗，使用本地時間")
            local_time = datetime.now()
            print(f"[INFO] 🕐 本地時間: {local_time.strftime('%Y-%m-%d %H:%M:%S')}")

            return local_time, "本地系統時間"

        def update_time_display():
            try:
                from datetime import datetime, timedelta

                # 獲取 NTP 精準時間
                try:
                    current_time, time_source = get_ntp_time()
                    current_time_label.config(text=f"⏰ 當前時間: {current_time.strftime('%H:%M:%S')} 📡{time_source}")
                    print(f"[INFO] 時間來源: {time_source}")
                except Exception as e:
                    print(f"[ERROR] NTP 時間獲取失敗: {e}")
                    current_time = datetime.now()
                    time_source = "本地系統時間"
                    current_time_label.config(text=f"⏰ 當前時間: {current_time.strftime('%H:%M:%S')} 📡{time_source}")

                # 獲取觸發時間（優先級：用戶輸入 > CSV > 預設）
                try:
                    print(f"[DEBUG] filtered_tasks 數量: {len(filtered_tasks) if filtered_tasks else 0}")

                    # 檢查是否有用戶在 GUI 中輸入的時間
                    # 從主 GUI 實例中獲取用戶設定的觸發時間
                    user_input_time = None
                    try:
                        # 嘗試從全局變量或主 GUI 實例獲取
                        if hasattr(self, 'trigger_time') and self.trigger_time != "09:30:00.001":
                            user_input_time = self.trigger_time
                            print(f"[DEBUG] 從主 GUI 獲取用戶時間: '{user_input_time}'")
                    except:
                        user_input_time = None

                    trigger_time_str = ""
                    trigger_time_source = ""

                    if user_input_time:
                        # 優先使用用戶輸入的時間
                        trigger_time_str = user_input_time
                        trigger_time_source = "用戶輸入"
                        print(f"[DEBUG] 使用用戶輸入時間: '{trigger_time_str}'")
                    elif filtered_tasks and len(filtered_tasks) > 0:
                        # 其次使用 CSV 中的時間
                        task = filtered_tasks[0]
                        print(f"[DEBUG] 第一個任務: {task}")
                        trigger_time_str = task.get('trigger_time', '')
                        if trigger_time_str:
                            trigger_time_source = "orders.csv"
                            print(f"[DEBUG] 使用 CSV 時間: '{trigger_time_str}'")
                        else:
                            # 最後使用預設時間
                            trigger_time_str = "09:30:00.001"
                            trigger_time_source = "系統預設"
                            print(f"[DEBUG] 使用預設時間: '{trigger_time_str}'")
                    else:
                        # 沒有任務時使用預設時間
                        trigger_time_str = "09:30:00.001"
                        trigger_time_source = "系統預設"
                        print(f"[DEBUG] 無任務，使用預設時間: '{trigger_time_str}'")

                    if trigger_time_str:
                        # 計算倒數計時和日期標示
                        try:
                            # 解析觸發時間
                            if '.' in trigger_time_str:
                                # 包含毫秒的格式 HH:MM:SS.fff
                                trigger_time_obj = datetime.strptime(trigger_time_str, '%H:%M:%S.%f').time()
                            else:
                                # 標準格式 HH:MM:SS
                                trigger_time_obj = datetime.strptime(trigger_time_str, '%H:%M:%S').time()

                            # 組合今天的日期和觸發時間
                            today = current_time.date()
                            trigger_datetime = datetime.combine(today, trigger_time_obj)

                            print(f"[DEBUG] 當前時間: {current_time}")
                            print(f"[DEBUG] 今天觸發時間: {trigger_datetime}")

                            # 判斷是今日還是明日
                            is_tomorrow = False
                            if trigger_datetime <= current_time:
                                trigger_datetime += timedelta(days=1)
                                is_tomorrow = True
                                print(f"[DEBUG] 觸發時間已過，設為明天: {trigger_datetime}")

                            # 生成日期標示
                            if is_tomorrow:
                                date_indicator = "明日"
                                date_emoji = "🌅"
                            else:
                                date_indicator = "今日"
                                date_emoji = "☀️"

                            # 更新觸發時間顯示（包含日期標示）
                            trigger_time_display = f"🎯 觸發時間: {date_emoji}{date_indicator} {trigger_time_str} 📡{trigger_time_source}"
                            trigger_time_label.config(text=trigger_time_display)

                            # 計算時間差
                            time_diff = trigger_datetime - current_time
                            print(f"[DEBUG] 時間差: {time_diff}")
                            print(f"[DEBUG] 總秒數: {time_diff.total_seconds()}")

                            # 處理負數情況
                            if time_diff.total_seconds() < 0:
                                countdown_label.config(text="⏳ 倒數計時: 已過期")
                            else:
                                total_seconds = int(time_diff.total_seconds())
                                hours, remainder = divmod(total_seconds, 3600)
                                minutes, seconds = divmod(remainder, 60)

                                print(f"[DEBUG] 倒數計時: {hours}小時 {minutes}分 {seconds}秒")

                                # 如果超過 24 小時，顯示天數
                                if hours >= 24:
                                    days = hours // 24
                                    remaining_hours = hours % 24
                                    countdown_text = f"⏳ 倒數計時: {days}天 {remaining_hours:02d}:{minutes:02d}:{seconds:02d}"
                                else:
                                    countdown_text = f"⏳ 倒數計時: {hours:02d}:{minutes:02d}:{seconds:02d}"

                                countdown_label.config(text=countdown_text)

                        except Exception as e:
                            countdown_label.config(text=f"⏳ 倒數計時: 解析錯誤")
                            print(f"[ERROR] 倒數計時計算錯誤: {e}")
                            print(f"[ERROR] 觸發時間字串: '{trigger_time_str}'")
                    else:
                        trigger_time_label.config(text="🎯 觸發時間: 未設定")
                        countdown_label.config(text="⏳ 倒數計時: 未設定")

                except Exception as e:
                    trigger_time_label.config(text="🎯 觸發時間: 解析失敗")
                    countdown_label.config(text="⏳ 倒數計時: 解析失敗")
                    print(f"[ERROR] 觸發時間處理失敗: {e}")

                # 每秒更新一次
                try:
                    window.after(1000, update_time_display)
                except Exception as e:
                    print(f"[ERROR] 設定下次更新失敗: {e}")

            except Exception as e:
                print(f"[ERROR] 時間更新主函數失敗: {e}")
                import traceback
                print(f"[ERROR] 詳細錯誤: {traceback.format_exc()}")

                # 設定錯誤訊息
                try:
                    current_time_label.config(text="⏰ 當前時間: 載入失敗")
                    trigger_time_label.config(text="🎯 觸發時間: 載入失敗")
                    countdown_label.config(text="⏳ 倒數計時: 載入失敗")
                except:
                    pass

        # 啟動時間更新
        update_time_display()

        # 按鈕區域
        btn_frame = tk.Frame(scrollable_frame)
        btn_frame.pack(pady=(20, 0))

        # 第一排按鈕
        btn_frame1 = tk.Frame(scrollable_frame)
        btn_frame1.pack(pady=(10, 5))

        ready_btn = tk.Button(
            btn_frame1,
            text="✅ 準備完成",
            command=on_ready,
            width=15,
            bg="lightgreen",
            font=("Arial", 10, "bold")
        )
        ready_btn.pack(side="left", padx=5)

        # 第二排按鈕
        btn_frame2 = tk.Frame(scrollable_frame)
        btn_frame2.pack(pady=5)

        test_btn = tk.Button(
            btn_frame2,
            text="🧪 測試模式",
            command=lambda: self._handle_mode_selection('test', window, filtered_tasks),
            width=15,
            bg="lightyellow",
            font=("Arial", 10, "bold")
        )
        test_btn.pack(side="left", padx=5)

        cancel_btn = tk.Button(
            btn_frame2,
            text="❌ 取消",
            command=on_cancel,
            width=15,
            bg="lightcoral"
        )
        cancel_btn.pack(side="left", padx=5)

        window.protocol("WM_DELETE_WINDOW", on_cancel)

        print("[DEBUG] GUI 視窗已創建，準備顯示...")
        try:
            window.mainloop()
        except Exception as e:
            print(f"[WARN] GUI 顯示失敗: {e}")
            print("[INFO] 使用控制台交互模式...")

            # 控制台交互模式
            print("\n" + "="*60)
            print("🚀 AGES-KH 搶單系統 - 控制台模式")
            print("="*60)
            print("\n📋 操作步驟：")
            print("1. 🌐 瀏覽器已自動啟動並導航到平台首頁")
            print("2. 🔐 請手動登入您的帳號")
            print("3. 📄 導航到「進廠確認單管理」頁面")
            print("4. ✅ 確認可以看到訂單列表")
            print("5. 🎯 輸入 'ready' 開始搶單")
            print("\n⚠️ 注意事項：")
            print("• 請確保已在正確的訂單管理頁面")
            print("• 系統將自動尋找並點擊編輯按鈕")
            print("• 驗證碼需要手動輸入")
            print("• 系統會自動計算最佳提交時間")

            while True:
                user_input = input("\n請輸入 'ready' 開始搶單，或 'quit' 退出: ").strip().lower()
                if user_input == 'ready':
                    on_ready()
                    break
                elif user_input == 'quit':
                    on_cancel()
                    break
                else:
                    print("請輸入 'ready' 或 'quit'")

    def _handle_mode_selection(self, mode, window, filtered_tasks):
        """處理不同模式的選擇"""
        window.attributes('-topmost', False)
        window.destroy()

        if mode == 'verification_ready':
            print("[INFO] 用戶確認驗證碼輸入完成，開始搶單流程...")
            logger.info("🎯 用戶確認驗證碼已輸入，開始搶單...")
        elif mode == 'test':
            print("[INFO] 用戶選擇測試模式...")
            logger.info("🧪 測試模式啟動...")

        # 設置全局模式變量
        global current_mode
        current_mode = mode

        # 開始搶單流程
        execute_order_grabbing(filtered_tasks)

    def _verify_correct_page(self):
        """驗證用戶是否在正確的搶單頁面"""
        global driver

        try:
            # 等待頁面穩定
            time.sleep(2)

            # 🔄 重新獲取當前頁面信息（重要！）
            current_url = driver.current_url
            page_title = driver.title
            print(f"[INFO] 檢查當前頁面: {current_url}")
            print(f"[INFO] 頁面標題: {page_title}")

            # 🔍 更智能的頁面檢測
            try:
                # 多次嘗試檢測頁面內容
                for attempt in range(3):
                    print(f"[INFO] 第 {attempt + 1} 次檢測頁面內容...")

                    # 重新獲取頁面元素
                    tables = driver.find_elements(By.XPATH, "//table")
                    rows = driver.find_elements(By.XPATH, "//tr")
                    page_source = driver.page_source

                    print(f"[INFO] 頁面包含 {len(tables)} 個表格，{len(rows)} 個表格行")

                    # 檢查是否有訂單相關的內容
                    has_order_content = any(keyword in page_source for keyword in [
                        "進廠確認單", "訂單", "編輯", "E48B", "預約", "確認單", "搶單"
                    ])

                    print(f"[INFO] 頁面包含訂單相關內容: {has_order_content}")

                    # 🎯 關鍵檢查：是否包含目標訂單號
                    target_order_in_page = "E48B201611405190953" in page_source
                    print(f"[INFO] 頁面包含目標訂單號: {target_order_in_page}")

                    # 如果找到目標訂單，直接確認為正確頁面
                    if target_order_in_page:
                        print("[INFO] ✅ 檢測到目標訂單，確認為正確頁面")
                        return True

                    # 如果有表格行且有訂單內容，也認為是正確頁面
                    if len(rows) > 1 and has_order_content:
                        print("[INFO] ✅ 檢測到正確的搶單頁面（有表格和訂單內容）")
                        return True

                    if attempt < 2:
                        print("[INFO] 等待 2 秒後重新檢測...")
                        time.sleep(2)

            except Exception as e:
                print(f"[WARN] 檢查表格失敗: {e}")

            # 如果檢測不到訂單內容，詢問用戶
            print(f"[WARN] 無法確認當前頁面是否為搶單頁面")

            # 顯示確認對話框
            import tkinter.messagebox as msgbox
            response = msgbox.askyesno(
                "GUI-07 頁面確認",
                f"當前頁面資訊：\n"
                f"URL: {current_url}\n"
                f"標題: {page_title}\n"
                f"表格數量: {len(tables) if 'tables' in locals() else '未知'}\n"
                f"表格行數: {len(rows) if 'rows' in locals() else '未知'}\n\n"
                f"請確認您是否已經：\n"
                f"1. 成功登入系統\n"
                f"2. 導航到進廠確認單列表頁面\n"
                f"3. 能看到訂單 E48B201611405190953\n\n"
                f"如果是，請點擊「是」繼續搶單\n"
                f"如果不是，請點擊「否」並手動導航到正確頁面"
            )

            if not response:
                print("[INFO] 用戶選擇不繼續，程序終止")
                safe_exit()
                return False
            else:
                print("[INFO] 用戶確認當前頁面正確，繼續執行搶單")
                return True

        except Exception as e:
            print(f"[ERROR] 頁面驗證失敗: {e}")
            return False

    def run(self):
        """執行主程式"""
        self.window.mainloop()

# ===== 搶單執行核心邏輯 =====
def execute_order_grabbing(tasks):
    """🔒 [v1.6.0-CORE] 執行搶單主流程 - 已驗證功能，不可修改

    功能：搶單核心邏輯，包含訂單掃描、編輯按鈕點擊、彈窗處理
    狀態：🔒 已驗證正常，絕對不可修改
    """
    global driver, result_detector, dom_inspector

    print("[DEBUG] 進入 execute_order_grabbing 函數")
    print(f"[DEBUG] 任務數量: {len(tasks) if tasks else 0}")

    if not driver or not result_detector:
        print("[ERROR] 瀏覽器或檢測器未初始化")
        print(f"[DEBUG] driver: {driver is not None}")
        print(f"[DEBUG] result_detector: {result_detector is not None}")
        return

    try:
        print("[INFO] 🚀 開始執行搶單流程...")

        # 執行每個任務
        for task in tasks:
            order_id = task.get('order_id', 'N/A')
            trigger_time = task.get('trigger_time', '09:30:00.001')

            print(f"[INFO] 📋 處理訂單: {order_id}")
            print(f"[INFO] ⏰ 觸發時間: {trigger_time}")

            # 1. 掃描頁面 DOM 元素
            print("[INFO] 📋 掃描頁面 DOM 元素...")
            scan_result = scan_current_page_dom()
            if not scan_result:
                print("[WARN] DOM 掃描未找到元素，但繼續嘗試搶單...")

            # 2. 執行搶單動作（包含等待觸發時間）
            print(f"[INFO] 🎯 開始執行搶單流程: {order_id}")
            result = execute_single_order_grab(task)

            # 3. 檢查是否應該終止
            if result_detector.should_terminate_process(result):
                print("[INFO] 🔚 搶單生命週期結束，程序終止")
                break
            else:
                print("[INFO] 🔄 繼續下一個任務...")

    except Exception as e:
        print(f"[ERROR] 搶單執行過程發生異常: {e}")
        logger.error(f"搶單執行異常: {e}")
    finally:
        print("[INFO] 搶單流程結束")

def scan_current_page_dom():
    """掃描當前頁面的 DOM 元素"""
    global driver, dom_inspector

    try:
        print("[INFO] 開始掃描當前頁面...")

        # 檢查 driver 是否可用
        if not driver:
            print("[ERROR] 瀏覽器 driver 未初始化")
            return False

        try:
            current_url = driver.current_url
            print(f"[INFO] 正在掃描頁面: {current_url}")
        except Exception as e:
            print(f"[ERROR] 無法獲取當前頁面 URL: {e}")
            return False

        # 使用 DOM 檢查器的 find_order_elements 方法，傳入 driver
        elements = dom_inspector.find_order_elements(driver)

        if elements:
            print(f"[INFO] ✅ 掃描完成，找到 {len(elements)} 個元素")

            # 保存掃描結果
            dom_inspector.save_elements_config(elements)
            print("[INFO] DOM 配置已更新")

            return True
        else:
            print("[WARN] ⚠️ 未找到任何相關元素")
            return False

    except Exception as e:
        print(f"[ERROR] DOM 掃描失敗: {e}")
        logger.error(f"DOM 掃描失敗: {e}")
        return False

def wait_for_trigger_time(trigger_time_str, task=None):
    """等待觸發時間 - 整合 RTT 預測功能"""
    try:
        # 解析觸發時間
        trigger_time = datetime.strptime(trigger_time_str, "%H:%M:%S.%f").time()
        print(f"[INFO] ⏰ 目標觸發時間: {trigger_time_str}")

        # RTT 預測和調整
        rtt_adjustment = 0
        if task:
            try:
                # 獲取 RTT 預測模型
                model_name = task.get('model', 'A')  # 默認使用模型 A
                print(f"[INFO] 🔮 使用 RTT 預測模型: {model_name}")

                # 計算平均 RTT - 修復調用方式
                avg_rtt, rtts, timestamps = get_avg_rtt(model_name)
                if avg_rtt > 0:
                    rtt_adjustment = avg_rtt / 1000.0  # 轉換為秒
                    print(f"[INFO] 📊 平均 RTT: {avg_rtt:.2f}ms ({rtt_adjustment:.3f}s)")
                    print(f"[INFO] 📈 RTT 樣本數: {len(rtts)}")
                    print(f"[INFO] ⚡ 將提前 {rtt_adjustment:.3f} 秒執行以補償網路延遲")

                    # 記錄 RTT 日誌
                    logger.info(f"RTT 預測完成: model={model_name}, avg_rtt={avg_rtt:.2f}ms, samples={len(rtts)}, adjustment={rtt_adjustment:.3f}s")
                else:
                    print("[WARN] ⚠️ 無法獲取 RTT 數據，使用原始觸發時間")
                    logger.warning("RTT 預測返回 0，使用原始觸發時間")
            except Exception as e:
                print(f"[WARN] RTT 預測失敗，使用原始觸發時間: {e}")
                logger.error(f"RTT 預測失敗: {e}")
                # 如果 RTT 預測失敗，繼續使用原始時間

        # 調整後的實際觸發時間
        adjusted_trigger_time = trigger_time
        if rtt_adjustment > 0:
            # 將時間轉換為 datetime 進行計算
            trigger_datetime = datetime.combine(date.today(), trigger_time)
            adjusted_datetime = trigger_datetime - timedelta(seconds=rtt_adjustment)
            adjusted_trigger_time = adjusted_datetime.time()
            print(f"[INFO] 🎯 調整後觸發時間: {adjusted_trigger_time.strftime('%H:%M:%S.%f')[:-3]}")

        # 等待邏輯
        while True:
            current_time = datetime.now().time()

            if current_time >= adjusted_trigger_time:
                print(f"[INFO] 🚀 觸發時間到達: {current_time}")
                if rtt_adjustment > 0:
                    print(f"[INFO] ⚡ RTT 補償: 提前了 {rtt_adjustment:.3f} 秒執行")
                break

            # 計算剩餘時間
            current_datetime = datetime.combine(date.today(), current_time)
            trigger_datetime = datetime.combine(date.today(), adjusted_trigger_time)

            if trigger_datetime < current_datetime:
                # 觸發時間是明天
                trigger_datetime += timedelta(days=1)

            remaining = (trigger_datetime - current_datetime).total_seconds()

            if remaining > 60:
                print(f"[INFO] ⏳ 距離觸發時間還有 {remaining:.1f} 秒...")
                time.sleep(10)  # 每10秒檢查一次
            elif remaining > 1:
                time.sleep(0.1)  # 最後1秒每100毫秒檢查一次
            else:
                time.sleep(0.01)  # 最後1秒每10毫秒檢查一次

    except Exception as e:
        print(f"[ERROR] 等待觸發時間失敗: {e}")
        logger.error(f"等待觸發時間失敗: {e}")

def execute_single_order_grab(task):
    """🔒 [v1.6.0-CORE] 執行單個訂單搶單 - 已驗證功能，不可修改

    功能：單個訂單的完整搶單流程
    包含：編輯按鈕點擊 → 彈窗出現 → 驗證碼處理 → 確認流程
    狀態：🔒 步驟1-2已驗證，步驟3-4需要修改為GUI#09
    """
    global driver, result_detector

    try:
        order_id = task.get('order_id', 'N/A')
        print(f"[INFO] 🎯 開始搶單: {order_id}")

        # 🔒 [v1.6.0-CORE] 步驟1: 尋找並點擊編輯按鈕 - 已驗證，不可修改
        print(f"[INFO] 步驟 1: 尋找並點擊訂單 {order_id} 的編輯按鈕")
        if not find_and_click_edit_button(order_id):
            print(f"[ERROR] 找不到訂單 {order_id} 的編輯按鈕")
            return {"lifecycle_ended": True, "is_success": False, "message": "找不到編輯按鈕"}

        # 🔒 [v1.6.0-CORE] 步驟2: 等待彈窗出現 - 已驗證，不可修改
        print(f"[INFO] 步驟 2: 等待編輯頁面載入...")
        time.sleep(3)

        # 檢查是否成功進入編輯頁面
        try:
            current_url = driver.current_url
            page_title = driver.title
            print(f"[INFO] 當前頁面: {current_url}")
            print(f"[INFO] 頁面標題: {page_title}")
        except Exception as e:
            print(f"[WARN] 無法獲取頁面信息: {e}")

        # ⚠️ [v1.6.0-MODIFY] 步驟3: 需要修改為GUI#09 - 取代原有的驗證碼+確認流程
        # 原本流程：GUI-09(驗證碼輸入) → GUI-08(確認準備完成)
        # 希望流程：GUI#09(提供"正式送單"和"模擬送單"選項)
        print(f"[INFO] 步驟 3: 等待人工輸入驗證碼...")
        captcha_code = handle_captcha_input()  # 🔒 保留驗證碼輸入邏輯
        if not captcha_code:
            print("[ERROR] 驗證碼輸入失敗或被取消")
            return {"lifecycle_ended": True, "is_success": False, "message": "驗證碼輸入失敗"}

        # ⚠️ [v1.6.0-REPLACE] 步驟4: 用GUI#09取代GUI-08
        # 原本：wait_for_user_ready_confirmation() → GUI-08(確認準備完成)
        # 修改：show_gui09_verification_reminder() → GUI#09(正式送單/模擬送單)
        print(f"[INFO] 步驟 4: 等待用戶確認準備完成...")
        if not wait_for_user_ready_confirmation():  # ⚠️ 需要替換為GUI#09
            print("[ERROR] 用戶取消操作")
            return {"lifecycle_ended": True, "is_success": False, "message": "用戶取消操作"}

        # ⚠️ [v1.6.0-NEW] 步驟5: 實現核心功能 - 根據GUI#09選擇執行送出/取消
        # 核心功能1：正式送單 → 自動點擊"送出"按鈕
        # 核心功能2：模擬送單 → 自動點擊"取消"按鈕
        print(f"[INFO] 步驟 5: 根據 RTT 精確計算送出時機...")
        if not execute_precise_submit(task):  # ⚠️ 需要修改為支援送出/取消選擇
            print("[ERROR] 精確送出失敗")
            return {"lifecycle_ended": True, "is_success": False, "message": "精確送出失敗"}

        # 6. 檢測送出結果
        print("[INFO] 步驟 6: 檢測送出結果...")
        result = result_detector.detect_submission_result(timeout=10)

        # 7. 記錄結果
        print("[INFO] 步驟 7: 記錄搶單結果...")
        log_order_result(task, result)

        return result

    except Exception as e:
        print(f"[ERROR] 搶單執行異常: {e}")
        logger.error(f"搶單執行異常: {e}")
        return {"lifecycle_ended": True, "is_success": False, "message": f"執行異常: {e}"}

def find_and_click_edit_button(order_id):
    """🔒 [v1.6.0-CORE] 自動尋找並點擊編輯按鈕 - 已驗證功能，不可修改

    功能：在頁面中尋找指定訂單的編輯按鈕並點擊
    狀態：🔒 已驗證正常，絕對不可修改
    """
    global driver

    try:
        print(f"[INFO] 🔍 自動尋找訂單 {order_id} 的編輯按鈕...")

        # 🔄 使用 WebDriverWait 等待 AJAX 動態載入完成（推薦方法）
        print(f"[INFO] 🔄 使用 WebDriverWait 等待 AJAX 內容載入...")

        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # 方法 1: 等待目標訂單號出現
            print(f"[INFO] 等待目標訂單號 {order_id} 出現...")
            try:
                WebDriverWait(driver, 30).until(
                    EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{order_id}')]"))
                )
                print(f"[INFO] ✅ 成功檢測到目標訂單 {order_id}！")
            except Exception as e:
                print(f"[WARN] 等待目標訂單超時: {e}")

                # 方法 2: 等待任何包含 E48B 的元素出現（更寬泛的條件）
                print(f"[INFO] 嘗試等待任何 E48B 訂單出現...")
                try:
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'E48B')]"))
                    )
                    print(f"[INFO] ✅ 檢測到 E48B 訂單，頁面載入完成！")
                except Exception as e2:
                    print(f"[WARN] 等待 E48B 訂單也超時: {e2}")

                    # 方法 3: 等待表格行數達到一定數量
                    print(f"[INFO] 嘗試等待表格內容載入...")
                    try:
                        WebDriverWait(driver, 15).until(
                            lambda driver: len(driver.find_elements(By.XPATH, "//tr")) > 5
                        )
                        print(f"[INFO] ✅ 檢測到足夠的表格行，認為頁面載入完成")
                    except Exception as e3:
                        print(f"[WARN] 等待表格內容也超時: {e3}")

            # 額外等待 2 秒確保渲染完成
            print(f"[INFO] 額外等待 2 秒確保渲染完成...")
            time.sleep(2)

            # 🎯 使用 JavaScript 再次檢查動態內容
            print(f"[INFO] 使用 JavaScript 檢查最終載入狀態...")
            js_final_check = f"""
            // 檢查頁面最終狀態
            var bodyText = document.body.innerText || document.body.textContent || '';
            var hasTargetOrder = bodyText.includes('{order_id}');
            var rows = document.querySelectorAll('tr');
            var rowCount = rows.length;

            // 檢查是否有編輯按鈕
            var editButtons = document.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
            var editButtonCount = 0;
            editButtons.forEach(function(btn) {{
                var text = (btn.textContent || btn.value || '').toLowerCase();
                if (text.includes('編輯') || text.includes('edit')) {{
                    editButtonCount++;
                }}
            }});

            return {{
                hasTargetOrder: hasTargetOrder,
                rowCount: rowCount,
                editButtonCount: editButtonCount,
                bodyTextLength: bodyText.length,
                sampleText: bodyText.substring(0, 200)
            }};
            """

            final_result = driver.execute_script(js_final_check)
            print(f"[INFO] 最終檢測結果:")
            print(f"  - 包含目標訂單: {final_result['hasTargetOrder']}")
            print(f"  - 表格行數: {final_result['rowCount']}")
            print(f"  - 編輯按鈕數量: {final_result['editButtonCount']}")
            print(f"  - 頁面文字長度: {final_result['bodyTextLength']}")
            print(f"  - 頁面內容樣本: {final_result['sampleText']}...")

            # 🔍 詳細診斷：使用新的日誌記錄功能
            print(f"\n[DEBUG] 🔍 詳細診斷 - 記錄頁面內容到日誌文件...")
            page_info = log_page_content(driver, "WebDriverWait完成後")

            if page_info:
                print(f"[DEBUG] 頁面信息已記錄到日誌文件: {current_log_file}")
                print(f"[DEBUG] 快速摘要:")
                print(f"  - URL: {page_info['url']}")
                print(f"  - 標題: {page_info['title']}")
                print(f"  - 包含目標訂單: {page_info['has_target_order']}")
                print(f"  - 包含 E48B: {page_info['has_e48b']}")
                print(f"  - 表格數量: {page_info['table_count']}")
                print(f"  - 表格行數: {page_info['row_count']}")
                print(f"  - 編輯按鈕數量: {page_info['edit_button_count']}")
                print(f"  - E48B 元素數量: {page_info['e48b_element_count']}")
            else:
                print(f"[ERROR] 無法記錄頁面內容")

        except Exception as e:
            print(f"[ERROR] WebDriverWait 等待失敗: {e}")

        print(f"[INFO] 動態內容等待完成，開始搜尋訂單...")

        # 🎯 確保我們在 iframe 內進行搜尋
        print(f"[INFO] 🎯 確保在 iframe 內搜尋...")
        try:
            # 檢查是否有 iframe - 使用雙層切換邏輯
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            if iframes:
                print(f"[INFO] 檢測到 {len(iframes)} 個 iframe，執行雙層切換")

                # 第一層：切換到訂單清單 iframe
                driver.switch_to.frame(0)
                print(f"[INFO] ✅ 已切換到第一層 iframe (訂單清單)")

                # 檢查第一層中是否有第二層 iframe
                second_iframes = driver.find_elements(By.TAG_NAME, "iframe")
                if second_iframes:
                    # 第二層：切換到編輯彈窗 iframe
                    driver.switch_to.frame(0)
                    print(f"[INFO] ✅ 已切換到第二層 iframe (編輯彈窗)")
                else:
                    print(f"[INFO] 第一層 iframe 中未檢測到第二層 iframe")
            else:
                print(f"[INFO] 未檢測到 iframe，在主頁面搜尋")
        except Exception as e:
            print(f"[WARN] 切換到 iframe 時發生錯誤: {e}")
            # 嘗試回到主頁面
            try:
                driver.switch_to.default_content()
            except:
                pass

        # 獲取當前頁面信息
        current_url = driver.current_url
        page_title = driver.title
        print(f"[INFO] 當前頁面: {current_url}")
        print(f"[INFO] 頁面標題: {page_title}")

        # 策略 1: 直接尋找包含訂單號的表格行
        print(f"[INFO] 🔍 搜尋包含訂單號 {order_id} 的表格行...")

        # 更全面的選擇器
        row_selectors = [
            f"//tr[contains(., '{order_id}')]",
            f"//tbody//tr[contains(., '{order_id}')]",
            f"//table//tr[contains(., '{order_id}')]",
            f"//tr[td[contains(text(), '{order_id}')]]",
            f"//tr[td[contains(., '{order_id}')]]",
            f"//tr[descendant::*[contains(text(), '{order_id}')]]",
            f"//*[contains(text(), '{order_id}')]/ancestor::tr",
            f"//div[contains(., '{order_id}')]/ancestor::tr"
        ]

        target_row = None
        for i, row_selector in enumerate(row_selectors, 1):
            try:
                print(f"[DEBUG] 嘗試選擇器 {i}: {row_selector}")
                rows = driver.find_elements(By.XPATH, row_selector)
                print(f"[DEBUG] 找到 {len(rows)} 個匹配的行")

                if rows:
                    # 檢查每一行是否真的包含完整的訂單號
                    for row in rows:
                        row_text = row.text
                        print(f"[DEBUG] 檢查行內容: {row_text[:100]}...")
                        if order_id in row_text:
                            target_row = row
                            print(f"[INFO] ✅ 找到包含訂單 {order_id} 的表格行")
                            print(f"[INFO] 行內容預覽: {row_text[:200]}...")
                            break

                    if target_row:
                        break

            except Exception as e:
                print(f"[DEBUG] 選擇器 {row_selector} 失敗: {e}")
                continue

        if not target_row:
            print(f"[ERROR] ❌ 找不到包含訂單 {order_id} 的表格行")

            # 嘗試獲取頁面上所有的表格行來調試
            try:
                all_rows = driver.find_elements(By.XPATH, "//tr")
                print(f"[DEBUG] 頁面總共有 {len(all_rows)} 個表格行")

                # 顯示所有行的內容來調試（增加顯示數量）
                print(f"[DEBUG] 顯示所有表格行內容以供調試：")
                for i, row in enumerate(all_rows[:20]):  # 顯示前20行
                    try:
                        row_text = row.text.strip()
                        if row_text:
                            print(f"[DEBUG] 行 {i+1}: {row_text}")
                        else:
                            print(f"[DEBUG] 行 {i+1}: (空行)")
                    except Exception as e:
                        print(f"[DEBUG] 行 {i+1}: 無法讀取 - {e}")

                # 額外檢查：搜尋頁面中是否包含訂單號（不限於表格）
                print(f"[DEBUG] 檢查整個頁面是否包含訂單號 {order_id}...")
                page_source = driver.page_source
                if order_id in page_source:
                    print(f"[INFO] ✅ 頁面原始碼中包含訂單號 {order_id}")

                    # 🔍 實現類似 Ctrl+F 的搜尋機制
                    print(f"[INFO] 🔍 使用 Ctrl+F 類似的搜尋機制...")

                    # 策略 1: 使用 JavaScript 搜尋文字並高亮
                    try:
                        # 使用 JavaScript 在頁面中搜尋文字
                        js_search_script = f"""
                        // 搜尋包含訂單號的所有元素
                        var walker = document.createTreeWalker(
                            document.body,
                            NodeFilter.SHOW_TEXT,
                            null,
                            false
                        );

                        var foundElements = [];
                        var node;
                        while (node = walker.nextNode()) {{
                            if (node.textContent.includes('{order_id}')) {{
                                foundElements.push(node.parentElement);
                            }}
                        }}

                        return foundElements.map(el => {{
                            return {{
                                tagName: el.tagName,
                                text: el.textContent.substring(0, 200),
                                className: el.className,
                                id: el.id
                            }};
                        }});
                        """

                        found_elements = driver.execute_script(js_search_script)
                        print(f"[INFO] JavaScript 搜尋找到 {len(found_elements)} 個包含訂單號的元素")

                        for i, elem_info in enumerate(found_elements[:5]):
                            print(f"[DEBUG] JS元素 {i+1}: {elem_info['tagName']} - {elem_info['text'][:100]}...")

                        # 如果找到元素，嘗試定位到編輯按鈕
                        if found_elements:
                            print(f"[INFO] 🎯 嘗試在找到的元素附近尋找編輯按鈕...")

                            # 使用 JavaScript 尋找編輯按鈕
                            js_find_edit_script = f"""
                            var walker = document.createTreeWalker(
                                document.body,
                                NodeFilter.SHOW_TEXT,
                                null,
                                false
                            );

                            var orderElements = [];
                            var node;
                            while (node = walker.nextNode()) {{
                                if (node.textContent.includes('{order_id}')) {{
                                    orderElements.push(node.parentElement);
                                }}
                            }}

                            // 在每個包含訂單號的元素附近尋找編輯按鈕
                            var editButtons = [];
                            orderElements.forEach(function(orderEl) {{
                                // 向上尋找表格行
                                var row = orderEl.closest('tr');
                                if (row) {{
                                    // 在行中尋找編輯相關的元素
                                    var editElements = row.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
                                    editElements.forEach(function(el) {{
                                        var text = (el.textContent || el.value || '').toLowerCase();
                                        if (text.includes('編輯') || text.includes('edit')) {{
                                            editButtons.push({{
                                                element: el,
                                                text: el.textContent || el.value,
                                                tagName: el.tagName,
                                                className: el.className,
                                                id: el.id
                                            }});
                                        }}
                                    }});
                                }}
                            }});

                            return editButtons.map(btn => {{
                                return {{
                                    text: btn.text,
                                    tagName: btn.tagName,
                                    className: btn.className,
                                    id: btn.id
                                }};
                            }});
                            """

                            edit_buttons = driver.execute_script(js_find_edit_script)
                            print(f"[INFO] 找到 {len(edit_buttons)} 個可能的編輯按鈕")

                            for i, btn_info in enumerate(edit_buttons):
                                print(f"[DEBUG] 編輯按鈕 {i+1}: {btn_info['tagName']} - '{btn_info['text']}'")

                            # 如果找到編輯按鈕，嘗試點擊第一個
                            if edit_buttons:
                                try:
                                    click_script = f"""
                                    var walker = document.createTreeWalker(
                                        document.body,
                                        NodeFilter.SHOW_TEXT,
                                        null,
                                        false
                                    );

                                    var node;
                                    while (node = walker.nextNode()) {{
                                        if (node.textContent.includes('{order_id}')) {{
                                            var row = node.parentElement.closest('tr');
                                            if (row) {{
                                                var editElements = row.querySelectorAll('a, button, input[type="button"], input[type="submit"]');
                                                for (var i = 0; i < editElements.length; i++) {{
                                                    var el = editElements[i];
                                                    var text = (el.textContent || el.value || '').toLowerCase();
                                                    if (text.includes('編輯') || text.includes('edit')) {{
                                                        el.click();
                                                        return true;
                                                    }}
                                                }}
                                            }}
                                        }}
                                    }}
                                    return false;
                                    """

                                    clicked = driver.execute_script(click_script)
                                    if clicked:
                                        print(f"[INFO] ✅ 成功使用 JavaScript 點擊編輯按鈕!")
                                        time.sleep(2)
                                        return True
                                    else:
                                        print(f"[WARN] JavaScript 點擊失敗")

                                except Exception as e:
                                    print(f"[WARN] JavaScript 點擊編輯按鈕失敗: {e}")

                    except Exception as e:
                        print(f"[WARN] JavaScript 搜尋失敗: {e}")

                    # 嘗試用更廣泛的選擇器
                    broader_selectors = [
                        f"//*[contains(text(), '{order_id}')]",
                        f"//div[contains(., '{order_id}')]",
                        f"//span[contains(., '{order_id}')]",
                        f"//td[contains(., '{order_id}')]"
                    ]

                    for selector in broader_selectors:
                        try:
                            elements = driver.find_elements(By.XPATH, selector)
                            if elements:
                                print(f"[INFO] 使用選擇器 {selector} 找到 {len(elements)} 個元素")
                                for elem in elements[:3]:  # 顯示前3個
                                    print(f"[DEBUG] 元素內容: {elem.text[:100]}...")
                        except Exception as e:
                            print(f"[DEBUG] 選擇器 {selector} 失敗: {e}")
                else:
                    print(f"[ERROR] ❌ 頁面原始碼中不包含訂單號 {order_id}")
                    print(f"[INFO] 這可能表示：")
                    print(f"  1. 頁面內容尚未完全載入")
                    print(f"  2. 需要手動刷新頁面")
                    print(f"  3. 訂單號格式不匹配")
                    print(f"  4. 當前不在正確的頁面")

                    # 顯示當前頁面的一些內容來調試
                    print(f"[DEBUG] 當前頁面內容預覽（前500字符）:")
                    print(f"{page_source[:500]}...")

            except Exception as e:
                print(f"[DEBUG] 獲取所有行失敗: {e}")

            return False

        # 在找到的行中尋找編輯按鈕
        print(f"[INFO] � 在找到的行中搜尋編輯按鈕...")

        edit_selectors_in_row = [
            ".//a[contains(text(), '編輯')]",
            ".//button[contains(text(), '編輯')]",
            ".//input[@value='編輯']",
            ".//a[contains(@class, 'edit')]",
            ".//button[contains(@class, 'edit')]",
            ".//input[contains(@class, 'edit')]",
            ".//a",  # 最後嘗試任何連結
            ".//button",  # 最後嘗試任何按鈕
            ".//input[@type='button']",  # 嘗試按鈕類型的 input
            ".//input[@type='submit']"   # 嘗試提交類型的 input
        ]

        for i, edit_selector in enumerate(edit_selectors_in_row, 1):
            try:
                print(f"[DEBUG] 嘗試編輯選擇器 {i}: {edit_selector}")
                edit_elements = target_row.find_elements(By.XPATH, edit_selector)
                print(f"[DEBUG] 找到 {len(edit_elements)} 個候選編輯元素")

                for j, edit_element in enumerate(edit_elements):
                    try:
                        # 獲取元素信息
                        element_text = edit_element.text.strip()
                        element_value = edit_element.get_attribute('value') or ''
                        element_class = edit_element.get_attribute('class') or ''
                        element_tag = edit_element.tag_name

                        print(f"[DEBUG] 元素 {j+1}: tag={element_tag}, text='{element_text}', value='{element_value}', class='{element_class}'")

                        # 檢查元素是否可見且可點擊
                        if not (edit_element.is_displayed() and edit_element.is_enabled()):
                            print(f"[DEBUG] 元素 {j+1} 不可見或不可點擊")
                            continue

                        # 如果是通用選擇器，檢查文字內容
                        if edit_selector in [".//a", ".//button", ".//input[@type='button']", ".//input[@type='submit']"]:
                            combined_text = f"{element_text} {element_value}".strip()
                            if "編輯" not in combined_text:
                                print(f"[DEBUG] 元素 {j+1} 不包含'編輯'文字: '{combined_text}'")
                                continue

                        # 點擊編輯按鈕
                        print(f"[INFO] 🎯 嘗試點擊編輯元素: {element_tag} - '{element_text or element_value}'")
                        edit_element.click()
                        print(f"[INFO] ✅ 成功點擊編輯按鈕!")
                        print(f"[INFO] 按鈕詳情: {element_tag}[text='{element_text}', value='{element_value}']")
                        logger.info(f"🎯 成功點擊編輯按鈕: {element_tag}[text='{element_text}', value='{element_value}']")

                        # 🔍 立即檢測點擊後的 DOM 變化 - 增強版本
                        print(f"[INFO] 🔍 檢測點擊編輯按鈕後的 DOM 變化...")
                        logger.info(f"🔍 檢測點擊編輯按鈕後的 DOM 變化...")

                        # 🚀 使用增強的檢測功能
                        dialog_detection_result = monitor_dialog_opening()

                        if dialog_detection_result:
                            print(f"[INFO] ✅ 編輯彈窗檢測成功")
                            logger.info(f"✅ 編輯彈窗檢測成功")

                            # 保存檢測結果到全域變數供後續使用
                            global last_dialog_detection
                            last_dialog_detection = dialog_detection_result
                        else:
                            print(f"[WARN] ⚠️ 編輯彈窗檢測未完全成功，但繼續執行")
                            logger.warning(f"⚠️ 編輯彈窗檢測未完全成功，但繼續執行")

                        # 等待頁面跳轉
                        time.sleep(2)
                        return True

                    except Exception as e:
                        print(f"[DEBUG] 點擊元素 {j+1} 失敗: {e}")
                        continue

            except Exception as e:
                print(f"[DEBUG] 編輯選擇器 {edit_selector} 失敗: {e}")
                continue

        print(f"[ERROR] ❌ 在訂單 {order_id} 的行中找不到可點擊的編輯按鈕")

        # 顯示該行的所有可點擊元素來調試
        try:
            all_clickable = target_row.find_elements(By.XPATH, ".//a | .//button | .//input[@type='button'] | .//input[@type='submit']")
            print(f"[DEBUG] 該行共有 {len(all_clickable)} 個可點擊元素:")
            for i, elem in enumerate(all_clickable[:5]):  # 只顯示前5個
                try:
                    print(f"[DEBUG] 元素 {i+1}: {elem.tag_name} - text='{elem.text}' value='{elem.get_attribute('value') or ''}' class='{elem.get_attribute('class') or ''}'")
                except:
                    continue
        except Exception as e:
            print(f"[DEBUG] 獲取可點擊元素失敗: {e}")

        return False

    except Exception as e:
        print(f"[ERROR] 尋找編輯按鈕失敗: {e}")
        logger.error(f"尋找編輯按鈕失敗: {e}")
        return False

def wait_for_user_ready_confirmation():
    """⚠️ [v1.6.0-REPLACE] GUI-08 確認準備完成 - 需要替換為GUI#09

    原本功能：顯示確認對話框，用戶點擊"是"/"否"
    替換目標：GUI#09 提供"正式送單"/"模擬送單"選擇
    狀態：⚠️ 整個函數需要替換為GUI#09實現
    """
    try:
        import tkinter as tk
        import tkinter.messagebox as msgbox

        # 創建確認對話框
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)

        response = msgbox.askyesno(
            "GUI-08 確認準備完成",
            "請確認您已經：\n\n"
            "✅ 輸入了驗證碼\n"
            "✅ 檢查了所有信息正確\n"
            "✅ 準備好讓程式自動點擊送出\n\n"
            "點擊「是」程式將在觸發時間自動送出\n"
            "點擊「否」取消本次搶單",
            parent=root
        )

        root.destroy()

        if response:
            print("[INFO] ✅ 用戶確認準備完成，程式將在觸發時間自動送出")
            return True
        else:
            print("[INFO] ❌ 用戶取消操作")
            return False

    except Exception as e:
        print(f"[ERROR] 用戶確認失敗: {e}")
        return False

def execute_precise_submit(task):
    """⚠️ [v1.6.0-MODIFY] 精確送出執行 - 需要支援送出/取消選擇

    原本功能：只支援點擊"送出"按鈕
    修改目標：根據GUI#09選擇，支援點擊"送出"或"取消"按鈕
    狀態：⚠️ 需要修改為支援兩種模式
    """
    global driver

    try:
        # 獲取觸發時間
        trigger_time_str = task.get('trigger_time', '09:30:00.001')
        print(f"[INFO] 目標觸發時間: {trigger_time_str}")

        # 計算 RTT 補償（如果之前沒有計算過）
        rtt_adjustment = 0
        try:
            model_name = task.get('model', 'A')
            avg_rtt, rtts, timestamps = get_avg_rtt(model_name)
            if avg_rtt > 0:
                rtt_adjustment = avg_rtt / 1000.0  # 轉換為秒
                print(f"[INFO] RTT 補償: {avg_rtt:.2f}ms ({rtt_adjustment:.3f}s)")
        except Exception as e:
            print(f"[WARN] RTT 計算失敗: {e}")

        # 解析觸發時間
        from datetime import datetime, time as dt_time, timedelta
        trigger_time = datetime.strptime(trigger_time_str, "%H:%M:%S.%f").time()

        # 調整觸發時間（減去 RTT）
        if rtt_adjustment > 0:
            trigger_datetime = datetime.combine(datetime.now().date(), trigger_time)
            adjusted_datetime = trigger_datetime - timedelta(seconds=rtt_adjustment)
            adjusted_trigger_time = adjusted_datetime.time()
            print(f"[INFO] 調整後觸發時間: {adjusted_trigger_time.strftime('%H:%M:%S.%f')[:-3]}")
        else:
            adjusted_trigger_time = trigger_time

        # 等待精確的觸發時間
        print(f"[INFO] ⏰ 等待精確觸發時間...")
        while True:
            current_time = datetime.now().time()
            if current_time >= adjusted_trigger_time:
                print(f"[INFO] 🚀 觸發時間到達，立即送出!")
                break
            time.sleep(0.001)  # 1毫秒精度

        # 立即點擊送出按鈕
        return click_submit_button()

    except Exception as e:
        print(f"[ERROR] 精確送出失敗: {e}")
        return False

def handle_captcha_input():
    """🔒 [v1.6.0-CORE] GUI-09 驗證碼輸入 - 已驗證功能，不可修改

    功能：顯示驗證碼輸入對話框，用戶輸入驗證碼
    狀態：🔒 已驗證正常，絕對不可修改
    """
    global driver

    try:
        print("[INFO] 開始處理驗證碼輸入...")

        # 等待編輯頁面完全載入
        time.sleep(2)

        # 檢查是否有驗證碼圖片
        captcha_image_found = False
        captcha_image_selectors = [
            "img[src*='captcha']",
            "img[src*='verify']",
            "img[alt*='驗證']",
            "img[alt*='驗證碼']"
        ]

        for img_selector in captcha_image_selectors:
            try:
                captcha_images = driver.find_elements(By.CSS_SELECTOR, img_selector)
                if captcha_images:
                    captcha_image_found = True
                    print(f"[INFO] ✅ 找到驗證碼圖片: {img_selector}")
                    break
            except Exception:
                continue

        if not captcha_image_found:
            print("[WARN] 未找到驗證碼圖片，可能不需要驗證碼")

        # 檢查是否需要先點擊「確認取得驗證碼」按鈕
        refresh_button_selectors = [
            "button:contains('確認取得驗證碼')",
            "button:contains('重新產生')",
            "a:contains('重新產生')"
        ]

        for refresh_selector in refresh_button_selectors:
            try:
                if ":contains(" in refresh_selector:
                    text_content = refresh_selector.split("'")[1]
                    xpath = f"//button[contains(text(), '{text_content}')]"
                    refresh_buttons = driver.find_elements(By.XPATH, xpath)
                else:
                    refresh_buttons = driver.find_elements(By.CSS_SELECTOR, refresh_selector)

                if refresh_buttons:
                    refresh_button = refresh_buttons[0]
                    if refresh_button.is_displayed() and refresh_button.is_enabled():
                        print(f"[INFO] 點擊驗證碼刷新按鈕: {refresh_button.text}")
                        refresh_button.click()
                        time.sleep(1)  # 等待驗證碼重新載入
                        break
            except Exception:
                continue

        # 彈出對話框讓用戶輸入驗證碼
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)  # 確保對話框在最上層

        captcha_code = simpledialog.askstring(
            "GUI-09 驗證碼輸入",
            "請查看瀏覽器中的驗證碼圖片並輸入驗證碼:\n\n注意：驗證碼通常為4-5位數字或字母",
            parent=root
        )

        root.destroy()

        if captcha_code and captcha_code.strip():
            captcha_code = captcha_code.strip()
            print(f"[INFO] 用戶輸入驗證碼: {captcha_code}")

            # 尋找驗證碼輸入框 - 改進的選擇器
            captcha_selectors = [
                "input[name*='captcha']",
                "input[name*='verify']",
                "input[id*='captcha']",
                "input[id*='verify']",
                "input[placeholder*='驗證']",
                "input[placeholder*='驗證碼']",
                "input[type='text'][maxlength='4']",
                "input[type='text'][maxlength='5']",
                "input[type='text']"  # 最後嘗試所有文字輸入框
            ]

            for selector in captcha_selectors:
                try:
                    captcha_inputs = driver.find_elements(By.CSS_SELECTOR, selector)
                    for captcha_input in captcha_inputs:
                        if captcha_input.is_displayed() and captcha_input.is_enabled():
                            # 🎯 改進驗證碼輸入方式
                            try:
                                # 方法1: 等待元素可互動
                                WebDriverWait(driver, 3).until(EC.element_to_be_clickable(captcha_input))

                                # 方法2: 使用 JavaScript 清空並輸入
                                driver.execute_script("arguments[0].focus();", captcha_input)
                                driver.execute_script("arguments[0].value = '';", captcha_input)
                                time.sleep(0.2)  # 短暫等待
                                driver.execute_script("arguments[0].value = arguments[1];", captcha_input, captcha_code)

                                # 方法3: 觸發相關事件
                                driver.execute_script("""
                                    arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                                    arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                                """, captcha_input)

                                print(f"[INFO] ✅ 成功輸入驗證碼到: {selector}")
                                print(f"[INFO] 輸入的驗證碼: {captcha_code}")
                                logger.info(f"🎯 成功輸入驗證碼: {captcha_code}")

                                # 🎯 獲取驗證碼輸入後的事件日誌
                                print("[INFO] 📊 獲取驗證碼輸入後的事件日誌...")
                                event_log = get_browser_event_log()
                                if event_log:
                                    # 尋找驗證碼輸入相關的事件
                                    captcha_events = [e for e in event_log if
                                                    e.get('type') in ['input', 'keydown'] and
                                                    captcha_code in str(e.get('target', {}).get('value', ''))]
                                    if captcha_events:
                                        print(f"[INFO] 🎯 檢測到 {len(captcha_events)} 個驗證碼輸入事件")
                                        logger.info(f"🎯 檢測到 {len(captcha_events)} 個驗證碼輸入事件")

                                return captcha_code

                            except Exception as e:
                                print(f"[WARN] JavaScript 輸入失敗，嘗試傳統方式: {e}")
                                logger.warning(f"🎯 JavaScript 輸入失敗: {e}")
                                # 備用方法: 傳統輸入
                                try:
                                    captcha_input.clear()
                                    captcha_input.send_keys(captcha_code)
                                    print(f"[INFO] ✅ 傳統方式輸入驗證碼成功: {selector}")
                                    return captcha_code
                                except Exception as e2:
                                    print(f"[ERROR] 驗證碼輸入完全失敗: {e2}")
                                    logger.error(f"🎯 驗證碼輸入完全失敗: {e2}")
                                    continue
                except Exception as e:
                    print(f"[DEBUG] 嘗試驗證碼輸入框 {selector} 失敗: {e}")
                    continue

            print("[ERROR] 找不到可用的驗證碼輸入框")
            return None
        else:
            print("[WARN] 用戶取消驗證碼輸入或輸入為空")
            return None

    except Exception as e:
        print(f"[ERROR] 驗證碼處理失敗: {e}")
        logger.error(f"驗證碼處理失敗: {e}")
        return None

def setup_browser_event_monitoring():
    """🎯 設置瀏覽器事件監控 - 立即行動版本"""
    global driver

    try:
        print("[INFO] 🎯 設置瀏覽器事件監控...")
        logger.info("🎯 設置瀏覽器事件監控...")

        # JavaScript 事件監控腳本
        monitoring_script = """
        // 創建事件監控系統
        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];

        // 監控所有點擊事件 - 增強版本
        document.addEventListener('click', function(e) {
            var eventInfo = {
                type: 'click',
                timestamp: new Date().toISOString(),
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    textContent: (e.target.textContent || '').substring(0, 100),
                    value: e.target.value || '',
                    type: e.target.type || '',
                    name: e.target.name || '',
                    title: e.target.title || '',
                    alt: e.target.alt || '',
                    href: e.target.href || '',
                    onclick: (e.target.onclick || '').toString().substring(0, 200)
                },
                coordinates: {
                    clientX: e.clientX,
                    clientY: e.clientY,
                    pageX: e.pageX,
                    pageY: e.pageY,
                    screenX: e.screenX,
                    screenY: e.screenY
                },
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                },
                element_rect: (function() {
                    try {
                        var rect = e.target.getBoundingClientRect();
                        return {
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height,
                            right: rect.right,
                            bottom: rect.bottom
                        };
                    } catch(err) {
                        return null;
                    }
                })()
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Enhanced Click Event:', eventInfo);
        }, true);

        // 監控所有鍵盤輸入 - 增強版本
        document.addEventListener('keydown', function(e) {
            var eventInfo = {
                type: 'keydown',
                timestamp: new Date().toISOString(),
                key: e.key,
                code: e.code,
                keyCode: e.keyCode,
                which: e.which,
                altKey: e.altKey,
                ctrlKey: e.ctrlKey,
                shiftKey: e.shiftKey,
                metaKey: e.metaKey,
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    type: e.target.type || '',
                    name: e.target.name || '',
                    placeholder: e.target.placeholder || '',
                    value: (e.target.value || '').substring(0, 50),
                    maxLength: e.target.maxLength || '',
                    required: e.target.required || false
                },
                element_rect: (function() {
                    try {
                        var rect = e.target.getBoundingClientRect();
                        return {
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height
                        };
                    } catch(err) {
                        return null;
                    }
                })()
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Enhanced Key Event:', eventInfo);
        }, true);

        // 監控輸入框值變化
        document.addEventListener('input', function(e) {
            var eventInfo = {
                type: 'input',
                timestamp: new Date().toISOString(),
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    type: e.target.type || '',
                    value: (e.target.value || '').substring(0, 50)
                }
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Input Event:', eventInfo);
        }, true);

        // 監控表單提交
        document.addEventListener('submit', function(e) {
            var eventInfo = {
                type: 'submit',
                timestamp: new Date().toISOString(),
                target: {
                    tagName: e.target.tagName,
                    id: e.target.id || '',
                    className: e.target.className || '',
                    action: e.target.action || ''
                }
            };
            window.AGES_EVENT_LOG.push(eventInfo);
            console.log('AGES Submit Event:', eventInfo);
        }, true);

        // 監控 DOM 變化
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    var eventInfo = {
                        type: 'dom_change',
                        timestamp: new Date().toISOString(),
                        mutation_type: mutation.type,
                        added_nodes: mutation.addedNodes.length,
                        target: {
                            tagName: mutation.target.tagName,
                            id: mutation.target.id || '',
                            className: mutation.target.className || ''
                        }
                    };
                    window.AGES_EVENT_LOG.push(eventInfo);
                    console.log('AGES DOM Change:', eventInfo);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true
        });

        console.log('AGES Event Monitoring System Initialized');
        return true;
        """

        # 執行監控腳本
        driver.execute_script(monitoring_script)
        print("[INFO] ✅ 瀏覽器事件監控已啟動")
        logger.info("✅ 瀏覽器事件監控已啟動")
        return True

    except Exception as e:
        print(f"[ERROR] 設置瀏覽器事件監控失敗: {e}")
        logger.error(f"設置瀏覽器事件監控失敗: {e}")
        return False

def get_browser_event_log():
    """📊 獲取瀏覽器事件日誌"""
    global driver

    try:
        # 獲取事件日誌
        event_log = driver.execute_script("return window.AGES_EVENT_LOG || [];")

        if event_log:
            print(f"[INFO] 📊 獲取到 {len(event_log)} 個瀏覽器事件")
            logger.info(f"📊 獲取到 {len(event_log)} 個瀏覽器事件")

            # 記錄最近的事件
            recent_events = event_log[-10:]  # 最近10個事件
            for i, event in enumerate(recent_events):
                print(f"[INFO] 📋 事件 {len(event_log)-len(recent_events)+i+1}: {event['type']} - {event.get('target', {}).get('tagName', 'N/A')}")
                logger.info(f"📋 事件詳情: {event}")

        return event_log

    except Exception as e:
        print(f"[ERROR] 獲取瀏覽器事件日誌失敗: {e}")
        logger.error(f"獲取瀏覽器事件日誌失敗: {e}")
        return []

def wait_for_dialog_content_loaded():
    """🎯 等待編輯彈窗內容完全載入"""
    global driver

    try:
        print("[INFO] 🎯 等待編輯彈窗內容載入...")
        logger.info("🎯 等待編輯彈窗內容載入...")

        max_wait_time = 15  # 最多等待15秒
        wait_interval = 1   # 每1秒檢查一次

        for attempt in range(int(max_wait_time / wait_interval)):
            try:
                # 檢測彈窗內容
                current_text = driver.execute_script("return document.body.innerText || '';")

                # 檢查是否包含編輯相關內容
                content_indicators = [
                    '驗證碼',
                    '送出',
                    '確認',
                    '提交',
                    '儲存',
                    'submit',
                    'confirm',
                    'save',
                    '表單',
                    '輸入',
                    'input',
                    'form'
                ]

                found_indicators = [indicator for indicator in content_indicators if indicator in current_text]

                print(f"[INFO] 🔍 載入檢查 {attempt+1}: 內容長度={len(current_text)}, 找到指標={found_indicators}")
                logger.info(f"🔍 載入檢查 {attempt+1}: 內容長度={len(current_text)}, 找到指標={found_indicators}")

                # 如果找到編輯相關內容，認為載入完成
                if found_indicators:
                    print(f"[INFO] ✅ 編輯彈窗內容載入完成，找到指標: {found_indicators}")
                    logger.info(f"✅ 編輯彈窗內容載入完成，找到指標: {found_indicators}")
                    return True

                # 檢查內容是否有變化（表示還在載入中）
                if attempt > 0:
                    # 如果內容長度穩定且包含編輯標題，可能載入完成
                    if '編輯進廠確認單' in current_text and len(current_text) > 100:
                        print(f"[INFO] ✅ 編輯彈窗基本內容已載入")
                        logger.info(f"✅ 編輯彈窗基本內容已載入")
                        return True

                time.sleep(wait_interval)

            except Exception as e:
                print(f"[DEBUG] 載入檢查 {attempt+1} 失敗: {e}")
                time.sleep(wait_interval)

        print(f"[WARN] ⚠️ 編輯彈窗內容載入超時")
        logger.warning(f"⚠️ 編輯彈窗內容載入超時")
        return False

    except Exception as e:
        print(f"[ERROR] 等待編輯彈窗內容載入失敗: {e}")
        logger.error(f"等待編輯彈窗內容載入失敗: {e}")
        return False

def monitor_dialog_opening():
    """🔍 監控編輯彈窗打開過程 - 立即行動版本"""
    global driver

    try:
        print("[INFO] 🔍 開始監控編輯彈窗打開過程...")
        logger.info("🔍 開始監控編輯彈窗打開過程...")

        # 📸 截圖保存當前狀態
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            screenshot_path = f"screenshots/dialog_opening_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            driver.save_screenshot(screenshot_path)
            print(f"[INFO] 📸 彈窗打開截圖: {screenshot_path}")
            logger.info(f"📸 彈窗打開截圖: {screenshot_path}")
        except Exception as e:
            print(f"[WARN] 截圖失敗: {e}")

        # 🔍 等待彈窗完全載入 - 增加等待時間
        max_wait_time = 10  # 最多等待10秒
        wait_interval = 0.5  # 每500毫秒檢查一次

        for attempt in range(int(max_wait_time / wait_interval)):
            try:
                # 檢測彈窗是否出現
                dialog_appeared = False

                # 方法1: 檢測 jQuery UI Dialog
                ui_dialogs = driver.find_elements(By.CSS_SELECTOR, ".ui-dialog")
                if ui_dialogs:
                    dialog_appeared = True
                    print(f"[INFO] ✅ 檢測到 {len(ui_dialogs)} 個 jQuery UI Dialog")
                    logger.info(f"✅ 檢測到 {len(ui_dialogs)} 個 jQuery UI Dialog")

                # 方法2: 檢測 role='dialog'
                role_dialogs = driver.find_elements(By.CSS_SELECTOR, "[role='dialog']")
                if role_dialogs:
                    dialog_appeared = True
                    print(f"[INFO] ✅ 檢測到 {len(role_dialogs)} 個 role='dialog' 元素")
                    logger.info(f"✅ 檢測到 {len(role_dialogs)} 個 role='dialog' 元素")

                # 方法3: 檢測彈窗標題
                title_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '編輯進廠確認單')]")
                if title_elements:
                    dialog_appeared = True
                    print(f"[INFO] ✅ 檢測到編輯彈窗標題")
                    logger.info(f"✅ 檢測到編輯彈窗標題")

                if dialog_appeared:
                    print(f"[INFO] 🎯 彈窗已出現，等待內容完全載入...")

                    # 🎯 等待編輯彈窗內容完全載入
                    content_loaded = wait_for_dialog_content_loaded()
                    if content_loaded:
                        print(f"[INFO] ✅ 編輯彈窗內容已完全載入")
                        logger.info(f"✅ 編輯彈窗內容已完全載入")
                    else:
                        print(f"[WARN] ⚠️ 編輯彈窗內容載入可能不完整")
                        logger.warning(f"⚠️ 編輯彈窗內容載入可能不完整")

                    break

                time.sleep(wait_interval)

            except Exception as e:
                print(f"[DEBUG] 彈窗檢測嘗試 {attempt+1} 失敗: {e}")

        # 🔍 詳細檢測彈窗內容
        print("[INFO] 🔍 詳細檢測彈窗內容...")

        # 記錄點擊後的完整頁面狀態
        try:
            current_source = driver.page_source
            current_text = driver.execute_script("return document.body.innerText || document.body.textContent || '';")

            print(f"[INFO] 📊 彈窗打開後頁面狀態:")
            print(f"  - page_source 長度: {len(current_source)}")
            print(f"  - innerText 長度: {len(current_text)}")

            logger.info(f"📊 彈窗打開後頁面狀態: page_source={len(current_source)}, innerText={len(current_text)}")

            # 檢測關鍵內容
            has_captcha_text = any(keyword in current_text for keyword in ['驗證碼', '驗證', 'captcha', 'verify'])
            has_submit_text = '送出' in current_text
            has_cancel_text = '取消' in current_text
            has_close_text = 'Close' in current_text

            print(f"[INFO] 🔍 關鍵內容檢測:")
            print(f"  - 包含驗證碼相關文字: {has_captcha_text}")
            print(f"  - 包含'送出'文字: {has_submit_text}")
            print(f"  - 包含'取消'文字: {has_cancel_text}")
            print(f"  - 包含'Close'文字: {has_close_text}")

            logger.info(f"🔍 關鍵內容檢測: 驗證碼={has_captcha_text}, 送出={has_submit_text}, 取消={has_cancel_text}, Close={has_close_text}")

            # 檢測表單元素
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            text_inputs = [inp for inp in all_inputs if inp.get_attribute("type") in ["text", None]]
            all_buttons = driver.find_elements(By.TAG_NAME, "button")

            print(f"[INFO] 📋 表單元素統計:")
            print(f"  - 總 input 元素: {len(all_inputs)}")
            print(f"  - 文字輸入框: {len(text_inputs)}")
            print(f"  - 總 button 元素: {len(all_buttons)}")

            logger.info(f"📋 表單元素統計: input={len(all_inputs)}, text_input={len(text_inputs)}, button={len(all_buttons)}")

            # 🚀 立即執行增強的按鈕檢測
            print("[INFO] 🚀 執行增強的按鈕檢測...")
            button_detection = enhanced_dialog_button_detection()

            return {
                'dialog_appeared': dialog_appeared,
                'has_captcha_text': has_captcha_text,
                'has_submit_text': has_submit_text,
                'has_cancel_text': has_cancel_text,
                'has_close_text': has_close_text,
                'input_count': len(all_inputs),
                'text_input_count': len(text_inputs),
                'button_count': len(all_buttons),
                'button_detection': button_detection,
                'screenshot_path': screenshot_path if 'screenshot_path' in locals() else None,
                'page_source_length': len(current_source),
                'inner_text_length': len(current_text)
            }

        except Exception as e:
            print(f"[ERROR] 彈窗內容檢測失敗: {e}")
            logger.error(f"彈窗內容檢測失敗: {e}")
            return None

    except Exception as e:
        print(f"[ERROR] 監控彈窗打開失敗: {e}")
        logger.error(f"監控彈窗打開失敗: {e}")
        return None

def enhanced_dialog_button_detection():
    """🚀 增強的編輯彈窗按鈕檢測 - 立即行動版本 (支援 iframe)"""
    global driver

    try:
        print("[INFO] 🔍 開始增強的編輯彈窗按鈕檢測...")
        logger.info("🔍 開始增強的編輯彈窗按鈕檢測...")

        # 📸 第一步：截圖保存當前狀態
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            screenshot_path = f"screenshots/dialog_detection_{timestamp}.png"
            os.makedirs("screenshots", exist_ok=True)
            driver.save_screenshot(screenshot_path)
            print(f"[INFO] 📸 截圖已保存: {screenshot_path}")
            logger.info(f"📸 截圖已保存: {screenshot_path}")
        except Exception as e:
            print(f"[WARN] 截圖失敗: {e}")
            logger.warning(f"截圖失敗: {e}")

        # 🎯 第二步：檢測並切換到 iframe (關鍵修復)
        print("[INFO] 🎯 檢測 iframe 架構...")
        logger.info("🎯 檢測 iframe 架構...")

        # 檢測所有 iframe
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print(f"[INFO] 📊 檢測到 {len(iframes)} 個 iframe")
        logger.info(f"📊 檢測到 {len(iframes)} 個 iframe")

        # 嘗試切換到每個 iframe 並檢測內容
        iframe_detection_results = []

        for i, iframe in enumerate(iframes):
            try:
                print(f"[INFO] 🔍 檢測 iframe {i+1}...")

                # 切換到 iframe
                driver.switch_to.frame(iframe)

                # 檢測 iframe 內的內容
                iframe_content = driver.execute_script("return document.body.innerText || document.body.textContent || '';")
                iframe_buttons = driver.find_elements(By.TAG_NAME, "button")
                iframe_inputs = driver.find_elements(By.TAG_NAME, "input")

                # 檢測是否包含編輯相關內容
                has_edit_content = any(keyword in iframe_content for keyword in ['編輯進廠確認單', '送出', '取消', '驗證碼'])

                iframe_info = {
                    'index': i,
                    'content_length': len(iframe_content),
                    'button_count': len(iframe_buttons),
                    'input_count': len(iframe_inputs),
                    'has_edit_content': has_edit_content,
                    'content_preview': iframe_content[:200] if iframe_content else ''
                }

                iframe_detection_results.append(iframe_info)

                print(f"[INFO] 📋 iframe {i+1} 資訊:")
                print(f"  - 內容長度: {iframe_info['content_length']}")
                print(f"  - 按鈕數量: {iframe_info['button_count']}")
                print(f"  - 輸入框數量: {iframe_info['input_count']}")
                print(f"  - 包含編輯內容: {iframe_info['has_edit_content']}")
                if iframe_info['content_preview']:
                    print(f"  - 內容預覽: {iframe_info['content_preview'][:100]}...")

                logger.info(f"📋 iframe {i+1} 資訊: {iframe_info}")

                # 如果找到編輯相關內容，在此 iframe 中執行檢測
                if has_edit_content:
                    print(f"[INFO] 🎯 在 iframe {i+1} 中找到編輯內容，執行詳細檢測...")
                    logger.info(f"🎯 在 iframe {i+1} 中找到編輯內容，執行詳細檢測...")

                    # 在此 iframe 中執行按鈕檢測
                    detection_result = detect_buttons_in_current_context()
                    detection_result['active_iframe'] = i
                    detection_result['iframe_info'] = iframe_info

                    # 切換回主頁面
                    driver.switch_to.default_content()
                    return detection_result

                # 切換回主頁面
                driver.switch_to.default_content()

            except Exception as e:
                print(f"[DEBUG] 檢測 iframe {i+1} 失敗: {e}")
                logger.warning(f"檢測 iframe {i+1} 失敗: {e}")
                # 確保切換回主頁面
                try:
                    driver.switch_to.default_content()
                except:
                    pass

        # 如果沒有在 iframe 中找到編輯內容，在主頁面檢測
        print("[INFO] 🔍 在主頁面執行檢測...")
        logger.info("🔍 在主頁面執行檢測...")
        detection_result = detect_buttons_in_current_context()
        detection_result['active_iframe'] = None
        detection_result['iframe_detection_results'] = iframe_detection_results

        return detection_result

    except Exception as e:
        print(f"[ERROR] 增強按鈕檢測失敗: {e}")
        logger.error(f"增強按鈕檢測失敗: {e}")
        # 確保切換回主頁面
        try:
            driver.switch_to.default_content()
        except:
            pass
        return None

def detect_buttons_in_current_context():
    """在當前上下文中檢測按鈕 (可能是主頁面或 iframe)"""
    global driver

    try:
        # 🔍 第三步：全面掃描彈窗的各個區域
        print("[INFO] 🔍 掃描 jQuery UI Dialog 結構...")

        # A. 檢測 jQuery UI Dialog 容器
        dialog_containers = []
        dialog_selectors = [
            ".ui-dialog",
            "[role='dialog']",
            ".ui-dialog-content",
            ".modal",
            ".dialog",
            ".popup"
        ]

        for selector in dialog_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    dialog_containers.extend(elements)
                    print(f"[INFO] ✅ 找到 {len(elements)} 個 {selector} 容器")
                    logger.info(f"✅ 找到 {len(elements)} 個 {selector} 容器")
            except Exception as e:
                print(f"[DEBUG] 檢測 {selector} 失敗: {e}")

        # B. 檢測彈窗標題
        title_found = False
        title_selectors = [
            ".ui-dialog-title",
            ".ui-dialog-titlebar",
            ".modal-title",
            ".dialog-title"
        ]

        for selector in title_selectors:
            try:
                titles = driver.find_elements(By.CSS_SELECTOR, selector)
                for title in titles:
                    title_text = title.text.strip()
                    if "編輯進廠確認單" in title_text:
                        print(f"[INFO] ✅ 找到彈窗標題: {title_text}")
                        logger.info(f"✅ 找到彈窗標題: {title_text}")
                        title_found = True
                        break
            except Exception as e:
                print(f"[DEBUG] 檢測標題 {selector} 失敗: {e}")

        # C. 專門檢測 jQuery UI Dialog 按鈕區域
        print("[INFO] 🎯 專門檢測 jQuery UI Dialog 按鈕區域...")
        button_containers = []
        button_container_selectors = [
            ".ui-dialog-buttonpane",
            ".ui-dialog-buttonset",
            ".ui-dialog-buttons",
            ".modal-footer",
            ".dialog-footer",
            ".dialog-buttons"
        ]

        for selector in button_container_selectors:
            try:
                containers = driver.find_elements(By.CSS_SELECTOR, selector)
                if containers:
                    button_containers.extend(containers)
                    print(f"[INFO] ✅ 找到 {len(containers)} 個 {selector} 按鈕容器")
                    logger.info(f"✅ 找到 {len(containers)} 個 {selector} 按鈕容器")

                    # 檢測容器內的按鈕
                    for container in containers:
                        try:
                            container_text = container.text.strip()
                            print(f"[INFO] 📋 按鈕容器內容: {container_text}")
                            logger.info(f"📋 按鈕容器內容: {container_text}")
                        except Exception as e:
                            print(f"[DEBUG] 讀取容器內容失敗: {e}")
            except Exception as e:
                print(f"[DEBUG] 檢測按鈕容器 {selector} 失敗: {e}")

        # D. 全面檢測所有可點擊元素 (擴大搜尋範圍)
        print("[INFO] 🔍 全面檢測所有可點擊元素...")
        all_buttons = []
        button_selectors = [
            "button",
            "input[type='submit']",
            "input[type='button']",
            "input[type='reset']",
            "a[role='button']",
            ".ui-button",
            ".btn",
            "a[href='#']",
            "a[onclick]",
            "div[onclick]",
            "span[onclick]",
            "[role='button']"
        ]

        for selector in button_selectors:
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                all_buttons.extend(buttons)
                print(f"[INFO] 📊 找到 {len(buttons)} 個 {selector} 元素")
                logger.info(f"📊 找到 {len(buttons)} 個 {selector} 元素")
            except Exception as e:
                print(f"[DEBUG] 檢測元素 {selector} 失敗: {e}")

        # 額外檢測：尋找包含特定文字的所有元素
        print("[INFO] 🎯 搜尋包含送出相關文字的所有元素...")
        submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok', '確定']

        for keyword in submit_keywords:
            try:
                # 使用 XPath 搜尋包含特定文字的所有元素
                xpath_selectors = [
                    f"//*[contains(text(), '{keyword}')]",
                    f"//*[@value='{keyword}']",
                    f"//*[@title='{keyword}']",
                    f"//*[@alt='{keyword}']"
                ]

                for xpath in xpath_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, xpath)
                        if elements:
                            all_buttons.extend(elements)
                            print(f"[INFO] 🎯 找到 {len(elements)} 個包含 '{keyword}' 的元素 (XPath: {xpath})")
                            logger.info(f"🎯 找到 {len(elements)} 個包含 '{keyword}' 的元素")
                    except Exception as e:
                        print(f"[DEBUG] XPath 搜尋失敗 {xpath}: {e}")

            except Exception as e:
                print(f"[DEBUG] 搜尋關鍵字 '{keyword}' 失敗: {e}")

        # 去除重複元素
        unique_buttons = []
        seen_elements = set()
        for button in all_buttons:
            try:
                element_id = id(button)
                if element_id not in seen_elements:
                    unique_buttons.append(button)
                    seen_elements.add(element_id)
            except:
                unique_buttons.append(button)

        all_buttons = unique_buttons
        print(f"[INFO] 📊 去重後總計 {len(all_buttons)} 個可點擊元素")

        # E. 詳細分析每個按鈕
        print(f"[INFO] 📋 詳細分析 {len(all_buttons)} 個按鈕...")
        submit_buttons = []
        cancel_buttons = []
        other_buttons = []

        for i, button in enumerate(all_buttons):
            try:
                # 獲取按鈕信息
                button_text = button.text.strip()
                button_value = button.get_attribute('value') or ''
                button_class = button.get_attribute('class') or ''
                button_id = button.get_attribute('id') or ''
                button_tag = button.tag_name
                button_type = button.get_attribute('type') or ''

                # 檢查可見性
                is_displayed = button.is_displayed()
                is_enabled = button.is_enabled()

                # 獲取更多屬性信息
                button_title = button.get_attribute('title') or ''
                button_alt = button.get_attribute('alt') or ''
                button_onclick = button.get_attribute('onclick') or ''

                # 組合所有可能包含按鈕功能信息的文字
                all_text = f"{button_text} {button_value} {button_title} {button_alt} {button_onclick}".strip().lower()

                print(f"[INFO] 🔍 元素 {i+1}: {button_tag}[type='{button_type}'] text='{button_text}' value='{button_value}' class='{button_class}' id='{button_id}' title='{button_title}' visible={is_displayed} enabled={is_enabled}")
                logger.info(f"🔍 元素 {i+1}: {button_tag}[type='{button_type}'] text='{button_text}' value='{button_value}' class='{button_class}' id='{button_id}' title='{button_title}' visible={is_displayed} enabled={is_enabled}")

                # 更精確的按鈕分類
                submit_keywords = ['送出', 'submit', '提交', '確認', '儲存', 'save', 'confirm', 'ok', '確定']
                cancel_keywords = ['取消', 'cancel', 'close', '關閉', '刪除', 'delete']

                is_submit = any(keyword in all_text for keyword in submit_keywords)
                is_cancel = any(keyword in all_text for keyword in cancel_keywords)

                # 特殊處理：如果同時包含取消和其他關鍵字，優先判斷為取消
                if '取消' in all_text and any(other in all_text for other in ['刪除']):
                    is_cancel = True
                    is_submit = False

                button_info = {
                    'element': button,
                    'text': button_text,
                    'value': button_value,
                    'class': button_class,
                    'id': button_id,
                    'tag': button_tag,
                    'type': button_type,
                    'title': button_title,
                    'alt': button_alt,
                    'onclick': button_onclick[:100] if button_onclick else '',  # 限制長度
                    'visible': is_displayed,
                    'enabled': is_enabled,
                    'all_text': all_text
                }

                if is_submit and not is_cancel:
                    submit_buttons.append(button_info)
                    print(f"[INFO] ✅ 識別為送出按鈕: '{button_text or button_value}' (匹配: {[k for k in submit_keywords if k in all_text]})")
                    logger.info(f"✅ 識別為送出按鈕: '{button_text or button_value}'")
                elif is_cancel:
                    cancel_buttons.append(button_info)
                    print(f"[INFO] ⚠️ 識別為取消按鈕: '{button_text or button_value}' (匹配: {[k for k in cancel_keywords if k in all_text]})")
                    logger.info(f"⚠️ 識別為取消按鈕: '{button_text or button_value}'")
                else:
                    other_buttons.append(button_info)
                    print(f"[INFO] ❓ 其他元素: '{button_text or button_value}'")
                    logger.info(f"❓ 其他元素: '{button_text or button_value}'")

            except Exception as e:
                print(f"[DEBUG] 分析按鈕 {i+1} 失敗: {e}")
                logger.warning(f"分析按鈕 {i+1} 失敗: {e}")

        # F. 總結檢測結果
        print(f"\n[INFO] 📊 按鈕檢測總結:")
        print(f"  - 送出按鈕: {len(submit_buttons)} 個")
        print(f"  - 取消按鈕: {len(cancel_buttons)} 個")
        print(f"  - 其他按鈕: {len(other_buttons)} 個")
        print(f"  - 總計: {len(all_buttons)} 個")

        logger.info(f"📊 按鈕檢測總結: 送出={len(submit_buttons)}, 取消={len(cancel_buttons)}, 其他={len(other_buttons)}, 總計={len(all_buttons)}")

        # G. 返回檢測結果
        return {
            'submit_buttons': submit_buttons,
            'cancel_buttons': cancel_buttons,
            'other_buttons': other_buttons,
            'dialog_containers': dialog_containers,
            'button_containers': button_containers,
            'title_found': title_found
        }

    except Exception as e:
        print(f"[ERROR] 增強按鈕檢測失敗: {e}")
        logger.error(f"增強按鈕檢測失敗: {e}")
        return None

def click_submit_button():
    """點擊送出按鈕 - 增強版本"""
    global driver

    try:
        print("[INFO] 🎯 開始尋找並點擊送出按鈕...")
        logger.info("🎯 開始尋找並點擊送出按鈕...")

        # 🚀 使用增強的按鈕檢測
        detection_result = enhanced_dialog_button_detection()

        if not detection_result:
            print("[ERROR] 按鈕檢測失敗")
            return False

        submit_buttons = detection_result.get('submit_buttons', [])

        if not submit_buttons:
            print("[ERROR] ❌ 沒有找到送出按鈕")
            logger.error("❌ 沒有找到送出按鈕")

            # 顯示所有可用按鈕供調試
            all_buttons = detection_result.get('submit_buttons', []) + detection_result.get('cancel_buttons', []) + detection_result.get('other_buttons', [])
            print(f"[DEBUG] 可用的按鈕列表:")
            for i, btn_info in enumerate(all_buttons[:10]):  # 只顯示前10個
                print(f"  {i+1}. {btn_info['tag']} - '{btn_info['text'] or btn_info['value']}' (visible={btn_info['visible']}, enabled={btn_info['enabled']})")

            return False

        # 🎯 嘗試點擊第一個可用的送出按鈕
        for i, btn_info in enumerate(submit_buttons):
            try:
                button = btn_info['element']

                if not (btn_info['visible'] and btn_info['enabled']):
                    print(f"[WARN] 送出按鈕 {i+1} 不可點擊 (visible={btn_info['visible']}, enabled={btn_info['enabled']})")
                    continue

                print(f"[INFO] 🎯 嘗試點擊送出按鈕 {i+1}: '{btn_info['text'] or btn_info['value']}'")
                logger.info(f"🎯 嘗試點擊送出按鈕 {i+1}: '{btn_info['text'] or btn_info['value']}'")

                # 多種點擊方式
                try:
                    # 🎯 點擊前獲取事件日誌基準
                    pre_click_events = get_browser_event_log()
                    pre_click_count = len(pre_click_events)

                    # 方法1: 標準點擊
                    button.click()
                    print(f"[INFO] ✅ 成功點擊送出按鈕 (標準方式)")
                    logger.info(f"✅ 成功點擊送出按鈕 (標準方式)")

                    # 🎯 點擊後檢查事件日誌
                    time.sleep(0.5)  # 等待事件記錄
                    post_click_events = get_browser_event_log()
                    new_events = post_click_events[pre_click_count:]

                    if new_events:
                        print(f"[INFO] 📊 送出按鈕點擊產生了 {len(new_events)} 個新事件")
                        logger.info(f"📊 送出按鈕點擊產生了 {len(new_events)} 個新事件")

                        # 檢查是否有提交相關事件
                        submit_events = [e for e in new_events if e.get('type') in ['click', 'submit']]
                        if submit_events:
                            print(f"[INFO] 🎯 檢測到 {len(submit_events)} 個提交相關事件")
                            logger.info(f"🎯 檢測到 {len(submit_events)} 個提交相關事件")

                    return True
                except Exception as e1:
                    print(f"[WARN] 標準點擊失敗: {e1}")

                    try:
                        # 方法2: JavaScript 點擊
                        driver.execute_script("arguments[0].click();", button)
                        print(f"[INFO] ✅ 成功點擊送出按鈕 (JavaScript 方式)")
                        logger.info(f"✅ 成功點擊送出按鈕 (JavaScript 方式)")
                        return True
                    except Exception as e2:
                        print(f"[WARN] JavaScript 點擊失敗: {e2}")

                        try:
                            # 方法3: ActionChains 點擊
                            from selenium.webdriver.common.action_chains import ActionChains
                            ActionChains(driver).move_to_element(button).click().perform()
                            print(f"[INFO] ✅ 成功點擊送出按鈕 (ActionChains 方式)")
                            logger.info(f"✅ 成功點擊送出按鈕 (ActionChains 方式)")
                            return True
                        except Exception as e3:
                            print(f"[ERROR] 所有點擊方式都失敗: 標準={e1}, JS={e2}, ActionChains={e3}")
                            logger.error(f"所有點擊方式都失敗: 標準={e1}, JS={e2}, ActionChains={e3}")
                            continue

            except Exception as e:
                print(f"[ERROR] 點擊送出按鈕 {i+1} 失敗: {e}")
                logger.error(f"點擊送出按鈕 {i+1} 失敗: {e}")
                continue

        print("[ERROR] ❌ 所有送出按鈕都無法點擊")
        logger.error("❌ 所有送出按鈕都無法點擊")
        return False

    except Exception as e:
        print(f"[ERROR] 點擊送出按鈕失敗: {e}")
        logger.error(f"點擊送出按鈕失敗: {e}")
        return False

def log_order_result(task, result):
    """記錄訂單結果"""
    try:
        order_id = task.get('order_id', 'N/A')

        if result.get('is_success') is True:
            print(f"[INFO] 🎉 訂單 {order_id} 可能搶單成功！")
        elif result.get('is_success') is False:
            print(f"[INFO] ❌ 訂單 {order_id} 搶單失敗: {result.get('message', 'N/A')}")
        else:
            print(f"[INFO] ❓ 訂單 {order_id} 結果不明確: {result.get('message', 'N/A')}")

        # 記錄到 CSV
        results_path = "results/results.csv"
        os.makedirs("results", exist_ok=True)

        # 準備結果數據
        result_data = {
            'timestamp': result.get('timestamp', datetime.now().strftime('%Y%m%d_%H%M%S')),
            'order_id': order_id,
            'order_date': task.get('order_date', 'N/A'),
            'browser': task.get('browser', 'N/A'),
            'trigger_time': task.get('trigger_time', 'N/A'),
            'is_success': result.get('is_success'),
            'result_type': result.get('result_type', 'N/A'),
            'message': result.get('message', 'N/A'),
            'screenshot_path': result.get('screenshot_path', 'N/A')
        }

        # 寫入 CSV
        file_exists = os.path.exists(results_path)
        with open(results_path, 'a', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=result_data.keys())
            if not file_exists:
                writer.writeheader()
            writer.writerow(result_data)

        print(f"[INFO] 結果已記錄到: {results_path}")

    except Exception as e:
        print(f"[ERROR] 記錄結果失敗: {e}")
        logger.error(f"記錄結果失敗: {e}")

if __name__ == "__main__":
    try:
        app = GrabberGUI()
        app.run()
    except Exception as e:
        print(f"[ERROR] 主程式執行失敗: {str(e)}")
        safe_exit()