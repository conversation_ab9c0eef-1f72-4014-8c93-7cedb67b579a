2025-07-01 18:11:09,657 - INFO - 🔍 詳細日誌記錄已啟動，日誌文件: logs/grabber_detailed_20250701_181109.log
2025-07-01 18:11:12,544 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-07-01 18:11:12,544 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-07-01 18:11:12,596 - DEBUG - chromedriver not found in PATH
2025-07-01 18:11:12,596 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:11:12,596 - DEBUG - Detected browser: chrome 137.0.7151.120
2025-07-01 18:11:12,597 - DEBUG - Required driver: chromedriver 137.0.7151.119
2025-07-01 18:11:12,597 - DEBUG - chromedriver 137.0.7151.119 already in the cache
2025-07-01 18:11:12,597 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe
2025-07-01 18:11:12,597 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-01 18:11:12,602 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.119\chromedriver.exe` in a child process with pid: 18636 using 0 to output -3
2025-07-01 18:11:13,137 - DEBUG - POST http://localhost:57170/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--start-maximized', '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-07-01 18:11:13,138 - DEBUG - Starting new HTTP connection (1): localhost:57170
2025-07-01 18:11:13,685 - DEBUG - http://localhost:57170 "POST /session HTTP/1.1" 200 0
2025-07-01 18:11:13,685 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.120","chrome":{"chromedriverVersion":"137.0.7151.119 (e0ac9d12dff5f2d33c935958b06bf1ded7f1c08c-refs/branch-heads/7151@{#2356})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir18636_98523169"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:57174"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"2fb87e00cc92c852af9727d3a13c05a6"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:11:13,685 - DEBUG - Finished Request
2025-07-01 18:11:13,686 - DEBUG - POST http://localhost:57170/session/2fb87e00cc92c852af9727d3a13c05a6/url {'url': 'https://wmc.kcg.gov.tw/'}
2025-07-01 18:11:14,604 - DEBUG - http://localhost:57170 "POST /session/2fb87e00cc92c852af9727d3a13c05a6/url HTTP/1.1" 200 0
2025-07-01 18:11:14,605 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:11:14,605 - DEBUG - Finished Request
2025-07-01 18:11:14,605 - INFO - 🎯 設置瀏覽器事件監控...
2025-07-01 18:11:14,606 - DEBUG - POST http://localhost:57170/session/2fb87e00cc92c852af9727d3a13c05a6/execute/sync {'script': '\n        // 創建事件監控系統\n        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];\n\n        // 監控所有點擊...', 'args': []}
2025-07-01 18:11:14,615 - DEBUG - http://localhost:57170 "POST /session/2fb87e00cc92c852af9727d3a13c05a6/execute/sync HTTP/1.1" 200 0
2025-07-01 18:11:14,616 - DEBUG - Remote response: status=200 | data={"value":true} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:11:14,616 - DEBUG - Finished Request
2025-07-01 18:11:14,617 - INFO - ✅ 瀏覽器事件監控已啟動
2025-07-01 18:11:14,617 - DEBUG - GET http://localhost:57170/session/2fb87e00cc92c852af9727d3a13c05a6/url {}
2025-07-01 18:11:14,668 - DEBUG - http://localhost:57170 "GET /session/2fb87e00cc92c852af9727d3a13c05a6/url HTTP/1.1" 200 0
2025-07-01 18:11:14,668 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:11:14,669 - DEBUG - Finished Request
2025-07-01 18:11:15,670 - DEBUG - GET http://localhost:57170/session/2fb87e00cc92c852af9727d3a13c05a6/url {}
2025-07-01 18:11:15,678 - DEBUG - http://localhost:57170 "GET /session/2fb87e00cc92c852af9727d3a13c05a6/url HTTP/1.1" 200 0
2025-07-01 18:11:15,678 - DEBUG - Remote response: status=200 | data={"value":"https://wmc.kcg.gov.tw/Home/Login?ReturnUrl=%2f"} | headers=HTTPHeaderDict({'Content-Length': '59', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:11:15,678 - DEBUG - Finished Request
2025-07-01 18:11:15,748 - DEBUG - DELETE http://localhost:57170/session/2fb87e00cc92c852af9727d3a13c05a6 {}
2025-07-01 18:11:15,784 - DEBUG - http://localhost:57170 "DELETE /session/2fb87e00cc92c852af9727d3a13c05a6 HTTP/1.1" 200 0
2025-07-01 18:11:15,784 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-07-01 18:11:15,785 - DEBUG - Finished Request
2025-07-01 18:11:16,689 - DEBUG - DELETE http://localhost:57170/session/2fb87e00cc92c852af9727d3a13c05a6 {}
2025-07-01 18:11:16,689 - DEBUG - Starting new HTTP connection (1): localhost:57170
