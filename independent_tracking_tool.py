#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
獨立的滑鼠鍵盤追蹤工具 - 專注於找到送出按鈕
完全獨立運行，不依賴其他模組
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
from datetime import datetime
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 常量定義
SUBMIT_BUTTON_INDICATORS = [
    '送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok',
    'button', 'btn', 'primary', 'success', '確定', '提交', '保存',
    '送出', '確認送出', '提交訂單', '確認提交'
]

VERIFICATION_KEYWORDS = [
    'verif', 'captcha', 'code', 'verification', 'verify',
    '驗證碼', '驗證', '確認碼', 'authcode', 'vcode', 'checkcode'
]

EVENT_COLLECTION_INTERVAL = 50   # 毫秒 - 高性能 CPU 可以處理
WEBDRIVER_MONITOR_INTERVAL = 500  # 毫秒 - 更頻繁的結構監控
PAGE_CHANGE_CHECK_INTERVAL = 1000 # 毫秒 - 更快的頁面變化檢測

class IndependentTrackingTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("獨立追蹤工具 - 送出按鈕檢測器")
        self.root.geometry("800x600")
        
        # 狀態變數
        self.driver = None
        self.browser_started = False
        self.tracking_active = False
        self.event_log = []
        self.processed_events = 0
        self.last_event = None

        # WebDriver 監控狀態
        self.webdriver_monitoring = False
        self.last_dom_snapshot = None
        self.last_iframe_count = 0
        self.last_window_handles = []

        # 日誌檔案
        self.log_filename = f"tracking_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        self.setup_ui()

    def __del__(self):
        """清理資源"""
        try:
            if self.driver:
                self.driver.quit()
        except:
            pass  # 忽略清理時的錯誤

    def setup_ui(self):
        """設置用戶界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 標題
        title_label = ttk.Label(main_frame, text="🔍 送出按鈕檢測器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 控制按鈕框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 瀏覽器控制
        self.start_browser_btn = ttk.Button(control_frame, text="🚀 啟動瀏覽器", command=self.start_browser)
        self.start_browser_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.check_browser_btn = ttk.Button(control_frame, text="🔍 檢查瀏覽器", command=self.check_browser_status)
        self.check_browser_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 追蹤控制
        self.start_tracking_btn = ttk.Button(control_frame, text="▶️ 啟動追蹤", command=self.start_tracking, state="disabled")
        self.start_tracking_btn.grid(row=1, column=0, padx=(0, 10), pady=(5, 0))
        
        self.stop_tracking_btn = ttk.Button(control_frame, text="⏹️ 停止追蹤", command=self.stop_tracking, state="disabled")
        self.stop_tracking_btn.grid(row=1, column=1, padx=(0, 10), pady=(5, 0))
        
        self.analyze_btn = ttk.Button(control_frame, text="📊 分析結果", command=self.analyze_results, state="disabled")
        self.analyze_btn.grid(row=1, column=2, pady=(5, 0))

        # 測試按鈕
        self.test_btn = ttk.Button(control_frame, text="🧪 測試事件", command=self.test_event_monitoring, state="disabled")
        self.test_btn.grid(row=0, column=2, padx=(0, 10))

        # 掃描元素按鈕
        self.scan_btn = ttk.Button(control_frame, text="🔍 掃描元素", command=self.scan_page_elements, state="disabled")
        self.scan_btn.grid(row=1, column=3, pady=(5, 0))

        # 動態跟蹤按鈕
        self.track_btn = ttk.Button(control_frame, text="🔄 動態跟蹤", command=self.start_dynamic_tracking, state="disabled")
        self.track_btn.grid(row=0, column=3, padx=(0, 10))

        # 技術檢測按鈕
        self.tech_btn = ttk.Button(control_frame, text="🔬 技術檢測", command=self.detect_page_technology, state="disabled")
        self.tech_btn.grid(row=2, column=0, pady=(5, 0))

        # WebDriver 監控按鈕
        self.webdriver_monitor_btn = ttk.Button(control_frame, text="🎯 WebDriver監控", command=self.start_webdriver_monitoring, state="disabled")
        self.webdriver_monitor_btn.grid(row=2, column=1, pady=(5, 0))

        # 純 WebDriver 掃描按鈕
        self.pure_webdriver_btn = ttk.Button(control_frame, text="🔧 純WebDriver掃描", command=self.pure_webdriver_scan, state="disabled")
        self.pure_webdriver_btn.grid(row=2, column=2, pady=(5, 0))

        # 三重監控按鈕
        self.triple_monitor_btn = ttk.Button(control_frame, text="🚀 三重監控", command=self.start_triple_monitoring, state="disabled")
        self.triple_monitor_btn.grid(row=2, column=3, pady=(5, 0))

        # 截圖按鈕
        self.screenshot_btn = ttk.Button(control_frame, text="📸 截圖", command=self.take_screenshot, state="disabled")
        self.screenshot_btn.grid(row=3, column=0, pady=(5, 0))
        
        # 狀態顯示 - 使用 tk.Label 而不是 ttk.Label 以支援顏色
        self.status_label = tk.Label(main_frame, text="⚪ 準備就緒", font=("Arial", 12), bg="#f0f0f0")
        self.status_label.grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        # 日誌區域
        log_label = ttk.Label(main_frame, text="📋 操作日誌:")
        log_label.grid(row=3, column=0, sticky=tk.W, pady=(20, 5))

        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80, wrap=tk.WORD)
        self.log_text.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 讓日誌文字可以選取和複製
        self.log_text.config(state=tk.NORMAL)

        # 添加右鍵選單
        self.create_context_menu()
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)

    def create_context_menu(self):
        """創建右鍵選單"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="全選", command=self.select_all)
        self.context_menu.add_command(label="複製", command=self.copy_text)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="清空日誌", command=self.clear_log)
        self.context_menu.add_command(label="保存日誌", command=self.save_log)

        # 綁定右鍵事件
        self.log_text.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """顯示右鍵選單"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def select_all(self):
        """全選文字"""
        self.log_text.tag_add(tk.SEL, "1.0", tk.END)
        self.log_text.mark_set(tk.INSERT, "1.0")
        self.log_text.see(tk.INSERT)

    def copy_text(self):
        """複製選中的文字"""
        try:
            selected_text = self.log_text.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
        except tk.TclError:
            # 沒有選中文字時，複製全部
            all_text = self.log_text.get("1.0", tk.END)
            self.root.clipboard_clear()
            self.root.clipboard_append(all_text)

    def clear_log(self):
        """清空日誌"""
        self.log_text.delete("1.0", tk.END)

    def save_log(self):
        """保存日誌到檔案"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tracking_log_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get("1.0", tk.END))

            self.log(f"💾 日誌已保存到: {filename}")
        except Exception as e:
            self.log(f"❌ 保存日誌失敗: {e}")
        
    def log(self, message):
        """記錄日誌 - 同時顯示在GUI和寫入檔案"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"

        # 顯示在GUI
        self.log_text.insert(tk.END, log_message + "\n")
        self.log_text.see(tk.END)
        self.root.update()

        # 同時寫入檔案
        try:
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(log_message + "\n")
        except Exception as e:
            # 避免無限遞迴，直接寫入GUI
            self.log_text.insert(tk.END, f"[{timestamp}] ⚠️ 寫入日誌檔案失敗: {e}\n")
        
    def start_browser(self):
        """啟動瀏覽器"""
        if self.browser_started:
            self.log("⚠️ 瀏覽器已經啟動")
            return
            
        self.log("🌐 啟動瀏覽器...")
        self.start_browser_btn.config(state="disabled")
        
        def browser_thread():
            try:
                # Chrome 選項
                chrome_options = Options()
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                chrome_options.add_argument("--disable-web-security")
                chrome_options.add_argument("--allow-running-insecure-content")
                
                # 啟動瀏覽器
                self.driver = webdriver.Chrome(options=chrome_options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                
                # 導航到目標網站
                self.driver.get("https://wmc.kcg.gov.tw/")
                
                self.browser_started = True
                self.log("✅ 瀏覽器啟動成功")
                self.log("📍 已導航到: https://wmc.kcg.gov.tw/")
                self.log("💡 請手動登入並進入編輯頁面")
                
                # 啟用追蹤按鈕
                self.start_tracking_btn.config(state="normal")
                self.check_browser_btn.config(state="normal")
                self.test_btn.config(state="normal")
                self.scan_btn.config(state="normal")
                self.track_btn.config(state="normal")
                self.tech_btn.config(state="normal")
                self.webdriver_monitor_btn.config(state="normal")
                self.pure_webdriver_btn.config(state="normal")
                self.triple_monitor_btn.config(state="normal")
                self.screenshot_btn.config(state="normal")
                
            except Exception as e:
                self.log(f"❌ 瀏覽器啟動失敗: {e}")
                self.start_browser_btn.config(state="normal")
                
        threading.Thread(target=browser_thread, daemon=True).start()
        
    def check_browser_status(self):
        """檢查瀏覽器狀態"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return
                
            current_url = self.driver.current_url
            title = self.driver.title
            
            self.log(f"🔍 瀏覽器狀態檢查:")
            self.log(f"   📍 當前 URL: {current_url}")
            self.log(f"   📄 頁面標題: {title}")
            
            # 檢查 iframe
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            self.log(f"   🖼️ 發現 {len(iframes)} 個 iframe")
            
        except Exception as e:
            self.log(f"❌ 檢查瀏覽器狀態失敗: {e}")

    def detect_page_technology(self):
        """檢測頁面使用的技術"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🔬 檢測頁面技術...")

            # 檢測頁面技術的腳本
            tech_detection = self.driver.execute_script("""
                var tech = {
                    javascript: {
                        enabled: typeof window !== 'undefined',
                        version: '',
                        frameworks: []
                    },
                    server: {
                        technology: '',
                        headers: {},
                        viewstate: false
                    },
                    ui: {
                        frameworks: [],
                        components: []
                    },
                    forms: {
                        type: '',
                        postback: false,
                        ajax: false
                    }
                };

                // 檢測 JavaScript 框架
                if (typeof jQuery !== 'undefined') tech.javascript.frameworks.push('jQuery ' + jQuery.fn.jquery);
                if (typeof angular !== 'undefined') tech.javascript.frameworks.push('AngularJS');
                if (typeof React !== 'undefined') tech.javascript.frameworks.push('React');
                if (typeof Vue !== 'undefined') tech.javascript.frameworks.push('Vue.js');

                // 檢測 ASP.NET
                var viewstateInput = document.querySelector('input[name="__VIEWSTATE"]');
                if (viewstateInput) {
                    tech.server.technology = 'ASP.NET WebForms';
                    tech.server.viewstate = true;
                    tech.forms.postback = true;
                }

                // 檢測 ASP.NET MVC
                var antiForgeryToken = document.querySelector('input[name="__RequestVerificationToken"]');
                if (antiForgeryToken) {
                    tech.server.technology = tech.server.technology || 'ASP.NET MVC';
                }

                // 檢測 PHP
                var phpSession = document.cookie.includes('PHPSESSID');
                if (phpSession) {
                    tech.server.technology = tech.server.technology || 'PHP';
                }

                // 檢測 JSP/Java
                var javaSession = document.cookie.includes('JSESSIONID');
                if (javaSession) {
                    tech.server.technology = tech.server.technology || 'Java/JSP';
                }

                // 檢測 UI 框架
                if (document.querySelector('.bootstrap') || document.querySelector('[class*="btn-"]')) {
                    tech.ui.frameworks.push('Bootstrap');
                }

                if (document.querySelector('.ui-widget') || document.querySelector('[class*="ui-"]')) {
                    tech.ui.frameworks.push('jQuery UI');
                }

                // 檢測表單類型
                var forms = document.querySelectorAll('form');
                for (var i = 0; i < forms.length; i++) {
                    var form = forms[i];
                    if (form.method && form.method.toLowerCase() === 'post') {
                        tech.forms.type = 'POST';
                    }
                    if (form.action && form.action.includes('__doPostBack')) {
                        tech.forms.postback = true;
                    }
                }

                // 檢測 AJAX
                if (typeof XMLHttpRequest !== 'undefined' || typeof fetch !== 'undefined') {
                    tech.forms.ajax = true;
                }

                // 檢測頁面元信息
                var generator = document.querySelector('meta[name="generator"]');
                if (generator) {
                    tech.server.technology = tech.server.technology || generator.content;
                }

                // 檢測腳本標籤
                var scripts = document.querySelectorAll('script[src]');
                var scriptSources = [];
                for (var i = 0; i < scripts.length; i++) {
                    scriptSources.push(scripts[i].src);
                }
                tech.javascript.scripts = scriptSources;

                return tech;
            """)

            # 顯示檢測結果
            self.log("📊 頁面技術檢測結果:")

            # JavaScript 信息
            js_info = tech_detection.get('javascript', {})
            self.log(f"🟨 JavaScript: {'啟用' if js_info.get('enabled') else '未啟用'}")
            if js_info.get('frameworks'):
                self.log(f"   框架: {', '.join(js_info['frameworks'])}")

            # 服務器技術
            server_info = tech_detection.get('server', {})
            if server_info.get('technology'):
                self.log(f"🟦 服務器技術: {server_info['technology']}")
            if server_info.get('viewstate'):
                self.log("   🔍 檢測到 ASP.NET ViewState")

            # UI 框架
            ui_info = tech_detection.get('ui', {})
            if ui_info.get('frameworks'):
                self.log(f"🎨 UI 框架: {', '.join(ui_info['frameworks'])}")

            # 表單信息
            forms_info = tech_detection.get('forms', {})
            self.log(f"📝 表單類型: {forms_info.get('type', '未知')}")
            if forms_info.get('postback'):
                self.log("   🔄 使用 PostBack 機制")
            if forms_info.get('ajax'):
                self.log("   ⚡ 支持 AJAX")

            # 檢測 iframe 技術
            iframe_tech = self.driver.execute_script("""
                var iframes = document.querySelectorAll('iframe');
                var iframeInfo = [];

                for (var i = 0; i < iframes.length; i++) {
                    try {
                        var iframe = iframes[i];
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            var info = {
                                index: i,
                                src: iframe.src || 'same-origin',
                                hasViewState: !!iframeDoc.querySelector('input[name="__VIEWSTATE"]'),
                                hasJQuery: typeof iframeDoc.defaultView.jQuery !== 'undefined',
                                technology: ''
                            };

                            if (info.hasViewState) {
                                info.technology = 'ASP.NET WebForms';
                            }

                            iframeInfo.push(info);
                        }
                    } catch (e) {
                        iframeInfo.push({
                            index: i,
                            error: 'Cross-origin or access denied',
                            technology: '跨域或無法訪問'
                        });
                    }
                }

                return iframeInfo;
            """)

            # 顯示 iframe 技術信息
            if iframe_tech:
                self.log("🖼️ iframe 技術信息:")
                for info in iframe_tech:
                    if info.get('error'):
                        self.log(f"   iframe {info['index']}: {info['error']}")
                    else:
                        tech = info.get('technology', '未知')
                        src = info.get('src', '')[:50]
                        self.log(f"   iframe {info['index']}: {tech} (src: {src})")

            # 保存檢測結果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tech_detection_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'page_tech': tech_detection,
                    'iframe_tech': iframe_tech
                }, f, ensure_ascii=False, indent=2)

            self.log(f"💾 技術檢測結果已保存到: {filename}")

        except Exception as e:
            import traceback
            self.log(f"❌ 頁面技術檢測失敗: {e}")
            self.log(f"🔍 詳細錯誤: {traceback.format_exc()}")

    def start_webdriver_monitoring(self):
        """啟動 WebDriver 層級監控"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🎯 啟動 WebDriver 層級監控...")
            self.log("💡 這種監控不受跨域限制影響")

            # 初始化監控狀態
            self.webdriver_monitoring = True
            self.last_iframe_count = len(self.driver.find_elements(By.TAG_NAME, "iframe"))
            self.last_window_handles = self.driver.window_handles.copy()

            # 獲取初始 DOM 快照
            try:
                self.last_dom_snapshot = self.get_dom_snapshot()
                self.log(f"📊 初始狀態: {self.last_iframe_count} 個 iframe, {len(self.last_window_handles)} 個視窗")
            except Exception as e:
                self.log(f"⚠️ 獲取初始 DOM 快照失敗: {e}")
                self.last_dom_snapshot = None

            # 開始監控循環
            self.webdriver_monitor_loop()

        except Exception as e:
            self.log(f"❌ 啟動 WebDriver 監控失敗: {e}")

    def get_dom_snapshot(self):
        """獲取 DOM 快照"""
        try:
            # 獲取所有 iframe 的基本信息
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            iframe_info = []

            for i, iframe in enumerate(iframes):
                try:
                    info = {
                        'index': i,
                        'src': iframe.get_attribute('src') or '',
                        'id': iframe.get_attribute('id') or '',
                        'class': iframe.get_attribute('class') or '',
                        'visible': iframe.is_displayed(),
                        'size': iframe.size,
                        'location': iframe.location
                    }
                    iframe_info.append(info)
                except Exception as e:
                    iframe_info.append({'index': i, 'error': str(e)})

            # 嘗試獲取頁面標題和 URL
            snapshot = {
                'timestamp': time.time(),
                'url': self.driver.current_url,
                'title': self.driver.title,
                'iframe_count': len(iframes),
                'iframe_info': iframe_info,
                'window_handles': self.driver.window_handles.copy()
            }

            # 嘗試獲取頁面文字內容（如果可能）
            try:
                body_text = self.driver.find_element(By.TAG_NAME, "body").text
                snapshot['body_text_length'] = len(body_text)
                snapshot['has_edit_dialog'] = any(keyword in body_text for keyword in
                    ['修改進廠確認單', '預約進廠確認單功能', '驗證碼有效時間為5分鐘'])
            except:
                snapshot['body_text_length'] = 0
                snapshot['has_edit_dialog'] = False

            return snapshot

        except Exception as e:
            self.log(f"⚠️ 獲取 DOM 快照時發生錯誤: {e}")
            return None

    def webdriver_monitor_loop(self):
        """WebDriver 監控循環"""
        if not self.webdriver_monitoring or not self.driver:
            return

        try:
            # 獲取當前狀態
            current_snapshot = self.get_dom_snapshot()

            if current_snapshot and self.last_dom_snapshot:
                # 比較變化
                changes = self.compare_snapshots(self.last_dom_snapshot, current_snapshot)

                # 處理變化
                for change in changes:
                    self.log(f"🔄 WebDriver 檢測到變化: {change}")

                # 更新快照
                self.last_dom_snapshot = current_snapshot

            # 繼續監控（使用配置的高頻率間隔）
            self.root.after(WEBDRIVER_MONITOR_INTERVAL, self.webdriver_monitor_loop)

        except Exception as e:
            self.log(f"⚠️ WebDriver 監控循環錯誤: {e}")
            # 繼續監控
            self.root.after(WEBDRIVER_MONITOR_INTERVAL, self.webdriver_monitor_loop)

    def compare_snapshots(self, old_snapshot, new_snapshot):
        """比較兩個 DOM 快照"""
        changes = []

        try:
            # 檢查 iframe 數量變化
            if old_snapshot['iframe_count'] != new_snapshot['iframe_count']:
                changes.append(f"iframe 數量變化: {old_snapshot['iframe_count']} → {new_snapshot['iframe_count']}")

            # 檢查視窗數量變化
            if len(old_snapshot['window_handles']) != len(new_snapshot['window_handles']):
                changes.append(f"視窗數量變化: {len(old_snapshot['window_handles'])} → {len(new_snapshot['window_handles'])}")

            # 檢查 URL 變化
            if old_snapshot['url'] != new_snapshot['url']:
                changes.append(f"URL 變化: {new_snapshot['url']}")

            # 檢查標題變化
            if old_snapshot['title'] != new_snapshot['title']:
                changes.append(f"標題變化: {new_snapshot['title']}")

            # 檢查編輯彈窗狀態變化
            if old_snapshot['has_edit_dialog'] != new_snapshot['has_edit_dialog']:
                if new_snapshot['has_edit_dialog']:
                    changes.append("🎉 檢測到編輯彈窗出現！")
                else:
                    changes.append("📋 編輯彈窗已消失")

            # 檢查 iframe 詳細變化
            old_iframes = {info.get('index', i): info for i, info in enumerate(old_snapshot.get('iframe_info', []))}
            new_iframes = {info.get('index', i): info for i, info in enumerate(new_snapshot.get('iframe_info', []))}

            # 新增的 iframe
            for index, info in new_iframes.items():
                if index not in old_iframes:
                    changes.append(f"新增 iframe {index}: src={info.get('src', '')[:50]}")

            # 移除的 iframe
            for index, info in old_iframes.items():
                if index not in new_iframes:
                    changes.append(f"移除 iframe {index}")

            # iframe 屬性變化
            for index in old_iframes:
                if index in new_iframes:
                    old_info = old_iframes[index]
                    new_info = new_iframes[index]

                    if old_info.get('src') != new_info.get('src'):
                        changes.append(f"iframe {index} src 變化: {new_info.get('src', '')[:50]}")

                    if old_info.get('visible') != new_info.get('visible'):
                        visibility = "可見" if new_info.get('visible') else "隱藏"
                        changes.append(f"iframe {index} 可見性變化: {visibility}")

        except Exception as e:
            changes.append(f"比較快照時發生錯誤: {e}")

        return changes

    def pure_webdriver_scan(self):
        """純 WebDriver 掃描 - 不依賴 JavaScript"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🔧 開始純 WebDriver 掃描...")
            self.log("💡 此方法不依賴 JavaScript，適用於所有網站技術")

            # 獲取所有可能的容器元素
            containers = []

            # 1. 主頁面
            containers.append({
                'name': '主頁面',
                'driver': self.driver,
                'element': None
            })

            # 2. 所有 iframe
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            for i, iframe in enumerate(iframes):
                try:
                    self.driver.switch_to.frame(iframe)
                    containers.append({
                        'name': f'iframe {i}',
                        'driver': self.driver,
                        'element': iframe,
                        'src': iframe.get_attribute('src') or 'same-origin',
                        'id': iframe.get_attribute('id') or 'no-id'
                    })
                    self.driver.switch_to.default_content()
                except Exception as e:
                    self.log(f"⚠️ 無法訪問 iframe {i}: {e}")

            # 掃描每個容器
            all_results = {}

            for container in containers:
                try:
                    if container['element'] is not None:
                        # 切換到 iframe
                        self.driver.switch_to.frame(container['element'])

                    # 純 WebDriver 掃描
                    result = self.scan_container_pure_webdriver(container['name'])
                    all_results[container['name']] = result

                    # 切換回主頁面
                    self.driver.switch_to.default_content()

                except Exception as e:
                    self.log(f"❌ 掃描 {container['name']} 失敗: {e}")
                    self.driver.switch_to.default_content()

            # 分析結果
            self.analyze_pure_webdriver_results(all_results)

        except Exception as e:
            import traceback
            self.log(f"❌ 純 WebDriver 掃描失敗: {e}")
            self.log(f"🔍 詳細錯誤: {traceback.format_exc()}")
            # 確保回到主頁面
            try:
                self.driver.switch_to.default_content()
            except:
                pass

    def scan_container_pure_webdriver(self, container_name):
        """掃描單個容器 - 純 WebDriver 方法"""
        result = {
            'buttons': [],
            'inputs': [],
            'forms': [],
            'text_content': '',
            'title': ''
        }

        try:
            # 獲取頁面標題
            try:
                result['title'] = self.driver.title
            except:
                result['title'] = 'unknown'

            # 獲取頁面文字內容
            try:
                body = self.driver.find_element(By.TAG_NAME, "body")
                result['text_content'] = body.text[:500]  # 只取前500字符
            except:
                result['text_content'] = ''

            # 掃描所有按鈕
            button_selectors = [
                "button",
                "input[type='button']",
                "input[type='submit']",
                "input[type='reset']",
                "[role='button']"
            ]

            for selector in button_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for btn in buttons:
                        try:
                            btn_info = {
                                'tag_name': btn.tag_name,
                                'type': btn.get_attribute('type') or '',
                                'id': btn.get_attribute('id') or '',
                                'class': btn.get_attribute('class') or '',
                                'name': btn.get_attribute('name') or '',
                                'value': btn.get_attribute('value') or '',
                                'text': btn.text or '',
                                'visible': btn.is_displayed(),
                                'enabled': btn.is_enabled(),
                                'location': btn.location,
                                'size': btn.size
                            }
                            result['buttons'].append(btn_info)
                        except:
                            pass
                except:
                    pass

            # 掃描所有輸入框
            input_selectors = [
                "input[type='text']",
                "input[type='password']",
                "input[type='email']",
                "input[type='number']",
                "textarea"
            ]

            for selector in input_selectors:
                try:
                    inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for inp in inputs:
                        try:
                            inp_info = {
                                'tag_name': inp.tag_name,
                                'type': inp.get_attribute('type') or '',
                                'id': inp.get_attribute('id') or '',
                                'class': inp.get_attribute('class') or '',
                                'name': inp.get_attribute('name') or '',
                                'placeholder': inp.get_attribute('placeholder') or '',
                                'value': inp.get_attribute('value') or '',
                                'visible': inp.is_displayed(),
                                'enabled': inp.is_enabled()
                            }
                            result['inputs'].append(inp_info)
                        except:
                            pass
                except:
                    pass

            # 掃描所有表單
            try:
                forms = self.driver.find_elements(By.TAG_NAME, "form")
                for form in forms:
                    try:
                        form_info = {
                            'action': form.get_attribute('action') or '',
                            'method': form.get_attribute('method') or '',
                            'id': form.get_attribute('id') or '',
                            'class': form.get_attribute('class') or '',
                            'name': form.get_attribute('name') or ''
                        }
                        result['forms'].append(form_info)
                    except:
                        pass
            except:
                pass

        except Exception as e:
            self.log(f"⚠️ 掃描 {container_name} 時發生錯誤: {e}")

        return result

    def analyze_pure_webdriver_results(self, all_results):
        """分析純 WebDriver 掃描結果"""
        self.log("📊 純 WebDriver 掃描結果分析:")

        total_buttons = 0
        total_inputs = 0
        submit_candidates = []

        for container_name, result in all_results.items():
            buttons = result.get('buttons', [])
            inputs = result.get('inputs', [])
            text_content = result.get('text_content', '')

            total_buttons += len(buttons)
            total_inputs += len(inputs)

            self.log(f"\n📋 {container_name}:")
            self.log(f"   按鈕數量: {len(buttons)}")
            self.log(f"   輸入框數量: {len(inputs)}")

            # 檢查是否是編輯彈窗
            is_edit_dialog = any(keyword in text_content for keyword in
                ['修改進廠確認單', '預約進廠確認單功能', '驗證碼有效時間為5分鐘'])

            if is_edit_dialog:
                self.log(f"   🎉 這是編輯彈窗！")

            # 分析按鈕
            for i, btn in enumerate(buttons):
                if btn['visible']:
                    btn_text = btn['text'] or btn['value']
                    self.log(f"   按鈕 {i+1}: <{btn['tag_name']}> '{btn_text}' type='{btn['type']}' class='{btn['class'][:30]}'")

                    # 檢查是否是送出按鈕
                    submit_keywords = ['送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save']
                    all_text = f"{btn_text} {btn['class']} {btn['id']} {btn['value']}".lower()

                    if any(keyword in all_text for keyword in submit_keywords):
                        submit_candidates.append({
                            'container': container_name,
                            'button': btn,
                            'confidence': len([k for k in submit_keywords if k in all_text])
                        })
                        self.log(f"      🎯 可能的送出按鈕！")

            # 分析輸入框
            for i, inp in enumerate(inputs):
                if inp['visible']:
                    inp_type = inp['type']
                    placeholder = inp['placeholder']
                    self.log(f"   輸入框 {i+1}: <{inp['tag_name']}> type='{inp_type}' placeholder='{placeholder}'")

        # 顯示最佳送出按鈕候選
        if submit_candidates:
            best_candidate = max(submit_candidates, key=lambda x: x['confidence'])
            self.log(f"\n🏆 最可能的送出按鈕:")
            btn = best_candidate['button']
            self.log(f"   容器: {best_candidate['container']}")
            self.log(f"   標籤: {btn['tag_name']}")
            self.log(f"   文字: {btn['text'] or btn['value']}")
            self.log(f"   類別: {btn['class']}")
            self.log(f"   ID: {btn['id']}")
            self.log(f"   類型: {btn['type']}")
            self.log(f"   位置: {btn['location']}")
            self.log(f"   大小: {btn['size']}")
        else:
            self.log("\n❌ 未找到明確的送出按鈕")

        # 保存結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pure_webdriver_scan_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)

        self.log(f"\n💾 掃描結果已保存到: {filename}")

    def start_triple_monitoring(self):
        """啟動三重監控 - 同時執行三種監控方法"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🚀 啟動三重監控系統...")
            self.log("💡 同時執行三種監控方法，提供完整的監控覆蓋")

            # 1. 啟動 JavaScript 事件監控
            self.log("\n📋 第一重：JavaScript 事件監控")
            js_success = self.setup_browser_event_monitoring()
            if js_success:
                self.log("✅ JavaScript 事件監控已啟動")
            else:
                self.log("❌ JavaScript 事件監控啟動失敗")

            # 2. 啟動 WebDriver 結構監控
            self.log("\n📋 第二重：WebDriver 結構監控")
            try:
                self.webdriver_monitoring = True
                self.last_iframe_count = len(self.driver.find_elements(By.TAG_NAME, "iframe"))
                self.last_window_handles = self.driver.window_handles.copy()
                self.last_dom_snapshot = self.get_dom_snapshot()
                self.webdriver_monitor_loop()
                self.log("✅ WebDriver 結構監控已啟動")
            except Exception as e:
                self.log(f"❌ WebDriver 結構監控啟動失敗: {e}")

            # 3. 執行純 WebDriver 掃描
            self.log("\n📋 第三重：純 WebDriver 掃描")
            try:
                self.pure_webdriver_scan()
                self.log("✅ 純 WebDriver 掃描已完成")
            except Exception as e:
                self.log(f"❌ 純 WebDriver 掃描失敗: {e}")

            # 啟動事件收集（如果 JavaScript 監控成功）
            if js_success:
                self.tracking_active = True
                self.event_log = []
                self.processed_events = 0
                self.last_event = None
                self.collect_events()

                # 更新按鈕狀態
                self.start_tracking_btn.config(state="disabled")
                self.stop_tracking_btn.config(state="normal")
                self.status_label.config(text="🔴 三重監控中...", fg="red")

            self.log("\n🎉 三重監控系統已全部啟動！")
            self.log("📊 監控內容:")
            self.log("   🌐 JavaScript 事件 - 用戶點擊、輸入等操作")
            self.log("   🎯 WebDriver 結構 - 頁面變化、iframe 變化")
            self.log("   🔧 純 WebDriver - 深度元素掃描")
            self.log("\n💡 請執行您的操作，三種方法會同時記錄不同層面的信息")

        except Exception as e:
            import traceback
            self.log(f"❌ 啟動三重監控失敗: {e}")
            self.log(f"🔍 詳細錯誤: {traceback.format_exc()}")

    def test_event_monitoring(self):
        """測試事件監控是否正常工作"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🧪 測試事件監控...")

            # 在頁面上創建一個測試按鈕並點擊它
            test_result = self.driver.execute_script("""
                // 創建測試按鈕
                var testBtn = document.createElement('button');
                testBtn.id = 'ages-test-button';
                testBtn.textContent = '測試按鈕';
                testBtn.style.position = 'fixed';
                testBtn.style.top = '10px';
                testBtn.style.right = '10px';
                testBtn.style.zIndex = '9999';
                testBtn.style.backgroundColor = '#ff6b6b';
                testBtn.style.color = 'white';
                testBtn.style.border = 'none';
                testBtn.style.padding = '10px';
                testBtn.style.borderRadius = '5px';

                document.body.appendChild(testBtn);

                // 模擬點擊
                testBtn.click();

                // 等待一下讓事件被記錄
                setTimeout(function() {
                    document.body.removeChild(testBtn);
                }, 1000);

                return '測試按鈕已創建並點擊';
            """)

            self.log(f"🧪 {test_result}")
            self.log("💡 請等待 2 秒後檢查是否有點擊事件被記錄")

            # 2秒後檢查事件日誌
            def check_test_events():
                try:
                    events = self.driver.execute_script("return window.AGES_EVENT_LOG || [];")
                    recent_events = [e for e in events if e.get('target', {}).get('id') == 'ages-test-button']

                    if recent_events:
                        self.log(f"✅ 測試成功！捕獲到 {len(recent_events)} 個測試事件")
                        for event in recent_events:
                            self.log(f"   事件類型: {event.get('type')} 目標: {event.get('target', {}).get('tagName')}")
                    else:
                        self.log("❌ 測試失敗！沒有捕獲到測試按鈕的點擊事件")
                        self.log("🔍 這可能表示事件監控有問題")

                except Exception as e:
                    self.log(f"❌ 檢查測試事件失敗: {e}")

            # 延遲檢查
            self.root.after(2000, check_test_events)

        except Exception as e:
            self.log(f"❌ 測試事件監控失敗: {e}")

    def scan_page_elements(self):
        """掃描頁面元素，特別是按鈕和輸入框"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🔍 開始掃描頁面元素...")

            # 掃描主頁面和 iframe 中的元素
            scan_result = self.driver.execute_script("""
                function scanElements(doc, frameInfo) {
                    var elements = {
                        buttons: [],
                        inputs: [],
                        images: [],
                        links: []
                    };

                    // 掃描按鈕
                    var buttons = doc.querySelectorAll('button, input[type="button"], input[type="submit"]');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        elements.buttons.push({
                            tagName: btn.tagName,
                            type: btn.type || '',
                            id: btn.id || '',
                            className: btn.className || '',
                            textContent: (btn.textContent || '').trim().substring(0, 50),
                            value: btn.value || '',
                            onclick: btn.onclick ? 'has onclick' : '',
                            visible: btn.offsetWidth > 0 && btn.offsetHeight > 0,
                            frameInfo: frameInfo
                        });
                    }

                    // 掃描輸入框
                    var inputs = doc.querySelectorAll('input[type="text"], input[type="password"], textarea');
                    for (var i = 0; i < inputs.length; i++) {
                        var input = inputs[i];
                        elements.inputs.push({
                            tagName: input.tagName,
                            type: input.type || '',
                            id: input.id || '',
                            className: input.className || '',
                            name: input.name || '',
                            placeholder: input.placeholder || '',
                            value: input.value || '',
                            visible: input.offsetWidth > 0 && input.offsetHeight > 0,
                            frameInfo: frameInfo
                        });
                    }

                    // 掃描圖片（可能是驗證碼）
                    var images = doc.querySelectorAll('img');
                    for (var i = 0; i < images.length; i++) {
                        var img = images[i];
                        if (img.src && (img.src.includes('captcha') || img.src.includes('verify') || img.src.includes('code'))) {
                            elements.images.push({
                                src: img.src,
                                id: img.id || '',
                                className: img.className || '',
                                alt: img.alt || '',
                                visible: img.offsetWidth > 0 && img.offsetHeight > 0,
                                frameInfo: frameInfo
                            });
                        }
                    }

                    // 掃描可能的刷新連結
                    var links = doc.querySelectorAll('a');
                    for (var i = 0; i < links.length; i++) {
                        var link = links[i];
                        var text = (link.textContent || '').trim().toLowerCase();
                        if (text.includes('刷新') || text.includes('重新') || text.includes('refresh') || text.includes('reload')) {
                            elements.links.push({
                                href: link.href || '',
                                textContent: (link.textContent || '').trim().substring(0, 30),
                                id: link.id || '',
                                className: link.className || '',
                                visible: link.offsetWidth > 0 && link.offsetHeight > 0,
                                frameInfo: frameInfo
                            });
                        }
                    }

                    return elements;
                }

                // 掃描主頁面
                var allElements = scanElements(document, '主頁面');

                // 掃描所有 iframe（包括嵌套的）
                function scanAllIframes(doc, prefix) {
                    var iframes = doc.querySelectorAll('iframe');
                    for (var i = 0; i < iframes.length; i++) {
                        try {
                            var iframe = iframes[i];
                            var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            if (iframeDoc) {
                                var frameInfo = prefix + 'iframe ' + i;
                                var iframeElements = scanElements(iframeDoc, frameInfo);

                                // 合併結果
                                allElements.buttons = allElements.buttons.concat(iframeElements.buttons);
                                allElements.inputs = allElements.inputs.concat(iframeElements.inputs);
                                allElements.images = allElements.images.concat(iframeElements.images);
                                allElements.links = allElements.links.concat(iframeElements.links);

                                // 遞歸掃描嵌套的 iframe
                                scanAllIframes(iframeDoc, frameInfo + ' > ');
                            }
                        } catch (e) {
                            console.log('無法掃描', prefix + 'iframe ' + i + ':', e.message);
                        }
                    }
                }

                // 掃描主頁面的 iframe
                scanAllIframes(document, '');

                // 掃描可能的彈窗和模態框
                var modals = document.querySelectorAll('.modal, .dialog, .popup, [role="dialog"]');
                for (var i = 0; i < modals.length; i++) {
                    var modal = modals[i];
                    if (modal.offsetWidth > 0 && modal.offsetHeight > 0) { // 只掃描可見的彈窗
                        var modalElements = scanElements(modal, '彈窗 ' + i);
                        allElements.buttons = allElements.buttons.concat(modalElements.buttons);
                        allElements.inputs = allElements.inputs.concat(modalElements.inputs);
                        allElements.images = allElements.images.concat(modalElements.images);
                        allElements.links = allElements.links.concat(modalElements.links);
                    }
                }

                // 添加頁面文字內容檢測
                allElements.pageContent = {
                    title: document.title || '',
                    bodyText: (document.body ? document.body.textContent : '').substring(0, 500),
                    visibleText: ''
                };

                // 檢測可見的文字內容
                var visibleElements = document.querySelectorAll('*');
                var visibleTexts = [];
                for (var i = 0; i < visibleElements.length; i++) {
                    var el = visibleElements[i];
                    if (el.offsetWidth > 0 && el.offsetHeight > 0 && el.textContent) {
                        var text = el.textContent.trim();
                        if (text.length > 5 && text.length < 100) {
                            visibleTexts.push(text);
                        }
                    }
                }
                allElements.pageContent.visibleText = visibleTexts.slice(0, 20).join(' | ');

                return allElements;
            """)

            # 顯示掃描結果
            self.log("📊 掃描結果:")

            # 首先顯示頁面內容檢測
            page_content = scan_result.get('pageContent', {})
            self.log("📄 頁面內容檢測:")
            self.log(f"   標題: {page_content.get('title', '')}")
            self.log(f"   頁面文字: {page_content.get('bodyText', '')[:200]}...")
            self.log(f"   可見文字: {page_content.get('visibleText', '')}")

            # 檢查是否在正確的編輯彈窗中
            body_text = page_content.get('bodyText', '').lower()
            if '修改進廠確認單' in body_text:
                self.log("✅ 程式已進入編輯彈窗！")
            elif '預約進廠確認單功能' in body_text:
                self.log("✅ 程式已進入編輯彈窗！")
            elif '驗證碼有效時間為5分鐘' in body_text:
                self.log("✅ 程式已進入編輯彈窗！")
            else:
                self.log("❌ 程式還在外層清單，未進入編輯彈窗")
                self.log("💡 請手動點擊編輯按鈕進入彈窗後再掃描")

            # 顯示按鈕
            buttons = scan_result.get('buttons', [])
            self.log(f"🔘 找到 {len(buttons)} 個按鈕:")
            for i, btn in enumerate(buttons):
                visible_text = "可見" if btn['visible'] else "隱藏"
                self.log(f"   按鈕 {i+1}: <{btn['tagName']}> '{btn['textContent']}' type='{btn['type']}' class='{btn['className'][:30]}' ({visible_text}) [{btn['frameInfo']}]")

            # 顯示輸入框
            inputs = scan_result.get('inputs', [])
            self.log(f"📝 找到 {len(inputs)} 個輸入框:")
            for i, inp in enumerate(inputs):
                visible_text = "可見" if inp['visible'] else "隱藏"
                self.log(f"   輸入框 {i+1}: <{inp['tagName']}> type='{inp['type']}' placeholder='{inp['placeholder']}' class='{inp['className'][:30]}' ({visible_text}) [{inp['frameInfo']}]")

            # 顯示驗證碼圖片
            images = scan_result.get('images', [])
            if images:
                self.log(f"🖼️ 找到 {len(images)} 個可能的驗證碼圖片:")
                for i, img in enumerate(images):
                    visible_text = "可見" if img['visible'] else "隱藏"
                    self.log(f"   圖片 {i+1}: src='{img['src'][:50]}...' alt='{img['alt']}' ({visible_text}) [{img['frameInfo']}]")

            # 顯示刷新連結
            links = scan_result.get('links', [])
            if links:
                self.log(f"🔗 找到 {len(links)} 個可能的刷新連結:")
                for i, link in enumerate(links):
                    visible_text = "可見" if link['visible'] else "隱藏"
                    self.log(f"   連結 {i+1}: '{link['textContent']}' href='{link['href'][:50]}...' ({visible_text}) [{link['frameInfo']}]")

            # 保存掃描結果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"page_scan_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(scan_result, f, ensure_ascii=False, indent=2)

            self.log(f"💾 掃描結果已保存到: {filename}")

        except Exception as e:
            import traceback
            self.log(f"❌ 掃描頁面元素失敗: {e}")
            self.log(f"🔍 詳細錯誤: {traceback.format_exc()}")

    def start_dynamic_tracking(self):
        """開始動態跟蹤頁面變化"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            self.log("🔄 開始動態跟蹤頁面變化...")

            # 注入動態跟蹤腳本
            tracking_script = """
                // 創建頁面變化監控
                window.AGES_PAGE_TRACKER = {
                    lastIframeCount: 0,
                    lastModalCount: 0,
                    lastUrl: window.location.href,
                    changes: []
                };

                function detectChanges() {
                    var changes = [];
                    var currentTime = Date.now();

                    // 檢測 iframe 變化
                    var currentIframes = document.querySelectorAll('iframe').length;
                    if (currentIframes !== window.AGES_PAGE_TRACKER.lastIframeCount) {
                        changes.push({
                            type: 'iframe_change',
                            timestamp: currentTime,
                            oldCount: window.AGES_PAGE_TRACKER.lastIframeCount,
                            newCount: currentIframes
                        });
                        window.AGES_PAGE_TRACKER.lastIframeCount = currentIframes;
                    }

                    // 檢測彈窗變化
                    var modals = document.querySelectorAll('.modal, .dialog, .popup, [role="dialog"]');
                    var visibleModals = 0;
                    for (var i = 0; i < modals.length; i++) {
                        if (modals[i].offsetWidth > 0 && modals[i].offsetHeight > 0) {
                            visibleModals++;
                        }
                    }

                    if (visibleModals !== window.AGES_PAGE_TRACKER.lastModalCount) {
                        changes.push({
                            type: 'modal_change',
                            timestamp: currentTime,
                            oldCount: window.AGES_PAGE_TRACKER.lastModalCount,
                            newCount: visibleModals
                        });
                        window.AGES_PAGE_TRACKER.lastModalCount = visibleModals;
                    }

                    // 檢測 URL 變化
                    if (window.location.href !== window.AGES_PAGE_TRACKER.lastUrl) {
                        changes.push({
                            type: 'url_change',
                            timestamp: currentTime,
                            oldUrl: window.AGES_PAGE_TRACKER.lastUrl,
                            newUrl: window.location.href
                        });
                        window.AGES_PAGE_TRACKER.lastUrl = window.location.href;
                    }

                    // 檢測頁面內容變化（關鍵字）
                    var bodyText = document.body ? document.body.textContent : '';
                    var hasEditDialog = bodyText.includes('修改進廠確認單') ||
                                       bodyText.includes('預約進廠確認單功能') ||
                                       bodyText.includes('驗證碼有效時間為5分鐘');

                    if (hasEditDialog && !window.AGES_PAGE_TRACKER.editDialogDetected) {
                        changes.push({
                            type: 'edit_dialog_opened',
                            timestamp: currentTime,
                            content: bodyText.substring(0, 200)
                        });
                        window.AGES_PAGE_TRACKER.editDialogDetected = true;
                    } else if (!hasEditDialog && window.AGES_PAGE_TRACKER.editDialogDetected) {
                        changes.push({
                            type: 'edit_dialog_closed',
                            timestamp: currentTime
                        });
                        window.AGES_PAGE_TRACKER.editDialogDetected = false;
                    }

                    // 記錄變化
                    if (changes.length > 0) {
                        window.AGES_PAGE_TRACKER.changes = window.AGES_PAGE_TRACKER.changes.concat(changes);
                        console.log('頁面變化檢測:', changes);
                    }

                    return changes;
                }

                // 初始化
                detectChanges();

                // 定期檢測
                setInterval(detectChanges, 1000);

                console.log('動態頁面跟蹤已啟動');
                return true;
            """

            result = self.driver.execute_script(tracking_script)

            if result:
                self.log("✅ 動態跟蹤已啟動")
                self.log("🔄 每秒檢測頁面變化...")

                # 開始定期檢查變化
                self.check_page_changes()
            else:
                self.log("❌ 動態跟蹤啟動失敗")

        except Exception as e:
            self.log(f"❌ 啟動動態跟蹤失敗: {e}")

    def check_page_changes(self):
        """檢查頁面變化"""
        try:
            if not self.driver:
                return

            # 獲取頁面變化
            changes = self.driver.execute_script("""
                if (window.AGES_PAGE_TRACKER && window.AGES_PAGE_TRACKER.changes.length > 0) {
                    var changes = window.AGES_PAGE_TRACKER.changes;
                    window.AGES_PAGE_TRACKER.changes = []; // 清空已讀取的變化
                    return changes;
                }
                return [];
            """)

            # 處理變化
            for change in changes:
                change_type = change.get('type', '')
                timestamp = change.get('timestamp', 0)

                if change_type == 'iframe_change':
                    self.log(f"🖼️ iframe 數量變化: {change['oldCount']} → {change['newCount']}")
                elif change_type == 'modal_change':
                    self.log(f"📋 彈窗數量變化: {change['oldCount']} → {change['newCount']}")
                elif change_type == 'url_change':
                    self.log(f"🌐 URL 變化: {change['newUrl']}")
                elif change_type == 'edit_dialog_opened':
                    self.log("🎉 檢測到編輯彈窗打開！")
                    self.log("💡 建議現在執行掃描以檢測彈窗中的元素")
                elif change_type == 'edit_dialog_closed':
                    self.log("📋 編輯彈窗已關閉")

            # 繼續檢查（使用配置的高頻率間隔）
            self.root.after(PAGE_CHANGE_CHECK_INTERVAL, self.check_page_changes)

        except Exception as e:
            self.log(f"⚠️ 檢查頁面變化時發生錯誤: {e}")
            # 繼續檢查
            self.root.after(PAGE_CHANGE_CHECK_INTERVAL, self.check_page_changes)

    def setup_browser_event_monitoring(self):
        """設置瀏覽器事件監控 - 完全獨立版本"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未連接")
                return False
            
            self.log("🎯 設置瀏覽器事件監控...")
            
            # 完整的事件監控 JavaScript 代碼
            monitoring_script = """
            // 創建事件監控系統
            window.AGES_EVENT_LOG = window.AGES_EVENT_LOG || [];
            
            // 清除舊的監聽器（如果存在）
            if (window.AGES_LISTENERS) {
                window.AGES_LISTENERS.forEach(listener => {
                    document.removeEventListener(listener.type, listener.handler, true);
                });
            }
            window.AGES_LISTENERS = [];
            
            // 事件處理函數
            function logEvent(event) {
                try {
                    var target = event.target || event.srcElement;
                    var eventData = {
                        type: event.type,
                        timestamp: Date.now(),
                        coordinates: {
                            clientX: event.clientX || 0,
                            clientY: event.clientY || 0,
                            pageX: event.pageX || 0,
                            pageY: event.pageY || 0
                        },
                        target: {
                            tagName: target.tagName || '',
                            id: target.id || '',
                            className: target.className || '',
                            name: target.name || '',
                            type: target.type || '',
                            value: target.value || '',
                            textContent: (target.textContent || '').trim().substring(0, 100),
                            innerHTML: (target.innerHTML || '').substring(0, 200),
                            role: target.getAttribute('role') || '',
                            ariaLabel: target.getAttribute('aria-label') || '',
                            title: target.title || '',
                            onclick: target.onclick ? target.onclick.toString().substring(0, 100) : ''
                        },
                        key: event.key || '',
                        code: event.code || '',
                        ctrlKey: event.ctrlKey || false,
                        shiftKey: event.shiftKey || false,
                        altKey: event.altKey || false,
                        url: window.location.href,
                        frameDepth: window !== window.top ? 1 : 0
                    };
                    
                    window.AGES_EVENT_LOG.push(eventData);
                    
                    // 限制日誌大小
                    if (window.AGES_EVENT_LOG.length > 1000) {
                        window.AGES_EVENT_LOG = window.AGES_EVENT_LOG.slice(-500);
                    }
                    
                } catch (e) {
                    console.error('事件記錄錯誤:', e);
                }
            }
            
            // 監控的事件類型 - 增加更多類型
            var eventTypes = ['click', 'mousedown', 'mouseup', 'keydown', 'keyup', 'input', 'change', 'submit', 'focus', 'blur'];

            // 設置事件監聽器的函數
            function setupEventListeners(doc, frameDepth) {
                frameDepth = frameDepth || 0;

                eventTypes.forEach(function(eventType) {
                    var handler = function(event) {
                        event.frameDepth = frameDepth;
                        logEvent(event);
                    };
                    doc.addEventListener(eventType, handler, true);
                    window.AGES_LISTENERS.push({type: eventType, handler: handler, doc: doc});
                });

                console.log('已為', frameDepth === 0 ? '主頁面' : 'iframe ' + frameDepth, '設置事件監聽器');
            }

            // 為主頁面設置監聽器
            setupEventListeners(document, 0);

            // 監控 iframe 的函數
            function monitorIframes() {
                var iframes = document.querySelectorAll('iframe');
                console.log('發現', iframes.length, '個 iframe');

                for (var i = 0; i < iframes.length; i++) {
                    try {
                        var iframe = iframes[i];
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                        if (iframeDoc && !iframeDoc.AGES_MONITORED) {
                            iframeDoc.AGES_MONITORED = true;
                            setupEventListeners(iframeDoc, i + 1);
                            console.log('已為 iframe', i + 1, '設置事件監聽');

                            // 為 iframe 設置 MutationObserver 監控動態內容
                            if (iframeDoc.defaultView && iframeDoc.defaultView.MutationObserver) {
                                var observer = new iframeDoc.defaultView.MutationObserver(function(mutations) {
                                    mutations.forEach(function(mutation) {
                                        if (mutation.type === 'childList') {
                                            mutation.addedNodes.forEach(function(node) {
                                                if (node.nodeType === 1) { // Element node
                                                    // 為新添加的元素設置監聽器
                                                    eventTypes.forEach(function(eventType) {
                                                        var handler = function(event) {
                                                            event.frameDepth = i + 1;
                                                            logEvent(event);
                                                        };
                                                        node.addEventListener(eventType, handler, true);
                                                    });
                                                }
                                            });
                                        }
                                    });
                                });

                                observer.observe(iframeDoc.body || iframeDoc.documentElement, {
                                    childList: true,
                                    subtree: true
                                });

                                console.log('已為 iframe', i + 1, '設置動態內容監控');
                            }
                        }
                    } catch (e) {
                        console.log('❌ 跨域 iframe 無法監聽:', iframe.src || 'unknown source');
                        console.log('   錯誤詳情:', e.message);
                    }
                }
            }

            // 立即監控現有 iframe
            monitorIframes();

            // 定期檢查新的 iframe（降低頻率到2秒）
            setInterval(monitorIframes, 2000);

            // 為主頁面設置 MutationObserver
            if (window.MutationObserver) {
                var mainObserver = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            // 檢查是否有新的 iframe
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1 && node.tagName === 'IFRAME') {
                                    setTimeout(monitorIframes, 100); // 延遲一點確保 iframe 加載完成
                                }
                            });
                        }
                    });
                });

                mainObserver.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true
                });

                console.log('已設置主頁面動態內容監控');
            }

            console.log('AGES 事件監控系統已啟動，監控事件類型:', eventTypes);
            return true;
            """
            
            # 執行監控腳本
            self.log("🔧 正在注入事件監控腳本...")
            result = self.driver.execute_script(monitoring_script)

            # 驗證監控是否成功設置
            self.log("🔍 驗證事件監控設置...")
            verification_result = self.driver.execute_script("""
                var result = {
                    hasEventLog: typeof window.AGES_EVENT_LOG !== 'undefined',
                    hasListeners: typeof window.AGES_LISTENERS !== 'undefined',
                    listenerCount: window.AGES_LISTENERS ? window.AGES_LISTENERS.length : 0,
                    iframeCount: document.querySelectorAll('iframe').length,
                    iframeDetails: []
                };

                // 檢查每個 iframe 的監聽狀態
                var iframes = document.querySelectorAll('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    try {
                        var iframe = iframes[i];
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        var detail = {
                            index: i,
                            src: iframe.src || 'no src',
                            id: iframe.id || 'no id',
                            accessible: !!iframeDoc,
                            monitored: iframeDoc ? !!iframeDoc.AGES_MONITORED : false,
                            hasEventLog: iframeDoc ? (typeof iframeDoc.defaultView.AGES_EVENT_LOG !== 'undefined') : false
                        };
                        result.iframeDetails.push(detail);
                    } catch (e) {
                        result.iframeDetails.push({
                            index: i,
                            error: e.message,
                            accessible: false
                        });
                    }
                }

                return result;
            """)

            self.log(f"📊 監控狀態: {verification_result}")

            # 詳細顯示 iframe 信息
            iframe_details = verification_result.get('iframeDetails', [])
            if iframe_details:
                self.log("🖼️ iframe 詳細信息:")
                for detail in iframe_details:
                    if detail.get('accessible'):
                        self.log(f"   iframe {detail['index']}: 可訪問={detail['accessible']}, 已監控={detail['monitored']}, 有事件日誌={detail['hasEventLog']}")
                    else:
                        self.log(f"   iframe {detail['index']}: 無法訪問 - {detail.get('error', '未知錯誤')}")

            if verification_result.get('hasEventLog') and verification_result.get('hasListeners'):
                self.log("✅ 瀏覽器事件監控已啟動")
                self.log("📋 監控事件類型: click, mousedown, mouseup, keydown, keyup, input, change, submit, focus, blur")
                self.log(f"🎯 監聽器數量: {verification_result.get('listenerCount', 0)}")
                self.log(f"🖼️ iframe 數量: {verification_result.get('iframeCount', 0)}")
                return True
            else:
                self.log("❌ 瀏覽器事件監控啟動失敗")
                self.log(f"🔍 監控狀態檢查: {verification_result}")
                return False

        except Exception as e:
            import traceback
            self.log(f"❌ 設置瀏覽器事件監控失敗: {e}")
            self.log(f"🔍 詳細錯誤: {traceback.format_exc()}")
            return False
            
    def start_tracking(self):
        """啟動追蹤"""
        try:
            if not self.browser_started:
                self.log("❌ 請先啟動瀏覽器")
                return

            if not self.driver:
                self.log("❌ 瀏覽器連接已斷開，請重新啟動瀏覽器")
                self.browser_started = False
                self.start_browser_btn.config(state="normal")
                return

            self.log("🎯 啟動滑鼠鍵盤追蹤...")
            self.log(f"📍 當前頁面: {self.driver.current_url}")

            # 設置事件監控
            self.log("🔧 正在設置事件監控...")
            if not self.setup_browser_event_monitoring():
                self.log("❌ 事件監控設置失敗")
                return

            self.tracking_active = True
            self.event_log = []
            self.processed_events = 0
            self.last_event = None

            self.start_tracking_btn.config(state="disabled")
            self.stop_tracking_btn.config(state="normal")
            self.status_label.config(text="🔴 追蹤中...", fg="red")

            self.log("✅ 追蹤已啟動")
            self.log("📋 請在瀏覽器中執行以下操作：")
            self.log("   1. 確保已在編輯頁面")
            self.log("   2. 輸入驗證碼")
            self.log("   3. 點擊送出按鈕")
            self.log("   4. 回到此工具點擊 '停止追蹤'")

            # 開始收集事件
            self.collect_events()

        except Exception as e:
            import traceback
            self.log(f"❌ 啟動追蹤失敗: {e}")
            self.log(f"🔍 詳細錯誤: {traceback.format_exc()}")

            # 重置狀態
            self.tracking_active = False
            self.start_tracking_btn.config(state="normal")
            self.stop_tracking_btn.config(state="disabled")
            
    def collect_events(self):
        """收集事件 - 提高頻率到100毫秒"""
        if not self.tracking_active:
            return
            
        try:
            # 獲取瀏覽器事件
            browser_events = self.driver.execute_script("return window.AGES_EVENT_LOG || [];")
            
            # 只處理新事件
            new_events = browser_events[self.processed_events:]
            
            if new_events:
                # 事件去重處理
                filtered_events = []
                for event in new_events:
                    # 簡單去重：檢查是否與上一個事件相同
                    if (self.last_event is None or
                        event.get('type') != self.last_event.get('type') or
                        event.get('target', {}).get('tagName') != self.last_event.get('target', {}).get('tagName') or
                        abs(event.get('timestamp', 0) - self.last_event.get('timestamp', 0)) > 100):  # 100ms 內的重複事件過濾
                        filtered_events.append(event)
                        self.last_event = event

                if filtered_events:
                    self.event_log.extend(filtered_events)
                    self.processed_events = len(browser_events)

                    # 處理新事件
                    for event in filtered_events:
                        self.process_event(event)
                    
        except Exception as e:
            self.log(f"⚠️ 收集事件時發生錯誤: {e}")
        
        # 50毫秒後再次檢查（高頻率監控）
        if self.tracking_active:
            self.root.after(EVENT_COLLECTION_INTERVAL, self.collect_events)
            
    def process_event(self, event):
        """處理單個事件"""
        try:
            event_type = event.get('type', '')
            target = event.get('target', {})
            
            # 記錄所有點擊事件的詳細信息
            if event_type == 'click':
                tag_name = target.get('tagName', '')
                text_content = target.get('textContent', '')
                class_name = target.get('className', '')
                element_id = target.get('id', '')
                element_type = target.get('type', '')
                value = target.get('value', '')
                
                self.log(f"🖱️ 點擊事件: <{tag_name}> '{text_content[:30]}' class='{class_name[:30]}' id='{element_id}' type='{element_type}' value='{value[:20]}'")
                
                # 使用統一的按鈕檢測邏輯
                if self.is_submit_button(target):
                    confidence = self.calculate_button_confidence(target)
                    self.log(f"🎯 可能的送出按鈕! 置信度: {confidence}")
                    
            elif event_type in ['keydown', 'input']:
                key = event.get('key', '')
                target_type = target.get('type', '')
                value = target.get('value', '')
                
                if event_type == 'keydown':
                    self.log(f"⌨️ 按鍵: '{key}' 目標類型: '{target_type}'")
                elif event_type == 'input' and target_type in ['text', 'password']:
                    # 記錄所有輸入，包括密碼（用於監聽分析）
                    if target_type == 'password':
                        self.log(f"🔑 密碼輸入: 值='{value}' 長度: {len(value)}")
                    elif self.is_verification_field(target):
                        self.log(f"🔢 驗證碼輸入: 值='{value}' 長度: {len(value)}")
                    else:
                        self.log(f"📝 文字輸入: 值='{value}' 長度: {len(value)}")
                    
        except Exception as e:
            self.log(f"⚠️ 處理事件時發生錯誤: {e}")

    def is_verification_field(self, target):
        """判斷是否為驗證碼欄位"""
        # 檢查各種可能的驗證碼標識
        element_id = target.get('id', '').lower()
        class_name = target.get('className', '').lower()
        name = target.get('name', '').lower()
        placeholder = target.get('placeholder', '').lower()
        aria_label = target.get('ariaLabel', '').lower()

        # 驗證碼關鍵字（中英文）
        verification_keywords = [
            'verif', 'captcha', 'code', 'verification', 'verify',
            '驗證碼', '驗證', '確認碼', 'authcode', 'vcode', 'checkcode'
        ]

        # 檢查所有可能的屬性
        all_text = f"{element_id} {class_name} {name} {placeholder} {aria_label}"

        return any(keyword in all_text for keyword in VERIFICATION_KEYWORDS)

    def is_submit_button(self, element_info):
        """統一的送出按鈕判斷邏輯"""
        try:
            # 獲取所有相關文字
            text_content = element_info.get('textContent', '') or element_info.get('text', '')
            class_name = element_info.get('className', '') or element_info.get('class', '')
            element_id = element_info.get('id', '')
            value = element_info.get('value', '')

            # 合併所有文字進行檢查
            all_text = f"{text_content} {class_name} {element_id} {value}".lower()

            # 檢查是否包含送出按鈕關鍵字
            return any(keyword in all_text for keyword in SUBMIT_BUTTON_INDICATORS)

        except Exception as e:
            return False

    def calculate_button_confidence(self, element_info):
        """計算按鈕是送出按鈕的置信度"""
        try:
            confidence_score = 0

            text_content = element_info.get('textContent', '') or element_info.get('text', '')
            class_name = element_info.get('className', '') or element_info.get('class', '')
            element_id = element_info.get('id', '')
            value = element_info.get('value', '')
            tag_name = element_info.get('tagName', '')
            element_type = element_info.get('type', '')

            # 文字內容匹配（最高權重）
            if any(keyword in text_content.lower() for keyword in ['送出', '提交', '確認', 'submit']):
                confidence_score += 10

            # 按鈕類型匹配
            if tag_name.lower() in ['button', 'input']:
                confidence_score += 5
                if element_type.lower() in ['submit', 'button']:
                    confidence_score += 5

            # CSS 類別匹配
            if any(keyword in class_name.lower() for keyword in ['submit', 'btn', 'button', 'primary']):
                confidence_score += 3

            # ID 匹配
            if any(keyword in element_id.lower() for keyword in ['submit', 'send', 'confirm']):
                confidence_score += 3

            # onclick 事件匹配
            onclick = element_info.get('onclick', '')
            if any(keyword in onclick.lower() for keyword in ['submit', 'send', 'confirm']):
                confidence_score += 2

            return confidence_score

        except Exception as e:
            return 0

    def take_screenshot(self):
        """截取當前頁面截圖"""
        try:
            if not self.driver:
                self.log("❌ 瀏覽器未啟動")
                return

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

            self.driver.save_screenshot(filename)
            self.log(f"📸 已截圖保存到: {filename}")

            # 同時記錄當前頁面信息
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title
                self.log(f"📍 截圖時頁面: {page_title}")
                self.log(f"🌐 截圖時 URL: {current_url}")
            except:
                pass

        except Exception as e:
            self.log(f"❌ 截圖失敗: {e}")

    def stop_tracking(self):
        """停止追蹤"""
        self.tracking_active = False
        self.start_tracking_btn.config(state="normal")
        self.stop_tracking_btn.config(state="disabled")
        self.analyze_btn.config(state="normal")
        self.status_label.config(text="⚪ 追蹤已停止", fg="black")
        
        self.log("⏹️ 追蹤已停止")
        self.log(f"📊 總共記錄了 {len(self.event_log)} 個事件")
        
    def analyze_results(self):
        """分析結果"""
        if not self.event_log:
            self.log("❌ 沒有事件記錄可分析")
            return
            
        self.log("📊 開始分析事件...")
        
        # 統計事件類型
        event_types = {}
        click_events = []
        key_events = []
        input_events = []
        
        for event in self.event_log:
            event_type = event.get('type', 'unknown')
            event_types[event_type] = event_types.get(event_type, 0) + 1
            
            if event_type == 'click':
                click_events.append(event)
            elif event_type == 'keydown':
                key_events.append(event)
            elif event_type == 'input':
                input_events.append(event)
        
        self.log(f"📋 事件類型統計:")
        for event_type, count in event_types.items():
            self.log(f"   {event_type}: {count} 個")
        
        # 分析點擊事件，尋找送出按鈕
        if click_events:
            self.log(f"\n🖱️ 點擊事件分析 ({len(click_events)} 個):")
            
            submit_candidates = []
            
            for i, event in enumerate(click_events):
                target = event.get('target', {})
                coords = event.get('coordinates', {})
                
                tag_name = target.get('tagName', '')
                text = target.get('textContent', '')
                value = target.get('value', '')
                class_name = target.get('className', '')
                element_id = target.get('id', '')
                
                # 增強的送出按鈕檢測邏輯
                submit_indicators = [
                    '送出', '提交', '確認', '儲存', 'submit', 'confirm', 'save', 'ok',
                    'button', 'btn', 'primary', 'success', '確定', '提交', '保存',
                    '送出', '確認送出', '提交訂單', '確認提交'
                ]

                # 多層次檢測
                all_text = f"{text} {value} {class_name} {element_id}".lower()
                found_indicators = [ind for ind in submit_indicators if ind in all_text]

                # 計算置信度分數
                confidence_score = 0

                # 文字內容匹配（最高權重）
                if any(keyword in text.lower() for keyword in ['送出', '提交', '確認', 'submit']):
                    confidence_score += 10

                # 按鈕類型匹配
                if tag_name.lower() in ['button', 'input']:
                    confidence_score += 5
                    if target.get('type', '').lower() in ['submit', 'button']:
                        confidence_score += 5

                # CSS 類別匹配
                if any(keyword in class_name.lower() for keyword in ['submit', 'btn', 'button', 'primary']):
                    confidence_score += 3

                # ID 匹配
                if any(keyword in element_id.lower() for keyword in ['submit', 'send', 'confirm']):
                    confidence_score += 3

                # onclick 事件匹配
                onclick = target.get('onclick', '')
                if any(keyword in onclick.lower() for keyword in ['submit', 'send', 'confirm']):
                    confidence_score += 2

                # 座標信息（用於定位策略）
                coords = event.get('coordinates', {})
                client_x = coords.get('clientX', 0)
                client_y = coords.get('clientY', 0)

                self.log(f"   點擊 {i+1}: <{tag_name}> '{text[:30]}' class='{class_name[:30]}' id='{element_id}' 座標=({client_x},{client_y})")

                if found_indicators or confidence_score > 0:
                    submit_candidates.append({
                        'event': event,
                        'indicators': found_indicators,
                        'confidence': confidence_score,
                        'selector_strategies': self.generate_selector_strategies(target)
                    })
                    self.log(f"      🎯 送出按鈕候選! 指標: {found_indicators} 置信度: {confidence_score}")

            # 顯示最可能的送出按鈕
            if submit_candidates:
                best_candidate = max(submit_candidates, key=lambda x: x['confidence'])
                self.log(f"\n🏆 最可能的送出按鈕:")
                target = best_candidate['event'].get('target', {})
                self.log(f"   標籤: {target.get('tagName', '')}")
                self.log(f"   文字: {target.get('textContent', '')}")
                self.log(f"   類別: {target.get('className', '')}")
                self.log(f"   ID: {target.get('id', '')}")
                self.log(f"   類型: {target.get('type', '')}")
                self.log(f"   置信度: {best_candidate['confidence']}")
                self.log(f"   指標: {best_candidate['indicators']}")

                # 顯示定位策略
                strategies = best_candidate.get('selector_strategies', [])
                if strategies:
                    self.log(f"   🎯 建議的定位策略:")
                    for strategy in strategies[:5]:  # 只顯示前5個
                        self.log(f"      {strategy}")
            else:
                self.log("❌ 未找到明確的送出按鈕")

        # 保存分析結果
        self.save_analysis_results()

    def generate_selector_strategies(self, target):
        """生成多種元素定位策略"""
        strategies = []

        # ID 策略（最優先）
        if target.get('id'):
            strategies.append(f"By.ID: '{target['id']}'")

        # Name 策略
        if target.get('name'):
            strategies.append(f"By.NAME: '{target['name']}'")

        # 測試屬性策略（常用於自動化測試）
        if target.get('ariaLabel'):
            strategies.append(f"By.CSS_SELECTOR: \"[aria-label='{target['ariaLabel']}']\"")

        # 角色屬性策略
        if target.get('role'):
            strategies.append(f"By.CSS_SELECTOR: \"[role='{target['role']}']\"")

        # Class 策略
        if target.get('className'):
            class_name = target['className'].strip()
            if class_name:
                # 單一類別
                first_class = class_name.split()[0]
                strategies.append(f"By.CLASS_NAME: '{first_class}'")
                # 完整類別
                strategies.append(f"By.CSS_SELECTOR: \".{class_name.replace(' ', '.')}\"")

        # 文字內容策略
        if target.get('textContent'):
            text = target['textContent'].strip()
            if text and len(text) < 50:  # 避免過長的文字
                # 精確匹配
                strategies.append(f"By.XPATH: \"//button[text()='{text}']\"")
                # 包含匹配
                strategies.append(f"By.XPATH: \"//button[contains(text(),'{text}')]\"")
                # 任意元素匹配
                strategies.append(f"By.XPATH: \"//*[contains(text(),'{text}')]\"")

        # 類型策略
        if target.get('type'):
            element_type = target['type']
            strategies.append(f"By.CSS_SELECTOR: \"input[type='{element_type}']\"")

        # 組合策略
        tag_name = target.get('tagName', '').lower()
        if tag_name and target.get('className'):
            first_class = target['className'].split()[0]
            strategies.append(f"By.CSS_SELECTOR: \"{tag_name}.{first_class}\"")

        # 標籤 + 類型組合
        if tag_name and target.get('type'):
            strategies.append(f"By.CSS_SELECTOR: \"{tag_name}[type='{target['type']}']\"")

        # XPath 組合策略
        if tag_name:
            xpath_conditions = []
            if target.get('id'):
                xpath_conditions.append(f"@id='{target['id']}'")
            if target.get('className'):
                xpath_conditions.append(f"contains(@class,'{target['className'].split()[0]}')")
            if target.get('type'):
                xpath_conditions.append(f"@type='{target['type']}'")

            if xpath_conditions:
                conditions_str = ' and '.join(xpath_conditions)
                strategies.append(f"By.XPATH: \"//{tag_name}[{conditions_str}]\"")

        return strategies
        
    def save_analysis_results(self):
        """保存分析結果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tracking_analysis_{timestamp}.json"
            
            analysis_data = {
                'timestamp': timestamp,
                'total_events': len(self.event_log),
                'events': self.event_log
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)
            
            self.log(f"💾 分析結果已保存到: {filename}")
            
        except Exception as e:
            self.log(f"❌ 保存分析結果失敗: {e}")
            
    def cleanup(self):
        """清理資源"""
        try:
            if self.driver:
                self.log("🧹 清理瀏覽器資源...")
                self.driver.quit()
                self.driver = None
        except Exception as e:
            self.log(f"⚠️ 清理資源時發生錯誤: {e}")

    def on_closing(self):
        """視窗關閉事件"""
        self.cleanup()
        self.root.destroy()

    def run(self):
        """運行應用程式"""
        self.log("🚀 獨立追蹤工具已啟動")
        self.log("💡 使用說明:")
        self.log("   1. 點擊 '啟動瀏覽器' 開始")
        self.log("   2. 手動登入並進入編輯頁面")
        self.log("   3. 點擊 '啟動追蹤' 開始記錄")
        self.log("   4. 執行操作後點擊 '停止追蹤'")
        self.log("   5. 點擊 '分析結果' 查看送出按鈕")

        # 設置視窗關閉事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("⚠️ 程式被中斷")
        finally:
            self.cleanup()

if __name__ == "__main__":
    app = IndependentTrackingTool()
    app.run()
